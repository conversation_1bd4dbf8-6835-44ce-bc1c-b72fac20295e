{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(find:*)", "Bash(pnpm relay:*)", "Bash(pnpm check:*)", "Bash(git add:*)", "Bash(git --no-pager diff ui)", "Bash(git commit:*)", "Bash(rm:*)", "Bash(grep:*)", "<PERSON><PERSON>(pushd:*)", "<PERSON><PERSON>(popd:*)", "Bash(pnpm test:*)", "Bash(echo)", "Bash(timeout 30 pnpm check)", "Bash(rg:*)", "<PERSON><PERSON>(sed:*)", "Bash(npx tsc:*)", "<PERSON><PERSON>(timeout 60 pnpm test:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(npx jest:*)", "Bash(ls:*)", "<PERSON><PERSON>(claude:*)", "Bash(git reset:*)", "<PERSON><PERSON>(chmod:*)", "Bash(node:*)", "Bash(pnpm add:*)", "<PERSON><PERSON>(mv:*)", "Bash(pnpm lint:fragments:*)", "Bash(pnpm eslint:*)", "Bash(pnpm lint:timesheet:core:*)", "Bash(pnpm lint:timesheet:*)", "Bash(npx eslint:*)", "Bash(dotnet build)", "WebFetch(domain:learn.microsoft.com)", "Bash(dotnet build:*)", "Bash(dotnet clean:*)", "Bash(dotnet test)", "Bash(pnpm jest:*)", "Bash(pnmp test:all)", "Bash(pnpm run build:*)", "WebFetch(domain:relay.dev)", "<PERSON><PERSON>(curl:*)", "Bash(npm:*)", "Bash(git rebase:*)", "Bash(git fetch:*)", "Bash(git checkout:*)", "Bash(cp:*)", "Bash(git push:*)", "Bash(git branch:*)", "Bash(git restore:*)", "Bash(pnpm lint:*)", "Bash(dotnet test:*)", "Bash(pnpm type-check:*)", "Bash(pnpm:*)", "Bash(awk:*)", "<PERSON><PERSON>(wait)"], "deny": []}, "enableAllProjectMcpServers": false}