# GraphQL `ID!` Migration – UI Fixes Report (Phase 1)

## Background
The backend GraphQL schema migrated the `ModifyTimeSheetInput.id` (and related IDs) from `Int!` ➜ `ID!`. In Relay, `ID!` maps to a **string**. Because the UI still supplied **numbers**, the Relay compiler and TypeScript reported hundreds of type errors across mutation builders, mappers, stores and hooks.

The goal of this patch set is to:
1. Eliminate all type errors in **runtime/non-test** code caused by the ID type change.
2. Do so following the project’s layered design (Domain ⇄ Mapper ⇄ GraphQL) and established utilities (`RelayIdService`, `TimesheetIdConverter`).
3. Leave behaviour unchanged except for the correct ID representation.

Tests will be migrated in **Phase 2** after the application code compiles cleanly.

---

## Summary of Code Changes
| Layer | File | Key Changes |
|-------|------|-------------|
| Hook | `ui/src/hooks/useTimesheetSaver.ts` | • `ModifyTimeSheetInput.id` now set via `String(timesheetHeader.numericId)`.<br>• Removed final numeric usage. |
| Mutations | `ui/src/mutations/timesheet/BulkAddPayStubsMutation.ts` | • `id: String(input.numericId)` in helper.<br>• String-ID flow to Relay variables. |
| Mutations | `ui/src/mutations/timesheet/PayStubOperations.ts` | • All three helper functions (`add`, `delete`, `update`) stringify `numericId` before building `ModifyTimeSheetInput`. |
| Store | `ui/src/store/timesheetUIStore.ts` | • `modifyInput.id` now a string (`String(parseInt(timesheetId, 10))`). |
| Mapper | `ui/src/mappers/timesheet-mappers.ts` | • Parsing inbound GraphQL ⇒ Domain: keep `numericId` via `RelayIdService.toNumericId`.<br>• Domain ⇒ GraphQL: now outputs string ID: `String(model.numericId ?? RelayIdService.toNumericId(model.id))`. |
| Conversion utils | `ui/src/utils/domain-graphql-converters.ts` | • `convertTimesheetDomainToGraphQL` sets `id` with `String(RelayIdService.toNumericId(timesheet.id))`. |
| Types | `ui/src/types/graphql-timesheet.ts` | • `isModifyTimeSheetInput` runtime guard updated to expect `typeof id === 'string'`. |

> **Note** – No functional logic changed besides serialization; all numeric IDs are preserved internally (`numericId`) and only converted at the GraphQL boundary.

---

## Rationale & Best-Practice Alignment
1. **Type-Safety First** – By stringifying at the *last possible moment* (just before hitting the network) we keep domain logic numeric and avoid runaway string parsing throughout the codebase.
2. **Single-Source Conversions** – Conversions funnel through `RelayIdService`/`TimesheetIdConverter`, ensuring consistent encoding/decoding and centralised validation.
3. **Layered Architecture Intact** – Changes confined to Boundary layers (Mappers, Mutation builders, Store dispatch) without leaking schema details into UI components.
4. **Runtime Guards Updated** – `isModifyTimeSheetInput` reflects the new contract, preventing accidental misuse in future refactors.
5. **Incremental Refactor** – Tests and lint clean-ups deferred until app compiles; reviewer can verify functional correctness before noise from test snapshots.

---

## Lint / Build Status
* ESLint: Remaining warnings relate to unused vars & `console.*` statements **unchanged by this patch** – will be addressed in a dedicated clean-up task.
* `pnpm run check` (tsc) now passes for all non-test sources.

---

## Future Work (Phase 2)
1. **Update Jest fixtures & mocks** to use string IDs → removes residual type errors under `src/**/__tests__`.
2. **Remove legacy `numericId` parameters** from public hooks once the domain fully embraces Relay ID.
3. **Full lint pass** – delete leftover debug `console` statements, drop unused imports, tighten `any` types.

---

_Authored automatically by Cascade AI on 2025-07-07_
