/**
 * Jest configuration for the UI project
 */

module.exports = {
    // The test environment that will be used for testing
    testEnvironment: 'jsdom',

    // The root directory that <PERSON><PERSON> should scan for tests and modules
    rootDir: '.',

    // A list of paths to directories that <PERSON><PERSON> should use to search for files in
    roots: ['<rootDir>/src'],

    // The glob patterns <PERSON><PERSON> uses to detect test files
    testMatch: ['**/__tests__/**/*.+(ts|tsx|js)', '**/?(*.)+(spec|test|integration.test).+(ts|tsx|js)'],

    // An array of file extensions your modules use
    moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],

    // A map from regular expressions to module names that allow to stub out resources
    moduleNameMapper: {
        // Handle CSS imports (with CSS modules)
        '\\.module\\.(css|sass|scss)$': 'identity-obj-proxy',

        // Handle CSS imports (without CSS modules)
        '\\.(css|sass|scss)$': 'identity-obj-proxy',

        // Handle image imports
        '\\.(jpg|jpeg|png|gif|webp|svg)$': '<rootDir>/src/__mocks__/fileMock.js',

        // Handle module aliases
        '^@/src/(.*)$': '<rootDir>/src/$1',
        '^@/lib$': '<rootDir>/src/lib/index',
        '^@/lib/(.*)$': '<rootDir>/src/lib/$1',
        '^@/errorHandling$': '<rootDir>/src/errorHandling/index',
        '^@/errorHandling/(.*)$': '<rootDir>/src/errorHandling/$1',
        '^@/relay/(.*)$': '<rootDir>/src/relay/$1',
        '^@ui/(.*)$': '<rootDir>/src/components/UI/$1'
    },

    // Transform files with babel-jest
    transform: {
        // Use babel-jest for all JS, TS, JSX, and TSX files
        // This ensures that the Relay GraphQL transforms are applied consistently
        '^.+\\.(js|jsx|ts|tsx)$': ['babel-jest', { rootMode: 'upward' }]
    },

    // Indicates whether each individual test should be reported during the run
    verbose: true,

    // Automatically clear mock calls and instances between every test
    clearMocks: true,

    // The directory where Jest should output its coverage files
    coverageDirectory: 'coverage',

    // An array of regexp pattern strings that are matched against all test paths before executing the test
    testPathIgnorePatterns: [
        '/node_modules/',
        '/__tests__/testUtils\\.(ts|tsx)$',
        '/__tests__/.*Utils\\.(ts|tsx)$'
    ],

    // An array of regexp pattern strings that are matched against all source file paths before re-running tests
    watchPathIgnorePatterns: ['/node_modules/'],

    // Indicates whether the coverage information should be collected while executing the test
    collectCoverage: false,

    // An array of glob patterns indicating a set of files for which coverage information should be collected
    collectCoverageFrom: [
        'src/**/*.{ts,tsx}',
        '!src/**/*.d.ts',
        '!src/**/*.stories.{ts,tsx}',
        '!src/**/__tests__/**',
        '!src/**/__mocks__/**'
    ],

    // The directory where Jest should store its cached dependency information
    cacheDirectory: '<rootDir>/node_modules/.cache/jest',

    // Setup files after environment is set up
    setupFilesAfterEnv: ['<rootDir>/jest.setup.cjs'],

    // A list of paths to modules that run some code to configure or set up the testing framework before each test
    setupFiles: [],

    // The maximum amount of workers used to run your tests
    maxWorkers: '50%',

    // An array of regexp pattern strings that are matched against all file paths before executing the test
    transformIgnorePatterns: [
        '/node_modules/(?!(@adobe|@spectrum-icons|@react-spectrum|@react-aria|@react-stately|@internationalized|react-csv)/)'
    ],

    // Performance testing configuration
    testTimeout: 30000 // Increased timeout for performance tests
};
