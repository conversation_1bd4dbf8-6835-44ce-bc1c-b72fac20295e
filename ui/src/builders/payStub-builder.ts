/**
 * PayStub Builder - Phase 2: Incremental Builder Pattern
 * 
 * This file provides a builder pattern for constructing PayStub objects incrementally,
 * enabling type-safe and flexible PayStub construction for various scenarios.
 * 
 * The builder pattern allows for:
 * - Step-by-step construction of complex PayStub objects
 * - Validation at build time to ensure required fields are present
 * - Flexibility to create PayStubs from different data sources
 * - Type safety throughout the construction process
 */

import { 
    convertPayStubToGraphQL, 
    convertPayStubFromGraphQL,
    validatePayStubForGraphQL,
    createNewPayStubGraphQLInput 
} from '../mappers/timesheet-mappers';
import type { PayStubDomainModel, PayStubDetailDomainModel } from '../types/timesheet-domain';
import type { ModifyPayStubInput } from '../types/graphql-timesheet';
import { RelayIdService } from '../services/RelayIdService';

// =============================================================================
// PAYSTUB BUILDER CLASS
// =============================================================================

/**
 * Builder class for constructing PayStub objects incrementally
 * Provides a fluent interface for building PayStubs with validation
 */
export class PayStubBuilder {
    private draft: Partial<ModifyPayStubInput> = {};
    private domainData: Partial<PayStubDomainModel> = {};

    // =============================================================================
    // STATIC FACTORY METHODS
    // =============================================================================

    /**
     * Creates a builder from an existing PayStub domain model
     */
    static fromExisting(payStub: PayStubDomainModel): PayStubBuilder {
        const builder = new PayStubBuilder();
        builder.draft = convertPayStubToGraphQL(payStub);
        builder.domainData = { ...payStub };
        return builder;
    }

    /**
     * Creates a builder from GraphQL input data
     */
    static fromGraphQL(input: ModifyPayStubInput): PayStubBuilder {
        const builder = new PayStubBuilder();
        builder.draft = { ...input };
        builder.domainData = convertPayStubFromGraphQL(input);
        return builder;
    }

    /**
     * Creates a builder for a new PayStub with minimal required data
     */
    static forNewPayStub(employeeId: number, employeeName: string): PayStubBuilder {
        const builder = new PayStubBuilder();
        const employeeGlobalId = RelayIdService.toGlobalId('Employee', employeeId);
        builder.draft = createNewPayStubGraphQLInput(employeeGlobalId, employeeName);
        builder.domainData = {
            id: '',
            employeeId: String(employeeId),
            employeeName,
            name: '',
            hours: { standard: 0, overtime: 0, doubletime: 0, total: 0 },
            amounts: { bonus: 0, expenses: 0 },
            details: [],
            employee: {
                id: String(employeeId),
                firstName: '',
                lastName: '',
                fullName: employeeName,
                externalEmployeeId: String(employeeId),
                active: true,
            },
            ui: {
                expanded: true,
                isEditing: true,
                hasErrors: false,
                isSelected: false,
                isTemporary: true,
            }
        };
        return builder;
    }

    /**
     * Creates an empty builder for manual construction
     */
    static create(): PayStubBuilder {
        return new PayStubBuilder();
    }

    // =============================================================================
    // FLUENT BUILDER METHODS
    // =============================================================================

    /**
     * Sets the PayStub ID
     */
    withId(id: string): this {
        this.draft.id = id;
        if (this.domainData) this.domainData.id = id;
        return this;
    }

    /**
     * Sets the employee information
     */
    withEmployee(employeeId: string, employeeName: string): this {
        this.draft.employeeId = employeeId;
        this.draft.employeeName = employeeName;
        
        if (this.domainData) {
            this.domainData.employeeId = String(employeeId);
            this.domainData.employeeName = employeeName;
            if (this.domainData.employee) {
                this.domainData.employee.id = String(employeeId);
                this.domainData.employee.fullName = employeeName;
                this.domainData.employee.externalEmployeeId = String(employeeId);
            }
        }
        return this;
    }

    /**
     * Sets the PayStub name/description
     */
    withName(name: string): this {
        this.draft.name = name;
        if (this.domainData) this.domainData.name = name;
        return this;
    }

    /**
     * Sets the hours worked (all types)
     */
    withHours(standard: number, overtime?: number, doubletime?: number): this {
        this.draft.stHours = standard;
        this.draft.otHours = overtime ?? 0;
        this.draft.dtHours = doubletime ?? 0;
        
        if (this.domainData && this.domainData.hours) {
            this.domainData.hours.standard = standard;
            this.domainData.hours.overtime = overtime ?? 0;
            this.domainData.hours.doubletime = doubletime ?? 0;
            this.domainData.hours.total = standard + (overtime ?? 0) + (doubletime ?? 0);
        }
        return this;
    }

    /**
     * Sets standard hours only
     */
    withStandardHours(hours: number): this {
        this.draft.stHours = hours;
        if (this.domainData && this.domainData.hours) {
            this.domainData.hours.standard = hours;
            this.domainData.hours.total = hours + this.domainData.hours.overtime + this.domainData.hours.doubletime;
        }
        return this;
    }

    /**
     * Sets overtime hours only
     */
    withOvertimeHours(hours: number): this {
        this.draft.otHours = hours;
        if (this.domainData && this.domainData.hours) {
            this.domainData.hours.overtime = hours;
            this.domainData.hours.total = this.domainData.hours.standard + hours + this.domainData.hours.doubletime;
        }
        return this;
    }

    /**
     * Sets doubletime hours only
     */
    withDoubletimeHours(hours: number): this {
        this.draft.dtHours = hours;
        if (this.domainData && this.domainData.hours) {
            this.domainData.hours.doubletime = hours;
            this.domainData.hours.total = this.domainData.hours.standard + this.domainData.hours.overtime + hours;
        }
        return this;
    }

    /**
     * Sets monetary amounts
     */
    withAmounts(bonus: number, expenses: number): this {
        this.draft.bonus = bonus;
        this.draft.expenses = expenses;
        
        if (this.domainData && this.domainData.amounts) {
            this.domainData.amounts.bonus = bonus;
            this.domainData.amounts.expenses = expenses;
        }
        return this;
    }

    /**
     * Sets bonus amount only
     */
    withBonus(bonus: number): this {
        this.draft.bonus = bonus;
        if (this.domainData && this.domainData.amounts) {
            this.domainData.amounts.bonus = bonus;
        }
        return this;
    }

    /**
     * Sets expenses amount only
     */
    withExpenses(expenses: number): this {
        this.draft.expenses = expenses;
        if (this.domainData && this.domainData.amounts) {
            this.domainData.amounts.expenses = expenses;
        }
        return this;
    }

    /**
     * Sets the details array
     */
    withDetails(details: PayStubDetailDomainModel[]): this {
        // Convert domain details to GraphQL format for draft
        this.draft.details = details.map(detail => ({
            id: detail.id,
            payStubId: detail.payStubId,
            workDate: detail.workDate,
            name: detail.name,
            stHours: detail.hours.standard,
            otHours: detail.hours.overtime,
            dtHours: detail.hours.doubletime,
            jobCode: detail.job.jobCode,
            costCenter: detail.job.costCenter,
            hourlyRate: detail.job.hourlyRate,
            agreementId: detail.agreements.agreementId,
            classificationId: detail.agreements.classificationId,
            subClassificationId: detail.agreements.subClassificationId,
            bonus: detail.amounts.bonus,
            expenses: detail.amounts.expenses,
            earningsCode: detail.earnings.earningsCode,
            reportLineItemId: detail.reportLineItemId,
        }));

        if (this.domainData) {
            this.domainData.details = details;
        }
        return this;
    }

    /**
     * Adds a single detail to the existing details
     */
    addDetail(detail: PayStubDetailDomainModel): this {
        const currentDetails = this.draft.details ? [...this.draft.details] : [];
        if (!this.domainData.details) this.domainData.details = [];

        // Add to GraphQL draft (create new array since it's readonly)
        currentDetails.push({
            id: detail.id,
            payStubId: detail.payStubId,
            workDate: detail.workDate,
            name: detail.name,
            stHours: detail.hours.standard,
            otHours: detail.hours.overtime,
            dtHours: detail.hours.doubletime,
            jobCode: detail.job.jobCode,
            costCenter: detail.job.costCenter,
            hourlyRate: detail.job.hourlyRate,
            agreementId: detail.agreements.agreementId,
            classificationId: detail.agreements.classificationId,
            subClassificationId: detail.agreements.subClassificationId,
            bonus: detail.amounts.bonus,
            expenses: detail.amounts.expenses,
            earningsCode: detail.earnings.earningsCode,
            reportLineItemId: detail.reportLineItemId,
        });

        // Update the draft details
        this.draft.details = currentDetails;

        // Add to domain data
        this.domainData.details.push(detail);
        return this;
    }

    /**
     * Sets UI state
     */
    withUIState(state: { expanded?: boolean; isEditing?: boolean; isSelected?: boolean }): this {
        if (state.expanded !== undefined) this.draft.expanded = state.expanded;
        if (state.isEditing !== undefined) this.draft.inEdit = state.isEditing;
        
        if (this.domainData && this.domainData.ui) {
            if (state.expanded !== undefined) this.domainData.ui.expanded = state.expanded;
            if (state.isEditing !== undefined) this.domainData.ui.isEditing = state.isEditing;
            if (state.isSelected !== undefined) this.domainData.ui.isSelected = state.isSelected;
        }
        return this;
    }

    /**
     * Marks the PayStub as expanded
     */
    expanded(expanded: boolean = true): this {
        this.draft.expanded = expanded;
        if (this.domainData && this.domainData.ui) {
            this.domainData.ui.expanded = expanded;
        }
        return this;
    }

    /**
     * Marks the PayStub as being edited
     */
    editing(editing: boolean = true): this {
        this.draft.inEdit = editing;
        if (this.domainData && this.domainData.ui) {
            this.domainData.ui.isEditing = editing;
        }
        return this;
    }

    /**
     * Marks the PayStub for deletion (handled separately in separate arrays pattern)
     * Note: In the new separate arrays pattern, deletion is handled via deletePayStubIds array
     */
    forDeletion(shouldDelete: boolean = true): this {
        // Store deletion intent in a custom property (not part of GraphQL schema)
        (this.draft as any).markedForDeletion = shouldDelete;
        return this;
    }

    // =============================================================================
    // BUILD METHODS
    // =============================================================================

    /**
     * Builds and returns the GraphQL input
     * Validates required fields before building
     */
    buildGraphQL(): ModifyPayStubInput {
        // Validate required fields for ModifyPayStubInput
        const required = ['id', 'employeeId'];
        const missing = required.filter(field => 
            this.draft[field as keyof ModifyPayStubInput] === undefined || 
            this.draft[field as keyof ModifyPayStubInput] === null ||
            this.draft[field as keyof ModifyPayStubInput] === ''
        );

        if (missing.length > 0) {
            throw new Error(`PayStub is missing required fields: ${missing.join(', ')}`);
        }

        // Build GraphQL input (only include GraphQL-compatible fields)
        const result: ModifyPayStubInput = {
            id: this.draft.id!,
            employeeId: this.draft.employeeId!,
            employeeName: this.draft.employeeName,
            name: this.draft.name,
            stHours: this.draft.stHours ?? 0,
            otHours: this.draft.otHours ?? 0,
            dtHours: this.draft.dtHours ?? 0,
            bonus: this.draft.bonus ?? 0,
            expenses: this.draft.expenses ?? 0,
            details: this.draft.details ?? [],
        };

        return result;
    }

    /**
     * Builds and returns the domain model
     * Converts from GraphQL if needed and ensures consistency
     */
    buildDomainModel(): PayStubDomainModel {
        // If we have domain data, use it; otherwise convert from GraphQL draft
        if (this.domainData && this.domainData.id !== undefined) {
            // Ensure required fields
            const result: PayStubDomainModel = {
                id: this.domainData.id ?? '',
                employeeId: this.domainData.employeeId ?? '',
                employeeName: this.domainData.employeeName ?? '',
                name: this.domainData.name ?? '',
                hours: this.domainData.hours ?? { standard: 0, overtime: 0, doubletime: 0, total: 0 },
                amounts: this.domainData.amounts ?? { bonus: 0, expenses: 0 },
                details: this.domainData.details ?? [],
                employee: this.domainData.employee ?? {
                    id: this.domainData.employeeId ?? '',
                    firstName: '',
                    lastName: '',
                    fullName: this.domainData.employeeName ?? '',
                    externalEmployeeId: this.domainData.employeeId ?? '',
                    active: true,
                },
                ui: this.domainData.ui ?? {
                    expanded: false,
                    isEditing: false,
                    hasErrors: false,
                    isSelected: false,
                    isTemporary: false,
                }
            };

            return result;
        } else {
            // Convert from GraphQL draft
            const graphqlInput = this.buildGraphQL();
            return convertPayStubFromGraphQL(graphqlInput);
        }
    }

    /**
     * Validates the current builder state
     * Returns validation results without building
     */
    validate(): { isValid: boolean; errors: string[] } {
        try {
            const domainModel = this.buildDomainModel();
            return validatePayStubForGraphQL(domainModel);
        } catch (error) {
            return {
                isValid: false,
                errors: [error instanceof Error ? error.message : 'Unknown validation error']
            };
        }
    }

    /**
     * Returns a copy of the current draft state for inspection
     */
    getDraft(): Partial<ModifyPayStubInput> {
        return { ...this.draft };
    }

    /**
     * Returns a copy of the current domain data for inspection
     */
    getDomainData(): Partial<PayStubDomainModel> {
        return { ...this.domainData };
    }

    /**
     * Clears the builder state
     */
    reset(): this {
        this.draft = {};
        this.domainData = {};
        return this;
    }

    /**
     * Creates a copy of this builder
     */
    clone(): PayStubBuilder {
        const newBuilder = new PayStubBuilder();
        newBuilder.draft = { ...this.draft };
        newBuilder.domainData = JSON.parse(JSON.stringify(this.domainData));
        return newBuilder;
    }
}

// =============================================================================
// CONVENIENCE FUNCTIONS
// =============================================================================

/**
 * Quick builder function for creating a new PayStub
 */
export function buildNewPayStub(
    employeeId: number,
    employeeName: string,
    customizer?: (builder: PayStubBuilder) => PayStubBuilder
): PayStubDomainModel {
    let builder = PayStubBuilder.forNewPayStub(employeeId, employeeName);
    
    if (customizer) {
        builder = customizer(builder);
    }
    
    return builder.buildDomainModel();
}

/**
 * Quick builder function for updating an existing PayStub
 */
export function buildUpdatedPayStub(
    existingPayStub: PayStubDomainModel,
    customizer: (builder: PayStubBuilder) => PayStubBuilder
): PayStubDomainModel {
    const builder = PayStubBuilder.fromExisting(existingPayStub);
    const updatedBuilder = customizer(builder);
    return updatedBuilder.buildDomainModel();
}

/**
 * Quick builder function for creating a PayStub with specific hours
 */
export function buildPayStubWithHours(
    employeeId: number,
    employeeName: string,
    standard: number,
    overtime: number = 0,
    doubletime: number = 0
): PayStubDomainModel {
    return PayStubBuilder
        .forNewPayStub(employeeId, employeeName)
        .withHours(standard, overtime, doubletime)
        .buildDomainModel();
}

// =============================================================================
// TYPE EXPORTS
// =============================================================================

// PayStubBuilder is already exported as a class above, no need for type export here