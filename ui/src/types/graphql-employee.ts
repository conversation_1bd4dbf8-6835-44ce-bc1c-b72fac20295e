/**
 * GraphQL Type Barrel System for Employee Operations
 *
 * This file provides stable barrel exports for GraphQL employee types,
 * avoiding direct imports from generated files and following Rule 14
 * requirements for GraphQL-Generated Types usage.
 *
 * Architecture:
 * ┌─────────────────────────────────────────┐
 * │ UI Components (Domain Models)           │ ← Rich, UI-friendly types
 * ├─────────────────────────────────────────┤
 * │ Conversion Layer (Mappers)              │ ← Transform between layers
 * ├─────────────────────────────────────────┤
 * │ GraphQL Operations (Wire Format)        │ ← Exact server contract (THIS FILE)
 * └─────────────────────────────────────────┘
 */

// =============================================================================
// STABLE BARREL IMPORTS - EMPLOYEE QUERY TYPES
// =============================================================================

/**
 * Employee default settings query types
 */
export type {
    useEmployeeDefaultSettingsQueriesSingleEmployeeQuery,
    useEmployeeDefaultSettingsQueriesSingleEmployeeQuery$data,
    useEmployeeDefaultSettingsQueriesSingleEmployeeQuery$variables
} from '@/src/relay/__generated__/useEmployeeDefaultSettingsQueriesSingleEmployeeQuery.graphql';

/**
 * Core employee types from fragments and queries
 * Note: Employee base type comes from the schema, not fragment files
 */

/**
 * Employee fragment types for component usage
 */
export type {
    EmployeeDisplayFragment_employee$key,
    EmployeeDisplayFragment_employee$data
} from '@/src/relay/__generated__/EmployeeDisplayFragment_employee.graphql';

export type {
    TimeSheetDetailRow_employee$key,
    TimeSheetDetailRow_employee$data
} from '@/src/relay/__generated__/TimeSheetDetailRow_employee.graphql';

// =============================================================================
// EMPLOYEE-SPECIFIC UTILITY TYPES
// =============================================================================

/**
 * Employee draft type for form editing
 * Note: Using EmployeeDisplayData as base since EmployeeInput doesn't exist in current schema
 */
export type EmployeeDraft = Partial<EmployeeDisplayData>;

/**
 * Employee search filters for UI components
 */
export type EmployeeSearchFilters = {
    firstName?: string | null;
    lastName?: string | null;
    active?: boolean | null;
    externalEmployeeId?: string | null;
};

/**
 * Employee display data for UI components
 */
export type EmployeeDisplayData = {
    id: string;
    firstName?: string | null;
    lastName?: string | null;
    fullName: string;
    externalEmployeeId?: string | null;
    active?: boolean;
};

/**
 * Employee default settings data structure
 */
export type EmployeeDefaultSettings = {
    defaultAgreementId?: number | null;
    defaultClassificationId?: number | null;
    defaultHourlyRate?: string | null;
};

// =============================================================================
// CONSTANTS FOR EMPLOYEE FIELD NAMES
// =============================================================================

/**
 * Constants for employee GraphQL field names to prevent typos
 * These are the EXACT field names used in GraphQL operations
 */
export const EMPLOYEE_FIELD_NAMES = {
    ID: 'id',
    FIRST_NAME: 'firstName',
    LAST_NAME: 'lastName',
    EXTERNAL_EMPLOYEE_ID: 'externalEmployeeId',
    ACTIVE: 'active',
    DEFAULT_AGREEMENT_ID: 'defaultAgreementId',
    DEFAULT_CLASSIFICATION_ID: 'defaultClassificationId',
    DEFAULT_HOURLY_RATE: 'defaultHourlyRate'
} as const;

/**
 * Type for employee field names to ensure type safety
 */
export type EmployeeFieldName = (typeof EMPLOYEE_FIELD_NAMES)[keyof typeof EMPLOYEE_FIELD_NAMES];
