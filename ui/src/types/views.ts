import { EmployerRosterViews, TimesheetsRosterViews } from '../constants/views';
import { ColumnType } from '@/src/types/rosters';
import { TimesheetRosterQuery$variables } from '@/src/relay/__generated__/TimesheetRosterQuery.graphql';
import { EmployerRosterGridQuery$variables, EmployerRosterViewSortInput } from '@/src/relay/__generated__/EmployerRosterGridQuery.graphql';

export type BaseViewOption = {
    id: string;
    name: string;
    description: string;
    isBuiltIn?: boolean;
    isSystemDefault?: boolean;
    columns?: ColumnType[];
    sortOrder: readonly EmployerRosterViewSortInput[] | null | undefined;
    filter: EmployerRosterGridQuery$variables | TimesheetRosterQuery$variables;
};

export type EmployerRosterViewsOption = BaseViewOption & {
    id: EmployerRosterViews;
};

export type TimesheetsRosterViewsOption = BaseViewOption & { id: TimesheetsRosterViews };

export interface SavedCustomView {
    id: string;
    name: string;
    description: string;
    type: string;
    filter: Filter;
    columns: ColumnType[];
    sortOrder: SortOrder[];
    savedLocalView: SavedLocalView;
}

interface Filter {
    chapterId: string;
    order: Order[];
    where: Where;
}

interface Order {
    [key: string]: 'ASC' | 'DESC'; // Dynamic field based on the column name, either ASC or DESC
}

interface Where {
    and: Condition[];
}

interface Condition {
    or?: ConditionItem[];
}

interface ConditionItem {
    [key: string]: {
        contains?: string;
        eq?: string;
        ne?: string;
    };
}

interface SortOrder {
    [key: string]: 'ASC' | 'DESC';
}

interface SavedLocalView {
    data: SavedLocalViewData;
    localFilter: LocalFilter;
}

interface SavedLocalViewData {
    dateRange: string;
    feinOptions: FeinOption[];
    employerNameOptions: EmployerNameOption[];
}

interface FeinOption {
    value: string;
    label: string;
}

interface EmployerNameOption {
    value: string;
    label: string;
}

interface LocalFilter {
    and: Condition[];
}
