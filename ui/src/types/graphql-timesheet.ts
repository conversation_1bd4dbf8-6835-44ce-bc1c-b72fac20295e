/**
 * GraphQL Type Barrel System for Timesheet Operations
 *
 * Phase 1: Foundation - Type Barrel System
 *
 * This file provides stable barrel exports for GraphQL timesheet types,
 * avoiding direct imports from generated files and following the
 * implementation plan's layered type architecture.
 *
 * Architecture:
 * ┌─────────────────────────────────────────┐
 * │ UI Components (Domain Models)           │ ← Rich, UI-friendly types
 * ├─────────────────────────────────────────┤
 * │ Conversion Layer (Mappers)              │ ← Transform between layers
 * ├─────────────────────────────────────────┤
 * │ GraphQL Operations (Wire Format)        │ ← Exact server contract (THIS FILE)
 * └─────────────────────────────────────────┘
 */

// =============================================================================
// STABLE BARREL IMPORTS - MUTATION INPUT TYPES
// =============================================================================

/**
 * Core timesheet mutation input types - these represent the exact GraphQL schema
 * and should be used for all mutation operations
 */
export type {
    ModifyTimeSheetInput,
    ModifyPayStubInput,
    ModifyPayStubDetailInput
} from '@/lib/relay/__generated__/ModifyTimeSheetMutation.graphql';

export type {
    ModifyTimeSheetMutation$variables,
    ModifyTimeSheetMutation$data
} from '@/lib/relay/__generated__/ModifyTimeSheetMutation.graphql';

/**
 * Bulk operations input types
 */
export type {
    BulkAddPayStubsMutation$variables,
    BulkAddPayStubsMutation$data,
    BulkAddPayStubsMutation
} from '@/lib/relay/__generated__/BulkAddPayStubsMutation.graphql';

// Central Relay ID helpers
import { RelayIdService } from '../services/RelayIdService';

/**
 * Add operations input types
 */
export type {
    AddEmptyPayStubMutation$variables,
    AddEmptyPayStubMutation$data,
    AddEmptyPayStubMutation
} from '@/lib/relay/__generated__/AddEmptyPayStubMutation.graphql';

export type {
    useTimesheetSaverAddMutation$variables,
    useTimesheetSaverAddMutation$data,
    useTimesheetSaverAddMutation,
    AddTimesheetInput,
    AddPayStubInput,
    AddPayStubDetailInput
} from '@/lib/relay/__generated__/useTimesheetSaverAddMutation.graphql';

export type {
    useTimesheetSaverModifyMutation$variables,
    useTimesheetSaverModifyMutation$data
} from '@/lib/relay/__generated__/useTimesheetSaverModifyMutation.graphql';

// Mutation operation types are now included in the main exports above

// =============================================================================
// IMPORT TYPES FOR DERIVED TYPES
// =============================================================================

import type {
    ModifyTimeSheetInput as _ModifyTimeSheetInput,
    ModifyPayStubInput as _ModifyPayStubInput,
    ModifyPayStubDetailInput as _ModifyPayStubDetailInput
} from '@/lib/relay/__generated__/ModifyTimeSheetMutation.graphql';

// =============================================================================
// UTILITY TYPES FOR COMMON PATTERNS
// =============================================================================

/**
 * Draft types for form state management
 * These represent partial input states before conversion to GraphQL
 */
export type ModifyTimeSheetDraft = Partial<_ModifyTimeSheetInput>;
export type ModifyPayStubDraft = Partial<_ModifyPayStubInput>;
export type ModifyPayStubDetailDraft = Partial<_ModifyPayStubDetailInput>;

/**
 * Required field helpers - these ensure type safety for operations that need
 * specific required fields to be present
 */
export type ModifyTimeSheetRequired = Required<Pick<_ModifyTimeSheetInput, 'id' | 'employerGuid'>> &
    Omit<_ModifyTimeSheetInput, 'id' | 'employerGuid'>;

export type ModifyPayStubRequired = Required<Pick<_ModifyPayStubInput, 'employeeId'>> & Omit<_ModifyPayStubInput, 'employeeId'>;

export type ModifyPayStubDetailRequired = Required<Pick<_ModifyPayStubDetailInput, 'workDate'>> &
    Omit<_ModifyPayStubDetailInput, 'workDate'>;

// =============================================================================
// ARRAY TYPE HELPERS
// =============================================================================

/**
 * Helper types for working with arrays of inputs
 */
export type PayStubInputArray = ReadonlyArray<_ModifyPayStubInput>;
export type PayStubDetailInputArray = ReadonlyArray<_ModifyPayStubDetailInput>;

/**
 * Non-null variants for cases where we know the arrays exist
 */
export type PayStubInputArrayNonNull = NonNullable<_ModifyTimeSheetInput['modifyPayStubs']>;
export type PayStubDetailInputArrayNonNull = NonNullable<_ModifyPayStubInput['details']>;

// =============================================================================
// VALIDATION HELPERS
// =============================================================================

/**
 * Type guards for runtime validation of GraphQL input types
 * These help ensure type safety at mutation boundaries
 */
export function isModifyTimeSheetInput(input: unknown): input is _ModifyTimeSheetInput {
    if (!input || typeof input !== 'object') return false;
    const candidate = input as Record<string, unknown>;

    if (typeof candidate.id !== 'string') return false;
    if (!RelayIdService.isPositiveNumericString(candidate.id) && !RelayIdService.isGlobalId(candidate.id)) {
        return false;
    }
    return candidate.employerGuid !== undefined;
}

export function isModifyPayStubInput(input: unknown): input is _ModifyPayStubInput {
    if (!input || typeof input !== 'object') return false;
    const candidate = input as Record<string, unknown>;

    return typeof candidate.employeeId === 'string'; // Now expects Global ID string
}

export function isModifyPayStubDetailInput(input: unknown): input is _ModifyPayStubDetailInput {
    if (!input || typeof input !== 'object') return false;
    const candidate = input as Record<string, unknown>;

    return candidate.workDate !== undefined;
}

// =============================================================================
// FIELD NAME CONSTANTS
// =============================================================================

/**
 * Constants for GraphQL field names to prevent typos and ensure consistency
 * These are the EXACT field names used in GraphQL mutations
 */
export const TIMESHEET_FIELDS = {
    // TimeSheet fields
    ID: 'id',
    EMPLOYER_GUID: 'employerGuid',
    NAME: 'name',
    STATUS: 'status',
    TYPE: 'type',
    PAY_STUBS: 'payStubs',
    READ_ONLY: 'readOnly',
    SHOW_DT_HOURS_COLUMN: 'showDTHoursColumn',
    SHOW_COST_CENTER_COLUMN: 'showCostCenterColumn',
    SHOW_BONUS_COLUMN: 'showBonusColumn',
    SHOW_EXPENSES_COLUMN: 'showExpensesColumn',
    SHOW_EARNINGS_CODES_COLUMN: 'showEarningsCodesColumn',
    MODIFICATION_DATE: 'modificationDate',
    TIMESHEET_ID: 'timeSheetId'
} as const;

export const PAYSTUB_FIELDS = {
    // PayStub fields - CRITICAL: These are the correct GraphQL field names
    ID: 'id',
    EMPLOYEE_ID: 'employeeId',
    EMPLOYEE_NAME: 'employeeName',
    NAME: 'name',
    ST_HOURS: 'stHours', // ✅ GraphQL uses 'stHours'
    OT_HOURS: 'otHours', // ✅ GraphQL uses 'otHours'
    DT_HOURS: 'dtHours', // ✅ GraphQL uses 'dtHours'
    TOTAL_HOURS: 'totalHours',
    BONUS: 'bonus',
    EXPENSES: 'expenses',
    DETAILS: 'details',
    PAYSTUB_ID: 'payStubId'
    // UI state fields removed (DELETE, EXPANDED, IN_EDIT) - these should not be sent to GraphQL
} as const;

export const PAYSTUB_DETAIL_FIELDS = {
    // PayStubDetail fields - CRITICAL: These are the correct GraphQL field names
    ID: 'id',
    WORK_DATE: 'workDate',
    NAME: 'name',
    ST_HOURS: 'stHours', // ✅ GraphQL uses 'stHours'
    OT_HOURS: 'otHours', // ✅ GraphQL uses 'otHours'
    DT_HOURS: 'dtHours', // ✅ GraphQL uses 'dtHours'
    TOTAL_HOURS: 'totalHours',
    JOB_CODE: 'jobCode',
    EARNINGS_CODE: 'earningsCode',
    AGREEMENT_ID: 'agreementId',
    CLASSIFICATION_ID: 'classificationId',
    SUB_CLASSIFICATION_ID: 'subClassificationId',
    COST_CENTER: 'costCenter',
    HOURLY_RATE: 'hourlyRate',
    BONUS: 'bonus',
    EXPENSES: 'expenses',
    REPORT_LINE_ITEM_ID: 'reportLineItemId',
    DELETE: 'delete',
    PAYSTUB_ID: 'payStubId',
    PAYSTUB_DETAIL_ID: 'payStubDetailId'
} as const;

// =============================================================================
// TYPE ASSERTION HELPERS
// =============================================================================

/**
 * Safe type assertion helpers for converting unknown data to GraphQL types
 * These should be used sparingly and only after runtime validation
 */
export function assertModifyTimeSheetInput(input: unknown): _ModifyTimeSheetInput {
    if (!isModifyTimeSheetInput(input)) {
        throw new Error('Invalid ModifyTimeSheetInput: missing required fields id or employerGuid');
    }
    return input;
}

export function assertModifyPayStubInput(input: unknown): _ModifyPayStubInput {
    if (!isModifyPayStubInput(input)) {
        throw new Error('Invalid ModifyPayStubInput: missing required field employeeId');
    }
    return input;
}

export function assertModifyPayStubDetailInput(input: unknown): _ModifyPayStubDetailInput {
    if (!isModifyPayStubDetailInput(input)) {
        throw new Error('Invalid ModifyPayStubDetailInput: missing required field workDate');
    }
    return input;
}
