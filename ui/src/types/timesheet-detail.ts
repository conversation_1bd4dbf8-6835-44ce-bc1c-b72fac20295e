/**
 * TypeScript definitions for Timesheet Detail components.
 * Now uses relay-ui-extensions for proper Relay type composition.
 */

// Import from relay-ui-extensions (our new unified type system)
import type {
    RelayTimeSheet,
    RelayTimeSheetRef,
    RelayPayStub,
    RelayPayStubRef,
    RelayPayStubDetail,
    RelayPayStubDetailRef,
    PayStubDetailUI,
    PayStubUI,
    TimeSheetUI,
    EmployeeUI
} from './relay-ui-extensions';

// Import additional fragment types for specialized components
import type {
    TimeSheetDetailTableView_payStub$data,
    TimeSheetDetailTableView_payStub$key
} from '@/src/relay/__generated__/TimeSheetDetailTableView_payStub.graphql';

// Import mutation types
import type {
    ModifyTimeSheetInput,
    ModifyPayStubInput,
    ModifyPayStubDetailInput,
    AddTimesheetInput,
    AddPayStubInput,
    AddPayStubDetailInput
} from '@/src/types/graphql-timesheet';
import type { Disposable } from 'relay-runtime';

// =============================================================================
// TIMESHEET SETTINGS
// =============================================================================

export interface TimesheetSettings {
    showBonusColumn: boolean;
    showCostCenterColumn: boolean;
    showDTHoursColumn: boolean;
    showEarningsCodesColumn: boolean;
    showExpensesColumn: boolean;
}

// =============================================================================
// RELAY DATA TYPES (Re-exported from relay-ui-extensions)
// =============================================================================

export type {
    RelayTimeSheet,
    RelayTimeSheetRef,
    RelayPayStub,
    RelayPayStubRef,
    RelayPayStubDetail,
    RelayPayStubDetailRef
};

// Additional fragment types for specialized table views
export type RelayPayStubForTableView = TimeSheetDetailTableView_payStub$data;
export type RelayPayStubForTableViewRef = TimeSheetDetailTableView_payStub$key;

// =============================================================================
// UI EXTENSION TYPES (Re-exported from relay-ui-extensions)
// =============================================================================

export type {
    PayStubDetailUI,
    PayStubUI,
    TimeSheetUI,
    EmployeeUI
};

// Alias for backward compatibility during migration
export type ModifiablePayStubDetail = PayStubDetailUI;
export type ModifiablePayStub = PayStubUI;
export type ModifiableTimeSheet = TimeSheetUI;

// =============================================================================
// MUTATION INPUT TYPES
// =============================================================================

// Re-export mutation types for easy access
export type {
    ModifyTimeSheetInput,
    ModifyPayStubInput,
    ModifyPayStubDetailInput,
    AddTimesheetInput,
    AddPayStubInput,
    AddPayStubDetailInput
};

// =============================================================================
// VALIDATION TYPES
// =============================================================================

// Import ValidationError from canonical location
import type { ValidationError } from '../utils/validationUtils';

// Re-export ValidationError for easy access
export type { ValidationError };

export interface ValidationResult {
    isValid: boolean;
    errors: ValidationError[];
}

// =============================================================================
// AGGREGATION/CALCULATION TYPES
// =============================================================================

export interface PayStubTotals {
    stHours: number;
    otHours: number;
    dtHours: number;
    totalHours: number;
    bonus: number;
    expenses: number;
}

export interface TimesheetTotals {
    totalHours: number;
    totalPayStubs: number;
    totalEmployees: number;
}

// =============================================================================
// DISPLAY/UI HELPER TYPES
// =============================================================================

export interface DetailDisplayData {
    id: string;
    payStubId: string;
    workDate: string;
    name: string; // Day name (Monday, Tuesday, etc.)
    stHours: number | null;
    otHours: number | null;
    dtHours: number | null;
    totalHours: number | null;
    jobCode: string | null;
    earningsCode: string | null;
    agreementId: number | null;
    classificationId: number | null;
    subClassificationId: number | null;
    costCenter: string | null;
    hourlyRate: number | null;
    bonus: number | null;
    expenses: number | null;
    reportLineItemId: number | null;
    // UI state
    delete?: boolean;
    inError?: boolean;
    isTemporary?: boolean;
    employeeId: string | null;
}

// =============================================================================
// SAVE OPERATION TYPES
// =============================================================================

export interface SaveOptions {
    targetStatus: 'Saved' | 'Submitted';
}

export interface SaveResult {
    success: boolean;
    error?: Error;
    validationErrors?: ValidationError[];
}

// =============================================================================
// COMPONENT PROP TYPES
// =============================================================================

// Common props for timesheet detail components
export interface TimesheetDetailProps {
    timeSheetRef: RelayTimeSheetRef;
    employees: ReadonlyArray<EmployeeUI>;
    readOnly?: boolean;
    settings?: TimesheetSettings;
}

// Props for PayStub components
export interface PayStubComponentProps {
    payStubRef: RelayPayStubRef;
    readOnly?: boolean;
    employees: ReadonlyArray<EmployeeUI>;
    settings?: TimesheetSettings;
    onUpdate?: (updates: Partial<PayStubUI>) => void;
    onDelete?: (payStubId: string) => void;
}

// Props for PayStubDetail components
export interface PayStubDetailComponentProps {
    payStubDetailRef: RelayPayStubDetailRef;
    readOnly?: boolean;
    employees: ReadonlyArray<EmployeeUI>;
    settings?: TimesheetSettings;
    onUpdate?: (detailId: string, updates: Partial<PayStubDetailUI>) => void;
    onDelete?: (detailId: string) => void;
}

// =============================================================================
// HOOK RETURN TYPES
// =============================================================================

export interface UseTimesheetSaverReturn {
    saveTimesheet: (
        timesheetHeader: RelayTimeSheet | null,
        modifiablePayStubs: ReadonlyArray<PayStubUI>,
        options: SaveOptions
    ) => Promise<boolean>;
    isSaving: boolean;
    saveError: Error | null;
    // New optimistic mutation functions (return Disposable from commitMutation)
    saveWithOptimisticUpdates: (input: ModifyTimeSheetInput) => Disposable;
    addNewPayStub: (timeSheetId: string, numericId: number, employerGuid: string, newPayStub: AddPayStubInput) => Disposable;
    removePayStub: (timeSheetId: string, numericId: number, employerGuid: string, payStubId: string) => Disposable;
    updatePayStub: (timeSheetId: string, numericId: number, employerGuid: string, payStubUpdates: ModifyPayStubInput[]) => Disposable;
}

// =============================================================================
// UTILITY TYPES
// =============================================================================

// Type guards for runtime type checking (imported from relay-ui-extensions)
import { isPayStubUI, isPayStubDetailUI, isRelayPayStub, isRelayPayStubDetail } from './relay-ui-extensions';

// Re-export type guards
export { isPayStubUI, isPayStubDetailUI, isRelayPayStub, isRelayPayStubDetail };

// Backward compatibility aliases
export const isModifiablePayStub = isPayStubUI;
export const isModifiablePayStubDetail = isPayStubDetailUI;

// Helper type for transforming Relay data to UI data
export type RelayToUI<T> = T extends RelayPayStub ? PayStubUI : T extends RelayPayStubDetail ? PayStubDetailUI : T;

// Backward compatibility alias
export type RelayToModifiable<T> = RelayToUI<T>;

// =============================================================================
// CONSTANTS AND ENUMS
// =============================================================================

export const TIMESHEET_STATUS = {
    NEW: 'New',
    SAVED: 'Saved',
    SUBMITTED: 'Submitted'
} as const;

export type TimesheetStatus = (typeof TIMESHEET_STATUS)[keyof typeof TIMESHEET_STATUS];

export const VALIDATION_SEVERITY = {
    ERROR: 'error',
    WARNING: 'warning'
} as const;

export type ValidationSeverity = (typeof VALIDATION_SEVERITY)[keyof typeof VALIDATION_SEVERITY];
