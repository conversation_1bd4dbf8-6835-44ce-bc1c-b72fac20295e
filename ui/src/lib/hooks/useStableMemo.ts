import { useMemo } from 'react';

/**
 * Returns a stable reference as long as its `deps` values are deeply equal.
 * Useful for memoising expensive default objects like column arrays, filter templates, etc.
 *
 * Internally it stringifies each dependency to perform a deep-equality comparison between renders.
 * This should only be used for small dependency values (metadata objects, simple maps). Do NOT
 * pass large objects or class instances – the `JSON.stringify` cost will outweigh the benefit.
 */
export function useStableMemo<T>(factory: () => T, deps: unknown[]): T {
    // eslint-disable-next-line react-hooks/exhaustive-deps
    return useMemo(factory, deps.map((d) => JSON.stringify(d)));
}
