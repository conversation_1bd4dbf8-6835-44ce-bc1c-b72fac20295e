/**
 * Barrel export for lib directory
 * This centralizes all exports from the lib directory to avoid path alias drift
 */

// Store exports
export { useStore, type StoreState, type User } from './core/Store';

// ClientUtils exports
export { ClientUtils } from './core/ClientUtils';

// Error classes
export { AuthError } from './core/errors';

// MultiSelectXmlRequests exports
export { MultiSelectXmlRequests } from './core/MultiSelectXmlRequests';

// RelayIdService export
export { RelayIdService } from '@/src/services/RelayIdService';
