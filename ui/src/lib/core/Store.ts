import { create } from 'zustand';
import { ClientUtils } from './ClientUtils';
import { Constants } from '@/src/constants/global';

export interface User {
    username: string;
    roles: Constants.Role[];
    permissions: string[];
    showMyReportsMenu: boolean;
    showTimesheetsMenu: boolean;
    firstName: string;
    lastName: string;
    email: string;
    orgGUID: string;
    orgName: string;
    chapterId: number;
    phoneNumber: string;
    phoneTypeId: number;
    preferredMfamethod: string;
    phoneNumberExtension: string | null;
}

export type StoreState = {
    // User data (consolidated)
    user: User | null;

    // Primary chapter ID state - these are the source of truth
    selectedChapterGuid: string | null; // GUID format for API calls
    selectedChapterNumericId: number | null; // Numeric ID for database lookups
    selectedChapterEncodedId: string | null; // Base64 encoded ID for GraphQL queries

    // Other selection states
    selectedThirdParty: number | null;
    selectedEmployer: number | null;
    selectedThirdPartyGuid: string | null;
    selectedEmployerGuid: string | null;

    // Connection IDs for proxies
    proxyChaptersConnectionId: any;
    proxyThirdPartiesConnectionId: any;
    proxyEmployersConnectionId: any;

    // Display name for the selected chapter
    selectedChapterName: string | null;

    // Legacy properties for backward compatibility
    setEmail: (email: string) => void;
    roleGroups: string[];
    setOrgName: (orgName: string) => void;
    setOrgGuid: (orgGuid: string) => void;
    setChapterId: (chapterId: string) => void;
    setRoleGroups: (roleGroups: string[]) => void;

    // Setters
    setUser: (user: User) => void;
    clearUser: () => void;
    setSelectedChapterNumericId: (chapterId: number | null) => void;
    setSelectedThirdParty: (thirdPartyId: number | null) => void;
    setSelectedEmployer: (employerId: number | null) => void;
    setSelectedChapterGuid: (guid: string, numericId: number, chapterGuid: string) => void;
    setSelectedThirdPartyGuid: (thirdPartyId: string | null) => void;
    setSelectedEmployerGuid: (employerId: string | null) => void;
    setProxyChaptersConnectionId: (id: any) => void;
    setProxyThirdPartiesConnectionId: (id: any) => void;
    setProxyEmployersConnectionId: (id: any) => void;
    setSelectedChapterName: (chapterName: string | null) => void;
};

const useStore = create<StoreState>((set) => ({
    // User data
    user: null,

    // App state
    selectedChapterGuid: null,
    selectedChapterNumericId: null,
    selectedChapterEncodedId: null,
    selectedThirdParty: null,
    selectedEmployer: null,
    selectedThirdPartyGuid: null,
    selectedEmployerGuid: null,
    proxyChaptersConnectionId: null,
    proxyThirdPartiesConnectionId: null,
    proxyEmployersConnectionId: null,
    selectedChapterName: null,

    // Legacy properties for backward compatibility
    roleGroups: [],

    // User setters
    setUser: (user: User) => set(() => ({ user })),
    clearUser: () => set(() => ({ user: null })),

    // Legacy setters for backward compatibility
    setEmail: (email: string) =>
        set((state) => ({
            user: state.user ? { ...state.user, email } : null
        })),
    setOrgName: (orgName: string) =>
        set((state) => ({
            user: state.user ? { ...state.user, orgName } : null
        })),
    setOrgGuid: (orgGuid: string) =>
        set((state) => ({
            user: state.user ? { ...state.user, orgGUID: orgGuid } : null
        })),
    setChapterId: (chapterId: string) =>
        set((state) => ({
            user: state.user ? { ...state.user, chapterId: parseInt(chapterId) } : null
        })),
    setRoleGroups: (roleGroups: string[]) => set(() => ({ roleGroups })),

    // App state setters
    setSelectedChapterNumericId: (chapterId: number | null) => set(() => ({ selectedChapterNumericId: chapterId })),
    setSelectedThirdParty: (thirdPartyId: number | null) => set(() => ({ selectedThirdParty: thirdPartyId })),
    setSelectedEmployer: (employerId: number | null) => set(() => ({ selectedEmployer: employerId })),
    setSelectedChapterGuid: (guid: string, numericId: number, chapterGuid: string) => {
        const encodedChapterId = ClientUtils.encodeChapterId(numericId.toString());
        // Save the chapter selection to cookie
        ClientUtils.saveChapterIdToCookie(chapterGuid, numericId);
        set(() => ({
            selectedChapterGuid: chapterGuid,
            selectedChapterNumericId: numericId,
            selectedChapterEncodedId: encodedChapterId
        }));
    },
    setSelectedThirdPartyGuid: (thirdPartyId: string | null) => {
        // Save the third party selection to cookie
        if (thirdPartyId) {
            ClientUtils.setCookie(Constants.Cookies.selectedThirdPartyID, thirdPartyId, Constants.CookieExpiration.userPreferences);
        } else {
            ClientUtils.setCookie(Constants.Cookies.selectedThirdPartyID, '', Constants.CookieExpiration.userPreferences);
        }
        set(() => ({ selectedThirdPartyGuid: thirdPartyId }));
    },
    setSelectedEmployerGuid: (employerId: string | null) => {
        // Save the employer selection to cookie
        if (employerId) {
            ClientUtils.setCookie(Constants.Cookies.selectedEmployerID, employerId, Constants.CookieExpiration.userPreferences);
        } else {
            ClientUtils.setCookie(Constants.Cookies.selectedEmployerID, '', Constants.CookieExpiration.userPreferences);
        }
        set(() => ({ selectedEmployerGuid: employerId }));
    },
    setProxyChaptersConnectionId: (id: any) => set(() => ({ proxyChaptersConnectionId: id })),
    setProxyThirdPartiesConnectionId: (id: any) => set(() => ({ proxyThirdPartiesConnectionId: id })),
    setProxyEmployersConnectionId: (id: any) => set(() => ({ proxyEmployersConnectionId: id })),
    setSelectedChapterName: (chapterName: string | null) => set(() => ({ selectedChapterName: chapterName }))
}));

export { useStore };
