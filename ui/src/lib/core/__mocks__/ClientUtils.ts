/**
 * Mock implementation of ClientUtils for testing
 */

export const ClientUtils = {
    // Basic utility functions
    generateGuid: jest.fn().mockReturnValue('mock-guid-12345'),
    uuidv4: jest.fn().mockReturnValue('mock-guid-12345'),
    formatDate: jest.fn((date) => (date ? date.toString() : '')),
    formatCurrency: jest.fn((amount) => `$${amount}`),
    formatNumber: jest.fn((num) => num?.toString() || ''),
    parseDate: jest.fn((dateStr) => new Date(dateStr)),
    formatPhoneNumber: jest.fn((phone) => (phone ? '************' : '')),

    // Cookie related functions
    getCookie: jest.fn().mockReturnValue('mock-cookie-value'),
    setCookie: jest.fn(),

    // Authentication related functions

    getUsername: jest.fn().mockReturnValue('mock-user'),
    redirectToLogout: jest.fn(),

    // Chapter and employer related functions
    getEncodedChapterIdFromCookie: jest.fn().mockReturnValue('encoded-chapter-id'),
    saveChapterIdToCookie: jest.fn(),
    initializeChapter: jest.fn().mockReturnValue('mock-chapter-id'),
    initializeEmployer: jest.fn().mockReturnValue('mock-employer-id'),
    encodeChapterId: jest.fn((id) => `encoded-${id}`),
    decodeChapterId: jest.fn((id) => id.replace('encoded-', '')),

    // Validation and utility functions
    isDateFirstOfMonth: jest.fn().mockReturnValue(false),
    isOnlyNumbers: jest.fn((str) => /^\d+$/.test(str)),
    validateEmail: jest.fn((email) => email.includes('@')),
    arrayContentsAreEqual: jest.fn((a, b) => JSON.stringify(a) === JSON.stringify(b)),
    dateDiffInDays: jest.fn().mockReturnValue(1),
    onlyUnique: jest.fn((value, index, self) => self.indexOf(value) === index)
};
