import Cookies from 'js-cookie';
import isEqual from 'lodash/isEqual';
import { Constants } from '@/src/constants/global';
import { useStore } from '@/lib/core/Store';

const _MS_PER_DAY = 1000 * 60 * 60 * 24;

export namespace ClientUtils {
    export function getCookie(cookieName: string) {
        return Cookies.get(cookieName);
    }

    export function setCookie(name: string, value: string, days: number) {
        Cookies.set(name, value, { expires: days, path: '/' });
    }

    export function uuidv4() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
            const r = (Math.random() * 16) | 0,
                v = c === 'x' ? r : (r & 0x3) | 0x8;
            return v.toString(16);
        });
    }

    export function redirectToLogout() {
        window.location.href = import.meta.env.VITE_OLD_APP_URL + '/Logout.aspx?NewAppRedirect=' + window.location;
    }

    export function dateDiffInDays(a: Date, b: Date) {
        // Discard the time and time-zone information.
        const utc1 = Date.UTC(a.getFullYear(), a.getMonth(), a.getDate());
        const utc2 = Date.UTC(b.getFullYear(), b.getMonth(), b.getDate());

        return Math.floor((utc2 - utc1) / _MS_PER_DAY);
    }

    // Improved logic with lodash for deep array comparison
    export function arrayContentsAreEqual(a: unknown[], b: unknown[]): boolean {
        // Sorting the arrays before comparison
        const arr1 = [...a].sort();
        const arr2 = [...b].sort();

        // Using lodash to perform a deep comparison
        return isEqual(arr1, arr2);
    }

    // Check if the date is the first of the month
    export function isDateFirstOfMonth(date: Date): boolean {
        return date.getDate() === 1;
    }

    // Validate if a string contains only numbers
    export function isOnlyNumbers(str: string): boolean {
        return /^[0-9]+$/.test(str);
    }

    // Validate email using a regular expression
    export function validateEmail(email: string): boolean {
        const emailRegex =
            /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
        return emailRegex.test(email.toLowerCase());
    }

    export function getUsername() {
        const user = useStore.getState().user;
        return user?.username || '';
    }

    /**
     * Parse username from JWT token stored in cookies
     * Note: This is a legacy method for compatibility.
     * New code should use the user store instead.
     */
    export function parseUserNameFromJWT(): string {
        const user = useStore.getState().user;
        return user?.username || '';
    }

    export function formatPhoneNumber(phoneNumber: string | number): string {
        if (phoneNumber === '') return '';
        const phoneNumberStr = String(phoneNumber);
        const phoneNumberNoSymbols = phoneNumberStr.replace(/[^a-zA-Z0-9]/g, '').trim();
        return phoneNumberNoSymbols.slice(0, 3) + '-' + phoneNumberNoSymbols.slice(3, 6) + '-' + phoneNumberNoSymbols.slice(6);
    }

    // For use in the filter() function for arrays
    export function onlyUnique<T>(value: T, index: number, array: T[]): boolean {
        return array.indexOf(value) === index;
    }

    /**
     * Gets the chapter information from the combined value stored in the cookie
     * @returns Object with guid and numericId, or null if the cookie is missing or invalid
     */
    export function getChapterInfoFromCookie(): { guid: string; numericId: number } | null {
        const combinedValue = getCookie(Constants.Cookies.selectedChapterID);
        if (!combinedValue) return null;

        const parts = combinedValue.split(':');
        if (parts.length !== 2) return null;

        const guid = parts[0];
        const numericId = parseInt(parts[1], 10);

        if (!guid || isNaN(numericId)) return null;

        return { guid, numericId };
    }

    /**
     * Gets the encoded chapter ID from the combined value stored in the cookie
     * @returns The encoded chapter ID, or null if the cookie is missing or invalid
     */
    export function getEncodedChapterIdFromCookie(): string | null {
        const chapterInfo = getChapterInfoFromCookie();
        if (!chapterInfo) return null;

        return encodeChapterId(chapterInfo.numericId);
    }

    /**
     * Saves the chapter information to the cookie in the combined format
     * @param guid - The chapter's GUID
     * @param numericId - The chapter's numeric ID
     * @param expirationDays - Number of days until the cookie expires (default: 365)
     */
    export function saveChapterIdToCookie(guid: string, numericId: string | number, expirationDays: number = 365): void {
        const combinedValue = `${guid}:${numericId}`;
        setCookie(Constants.Cookies.selectedChapterID, combinedValue, expirationDays);
    }

    /**
     * Initializes chapter selection for a page.
     * If the user is a ChapterAdministrator, it will set their chapter as selected
     * when no chapter is currently selected.
     *
     * @param roles - User's roles
     * @param userChapterId - The user's assigned chapter ID (typically a GUID)
     * @param selectedChapterId - Currently selected chapter ID
     * @param setSelectedChapterGuid - Function to set the selected chapter
     * @returns The safe decoded chapter ID if one is selected, null otherwise
     */
    export function initializeChapter(
        roles: Constants.Role[] | undefined,
        userChapterId: string | undefined,
        selectedChapterId: string | null | undefined,
        setSelectedChapterGuid: (guid: string, numericId: number, chapterGuid: string) => void
    ): string | null {
        // Auto-select the user's chapter if they're a ChapterAdministrator and no chapter is selected
        if (roles?.includes(Constants.Roles.ChapterAdministrator) && userChapterId && (!selectedChapterId || selectedChapterId === '')) {
            // userChapterId is typically a GUID for ChapterAdministrators
            // We set the numeric ID to 0 temporarily - this should be updated later when chapter data is loaded
            setSelectedChapterGuid(userChapterId, 0, userChapterId);
            return userChapterId; // Return the GUID directly for API calls
        }

        // For System Administrators, initialize from cookie if no chapter is currently selected
        if (roles?.includes(Constants.Roles.SystemAdministrator) && (!selectedChapterId || selectedChapterId === '')) {
            const chapterInfo = getChapterInfoFromCookie();
            if (chapterInfo) {
                setSelectedChapterGuid(chapterInfo.guid, chapterInfo.numericId, chapterInfo.guid);
                return chapterInfo.guid; // Return the GUID for API calls
            }
        }

        // If a chapter is already selected, ensure we're using the right format for API calls
        if (selectedChapterId) {
            return selectedChapterId; // Already in the correct format for the store
        }

        // No chapter selected
        return null;
    }

    /**
     * Initializes employer selection for a page.
     * If selectedEmployerId is provided, it will be used.
     * Otherwise, checks if an employer ID is stored in a cookie.
     *
     * @param selectedEmployerId - The currently selected employer ID from the store
     * @param setSelectedEmployerGuid - Function to set the selected employer
     * @returns The employer GUID if one is selected, null otherwise
     */
    export function initializeEmployer(
        selectedEmployerId: string | null | undefined,
        setSelectedEmployerGuid: (employerGuid: string) => void
    ): string | null {
        // If an employer is already selected, return it
        if (selectedEmployerId) {
            return selectedEmployerId;
        }

        // Check if an employer is stored in the cookie
        const storedEmployerGuid = getCookie(Constants.Cookies.selectedEmployerID);
        if (storedEmployerGuid) {
            // Set the employer in the store
            setSelectedEmployerGuid(storedEmployerGuid);
            return storedEmployerGuid;
        }

        // No employer selected
        return null;
    }

    /**
     * Encodes a chapter's numeric ID for use with backend APIs.
     * Only numeric IDs should be encoded.
     *
     * @param chapterId - The numeric chapter ID to encode
     * @returns The encoded chapter ID string
     */
    export function encodeChapterId(chapterId: string | number): string {
        if (chapterId === null || chapterId === undefined) return '';

        // Convert to string for consistent handling
        const idStr = String(chapterId);

        // If it's not a numeric string, it might be a GUID or already encoded
        if (!/^\d+$/.test(idStr)) {
            console.warn('encodeChapterId: Expected numeric ID but received non-numeric value', chapterId);

            // Check if it's already encoded (has = character or starts with the known base64 prefix)
            if (idStr.includes('=') || idStr.startsWith('Q2hhcHRlcnNJbmZvRHRv')) {
                return idStr; // Already encoded, return as is
            }

            // Could be a GUID or other format, don't encode
            return idStr;
        }

        // Encode the numeric ID with the new GraphQL typename prefix expected by the backend
        // July 2025 migration: backend arguments now use `Chapter` instead of `ChaptersInfoDto`.
        return btoa(`Chapter:${idStr}`);
    }

    /**
     * Decodes an encoded chapter ID to retrieve the original numeric ID.
     *
     * @param encodedChapterId - The encoded chapter ID to decode
     * @returns The decoded numeric chapter ID as a string
     */
    export function decodeChapterId(encodedChapterId: string): string {
        if (!encodedChapterId) return '';

        // Check if it looks like a base64 string
        const base64Pattern = /^[A-Za-z0-9+/=]+$/;
        if (!base64Pattern.test(encodedChapterId)) {
            // Not a valid base64 string, could be a GUID or numeric ID already
            // If it's numeric, return as is
            if (/^\d+$/.test(encodedChapterId)) {
                return encodedChapterId;
            }

            // For GUIDs or other formats, log and return as is
            console.warn('decodeChapterId: Received non-base64 value that is also not numeric', encodedChapterId);
            return encodedChapterId;
        }

        try {
            const decoded = atob(encodedChapterId);

            // Support both the new and legacy prefixes during the transition window
            if (decoded.startsWith('Chapter:')) {
                return decoded.substring('Chapter:'.length);
            }

            if (decoded.startsWith('ChaptersInfoDto:')) {
                return decoded.substring('ChaptersInfoDto:'.length);
            }

            // Was base64 but didn't have our prefix
            console.warn('decodeChapterId: Decoded Base64 string but missing expected prefix', encodedChapterId);
            return encodedChapterId;
        } catch (e) {
            console.error('Error decoding chapterId:', e);
            return encodedChapterId;
        }
    }
}
