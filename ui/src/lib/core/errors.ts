export class AuthError extends Error {
    public readonly code: string;
    public readonly originalMessage: string;

    constructor(message: string, code: string, originalMessage: string) {
        super(message);
        this.name = 'AuthError';
        this.code = code;
        this.originalMessage = originalMessage;
        // This line is important for correctly setting the prototype chain
        // when targeting ES5 or lower, or when using certain bundlers/transpilers.
        Object.setPrototypeOf(this, AuthError.prototype);
    }
}
