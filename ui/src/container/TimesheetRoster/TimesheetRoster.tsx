'use client';

import uuid4 from 'uuid4';
import { Flex } from '@adobe/react-spectrum';
import { cloneDeep, isEqual } from 'lodash';
import { ToastQueue } from '@react-spectrum/toast';
import { ToastMessages } from '@/src/constants/views';
import { graphql, RecordSourceSelectorProxy } from 'relay-runtime';
import { useFragment, useLazyLoadQuery, useMutation } from 'react-relay';
import { useSWRQuery } from '@/src/relay/useSWRQuery';
import { Suspense, useEffect, useMemo, useCallback } from 'react';
import ContainerLoader from '@/src/components/UI/Loader/ContainerLoader/ContainerLoader';
import { TimeSheetSortInput } from '@/lib/relay/__generated__/TimesheetRosterQuery.graphql';
import { TimesheetsRosterViews, TimesheetsRosterViewsOptions } from '@/src/constants/views';
import { SavedCustomView } from '@/src/types/views';
import { TimeSheetFilterInput } from '@/lib/relay/__generated__/TimesheetRosterQuery.graphql';
import { TIMESHEET_ROSTER_DATABASE_CUSTOM_VIEW_NAME } from '@/src/constants/timesheet-roster';
import { getFilterByColumnData, getSortOrderByColumnData } from '@/src/services/timesheet-roster';
import { getViewById, getUpdatedView, getDecodedDefaultViewId, SavedView } from '@/src/services/saved-views';
import { getDeletedViews, getDecodedCustomSavedViews, getAddedViews } from '@/src/services/saved-views';
import { getDefaultTimesheetRosterQueryVariables, exportRosterView } from '@/src/services/timesheet-roster';
import TimesheetRosterTable from '@/src/components/TimesheetRoster/TimesheetRosterTable/TimesheetRosterTable';
import TimesheetRosterHeader from '@/src/components/TimesheetRoster/TimesheetRosterHeader/TimesheetRosterHeader';
import { UpdateCustomViewsInput } from '@/lib/relay/__generated__/TimesheetRosterUpdateCustomViewsMutation.graphql';
import { useTimesheetRosterColumnData } from '@/src/components/TimesheetRoster/TimesheetRosterTable/TimesheetRoster.data';
import { TimesheetRosterQuery as TimesheetRosterQueryType } from '@/lib/relay/__generated__/TimesheetRosterQuery.graphql';
import { TimesheetRosterCustomViewsFragment$key } from '@/lib/relay/__generated__/TimesheetRosterCustomViewsFragment.graphql';
import { TimesheetRosterCustomViewsFragment$data } from '@/lib/relay/__generated__/TimesheetRosterCustomViewsFragment.graphql';
import { TimesheetRosterCustomViewsFragment_updatable$key } from '@/lib/relay/__generated__/TimesheetRosterCustomViewsFragment_updatable.graphql';
import { TimesheetRosterUpdateCustomViewsMutation as TimesheetRosterUpdateCustomViewsMutationType } from '@/lib/relay/__generated__/TimesheetRosterUpdateCustomViewsMutation.graphql';
import { useTimesheetRosterFilterStore } from '@/src/store/rosterFilterStore';
// Temporary import for backward compatibility during migration
import { EmployeeUIProvider, EmployeeUIContextValue, EmployeeProvider, EmployeeContextValue } from '@/src/context/EmployeeContext';
import { ColumnType } from '@/src/types/rosters';
import { useTimesheetRosterState } from '@/src/hooks/useTimesheetRosterState';

type Props = {
    employerGuid: string;
};

export const TimesheetRosterCustomViewsFragment = graphql`
    fragment TimesheetRosterCustomViewsFragment on CustomViews {
        id
        value
    }
`;

export const TimesheetRosterQuery = graphql`
    query TimesheetRosterQuery(
        $employerGuid: UUID!
        $order: [TimeSheetSortInput!]
        $where: TimeSheetFilterInput!
        $customViewsName: String!
    ) {
        ...TimesheetRosterTableFragment @arguments(employerGuid: $employerGuid, order: $order, where: $where)
        customViewsByType(name: $customViewsName) {
            ...TimesheetRosterCustomViewsFragment
            ...TimesheetRosterCustomViewsFragment_updatable
        }
    }
`;

export const TimesheetRosterUpdateCustomViewsMutation = graphql`
    mutation TimesheetRosterUpdateCustomViewsMutation($input: UpdateCustomViewsInput!) {
        updateCustomViews(input: $input) {
            customViews {
                ...TimesheetRosterCustomViewsFragment
            }
        }
    }
`;

const TimesheetRosterContainer = ({ employerGuid }: Props) => {
    if (!employerGuid) {
        return <ContainerLoader parentStyles={{ height: '100%', flex: 1 }} />;
    }

    return (
        <Suspense fallback={<ContainerLoader parentStyles={{ height: '100%', flex: 1 }} />}>
            <TimesheetRosterContent employerGuid={employerGuid} />
        </Suspense>
    );
};

const TimesheetRosterContent = ({ employerGuid }: Props) => {
    // External store integration (Zustand)
    const setActiveRosterFilters = useTimesheetRosterFilterStore((state) => state.setActiveFilters);
    const setActiveRosterSortOrder = useTimesheetRosterFilterStore((state) => state.setActiveSortOrder);
    const currentActiveFilters = useTimesheetRosterFilterStore((state) => state.activeFilters);
    const currentActiveSortOrder = useTimesheetRosterFilterStore((state) => state.activeSortOrder);

    // Get default configuration
    const DEFAULT_TIMESHEET_ROSTER_ROOT_QUERY_PARAMS = useMemo(() => getDefaultTimesheetRosterQueryVariables(employerGuid), [employerGuid]);
    const metadataBasedTimesheetColumns = useTimesheetRosterColumnData();
    const initialTimesheetColumns = useMemo(() => cloneDeep(metadataBasedTimesheetColumns), [metadataBasedTimesheetColumns]);

    // Initialize unified state management
    const { state, actions, computedValues } = useTimesheetRosterState(
        DEFAULT_TIMESHEET_ROSTER_ROOT_QUERY_PARAMS,
        initialTimesheetColumns,
        employerGuid,
        {
            currentActiveFilters,
            currentActiveSortOrder,
            setActiveRosterFilters,
            setActiveRosterSortOrder
        }
    );

    // Computed query variables for GraphQL
    const filterForTimesheetRoster = useMemo(() => {
        const baseFilter = getFilterByColumnData(state.filter, state.columnData);

        if (currentActiveFilters && currentActiveFilters.and && currentActiveFilters.and.length > 0) {
            return {
                ...baseFilter,
                where: currentActiveFilters
            };
        }

        return baseFilter;
    }, [state.filter, state.columnData, currentActiveFilters]);

    const timesheetRosterSortOrder = useMemo(() => {
        const baseSortOrder = getSortOrderByColumnData(state.sortOrder, state.columnData);

        if (currentActiveSortOrder && currentActiveSortOrder.length > 0) {
            return currentActiveSortOrder;
        }

        return baseSortOrder;
    }, [state.sortOrder, state.columnData, currentActiveSortOrder]);

    const timesheetRosterData = useSWRQuery<TimesheetRosterQueryType>(TimesheetRosterQuery, {
        ...filterForTimesheetRoster,
        order: timesheetRosterSortOrder,
        customViewsName: TIMESHEET_ROSTER_DATABASE_CUSTOM_VIEW_NAME
    });

    // TEMPORARY: Testing instrumentation for cache behavior
    useEffect(() => {
        if (process.env.NODE_ENV === 'development') {
            console.log('🚀 TimesheetRoster: Data loaded', { 
                hasData: !!timesheetRosterData,
                dataKeys: timesheetRosterData ? Object.keys(timesheetRosterData) : [],
                timestamp: new Date().toISOString(),
                employerGuid
            });
        }
    }, [timesheetRosterData, employerGuid]);

    // Provide minimal employee context for roster page
    // Employee data will be loaded lazily in timesheet detail when needed
    const employeeContextValue: EmployeeContextValue = useMemo(
        () => ({
            employees: [],
            isLoading: false,
            employerGuid: employerGuid,
            signatoryAgreements: [],
            employeeDefaultSettings: new Map()
        }),
        [employerGuid]
    );

    // Handle employer GUID changes
    useEffect(() => {
        actions.updateEmployerGuid(employerGuid);
    }, [employerGuid, actions]);

    const customSavedViews = useFragment<TimesheetRosterCustomViewsFragment$key>(
        TimesheetRosterCustomViewsFragment,
        timesheetRosterData.customViewsByType
    );

    const decodedCustomSavedViews = useMemo(() => getDecodedCustomSavedViews(customSavedViews), [customSavedViews]);

    const [commitUpdateCustomViews, isCommitUpdateCustomViewsInFlight] = useMutation(TimesheetRosterUpdateCustomViewsMutation);

    // Use the computed value from the unified state
    const isSavedViewDisabled = computedValues.isSavedViewDisabled;

    const freshTimesheetRosterMetadata = useMemo(() => metadataBasedTimesheetColumns, [metadataBasedTimesheetColumns]);

    const applyFreshMetadata = useCallback(
        (columns: ColumnType[]) => {
            const updatedColumns = columns.map((currentCol: ColumnType) => {
                const freshMetadataCol = freshTimesheetRosterMetadata.find((col) => col.key === currentCol.key);
                const newLabel = freshMetadataCol?.label || currentCol.label;
                const newColumnLabel = freshMetadataCol?.columnLabel || currentCol.columnLabel;

                // Only create new object if metadata actually changed
                if (currentCol.label !== newLabel || currentCol.columnLabel !== newColumnLabel) {
                    return {
                        ...currentCol,
                        label: newLabel,
                        columnLabel: newColumnLabel
                    };
                }

                // Return same reference if no changes needed
                return currentCol;
            });

            // Only return new array if any columns actually changed
            const hasChanges = updatedColumns.some((col, index) => col !== columns[index]);
            return hasChanges ? updatedColumns : columns;
        },
        [freshTimesheetRosterMetadata]
    );

    const onUpdateCustomViewHandler = useCallback(
        ({
            input,
            onCompleted,
            onError,
            successMessage,
            errorMessage
        }: {
            input: UpdateCustomViewsInput;
            onCompleted: Function;
            onError: Function;
            successMessage: string;
            errorMessage: string;
        }) => {
            commitUpdateCustomViews({
                variables: {
                    input: {
                        id: customSavedViews?.id,
                        name: TIMESHEET_ROSTER_DATABASE_CUSTOM_VIEW_NAME,
                        ...input
                    }
                },
                optimisticUpdater: (store: RecordSourceSelectorProxy) => {
                    try {
                        const fragment = graphql`
                            fragment TimesheetRosterCustomViewsFragment_updatable on CustomViews @updatable {
                                id
                                value
                            }
                        `;
                        if (customSavedViews && timesheetRosterData.customViewsByType) {
                            const { updatableData } = store.readUpdatableFragment<TimesheetRosterCustomViewsFragment_updatable$key>(
                                fragment,
                                timesheetRosterData.customViewsByType
                            );
                            updatableData.value = input.value;
                        } else {
                            console.warn('customSavedViews or customViewsByType is null or undefined');
                        }
                    } catch (error) {
                        ToastQueue.negative(`Error updating store: ${JSON.stringify(error)}`, { timeout: 7000 });
                    }
                },
                onCompleted: (response: any) => {
                    const id = response?.updateCustomViews?.customViews?.__id;

                    if (id) {
                        ToastQueue.positive(`${successMessage}`, {
                            timeout: 7000
                        });
                        if (onCompleted) {
                            onCompleted();
                        }
                    } else {
                        ToastQueue.negative(`${errorMessage}`, { timeout: 7000 });
                        if (onError) {
                            onError();
                        }
                    }
                },
                onError: (error) => {
                    ToastQueue.negative(`${errorMessage}: ${error.message}`, {
                        timeout: 7000
                    });
                    if (onError) {
                        onError();
                    }
                }
            });
        },
        [commitUpdateCustomViews, customSavedViews, timesheetRosterData]
    );

    const onSaveDefaultViewId = useCallback(
        (id: string) => {
            onUpdateCustomViewHandler({
                input: {
                    value: { views: decodedCustomSavedViews, defaultViewId: id }
                },
                onCompleted: () => {},
                onError: () => {},
                successMessage: `${ToastMessages.UPDATE_DEFAULT_VIEW}`,
                errorMessage: `${ToastMessages.ERROR_UPDATE_DEFAULT_VIEW}`
            });
        },
        [decodedCustomSavedViews, onUpdateCustomViewHandler]
    );

    const onRootSystemViewChangeHandler = useCallback(
        (view: string | null) => {
            if (view !== null) {
                actions.setSelectedSystemView(view as TimesheetsRosterViews);
            }
        },
        [actions]
    );

    const onRootCustomViewChangeHandler = useCallback(
        (id: string | null) => {
            if (id) {
                const view = getViewById(id, decodedCustomSavedViews);

                if (view) {
                    const updatedColumns = applyFreshMetadata(view.columns || []);

                    actions.setSelectedCustomView({
                        ...view,
                        columns: updatedColumns
                    });
                } else {
                    actions.setSelectedCustomView(null);
                }
            } else {
                actions.setSelectedCustomView(null);
            }
        },
        [decodedCustomSavedViews, applyFreshMetadata, actions]
    );

    useEffect(() => {
        const defaultViewId = getDecodedDefaultViewId(customSavedViews);

        if (!defaultViewId) {
            const systemDefaultViewId = TimesheetsRosterViewsOptions.find((option) => option.isSystemDefault)?.id;
            if (systemDefaultViewId) {
                actions.setDefaultViewId(systemDefaultViewId);
                onSaveDefaultViewId(systemDefaultViewId);
                onRootSystemViewChangeHandler(systemDefaultViewId);
            }
        } else {
            const isBuiltInView = TimesheetsRosterViewsOptions.find((option) => option.id === defaultViewId);
            if (!isBuiltInView) {
                onRootCustomViewChangeHandler(defaultViewId);
            } else {
                onRootSystemViewChangeHandler(defaultViewId);
            }
            actions.setDefaultViewId(defaultViewId);
        }
    }, [actions, customSavedViews, onSaveDefaultViewId, onRootSystemViewChangeHandler]);

    useEffect(() => {
        if (state.pendingCustomViewSelectId) {
            onRootCustomViewChangeHandler(state.pendingCustomViewSelectId);
            actions.setPendingCustomViewSelectId(null);
        }
    }, [customSavedViews, state.pendingCustomViewSelectId, actions, onRootCustomViewChangeHandler]);

    useEffect(() => {
        if (state.selectedSystemView === TimesheetsRosterViews.TIMESHEET_ROSTER && !state.selectedCustomView) {
            // Add condition check to prevent unnecessary resets and break infinite loop
            const needsReset =
                !isEqual(state.filter, DEFAULT_TIMESHEET_ROSTER_ROOT_QUERY_PARAMS) ||
                !isEqual(state.sortOrder, DEFAULT_TIMESHEET_ROSTER_ROOT_QUERY_PARAMS.order || []) ||
                !isEqual(state.columnData, initialTimesheetColumns);

            if (needsReset) {
                actions.resetToDefault(DEFAULT_TIMESHEET_ROSTER_ROOT_QUERY_PARAMS, initialTimesheetColumns);
            }
        }
    }, [
        state.selectedSystemView,
        state.selectedCustomView,
        actions,
        state.filter,
        state.sortOrder,
        state.columnData,
        DEFAULT_TIMESHEET_ROSTER_ROOT_QUERY_PARAMS,
        initialTimesheetColumns
    ]);

    // Apply selected view when it changes - with stability checks to prevent infinite loops
    useEffect(() => {
        if (state.selectedCustomView && state.selectedCustomView.type === TimesheetsRosterViews.TIMESHEET_ROSTER && !state.isApplyingView) {
            const freshColumns = applyFreshMetadata(state.selectedCustomView.columns);

            // Only apply view if columns or filter/sort actually changed
            const hasFilterChanged = !isEqual(state.filter.where, state.selectedCustomView.filter?.where);
            const hasSortChanged = !isEqual(state.sortOrder, state.selectedCustomView.sortOrder);
            const hasColumnsChanged = freshColumns !== state.selectedCustomView.columns;

            if (hasFilterChanged || hasSortChanged || hasColumnsChanged) {
                actions.applyView(state.selectedCustomView, freshColumns);
            }
        }
    }, [state.selectedCustomView, state.isApplyingView, actions, applyFreshMetadata, state.filter.where, state.sortOrder]);

    const onToggleColumnShowHandler = useCallback(
        (values: string[]) => {
            actions.toggleColumnVisibility(values);
        },
        [actions]
    );

    const onRootFilterChangeHandler = useCallback(
        (filterData: TimeSheetFilterInput | null | undefined) => {
            actions.updateFilter(filterData);
        },
        [actions]
    );

    const onRootSortChangeHandler = useCallback(
        (sortData: TimeSheetSortInput[]) => {
            actions.updateSortOrder(sortData);
        },
        [actions]
    );

    const timesheetRosterView = (
        <TimesheetRosterTable
            queryRef={timesheetRosterData}
            columnData={state.columnData}
            filter={{
                ...filterForTimesheetRoster,
                order: timesheetRosterSortOrder
            }}
            onRootSortChange={onRootSortChangeHandler}
        />
    );

    const onRootSaveViewHandler = ({ savedLocalView, name, description }: { savedLocalView: any; name: string; description: string }) => {
        const id = uuid4();

        const columns = applyFreshMetadata(state.columnData);

        // Convert TimeSheetSortInput[] to SortConfig[]
        const convertedSortOrder = state.sortOrder
            ? state.sortOrder.map((sortInput) => {
                  const key = Object.keys(sortInput)[0];
                  const value = sortInput[key as keyof typeof sortInput];
                  return { [key]: value } as { [key: string]: 'ASC' | 'DESC' };
              })
            : undefined;

        const newSavedView: SavedView = {
            filter: state.filter.where ? { where: cloneDeep(state.filter.where) as any } : undefined,
            sortOrder: convertedSortOrder,
            id,
            name,
            description,
            columns
        };
        const updatedSavedViews = getAddedViews(newSavedView, decodedCustomSavedViews);

        onUpdateCustomViewHandler({
            input: {
                value: { views: updatedSavedViews, defaultViewId: state.defaultViewId }
            },
            onCompleted: () => {
                actions.setPendingCustomViewSelectId(id);
            },
            onError: () => {},
            successMessage: `${ToastMessages.CREATE_VIEW} "${name}"`,
            errorMessage: `${ToastMessages.ERROR_CREATE_VIEW} "${name}"`
        });
    };

    const onRootUpdateViewHandler = (id: string, updatedLocalView: any) => {
        const columns = applyFreshMetadata(state.columnData);

        // Convert TimeSheetSortInput[] to SortConfig[]
        const convertedSortOrder = state.sortOrder
            ? state.sortOrder.map((sortInput) => {
                  const key = Object.keys(sortInput)[0];
                  const value = sortInput[key as keyof typeof sortInput];
                  return { [key]: value } as { [key: string]: 'ASC' | 'DESC' };
              })
            : undefined;

        const updatedView: Partial<SavedView> = {
            filter: state.filter.where ? { where: cloneDeep(state.filter.where) as any } : undefined,
            sortOrder: convertedSortOrder,
            columns
        };
        const { updatedSavedViews, updatedViewResponse } = getUpdatedView(id, updatedView, decodedCustomSavedViews);

        if (updatedSavedViews) {
            onUpdateCustomViewHandler({
                input: {
                    value: { views: updatedSavedViews, defaultViewId: state.defaultViewId }
                },
                onCompleted: () => {
                    actions.setPendingCustomViewSelectId(id);
                },
                onError: () => {},
                successMessage: `${ToastMessages.UPDATE_VIEW} "${updatedViewResponse?.name}"`,
                errorMessage: `${ToastMessages.ERROR_UPDATE_VIEW} "${updatedViewResponse?.name}"`
            });
        }
    };

    const onRootRenameViewHandler = (id: string, name: string, description: string) => {
        const updatedView = {
            name,
            description
        };
        const { updatedSavedViews, updatedViewResponse } = getUpdatedView(id, updatedView, decodedCustomSavedViews);

        if (updatedSavedViews) {
            onUpdateCustomViewHandler({
                input: {
                    value: { views: updatedSavedViews, defaultViewId: state.defaultViewId }
                },
                onCompleted: () => {},
                onError: () => {},
                successMessage: `${ToastMessages.UPDATE_VIEW} "${updatedViewResponse?.name}"`,
                errorMessage: `${ToastMessages.ERROR_UPDATE_VIEW} "${updatedViewResponse?.name}"`
            });
        }
    };

    const onRootDeleteViewHandler = (id: string) => {
        const { updatedSavedViews, deletedView } = getDeletedViews(id, decodedCustomSavedViews);

        if (deletedView) {
            onUpdateCustomViewHandler({
                input: {
                    value: { views: updatedSavedViews, defaultViewId: state.defaultViewId }
                },
                onCompleted: () => {},
                onError: () => {},
                successMessage: `"${deletedView?.name}"${ToastMessages.DELETE_VIEW}`,
                errorMessage: `${ToastMessages.ERROR_DELETE_VIEW} "${deletedView?.name}"`
            });
        }
    };

    const onRootChangeDefaultViewHandler = (id: string) => {
        actions.setDefaultViewId(id);
        onSaveDefaultViewId(id);
    };

    const onDeleteAndChangeDefaultViewHandler = (id: string, newDefaultViewId: string) => {
        const { updatedSavedViews, deletedView } = getDeletedViews(id, decodedCustomSavedViews);

        if (deletedView) {
            const newDefaultView =
                getViewById(newDefaultViewId, decodedCustomSavedViews) ||
                TimesheetsRosterViewsOptions.find((option) => option.id === newDefaultViewId);

            onUpdateCustomViewHandler({
                input: {
                    value: { views: updatedSavedViews, defaultViewId: newDefaultViewId }
                },
                onCompleted: () => {
                    actions.setDefaultViewId(newDefaultViewId);
                },
                onError: () => {},
                successMessage: `${ToastMessages.DELETE_DEFAIULT_VIEW(deletedView?.name || '', newDefaultView?.name || '')}`,
                errorMessage: `${ToastMessages.ERROR_DELETE_VIEW} "${deletedView?.name}"`
            });
        }
    };

    const onExportHandler = (format: string) => {
        exportRosterView(employerGuid, state.columnData, format);
    };

    return (
        <EmployeeProvider value={employeeContextValue}>
            <Flex direction="column" alignItems="center" width="100%" flex={1}>
                <Flex width="100%" direction="column" marginBottom="size-100" flex={1}>
                    <TimesheetRosterHeader
                        onExport={onExportHandler}
                        employerGuid={employerGuid}
                        columnData={state.columnData}
                        defaultViewId={state.defaultViewId}
                        onRootSaveView={onRootSaveViewHandler}
                        selectedSystemView={state.selectedSystemView}
                        selectedCustomView={state.selectedCustomView}
                        isSavedViewDisabled={isSavedViewDisabled}
                        customSavedViews={decodedCustomSavedViews as SavedCustomView[]}
                        onFilterChange={onRootFilterChangeHandler}
                        onRootUpdateView={onRootUpdateViewHandler}
                        onRootDeleteView={onRootDeleteViewHandler}
                        onRootRenameView={onRootRenameViewHandler}
                        onToggleColumnShow={onToggleColumnShowHandler}
                        onRootSystemViewChange={onRootSystemViewChangeHandler}
                        onRootSelectCustomView={onRootCustomViewChangeHandler}
                        onRootChangeDefaultView={onRootChangeDefaultViewHandler}
                        onDeleteAndChangeDefaultView={onDeleteAndChangeDefaultViewHandler}
                    />

                    <Suspense fallback={<ContainerLoader parentStyles={{ height: '100%' }} />}>{timesheetRosterView}</Suspense>
                </Flex>
            </Flex>
        </EmployeeProvider>
    );
};

export default TimesheetRosterContainer;
