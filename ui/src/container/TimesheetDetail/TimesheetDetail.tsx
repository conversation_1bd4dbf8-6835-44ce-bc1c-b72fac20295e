import React from 'react';
import TimesheetDetail from '@/src/components/TimesheetDetail/TimesheetDetail';

interface TimesheetDetailContainerProps {
    employerGuid: string;
    timeSheetId: string;
}

/**
 * Simplified container that passes props to TimesheetDetail
 * Employee data is now loaded as part of the main TimesheetDetailQuery to avoid duplicate queries
 */
const TimesheetDetailContainer: React.FC<TimesheetDetailContainerProps> = ({ employerGuid, timeSheetId }) => {
    // No need for LazyEmployeeDataProvider since main query includes employee data
    return <TimesheetDetail timeSheetId={timeSheetId} />;
};

export default TimesheetDetailContainer;
