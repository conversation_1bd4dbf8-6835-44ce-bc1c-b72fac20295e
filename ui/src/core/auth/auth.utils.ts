import { AccessControlledRoute } from '@/src/types/routes';
import { ClientUtils } from "@/lib/core/ClientUtils";
import { Constants } from '@/src/constants/global';
import { useStore } from '@/lib';

export namespace AuthUtils {
    export const hasRequiredRoutePermissions = (PROTECTED_ROUTES: AccessControlledRoute[], userRoles: Constants.Role[], pathname: string) => {
        const route = PROTECTED_ROUTES.find((route) => route.path === pathname);

        if (!route) {
            return false;
        }

        const hasMandatoryRoles = route.mandatoryRoles ? hasEveryRole(userRoles, route.mandatoryRoles) : true;
        const hasOptionalRoles = route.optionalRoles ? hasAnyRole(userRoles, route.optionalRoles) : true;

        return hasMandatoryRoles && hasOptionalRoles;
    };

    /**
     * Checks if the user has every role in the requiredRoles array.
     * @param userRoles - The user's roles.
     * @param requiredRoles - The roles required to access the resource.
     * @returns true if the user has every role in the requiredRoles array, false otherwise.
     */
    export function hasEveryRole(userRoles: string[], requiredRoles: string[]) {
        return requiredRoles.every((role) => userRoles.includes(role));
    }

    /**
     * Checks if the user has any of the roles in the requiredRoles array.
     * @param userRoles - The user's roles.
     * @param requiredRoles - The roles required to access the resource.
     * @returns true if the user has any of the roles in the requiredRoles array, false otherwise.
     */
    export function hasAnyRole(userRoles: string[], requiredRoles: string[]) {
        return requiredRoles.some((role) => userRoles.includes(role));
    }

    /**
     * Checks if the user has a specific permission using the permissions stored in the global store.
     * @param permission - The permission to check for.
     * @returns boolean - true if the user has the specified permission, false otherwise.
     * @deprecated Use role-based checks instead of permission-based checks
     */
    export function hasPermission(permission: string): boolean {
        const user = useStore.getState().user;
        if (!user) return false; // User not loaded yet
        return user.permissions.includes(permission);
    }

    /**
     * Checks if the current pathname is in the whitelistRoutes array.
     * @param pathname - The current pathname.
     * @param whitelistRoutes - The routes to check against.
     * @returns true if the pathname is in the whitelistRoutes array, false otherwise.
     */
    export function isWhiteListedRoute(pathname: string, whitelistRoutes: string[]) {
        return whitelistRoutes.some((route) => pathname === route);
    }
}
