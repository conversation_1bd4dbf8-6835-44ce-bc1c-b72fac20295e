import axios from 'axios';
import { Constants } from '@/src/constants/global';
import { ClientUtils } from "@/lib/core/ClientUtils";
import { AuthErrorHandler } from '@/src/services/auth-error-handler';

// Create instance with API URL
const instance = axios.create({
    baseURL: import.meta.env.VITE_REST_API_URL,
    withCredentials: true // Include HttpOnly cookies in requests
});

// Remove manual authorization header since HttpOnly cookies are automatically sent
// The CookieToHeaderMiddleware will read the HttpOnly cookie and add Authorization header
instance.interceptors.request.use(function (config) {
    // No need to manually set Authorization header for HttpOnly cookies
    // The browser will automatically include HttpOnly cookies in the request
    return config;
});

// Add response interceptor to handle authentication errors - redirect to old app for refresh
instance.interceptors.response.use(
    (response) => response,
    async (error) => {
        const originalRequest = error.config;
        const statusCode = error.response ? error.response.status : null;
        
        // Handle 401 errors by redirecting to old app (only try once per request)
        if (statusCode === 401 && !originalRequest._retry) {
            originalRequest._retry = true;
            
            // Let the old Web Forms app handle token refresh using its HttpOnly cookies
            AuthErrorHandler.handleAuthError('AUTH_NOT_AUTHENTICATED', 'Your session has expired. Please log in again.');
            return Promise.reject(error);
        }
        
        // Handle other authentication errors (403)
        if (statusCode === 403) {
            AuthErrorHandler.handleAuthError('AUTH_NOT_AUTHORIZED', 'You are not authorized to access this resource.');
            return Promise.reject(error);
        }
        
        // Handle server errors with authentication-related error codes
        if (statusCode === 500 && error.response.data && error.response.data.errors) {
            const errorCode = error.response.data.errors[0]?.extensions?.code;

            if (AuthErrorHandler.isAuthError(statusCode, errorCode)) {
                const message = errorCode === 'AUTH_NOT_AUTHENTICATED' 
                    ? 'You are not authenticated. Please log in to continue.'
                    : 'You are not authorized to access this resource.';
                AuthErrorHandler.handleAuthError(errorCode, message);
            }
        }

        return Promise.reject(error);
    }
);

// Create local instance for requests to the same origin
let localInstance = null;

if (typeof window !== 'undefined') {
    const localUrl = window.location || import.meta.env.VITE_REST_API_URL;
    localInstance = axios.create({
        baseURL: `${localUrl.protocol}//${localUrl.host}/`,
        withCredentials: true // Include HttpOnly cookies in local requests too
    });
}

export { instance, localInstance };
