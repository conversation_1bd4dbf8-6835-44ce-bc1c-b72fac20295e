/**
 * Tests for PayStub ID conversion in buildModifyPayStubInput
 */

import { buildModifyPayStubInput, type DraftPayStub } from '../payStubMappers';
import { RelayIdService } from '@/src/services/RelayIdService';

describe('PayStub ID Conversion in buildModifyPayStubInput', () => {
    const mockDraftPayStub: DraftPayStub = {
        id: 'test-id', // Will be overridden in tests
        employeeId: 'RW1wbG95ZWU6MzA5OTEyMzc=', // Valid Relay Global ID
        name: 'Test PayStub',
        employeeName: 'Test Employee',
        details: [],
        stHours: 8,
        otHours: 0,
        dtHours: 0,
        bonus: 0,
        expenses: 0,
        expanded: false,
        inEdit: false
    };

    it('should convert raw integer ID to Relay Global ID', () => {
        const rawIntegerId = '123456';
        const payStub = { ...mockDraftPayStub, id: rawIntegerId };

        const result = buildModifyPayStubInput(payStub);

        // Should convert raw integer ID to proper Relay Global ID
        expect(result.id).toBe(RelayIdService.toGlobalId('PayStub', rawIntegerId));
        expect(result.id).not.toBe(rawIntegerId); // Should not be the raw integer ID
        expect(RelayIdService.isGlobalId(result.id)).toBe(true);
    });

    it('should preserve already valid Relay Global ID', () => {
        const validGlobalId = RelayIdService.toGlobalId('PayStub', '123456');
        const payStub = { ...mockDraftPayStub, id: validGlobalId };

        const result = buildModifyPayStubInput(payStub);

        // Should preserve the valid Relay Global ID unchanged
        expect(result.id).toBe(validGlobalId);
        expect(RelayIdService.isGlobalId(result.id)).toBe(true);
    });

    it('should convert numeric ID to Relay Global ID', () => {
        const numericId = '123456';
        const payStub = { ...mockDraftPayStub, id: numericId };

        const result = buildModifyPayStubInput(payStub);

        // Should convert numeric ID to proper Relay Global ID
        expect(result.id).toBe(RelayIdService.toGlobalId('PayStub', numericId));
        expect(result.id).not.toBe(numericId); // Should not be the raw numeric ID
        expect(RelayIdService.isGlobalId(result.id)).toBe(true);
    });

    it('should handle mixed ID types correctly', () => {
        const testCases = [
            { input: '123456', type: 'integer' },
            { input: '789012', type: 'large-integer' },
            { input: RelayIdService.toGlobalId('PayStub', '789'), type: 'already-global' },
            { input: '21501b66-046d-4529-9ecf-5d9e5e236ca1', type: 'legacy-GUID' } // Legacy support
        ];

        testCases.forEach(({ input, type }) => {
            const payStub = { ...mockDraftPayStub, id: input };
            const result = buildModifyPayStubInput(payStub);

            expect(RelayIdService.isGlobalId(result.id)).toBe(true);
            
            // Verify the ID can be parsed back correctly
            const parsed = RelayIdService.fromGlobalId(result.id);
            expect(parsed.type).toBe('PayStub');
            
            if (type === 'already-global') {
                expect(result.id).toBe(input); // Should be unchanged
            } else {
                expect(result.id).toBe(RelayIdService.toGlobalId('PayStub', input));
            }
        });
    });

    it('should throw error for missing ID', () => {
        const payStub = { ...mockDraftPayStub, id: '' };

        expect(() => buildModifyPayStubInput(payStub)).toThrow('Cannot build ModifyPayStubInput without an ID');
    });

    it('should properly handle edge cases', () => {
        const edgeCases = [
            { id: '12345', description: 'integer ID' },
            { id: '999999', description: 'large integer ID' },
            { id: 'client:temp:paystub:67890', description: 'client temporary ID' },
            { id: 'temp-12345', description: 'legacy temporary ID' }
        ];

        edgeCases.forEach(({ id, description }) => {
            const payStub = { ...mockDraftPayStub, id };
            const result = buildModifyPayStubInput(payStub);

            expect(RelayIdService.isGlobalId(result.id)).toBe(true);
            expect(result.id).toBe(RelayIdService.toGlobalId('PayStub', id));
        });
    });

    it('should ensure output ID is always a valid base64 string', () => {
        const testIds = [
            '123456',
            '789012',
            'client:temp:paystub:123',
            '21501b66-046d-4529-9ecf-5d9e5e236ca1' // Legacy GUID support
        ];

        testIds.forEach(id => {
            const payStub = { ...mockDraftPayStub, id };
            const result = buildModifyPayStubInput(payStub);

            // Should be valid base64
            expect(result.id).toMatch(/^[A-Za-z0-9+/]+={0,2}$/);
            
            // Should be parseable by RelayIdService
            expect(() => RelayIdService.fromGlobalId(result.id)).not.toThrow();
            
            // Should decode to proper format
            const parsed = RelayIdService.fromGlobalId(result.id);
            expect(parsed.type).toBe('PayStub');
            expect(parsed.id).toBe(id);
        });
    });
});