/**
 * PayStub Mappers Test Suite
 *
 * Tests for the separate arrays implementation mappers,
 * focusing on employeeId conversion and proper handling
 * of the new add/modify PayStub input builders.
 */

import { RelayIdService } from '../../services/RelayIdService';
import { buildAddPayStubInput, buildModifyPayStubInput, isNewPayStub, type DraftPayStub } from '../payStubMappers';

// =============================================================================
// TEST DATA FACTORIES
// =============================================================================

function createDraftPayStubWithNumericEmployeeId(): DraftPayStub {
    return {
        employeeId: '123', // Numeric string
        name: 'Test PayStub',
        employeeName: '<PERSON>',
        stHours: 40,
        otHours: 5,
        dtHours: 2,
        bonus: 100,
        expenses: 50
    };
}

function createDraftPayStubWithRelayEmployeeId(): DraftPayStub {
    return {
        id: '456', // Integer ID (will be converted to Relay Global ID)
        employeeId: RelayIdService.toGlobalId('Employee', 123), // Relay Global ID
        name: 'Test PayStub',
        employeeName: 'John Doe',
        stHours: 40,
        otHours: 5,
        dtHours: 2,
        bonus: 100,
        expenses: 50
    };
}

function createMinimalDraftPayStub(): DraftPayStub {
    return {
        employeeId: '456'
    };
}

// =============================================================================
// EMPLOYEE ID CONVERSION TESTS
// =============================================================================

describe('EmployeeId Conversion Tests', () => {
    describe('buildAddPayStubInput', () => {
        it('should convert numeric employeeId to Relay Global ID', () => {
            const stub = createDraftPayStubWithNumericEmployeeId();
            const result = buildAddPayStubInput(stub);

            expect(RelayIdService.isGlobalId(result.employeeId)).toBe(true);

            const parsed = RelayIdService.fromGlobalId(result.employeeId);
            expect(parsed.type).toBe('Employee');
            expect(parsed.id).toBe('123');
        });

        it('should preserve existing Relay Global ID', () => {
            const stub = createDraftPayStubWithRelayEmployeeId();
            const originalEmployeeId = stub.employeeId;

            const result = buildAddPayStubInput(stub);

            expect(result.employeeId).toBe(originalEmployeeId);
            expect(RelayIdService.isGlobalId(result.employeeId)).toBe(true);
        });

        it('should handle empty employeeId gracefully', () => {
            const stub: DraftPayStub = {
                employeeId: '', // Empty string
                name: 'Test Stub'
            };

            // Should not throw, but will create a Global ID for "0"
            expect(() => buildAddPayStubInput(stub)).not.toThrow();

            const result = buildAddPayStubInput(stub);
            expect(RelayIdService.isGlobalId(result.employeeId)).toBe(true);
        });

        it('should handle invalid numeric employeeId', () => {
            const stub: DraftPayStub = {
                employeeId: 'abc', // Non-numeric string
                name: 'Test Stub'
            };

            // Should not throw, parseInt will return NaN and RelayIdService will handle it
            expect(() => buildAddPayStubInput(stub)).not.toThrow();

            const result = buildAddPayStubInput(stub);
            expect(RelayIdService.isGlobalId(result.employeeId)).toBe(true);
        });

        it('should correctly map all other fields', () => {
            const stub = createDraftPayStubWithNumericEmployeeId();
            const result = buildAddPayStubInput(stub);

            expect(result.name).toBe(stub.name);
            expect(result.employeeName).toBe(stub.employeeName);
            expect(result.stHours).toBe(stub.stHours);
            expect(result.otHours).toBe(stub.otHours);
            expect(result.dtHours).toBe(stub.dtHours);
            expect(result.bonus).toBe(stub.bonus);
            expect(result.expenses).toBe(stub.expenses);
        });

        it('should handle null/undefined optional fields', () => {
            const stub = createMinimalDraftPayStub();
            const result = buildAddPayStubInput(stub);

            expect(result.name).toBe(null);
            expect(result.employeeName).toBe(null);
            expect(result.stHours).toBe(null);
            expect(result.otHours).toBe(null);
            expect(result.dtHours).toBe(null);
            expect(result.bonus).toBe(null);
            expect(result.expenses).toBe(null);
            expect(result.expanded).toBe(null);
            expect(result.inEdit).toBe(null);
        });
    });

    describe('buildModifyPayStubInput', () => {
        it('should convert numeric employeeId to Relay Global ID', () => {
            const stub = createDraftPayStubWithNumericEmployeeId();
            stub.id = '123'; // Integer ID required for modify

            const result = buildModifyPayStubInput(stub);

            expect(RelayIdService.isGlobalId(result.employeeId)).toBe(true);

            const parsed = RelayIdService.fromGlobalId(result.employeeId);
            expect(parsed.type).toBe('Employee');
            expect(parsed.id).toBe('123');
        });

        it('should preserve existing Relay Global ID', () => {
            const stub = createDraftPayStubWithRelayEmployeeId();
            const originalEmployeeId = stub.employeeId;

            const result = buildModifyPayStubInput(stub);

            expect(result.employeeId).toBe(originalEmployeeId);
            expect(RelayIdService.isGlobalId(result.employeeId)).toBe(true);
        });

        it('should require ID field', () => {
            const stub = createDraftPayStubWithNumericEmployeeId();
            // Don't set ID

            expect(() => buildModifyPayStubInput(stub)).toThrow('Cannot build ModifyPayStubInput without an ID');
        });

        it('should include ID in output', () => {
            const stub = createDraftPayStubWithRelayEmployeeId();
            const result = buildModifyPayStubInput(stub);

            // Should convert to proper Relay Global ID format
            expect(result.id).toBe(RelayIdService.toGlobalId('PayStub', stub.id));
            expect(RelayIdService.isGlobalId(result.id)).toBe(true);
        });

        it('should correctly map all other fields', () => {
            const stub = createDraftPayStubWithRelayEmployeeId();
            const result = buildModifyPayStubInput(stub);

            expect(result.name).toBe(stub.name);
            expect(result.employeeName).toBe(stub.employeeName);
            expect(result.stHours).toBe(stub.stHours);
            expect(result.otHours).toBe(stub.otHours);
            expect(result.dtHours).toBe(stub.dtHours);
            expect(result.bonus).toBe(stub.bonus);
            expect(result.expenses).toBe(stub.expenses);
        });
    });
});

// =============================================================================
// EDGE CASE TESTS
// =============================================================================

describe('Edge Case Tests', () => {
    describe('EmployeeId Edge Cases', () => {
        it('should handle zero employeeId', () => {
            const stub: DraftPayStub = {
                employeeId: '0',
                name: 'Test'
            };

            const result = buildAddPayStubInput(stub);
            // RelayIdService might handle '0' as a special case, just verify we get a string
            expect(typeof result.employeeId).toBe('string');
            expect(result.employeeId.length).toBeGreaterThan(0);
        });

        it('should handle large numeric employeeId', () => {
            const stub: DraftPayStub = {
                employeeId: '999999999',
                name: 'Test'
            };

            const result = buildAddPayStubInput(stub);
            expect(RelayIdService.isGlobalId(result.employeeId)).toBe(true);

            const parsed = RelayIdService.fromGlobalId(result.employeeId);
            expect(parsed.id).toBe('999999999');
        });

        it('should handle decimal employeeId by parsing as integer', () => {
            const stub: DraftPayStub = {
                employeeId: '123.456',
                name: 'Test'
            };

            const result = buildAddPayStubInput(stub);
            expect(RelayIdService.isGlobalId(result.employeeId)).toBe(true);

            const parsed = RelayIdService.fromGlobalId(result.employeeId);
            expect(parsed.id).toBe('123'); // parseInt truncates decimal
        });
    });

    describe('Boundary Value Tests', () => {
        it('should handle zero hours', () => {
            const stub = createDraftPayStubWithNumericEmployeeId();
            stub.stHours = 0;
            stub.otHours = 0;
            stub.dtHours = 0;

            const result = buildAddPayStubInput(stub);
            expect(result.stHours).toBe(0);
            expect(result.otHours).toBe(0);
            expect(result.dtHours).toBe(0);
        });

        it('should handle negative hours', () => {
            const stub = createDraftPayStubWithNumericEmployeeId();
            stub.stHours = -5;
            stub.otHours = -2;

            const result = buildAddPayStubInput(stub);
            expect(result.stHours).toBe(-5);
            expect(result.otHours).toBe(-2);
        });

        it('should handle decimal hours', () => {
            const stub = createDraftPayStubWithNumericEmployeeId();
            stub.stHours = 7.5;
            stub.otHours = 1.25;

            const result = buildAddPayStubInput(stub);
            expect(result.stHours).toBe(7.5);
            expect(result.otHours).toBe(1.25);
        });

        it('should handle zero amounts', () => {
            const stub = createDraftPayStubWithNumericEmployeeId();
            stub.bonus = 0;
            stub.expenses = 0;

            const result = buildAddPayStubInput(stub);
            expect(result.bonus).toBe(0);
            expect(result.expenses).toBe(0);
        });

        it('should handle negative amounts', () => {
            const stub = createDraftPayStubWithNumericEmployeeId();
            stub.bonus = -10;
            stub.expenses = -5;

            const result = buildAddPayStubInput(stub);
            expect(result.bonus).toBe(-10);
            expect(result.expenses).toBe(-5);
        });
    });
});

// =============================================================================
// PAYSTUB IDENTIFICATION TESTS
// =============================================================================

describe('PayStub Identification Tests', () => {
    describe('isNewPayStub', () => {
        it('should identify new PayStub in new timesheet', () => {
            const stub = createDraftPayStubWithNumericEmployeeId();
            const result = isNewPayStub(stub, true); // isNewTimesheet = true

            expect(result).toBe(true);
        });

        it('should identify new PayStub without ID in existing timesheet', () => {
            const stub = createDraftPayStubWithNumericEmployeeId();
            // stub has no ID
            const result = isNewPayStub(stub, false); // isNewTimesheet = false

            expect(result).toBe(true);
        });

        it('should identify existing PayStub with valid global ID', () => {
            const stub = createDraftPayStubWithRelayEmployeeId();
            stub.id = RelayIdService.toGlobalId('PayStub', 123);
            const result = isNewPayStub(stub, false);

            expect(result).toBe(false);
        });

        it('should identify new PayStub with temporary/client ID', () => {
            const stub = createDraftPayStubWithNumericEmployeeId();
            stub.id = 'client:temp:paystub:123';
            const result = isNewPayStub(stub, false);

            expect(result).toBe(true);
        });

        it('should respect explicit isNew flag', () => {
            const stub = createDraftPayStubWithRelayEmployeeId();
            stub.id = RelayIdService.toGlobalId('PayStub', 123);
            stub.isNew = true; // Explicit flag overrides ID check

            const result = isNewPayStub(stub, false);
            expect(result).toBe(true);
        });

        it('should respect explicit isNew=false flag', () => {
            const stub = createDraftPayStubWithNumericEmployeeId();
            stub.isNew = false; // Explicit flag overrides missing ID

            const result = isNewPayStub(stub, false);
            expect(result).toBe(false);
        });
    });
});

// =============================================================================
// INTEGRATION TESTS
// =============================================================================

describe('Integration Tests', () => {
    describe('Full Workflow Tests', () => {
        it('should handle add workflow with numeric employeeId', () => {
            const stub = createDraftPayStubWithNumericEmployeeId();

            // 1. Identify as new
            expect(isNewPayStub(stub, false)).toBe(true);

            // 2. Build add input with proper conversion
            const addInput = buildAddPayStubInput(stub);
            expect(RelayIdService.isGlobalId(addInput.employeeId)).toBe(true);

            // 3. Verify all data preserved
            expect(addInput.name).toBe(stub.name);
            expect(addInput.stHours).toBe(stub.stHours);
        });

        it('should handle modify workflow with existing PayStub', () => {
            const stub = createDraftPayStubWithRelayEmployeeId();
            stub.id = '456'; // Will be converted to Relay Global ID

            // 1. Identify as existing
            expect(isNewPayStub(stub, false)).toBe(false);

            // 2. Build modify input
            const modifyInput = buildModifyPayStubInput(stub);
            expect(modifyInput.id).toBe(RelayIdService.toGlobalId('PayStub', stub.id));
            expect(RelayIdService.isGlobalId(modifyInput.employeeId)).toBe(true);

            // 3. Verify all data preserved
            expect(modifyInput.name).toBe(stub.name);
            expect(modifyInput.stHours).toBe(stub.stHours);
        });

        it('should handle conversion from numeric to global ID and back', () => {
            const numericEmployeeId = '789';
            const stub: DraftPayStub = {
                employeeId: numericEmployeeId,
                name: 'Test Conversion'
            };

            // Convert to add input (numeric -> global)
            const addInput = buildAddPayStubInput(stub);
            const globalEmployeeId = addInput.employeeId;

            expect(RelayIdService.isGlobalId(globalEmployeeId)).toBe(true);

            // Verify we can extract the original numeric ID
            const parsed = RelayIdService.fromGlobalId(globalEmployeeId);
            expect(parsed.type).toBe('Employee');
            expect(parsed.id).toBe(numericEmployeeId);

            // Convert back to numeric
            const backToNumeric = RelayIdService.toNumericId(globalEmployeeId);
            expect(backToNumeric).toBe(parseInt(numericEmployeeId));
        });
    });

    describe('Error Recovery Tests', () => {
        it('should handle corrupted employeeId gracefully', () => {
            const stub: DraftPayStub = {
                employeeId: 'corrupted-employee-id-###',
                name: 'Test'
            };

            expect(() => buildAddPayStubInput(stub)).not.toThrow();

            const result = buildAddPayStubInput(stub);
            // RelayIdService should handle the invalid input gracefully
            expect(typeof result.employeeId).toBe('string');
            expect(result.employeeId.length).toBeGreaterThan(0);
        });

        it('should handle missing required fields gracefully', () => {
            const stub: DraftPayStub = {
                employeeId: '123'
                // Missing other optional fields
            };

            expect(() => buildAddPayStubInput(stub)).not.toThrow();

            const result = buildAddPayStubInput(stub);
            expect(RelayIdService.isGlobalId(result.employeeId)).toBe(true);
        });
    });
});

// =============================================================================
// WORKDATE PRESENCE TESTS (Regression for Missing workDate Field)
// =============================================================================

describe('PayStub Detail Mapper - workDate field', () => {
    it('should include workDate when building AddPayStubDetailInput', () => {
        // Minimal mock of ModifiablePayStubDetail with required workDate
        const mockDetail: any = {
            id: 'temp-detail-id',
            payStubId: 'temp-paystub-id',
            workDate: '2025-01-15',
            stHours: 4,
            otHours: 1,
            dtHours: 0,
            jobCode: 'TEST',
            earningsCode: null,
            agreementId: null,
            classificationId: null,
            subClassificationId: null,
            costCenter: null,
            hourlyRate: null,
            bonus: null,
            expenses: null,
            reportLineItemId: null
        };

        // Cast to satisfy type without using assertions on other fragment fields
        const input = (require('../payStubMappers') as typeof import('../payStubMappers')).buildAddPayStubInput({
            employeeId: '1',
            details: [mockDetail]
        });

        // Verify workDate made it through mapper to GraphQL input
        expect(input.details?.[0]?.workDate).toBe('2025-01-15');
    });
});
