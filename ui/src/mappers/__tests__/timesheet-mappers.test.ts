/**
 * Phase 5: Validation & Testing - Comprehensive Mapper Tests
 *
 * Test strategy for timesheet mapper functions, ensuring type safety
 * and validation without losing edge case coverage.
 *
 * This test suite implements the testing approach described in Phase 5
 * of the GraphQL Type Safety Implementation Plan.
 */

// @ts-nocheck

import {
    convertTimesheetFromGraphQL,
    convertTimesheetToGraphQL,
    convertPayStubFromGraphQL,
    convertPayStubToGraphQL,
    convertPayStubDetailFromGraphQL,
    convertPayStubDetailToGraphQL,
    convertPayStubDraftToGraphQL,
    convertPayStubDetailDraftToGraphQL,
    validateTimesheetForGraphQL,
    validatePayStubForGraphQL,
    validatePayStubDetailForGraphQL,
    safeConvertToTimesheetDomain,
    createNewPayStubGraphQLInput,
    createNewPayStubDetailGraphQLInput
} from '../timesheet-mappers';

import { isModifyTimeSheetInput, isModifyPayStubInput, isModifyPayStubDetailInput } from '../../types/graphql-timesheet';

import {
    validateTimesheetInputComprehensive,
    validatePayStubInputComprehensive,
    validatePayStubDetailInputComprehensive
} from '../../validation/timesheet-validation';

import type { ModifyTimeSheetInput, ModifyPayStubInput, ModifyPayStubDetailInput } from '../../types/graphql-timesheet';

import type { TimesheetDomainModel, PayStubDomainModel, PayStubDetailDomainModel } from '../../types/timesheet-domain';

// Import test helpers for proper ID formatting
import { createEmployeeId, createTimesheetId, createPayStubId } from '../../test-utils/idHelpers';

// =============================================================================
// TEST DATA FACTORIES
// =============================================================================

/**
 * Creates a valid ModifyTimeSheetInput for testing
 */
function createValidModifyTimeSheetInput(): ModifyTimeSheetInput {
    return {
        id: '12345',
        employerGuid: 'test-employer-guid-123',
        name: 'Test Timesheet',
        status: 'draft',
        type: 'weekly',
        readOnly: false,
        showDTHoursColumn: true,
        showCostCenterColumn: false,
        showBonusColumn: true,
        showExpensesColumn: false,
        showEarningsCodesColumn: true,
        modificationDate: '2023-12-01T10:00:00.000Z',
        modifyPayStubs: [createValidModifyPayStubInput()]
    };
}

/**
 * Creates a valid ModifyPayStubInput for testing
 */
function createValidModifyPayStubInput(): ModifyPayStubInput {
    return {
        id: 'paystub-123',
        employeeId: 'RW1wbG95ZWU6NDU2', // Global ID for Employee:456
        employeeName: 'John Doe',
        name: 'Week 1',
        stHours: 40,
        otHours: 5,
        dtHours: 2,
        bonus: 100,
        expenses: 50,
        expanded: true,
        inEdit: false,
        delete: false,
        payStubId: 'paystub-123',
        details: [createValidModifyPayStubDetailInput()]
    };
}

/**
 * Creates a valid ModifyPayStubDetailInput for testing
 */
function createValidModifyPayStubDetailInput(): ModifyPayStubDetailInput {
    return {
        id: 'detail-789',
        payStubId: 'paystub-123',
        reportLineItemId: 456,
        workDate: '2023-12-01',
        name: 'Friday Work',
        stHours: 8,
        otHours: 1,
        dtHours: 0.5,
        jobCode: 'JOB001',
        costCenter: 'CC001',
        agreementId: 1,
        classificationId: 2,
        subClassificationId: 3,
        hourlyRate: 25.5,
        bonus: 25,
        expenses: 15,
        earningsCode: 'REG',
        delete: false
    };
}

/**
 * Creates a valid TimesheetDomainModel for testing
 */
function createValidTimesheetDomainModel(): TimesheetDomainModel {
    return {
        id: createTimesheetId(12345),
        numericId: 12345,
        name: 'Test Timesheet',
        status: 'draft',
        type: 'weekly',
        payPeriodEndDate: '2023-12-08',
        employerGuid: 'test-employer-guid-123',
        payStubs: [createValidPayStubDomainModel()],
        settings: {
            showDTHoursColumn: true,
            showCostCenterColumn: false,
            showBonusColumn: true,
            showExpensesColumn: false,
            showEarningsCodesColumn: true,
            readOnly: false
        },
        meta: {
            canEdit: true,
            lastModified: new Date('2023-12-01T10:00:00.000Z'),
            hoursWorked: 47,
            totalCount: 1
        }
    };
}

/**
 * Creates a valid PayStubDomainModel for testing
 */
function createValidPayStubDomainModel(): PayStubDomainModel {
    const employeeGlobalId = createEmployeeId(456);
    return {
        id: 'paystub-123',
        employeeId: employeeGlobalId,
        employeeName: 'John Doe',
        name: 'Week 1',
        hours: {
            standard: 40,
            overtime: 5,
            doubletime: 2,
            total: 47
        },
        amounts: {
            bonus: 100,
            expenses: 50
        },
        details: [createValidPayStubDetailDomainModel()],
        employee: {
            id: employeeGlobalId,
            firstName: 'John',
            lastName: 'Doe',
            fullName: 'John Doe',
            externalEmployeeId: employeeGlobalId,
            active: true
        },
        ui: {
            expanded: true,
            isEditing: false,
            hasErrors: false,
            isSelected: false,
            isTemporary: false
        }
    };
}

/**
 * Creates a valid PayStubDetailDomainModel for testing
 */
function createValidPayStubDetailDomainModel(): PayStubDetailDomainModel {
    const employeeGlobalId = createEmployeeId(456);
    return {
        id: 'detail-789',
        payStubId: 'paystub-123',
        reportLineItemId: 456,
        workDate: '2023-12-01',
        dayName: 'Friday',
        name: 'Friday Work',
        hours: {
            standard: 8,
            overtime: 1,
            doubletime: 0.5,
            total: 9.5
        },
        job: {
            jobCode: 'JOB001',
            costCenter: 'CC001',
            hourlyRate: 25.5
        },
        agreements: {
            agreementId: 1,
            classificationId: 2,
            subClassificationId: 3
        },
        amounts: {
            bonus: 25,
            expenses: 15
        },
        earnings: {
            earningsCode: 'REG',
            earningsCodeText: 'REG'
        },
        employeeId: employeeGlobalId,
        ui: {
            isEditing: false,
            hasErrors: false,
            isSelected: false,
            isTemporary: false,
            validationErrors: []
        }
    };
}

/**
 * Creates a valid PayStubDomainModel with global IDs for testing domain → GraphQL conversion
 */
function createValidPayStubDomainModelWithGlobalIds(): PayStubDomainModel {
    const employeeGlobalId = createEmployeeId(456);
    return {
        id: 'paystub-123',
        employeeId: employeeGlobalId,
        employeeName: 'John Doe',
        name: 'Week 1',
        hours: {
            standard: 40,
            overtime: 5,
            doubletime: 2,
            total: 47
        },
        amounts: {
            bonus: 100,
            expenses: 50
        },
        details: [createValidPayStubDetailDomainModelWithGlobalIds()],
        employee: {
            id: employeeGlobalId,
            firstName: 'John',
            lastName: 'Doe',
            fullName: 'John Doe',
            externalEmployeeId: employeeGlobalId,
            active: true
        },
        ui: {
            expanded: true,
            isEditing: false,
            hasErrors: false,
            isSelected: false,
            isTemporary: false
        }
    };
}

/**
 * Creates a valid PayStubDetailDomainModel with global IDs for testing domain → GraphQL conversion
 */
function createValidPayStubDetailDomainModelWithGlobalIds(): PayStubDetailDomainModel {
    const employeeGlobalId = createEmployeeId(456);
    return {
        id: 'detail-789',
        payStubId: 'paystub-123',
        reportLineItemId: 456,
        workDate: '2023-12-01',
        dayName: 'Friday',
        name: 'Friday Work',
        hours: {
            standard: 8,
            overtime: 1,
            doubletime: 0.5,
            total: 9.5
        },
        job: {
            jobCode: 'JOB001',
            costCenter: 'CC001',
            hourlyRate: 25.5
        },
        agreements: {
            agreementId: 1,
            classificationId: 2,
            subClassificationId: 3
        },
        amounts: {
            bonus: 25,
            expenses: 15
        },
        earnings: {
            earningsCode: 'REG',
            earningsCodeText: 'REG'
        },
        employeeId: employeeGlobalId,
        ui: {
            isEditing: false,
            hasErrors: false,
            isSelected: false,
            isTemporary: false,
            validationErrors: []
        }
    };
}

/**
 * Creates a valid TimesheetDomainModel with global IDs for testing domain → GraphQL conversion
 */
function createValidTimesheetDomainModelWithGlobalIds(): TimesheetDomainModel {
    return {
        id: createTimesheetId(12345),
        numericId: 12345,
        name: 'Test Timesheet',
        status: 'draft',
        type: 'weekly',
        payPeriodEndDate: '2023-12-08',
        employerGuid: 'test-employer-guid-123',
        payStubs: [createValidPayStubDomainModelWithGlobalIds()],
        settings: {
            showDTHoursColumn: true,
            showCostCenterColumn: false,
            showBonusColumn: true,
            showExpensesColumn: false,
            showEarningsCodesColumn: true,
            readOnly: false
        },
        meta: {
            canEdit: true,
            lastModified: new Date('2023-12-01T10:00:00.000Z'),
            hoursWorked: 47,
            totalCount: 1
        }
    };
}

// =============================================================================
// CONVERSION TESTS - GraphQL to Domain
// =============================================================================

describe('GraphQL to Domain Conversion', () => {
    describe('convertTimesheetFromGraphQL', () => {
        it('should convert valid GraphQL input to domain model', () => {
            const graphqlInput = createValidModifyTimeSheetInput();
            const result = convertTimesheetFromGraphQL(graphqlInput);

            expect(result.id).toBe('12345');
            expect(result.numericId).toBe(12345);
            expect(result.name).toBe('Test Timesheet');
            expect(result.status).toBe('draft');
            expect(result.employerGuid).toBe('test-employer-guid-123');
            expect(result.payStubs).toHaveLength(1);
            expect(result.settings.showDTHoursColumn).toBe(true);
            expect(result.meta.canEdit).toBe(true);
        });

        it('should handle missing optional fields', () => {
            const minimalInput: ModifyTimeSheetInput = {
                id: '123',
                employerGuid: 'test-guid'
            };

            const result = convertTimesheetFromGraphQL(minimalInput);

            expect(result.id).toBe('123');
            expect(result.name).toBe('');
            expect(result.status).toBe('draft');
            expect(result.payStubs).toHaveLength(0);
            expect(result.settings.showDTHoursColumn).toBe(false);
        });

        it('should handle null payStubs array', () => {
            const inputWithNullPayStubs: ModifyTimeSheetInput = {
                id: '123',
                employerGuid: 'test-guid',
                payStubs: null
            };

            const result = convertTimesheetFromGraphQL(inputWithNullPayStubs);
            expect(result.payStubs).toHaveLength(0);
        });
    });

    describe('convertPayStubFromGraphQL', () => {
        it('should convert valid PayStub GraphQL input', () => {
            const graphqlInput = createValidModifyPayStubInput();
            const result = convertPayStubFromGraphQL(graphqlInput);

            expect(result.id).toBe('paystub-123');
            expect(result.employeeId).toBe(createEmployeeId(456)); // GraphQL input uses numeric, domain model converts to global ID
            expect(result.employeeName).toBe('John Doe');
            expect(result.hours.standard).toBe(40);
            expect(result.hours.overtime).toBe(5);
            expect(result.hours.doubletime).toBe(2);
            expect(result.hours.total).toBe(47);
            expect(result.details).toHaveLength(1);
        });

        it('should calculate aggregated hours from details when stub hours are null', () => {
            const inputWithoutHours: ModifyPayStubInput = {
                employeeId: 'RW1wbG95ZWU6NDU2', // Global ID for Employee:456
                employeeName: 'John Doe',
                details: [
                    {
                        workDate: '2023-12-01',
                        stHours: 8,
                        otHours: 2,
                        dtHours: 1
                    },
                    {
                        workDate: '2023-12-02',
                        stHours: 6,
                        otHours: 1,
                        dtHours: 0
                    }
                ]
            };

            const result = convertPayStubFromGraphQL(inputWithoutHours);

            expect(result.hours.standard).toBe(14); // 8 + 6
            expect(result.hours.overtime).toBe(3); // 2 + 1
            expect(result.hours.doubletime).toBe(1); // 1 + 0
            expect(result.hours.total).toBe(18); // 14 + 3 + 1
        });
    });

    describe('convertPayStubDetailFromGraphQL', () => {
        it('should convert valid PayStubDetail GraphQL input', () => {
            const graphqlInput = createValidModifyPayStubDetailInput();
            const result = convertPayStubDetailFromGraphQL(graphqlInput, '456');

            expect(result.id).toBe('detail-789');
            expect(result.workDate).toBe('2023-12-01');
            expect(result.hours.standard).toBe(8);
            expect(result.hours.overtime).toBe(1);
            expect(result.hours.doubletime).toBe(0.5);
            expect(result.hours.total).toBe(9.5);
            expect(result.employeeId).toBe('456'); // Passed as parameter to conversion function
        });

        it('should handle missing optional fields gracefully', () => {
            const minimalInput: ModifyPayStubDetailInput = {
                workDate: '2023-12-01'
            };

            const result = convertPayStubDetailFromGraphQL(minimalInput, '123');

            expect(result.workDate).toBe('2023-12-01');
            expect(result.hours.standard).toBe(0);
            expect(result.hours.overtime).toBe(0);
            expect(result.hours.doubletime).toBe(0);
            expect(result.hours.total).toBe(0);
            expect(result.employeeId).toBe('123');
        });
    });
});

// =============================================================================
// CONVERSION TESTS - Domain to GraphQL
// =============================================================================

describe('Domain to GraphQL Conversion', () => {
    describe('convertTimesheetToGraphQL', () => {
        it('should convert valid domain model to GraphQL input', () => {
            const domainModel = createValidTimesheetDomainModelWithGlobalIds();
            const result = convertTimesheetToGraphQL(domainModel);

            expect(result.id).toBe(12345);
            expect(result.employerGuid).toBe('test-employer-guid-123');
            expect(result.name).toBe('Test Timesheet');
            expect(result.status).toBe('draft');
            expect(result.type).toBe('weekly');
            expect(result.modifyPayStubs).toHaveLength(1);
            expect(result.showDTHoursColumn).toBe(true);
            expect(result.readOnly).toBe(false);
        });

        it('should handle string id conversion to numeric', () => {
            const domainModel = createValidTimesheetDomainModelWithGlobalIds();
            domainModel.numericId = undefined;
            domainModel.id = createTimesheetId(98765);

            const result = convertTimesheetToGraphQL(domainModel);
            expect(result.id).toBe(98765);
        });
    });

    describe('convertPayStubToGraphQL', () => {
        it('should convert valid domain model with correct field names', () => {
            const domainModel = createValidPayStubDomainModelWithGlobalIds();
            const result = convertPayStubToGraphQL(domainModel);

            expect(result.id).toBe('paystub-123');
            expect(result.employeeId).toBe(createEmployeeId(456)); // Domain model has global ID, passed through as-is to GraphQL
            expect(result.employeeName).toBe('John Doe');

            // CRITICAL: Verify correct GraphQL field names
            expect(result.stHours).toBe(40);
            expect(result.otHours).toBe(5);
            expect(result.dtHours).toBe(2);

            expect(result.bonus).toBe(100);
            expect(result.expenses).toBe(50);
            expect(result.details).toHaveLength(1);

            // UI state fields should NOT be present in GraphQL conversion per Rule 14
            expect(result.expanded).toBeUndefined();
            expect(result.inEdit).toBeUndefined();
            expect(result.delete).toBeUndefined();
        });
    });

    describe('convertPayStubDetailToGraphQL', () => {
        it('should convert with correct GraphQL field names', () => {
            const domainModel = createValidPayStubDetailDomainModelWithGlobalIds();
            const result = convertPayStubDetailToGraphQL(domainModel);

            expect(result.id).toBe('detail-789');
            expect(result.payStubId).toBe('paystub-123');
            expect(result.workDate).toBe('2023-12-01');

            // CRITICAL: Verify correct GraphQL field names
            expect(result.stHours).toBe(8);
            expect(result.otHours).toBe(1);
            expect(result.dtHours).toBe(0.5);

            expect(result.jobCode).toBe('JOB001');
            expect(result.costCenter).toBe('CC001');
            expect(result.agreementId).toBe(1);
            expect(result.classificationId).toBe(2);
            expect(result.subClassificationId).toBe(3);
            expect(result.hourlyRate).toBe(25.5);
            expect(result.bonus).toBe(25);
            expect(result.expenses).toBe(15);
            expect(result.earningsCode).toBe('REG');
            expect(result.delete).toBe(false);
        });
    });
});

// =============================================================================
// ROUND-TRIP CONVERSION TESTS
// =============================================================================

describe('Round-trip Conversion Tests', () => {
    describe('GraphQL → Domain → GraphQL', () => {
        it('should preserve data through full round-trip conversion', () => {
            const originalGraphQL = createValidModifyTimeSheetInput();

            // Convert GraphQL → Domain
            const domainModel = convertTimesheetFromGraphQL(originalGraphQL);

            // NOTE: Round-trip conversion has limitations due to ID format changes
            // GraphQL uses numeric employee IDs, but domain model uses string IDs
            // To convert back to GraphQL, we need to manually set global IDs
            if (domainModel.payStubs.length > 0) {
                domainModel.payStubs[0].employeeId = createEmployeeId(originalGraphQL.modifyPayStubs?.[0]?.employeeId || 456);
            }

            const backToGraphQL = convertTimesheetToGraphQL(domainModel);

            // Core fields should be preserved
            expect(backToGraphQL.id).toBe(originalGraphQL.id);
            expect(backToGraphQL.employerGuid).toBe(originalGraphQL.employerGuid);
            expect(backToGraphQL.name).toBe(originalGraphQL.name);
            expect(backToGraphQL.status).toBe(originalGraphQL.status);
            expect(backToGraphQL.type).toBe(originalGraphQL.type);
            expect(backToGraphQL.readOnly).toBe(originalGraphQL.readOnly);
            expect(backToGraphQL.showDTHoursColumn).toBe(originalGraphQL.showDTHoursColumn);
            expect(backToGraphQL.modifyPayStubs).toHaveLength(originalGraphQL.modifyPayStubs?.length || 0);
        });

        it('should preserve PayStub data through round-trip', () => {
            const originalPayStub = createValidModifyPayStubInput();

            const domainModel = convertPayStubFromGraphQL(originalPayStub);

            // employeeId is already a Global ID, no conversion needed
            // domainModel.employeeId = createEmployeeId(originalPayStub.employeeId);

            const backToGraphQL = convertPayStubToGraphQL(domainModel);

            expect(backToGraphQL.id).toBe(originalPayStub.id);
            expect(backToGraphQL.employeeId).toBe(originalPayStub.employeeId);
            expect(backToGraphQL.employeeName).toBe(originalPayStub.employeeName);
            expect(backToGraphQL.stHours).toBe(originalPayStub.stHours);
            expect(backToGraphQL.otHours).toBe(originalPayStub.otHours);
            expect(backToGraphQL.dtHours).toBe(originalPayStub.dtHours);
            expect(backToGraphQL.bonus).toBe(originalPayStub.bonus);
            expect(backToGraphQL.expenses).toBe(originalPayStub.expenses);
        });

        it('should preserve PayStubDetail data through round-trip', () => {
            const originalDetail = createValidModifyPayStubDetailInput();

            const domainModel = convertPayStubDetailFromGraphQL(originalDetail, '456');
            const backToGraphQL = convertPayStubDetailToGraphQL(domainModel);

            expect(backToGraphQL.id).toBe(originalDetail.id);
            expect(backToGraphQL.payStubId).toBe(originalDetail.payStubId);
            expect(backToGraphQL.workDate).toBe(originalDetail.workDate);
            expect(backToGraphQL.stHours).toBe(originalDetail.stHours);
            expect(backToGraphQL.otHours).toBe(originalDetail.otHours);
            expect(backToGraphQL.dtHours).toBe(originalDetail.dtHours);
            expect(backToGraphQL.jobCode).toBe(originalDetail.jobCode);
            expect(backToGraphQL.costCenter).toBe(originalDetail.costCenter);
        });
    });

    describe('Domain → GraphQL → Domain', () => {
        it('should preserve domain model through GraphQL conversion', () => {
            const originalDomain = createValidTimesheetDomainModelWithGlobalIds();

            const graphqlModel = convertTimesheetToGraphQL(originalDomain);
            const backToDomain = convertTimesheetFromGraphQL(graphqlModel);

            // NOTE: ID format changes during conversion
            // Domain → GraphQL: global ID becomes numeric ID
            // GraphQL → Domain: numeric ID becomes string ID
            expect(backToDomain.id).toBe(String(originalDomain.numericId)); // Converted to string
            expect(backToDomain.numericId).toBe(originalDomain.numericId);
            expect(backToDomain.name).toBe(originalDomain.name);
            expect(backToDomain.status).toBe(originalDomain.status);
            expect(backToDomain.employerGuid).toBe(originalDomain.employerGuid);
            expect(backToDomain.settings.showDTHoursColumn).toBe(originalDomain.settings.showDTHoursColumn);
        });
    });
});

// =============================================================================
// VALIDATION TESTS
// =============================================================================

describe('Validation Tests', () => {
    describe('GraphQL Input Validation', () => {
        it('should validate correct ModifyTimeSheetInput', () => {
            const validInput = createValidModifyTimeSheetInput();
            expect(isModifyTimeSheetInput(validInput)).toBe(true);
        });

        it('should reject input with missing required fields', () => {
            const invalidInput = {
                // Missing id and employerGuid
                name: 'Test'
            };
            expect(isModifyTimeSheetInput(invalidInput)).toBe(false);
        });

        it('should reject input with incorrect types', () => {
            const invalidInput = {
                id: '123', // Should be number
                employerGuid: 'test-guid'
            };
            expect(isModifyTimeSheetInput(invalidInput)).toBe(false);
        });

        it('should validate correct PayStub input', () => {
            const validInput = createValidModifyPayStubInput();
            expect(isModifyPayStubInput(validInput)).toBe(true);
        });

        it('should reject PayStub with invalid employeeId', () => {
            const invalidInput = {
                employeeId: 123, // Should be string (Global ID), not number
                employeeName: 'John Doe'
            };
            expect(isModifyPayStubInput(invalidInput)).toBe(false);
        });

        it('should allow negative hours in basic validation', () => {
            const inputWithNegativeHours = {
                employeeId: 'RW1wbG95ZWU6MTIz', // Valid Global ID for Employee:123
                stHours: -5 // Negative hours - basic validation allows this
            };
            expect(isModifyPayStubInput(inputWithNegativeHours)).toBe(true);
        });
    });

    describe('Comprehensive Validation', () => {
        it('should provide detailed error information', () => {
            const invalidInput = {
                id: '123', // Wrong type
                employerGuid: null, // Required field missing
                name: '',
                payStubs: [
                    {
                        employeeId: -1, // Invalid value
                        stHours: -5 // Negative hours
                    }
                ]
            };

            const result = validateTimesheetInputComprehensive(invalidInput);

            expect(result.isValid).toBe(false);
            expect(result.errors.length).toBeGreaterThan(0);
            expect(result.errors.some((e) => e.field === 'id')).toBe(true);
            expect(result.errors.some((e) => e.field === 'employerGuid')).toBe(true);
            expect(result.errors.some((e) => e.field.includes('employeeId'))).toBe(true);
        });

        it('should provide warnings for suspicious data', () => {
            const suspiciousInput = createValidModifyTimeSheetInput();
            if (suspiciousInput.payStubs?.[0]) {
                suspiciousInput.payStubs[0].stHours = 25; // > 24 hours
            }

            const result = validateTimesheetInputComprehensive(suspiciousInput);

            expect(result.isValid).toBe(true); // Still valid
            expect(result.warnings.length).toBeGreaterThan(0);
            expect(result.warnings.some((w) => w.code === 'EXCESSIVE_HOURS')).toBe(true);
        });
    });

    describe('Domain Model Validation', () => {
        it('should validate timesheet for GraphQL conversion', () => {
            const validDomain = createValidTimesheetDomainModelWithGlobalIds();
            const result = validateTimesheetForGraphQL(validDomain);

            expect(result.isValid).toBe(true);
            expect(result.errors).toHaveLength(0);
        });

        it('should catch validation errors in domain model', () => {
            const invalidDomain = createValidTimesheetDomainModelWithGlobalIds();
            invalidDomain.employerGuid = ''; // Invalid
            invalidDomain.payStubs[0].employeeId = ''; // Invalid

            const result = validateTimesheetForGraphQL(invalidDomain);

            expect(result.isValid).toBe(false);
            expect(result.errors.length).toBeGreaterThan(0);
        });

        it('should validate PayStub domain model', () => {
            const validPayStub = createValidPayStubDomainModelWithGlobalIds();
            const result = validatePayStubForGraphQL(validPayStub);

            expect(result.isValid).toBe(true);
            expect(result.errors).toHaveLength(0);
        });

        it('should validate PayStubDetail domain model', () => {
            const validDetail = createValidPayStubDetailDomainModelWithGlobalIds();
            const result = validatePayStubDetailForGraphQL(validDetail);

            expect(result.isValid).toBe(true);
            expect(result.errors).toHaveLength(0);
        });
    });
});

// =============================================================================
// EDGE CASE AND ERROR HANDLING TESTS
// =============================================================================

describe('Edge Cases and Error Handling', () => {
    describe('Null and Undefined Handling', () => {
        it('should handle null input gracefully', () => {
            expect(isModifyTimeSheetInput(null)).toBe(false);
            expect(isModifyPayStubInput(undefined)).toBe(false);
            expect(isModifyPayStubDetailInput(null)).toBe(false);
        });

        it('should handle empty objects', () => {
            expect(isModifyTimeSheetInput({})).toBe(false);
            expect(isModifyPayStubInput({})).toBe(false);
        });

        it('should handle arrays as input', () => {
            expect(isModifyTimeSheetInput([])).toBe(false);
            expect(isModifyPayStubInput([1, 2, 3])).toBe(false);
        });

        it('should handle primitive values', () => {
            expect(isModifyTimeSheetInput('string')).toBe(false);
            expect(isModifyPayStubInput(123)).toBe(false);
            expect(isModifyPayStubDetailInput(true)).toBe(false);
        });
    });

    describe('Boundary Value Testing', () => {
        it('should handle zero hours correctly', () => {
            const input = createValidModifyPayStubInput();
            input.stHours = 0;
            input.otHours = 0;
            input.dtHours = 0;
            // Remove existing details that would add to total
            input.details = [];

            expect(isModifyPayStubInput(input)).toBe(true);

            const domain = convertPayStubFromGraphQL(input);
            expect(domain.hours.total).toBe(0);
        });

        it('should handle very large hour values', () => {
            const input = createValidModifyPayStubInput();
            input.stHours = 1000;

            const result = validatePayStubInputComprehensive(input);
            expect(result.isValid).toBe(true);
            expect(result.warnings.some((w) => w.code === 'EXCESSIVE_HOURS')).toBe(true);
        });

        it('should handle decimal hours', () => {
            const input = createValidModifyPayStubDetailInput();
            input.stHours = 7.5;
            input.otHours = 1.25;
            input.dtHours = 0.75;

            expect(isModifyPayStubDetailInput(input)).toBe(true);

            const domain = convertPayStubDetailFromGraphQL(input, '123');
            expect(domain.hours.total).toBe(9.5);
        });
    });

    describe('Date Handling', () => {
        it('should handle various date formats', () => {
            const validDates = ['2023-12-01', '2023-01-01', '2023-12-31'];

            validDates.forEach((date) => {
                const input = createValidModifyPayStubDetailInput();
                input.workDate = date;
                expect(isModifyPayStubDetailInput(input)).toBe(true);
            });
        });

        it('should reject invalid date formats', () => {
            const invalidDates = ['12/01/2023', '2023-13-01', '2023-12-32', 'invalid-date', ''];

            invalidDates.forEach((date) => {
                const input = createValidModifyPayStubDetailInput();
                input.workDate = date;

                const result = validatePayStubDetailInputComprehensive(input);
                if (date === '') {
                    // Empty string might be valid in some contexts
                    return;
                }
                expect(result.isValid).toBe(false);
            });
        });
    });

    describe('Safe Conversion', () => {
        it('should safely convert valid input', () => {
            const validInput = createValidModifyTimeSheetInput();
            const result = safeConvertToTimesheetDomain(validInput);

            expect(result.success).toBe(true);
            if (result.success) {
                expect(result.data.id).toBe('12345');
            }
        });

        it('should handle conversion errors safely', () => {
            const invalidInput = {
                id: 'invalid',
                employerGuid: null
            };

            const result = safeConvertToTimesheetDomain(invalidInput);
            expect(result.success).toBe(false);
            if (!result.success) {
                expect(result.error).toBeTruthy();
            }
        });
    });

    describe('Draft Conversion', () => {
        it('should convert partial PayStub drafts', () => {
            const existingPayStub = createValidPayStubDomainModelWithGlobalIds();
            const draft: Partial<PayStubDomainModel> = {
                hours: {
                    standard: 35,
                    overtime: 3,
                    doubletime: 1,
                    total: 39
                }
            };

            const result = convertPayStubDraftToGraphQL(draft, existingPayStub);

            expect(result.stHours).toBe(35);
            expect(result.otHours).toBe(3);
            expect(result.dtHours).toBe(1);
            expect(result.employeeId).toBe(createEmployeeId(456)); // From existing - Global ID
        });

        it('should handle draft without existing data', () => {
            const draft: Partial<PayStubDomainModel> = {
                employeeId: createEmployeeId(789),
                hours: { standard: 8, overtime: 0, doubletime: 0, total: 8 }
            };

            const result = convertPayStubDraftToGraphQL(draft);

            expect(result.employeeId).toBe(createEmployeeId(789)); // Global ID for Employee:789
            expect(result.stHours).toBe(8);
        });
    });

    describe('Utility Functions', () => {
        it('should create new PayStub input correctly', () => {
            const result = createNewPayStubGraphQLInput('RW1wbG95ZWU6MTIz', 'Jane Doe'); // Global ID for Employee:123

            expect(result.employeeId).toBe('RW1wbG95ZWU6MTIz'); // Global ID for Employee:123
            expect(result.employeeName).toBe('Jane Doe');
            expect(result.stHours).toBe(0);
            expect(result.otHours).toBe(0);
            expect(result.dtHours).toBe(0);
            expect(result.expanded).toBe(true);
            expect(result.inEdit).toBe(true);
        });

        it('should create new PayStubDetail input correctly', () => {
            const result = createNewPayStubDetailGraphQLInput('paystub-456', '2023-12-05');

            expect(result.payStubId).toBe('paystub-456');
            expect(result.workDate).toBe('2023-12-05');
            expect(result.stHours).toBe(0);
            expect(result.otHours).toBe(0);
            expect(result.dtHours).toBe(0);
            expect(result.delete).toBe(false);
        });
    });
});

// =============================================================================
// INTEGRATION TESTS
// =============================================================================

describe('Integration Tests', () => {
    describe('Full Workflow Tests', () => {
        it('should handle complete timesheet creation workflow', () => {
            // 1. Create new timesheet input
            const timesheetInput: ModifyTimeSheetInput = {
                id: '999',
                employerGuid: 'workflow-test-guid',
                name: 'Integration Test Timesheet',
                payStubs: []
            };

            // 2. Convert to domain model
            const domain = convertTimesheetFromGraphQL(timesheetInput);
            expect(domain.id).toBe('999');
            expect(domain.payStubs).toHaveLength(0);

            // 3. Add a PayStub
            const newPayStubInput = createNewPayStubGraphQLInput('RW1wbG95ZWU6MTAx', 'Test Employee'); // Global ID for Employee:101
            const payStubDomain = convertPayStubFromGraphQL(newPayStubInput);
            domain.payStubs.push(payStubDomain);

            // 4. Validate the complete model
            const validation = validateTimesheetForGraphQL(domain);
            expect(validation.isValid).toBe(true);

            // 5. Convert back to GraphQL for mutation
            const finalGraphQL = convertTimesheetToGraphQL(domain);
            expect(finalGraphQL.modifyPayStubs).toHaveLength(1);
            expect(finalGraphQL.modifyPayStubs?.[0]?.employeeId).toBe(createEmployeeId(101)); // Global ID for Employee:101
        });

        it('should handle timesheet editing workflow', () => {
            // 1. Start with existing timesheet
            const existingTimesheet = createValidTimesheetDomainModelWithGlobalIds();

            // 2. Create draft changes
            const payStubDraft: Partial<PayStubDomainModel> = {
                id: existingTimesheet.payStubs[0].id,
                hours: {
                    standard: 45, // Changed from 40
                    overtime: 3, // Changed from 5
                    doubletime: 2,
                    total: 50
                }
            };

            // 3. Convert draft to GraphQL
            const draftGraphQL = convertPayStubDraftToGraphQL(payStubDraft, existingTimesheet.payStubs[0]);

            // 4. Apply changes
            const updatedPayStub = convertPayStubFromGraphQL(draftGraphQL as ModifyPayStubInput);
            existingTimesheet.payStubs[0] = updatedPayStub;

            // 5. Validate and convert final timesheet
            const validation = validateTimesheetForGraphQL(existingTimesheet);
            expect(validation.isValid).toBe(true);

            const finalTimesheet = convertTimesheetToGraphQL(existingTimesheet);
            expect(finalTimesheet.modifyPayStubs?.[0]?.stHours).toBe(45);
            expect(finalTimesheet.modifyPayStubs?.[0]?.otHours).toBe(3);
        });
    });
});
