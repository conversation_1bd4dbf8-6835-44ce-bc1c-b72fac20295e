/**
 * PayStub Mappers workDate Tests
 *
 * Focused tests to ensure workDate is properly validated and included
 * in PayStub detail inputs to prevent GraphQL validation errors
 */

import { buildAddPayStubInput, buildModifyPayStubInput, type DraftPayStub } from '../payStubMappers';
import { RelayIdService } from '../../services/RelayIdService';

describe('PayStub Mappers - workDate validation', () => {
    describe('buildAddPayStubInput', () => {
        it('should throw error when detail has no workDate', () => {
            const draft: DraftPayStub = {
                employeeId: '123',
                name: '<PERSON>',
                details: [
                    {
                        id: 'detail-1',
                        name: 'Monday',
                        // workDate is missing - this should cause an error
                        stHours: 8,
                        otHours: 0,
                        dtHours: 0,
                        payStubId: 'stub-1'
                    } as any // Type assertion to bypass TypeScript checking for test
                ]
            };

            expect(() => buildAddPayStubInput(draft)).toThrow('Attempted to build PayStubDetailInput without a valid workDate');
        });

        it('should throw error when detail has empty string workDate', () => {
            const draft: DraftPayStub = {
                employeeId: '123',
                name: 'John Doe',
                details: [
                    {
                        id: 'detail-1',
                        name: 'Monday',
                        workDate: '', // Empty string should be invalid
                        stHours: 8,
                        otHours: 0,
                        dtHours: 0,
                        payStubId: 'stub-1'
                    } as any
                ]
            };

            expect(() => buildAddPayStubInput(draft)).toThrow('Attempted to build PayStubDetailInput without a valid workDate');
        });

        it('should throw error when detail has non-string workDate', () => {
            const draft: DraftPayStub = {
                employeeId: '123',
                name: 'John Doe',
                details: [
                    {
                        id: 'detail-1',
                        name: 'Monday',
                        workDate: null as any, // null should be invalid
                        stHours: 8,
                        otHours: 0,
                        dtHours: 0,
                        payStubId: 'stub-1'
                    } as any
                ]
            };

            expect(() => buildAddPayStubInput(draft)).toThrow('Attempted to build PayStubDetailInput without a valid workDate');
        });

        it('should pass with valid workDate string', () => {
            const draft: DraftPayStub = {
                employeeId: '123',
                name: 'John Doe',
                details: [
                    {
                        id: 'detail-1',
                        name: 'Monday',
                        workDate: '2024-01-01',
                        stHours: 8,
                        otHours: 0,
                        dtHours: 0,
                        payStubId: 'stub-1'
                    } as any
                ]
            };

            const result = buildAddPayStubInput(draft);
            expect(result.details).toHaveLength(1);
            expect(result.details![0].workDate).toBe('2024-01-01');
        });

        it('should handle multiple details with valid workDates', () => {
            const draft: DraftPayStub = {
                employeeId: '123',
                name: 'John Doe',
                details: [
                    {
                        id: 'detail-1',
                        name: 'Monday',
                        workDate: '2024-01-01',
                        stHours: 8,
                        otHours: 0,
                        dtHours: 0,
                        payStubId: 'stub-1'
                    } as any,
                    {
                        id: 'detail-2',
                        name: 'Tuesday',
                        workDate: '2024-01-02',
                        stHours: 8,
                        otHours: 0,
                        dtHours: 0,
                        payStubId: 'stub-1'
                    } as any
                ]
            };

            const result = buildAddPayStubInput(draft);
            expect(result.details).toHaveLength(2);
            expect(result.details![0].workDate).toBe('2024-01-01');
            expect(result.details![1].workDate).toBe('2024-01-02');
        });
    });

    describe('buildModifyPayStubInput', () => {
        it('should throw error when detail has no workDate', () => {
            const draft: DraftPayStub = {
                id: 'paystub-123',
                employeeId: '123',
                name: 'John Doe',
                details: [
                    {
                        id: 'detail-1',
                        name: 'Monday',
                        // workDate is missing
                        stHours: 8,
                        otHours: 0,
                        dtHours: 0,
                        payStubId: 'paystub-123'
                    } as any
                ]
            };

            expect(() => buildModifyPayStubInput(draft)).toThrow('Attempted to build PayStubDetailInput without a valid workDate');
        });

        it('should pass with valid workDate string', () => {
            const draft: DraftPayStub = {
                id: 'paystub-123',
                employeeId: '123',
                name: 'John Doe',
                details: [
                    {
                        id: 'detail-1',
                        name: 'Monday',
                        workDate: '2024-01-01',
                        stHours: 8,
                        otHours: 0,
                        dtHours: 0,
                        payStubId: 'paystub-123'
                    } as any
                ]
            };

            const result = buildModifyPayStubInput(draft);
            expect(result.details).toHaveLength(1);
            expect(result.details![0].workDate).toBe('2024-01-01');
        });
    });

    describe('Integration with useTimesheetSaver', () => {
        it('should demonstrate the issue when workDate is missing from UI data', () => {
            // This test documents the bug scenario where UI creates details without workDate
            const uiGeneratedDetail = {
                id: 'temp-detail-123',
                payStubId: 'temp-stub-456',
                name: 'Work Day',
                // UI might create details without workDate field
                stHours: 8,
                otHours: 0,
                dtHours: 0,
                jobCode: 'PROJ-001'
            };

            // This would be the draft stub as seen in useTimesheetSaver
            const draftStub: DraftPayStub = {
                employeeId: '789',
                name: 'Employee Name',
                details: [uiGeneratedDetail as any]
            };

            // This should throw, preventing the bad data from reaching GraphQL
            expect(() => buildAddPayStubInput(draftStub)).toThrow('Attempted to build PayStubDetailInput without a valid workDate');
        });

        it('should show the fix: ensure workDate is always provided', () => {
            // The fix ensures workDate is always set before calling the mapper
            const uiGeneratedDetail = {
                id: 'temp-detail-123',
                payStubId: 'temp-stub-456',
                name: 'Work Day',
                workDate: '2024-01-15', // Fixed: workDate is now always provided
                stHours: 8,
                otHours: 0,
                dtHours: 0,
                jobCode: 'PROJ-001'
            };

            const draftStub: DraftPayStub = {
                employeeId: '789',
                name: 'Employee Name',
                details: [uiGeneratedDetail as any]
            };

            // This should now work - with explicit workDate in detail
            const result = buildAddPayStubInput(draftStub);
            expect(result.details).toHaveLength(1);
            expect(result.details![0].workDate).toBe('2024-01-15');
        });
    });
});