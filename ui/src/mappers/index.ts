/**
 * Mappers Index - Phase 2: Conversion Layer
 * 
 * Barrel exports for all mapper functions in the timesheet system
 */

export {
    // GraphQL → Domain converters
    convertTimesheetFromGraphQL,
    convertPayStubFromGraphQL,
    convertPayStubDetailFromGraphQL,
    
    // Domain → GraphQL converters
    convertTimesheetToGraphQL,
    convertPayStubToGraphQL,
    convertPayStubDetailToGraphQL,
    
    // Draft/Partial converters
    convertPayStubDraftToGraphQL,
    convertPayStubDetailDraftToGraphQL,
    
    // Validation functions
    validateTimesheetForGraphQL,
    validatePayStubForGraphQL,
    validatePayStubDetailForGraphQL,
    
    // Utility functions
    createNewPayStubGraphQLInput,
    createNewPayStubDetailGraphQLInput,
    safeConvertToTimesheetDomain,
} from './timesheet-mappers';

// PayStub separate arrays mappers
export {
    buildAddPayStubInput,
    buildModifyPayStubInput,
    isNewPayStub,
    type DraftPayStub
} from './payStubMappers';

// Future mappers can be added here:
// export { employeeMappers } from './employee-mappers';
// export { agreementMappers } from './agreement-mappers';