/**
 * useTimesheetDirtyFlag Hook
 * 
 * Provides a timesheet-scoped dirty flag that checks for unsaved draft changes
 * for a specific timesheet only. This prevents false positives where the dirty flag
 * is triggered by drafts from other timesheets stored in local storage.
 * 
 * This hook is part of the fix for the issue where the Save button appears
 * enabled even when the current timesheet has no actual changes, due to
 * the global draft count including drafts from other timesheets.
 * 
 * @param timesheetId - The ID of the timesheet to check for draft changes
 * @returns boolean - true if the specific timesheet has unsaved draft changes
 */

import { useTimesheetUIStore } from '@/src/store/timesheetUIStore';

export function useTimesheetDirtyFlag(timesheetId: string): boolean {
    const isDirty = useTimesheetUIStore((state) => state.hasDraftChanges(timesheetId));
    return isDirty;
}

export default useTimesheetDirtyFlag;