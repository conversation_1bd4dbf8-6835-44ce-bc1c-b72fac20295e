import { useCallback, useState } from 'react';
import { TimesheetError, shouldRetryError, incrementRetryCount } from '@/errorHandling';
import { useTimesheetUIStore } from '../store/timesheetUIStore';

interface UseErrorRecoveryOptions {
  maxRetries?: number;
  retryDelay?: number;
  onRetrySuccess?: () => void;
  onRetryFailure?: (error: TimesheetError) => void;
}

export function useErrorRecovery(options: UseErrorRecoveryOptions = {}) {
  const {
    maxRetries = 3,
    retryDelay = 1000,
    onRetrySuccess,
    onRetryFailure
  } = options;

  const [isRetrying, setIsRetrying] = useState(false);
  const clearError = useTimesheetUIStore(state => state.clearError);

  const retryWithBackoff = useCallback(async (
    operation: () => Promise<void>,
    error: TimesheetError,
    entityId: string
  ): Promise<boolean> => {
    if (!shouldRetryError(error)) {
      return false;
    }

    setIsRetrying(true);
    // TODO: Need to pass timesheetId parameter - for now skip clearing error
    // clearError(entityId);

    try {
      // Exponential backoff delay
      const delay = retryDelay * Math.pow(2, error.enhancedError.retryCount);
      await new Promise(resolve => setTimeout(resolve, delay));

      await operation();
      
      setIsRetrying(false);
      onRetrySuccess?.();
      return true;
    } catch (retryError) {
      const incrementedError = incrementRetryCount(error);
      setIsRetrying(false);
      onRetryFailure?.(incrementedError);
      return false;
    }
  }, [retryDelay, clearError, onRetrySuccess, onRetryFailure]);

  const recoverFromError = useCallback(async (
    error: TimesheetError,
    operation: () => Promise<void>,
    entityId: string
  ): Promise<void> => {
    const success = await retryWithBackoff(operation, error, entityId);
    
    if (!success && shouldRetryError(error)) {
      // If retry failed but we can still retry, schedule another attempt
      setTimeout(() => {
        recoverFromError(incrementRetryCount(error), operation, entityId);
      }, retryDelay * 2);
    }
  }, [retryWithBackoff, retryDelay]);

  return {
    isRetrying,
    recoverFromError,
    retryWithBackoff
  };
}