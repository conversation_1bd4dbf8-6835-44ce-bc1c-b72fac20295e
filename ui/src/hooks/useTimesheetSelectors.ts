import { useMemo } from 'react';
import { SubClassification, PayStubDetail } from '@/src/types/relay-ui-extensions'; // Import migrated types
// RelayPayStub types removed - using Relay data directly
// PayStubDetail might not be directly needed if accessing through stubs
// import { PayStubDetail } from '@/src/types/timesheet'; // Adjust path if necessary

// Define cache key format explicitly for clarity
const getSubClassCacheKey = (
    agreementId: string | number | null | undefined,
    classificationId: string | number | null | undefined
): string | null => {
    if (agreementId == null || classificationId == null) {
        return null;
    }
    // Consistent key format: ensure string conversion if IDs are numbers
    return `${String(agreementId)}:${String(classificationId)}`;
};

/**
 * Hook to calculate sub-classification column visibility for a PayStub's details.
 * This combines the logic previously in calculateSubClassificationVisibilityForPayStub
 * but as a proper hook to avoid Rules of Hooks violations.
 */
export const useSubClassificationVisibility = (
    details: ReadonlyArray<PayStubDetail>,
    temporaryPayStubDetailEdits: Map<string, Record<string, any>>
): boolean => {

    const hasValidPairs = useMemo(() => {
        for (const detail of details) {
            // Skip if detail is marked for deletion in temporary edits
            const detailId = detail.id;
            if (!detailId) continue;
            
            const tempEditForDetail = temporaryPayStubDetailEdits.get(detailId);
            if (tempEditForDetail?.delete) continue; // Check for temporary delete flag
            if ('delete' in detail && detail.delete) continue; // Check for potential delete flag on original data if applicable

            const effectiveAgreementId = tempEditForDetail?.agreementId ?? detail.agreementId;
            const effectiveClassificationId = tempEditForDetail?.classificationId ?? detail.classificationId;
            
            // If we find any detail with both agreement and classification, show the column
            if (effectiveAgreementId != null && effectiveClassificationId != null) {
                return true;
            }
        }

        return false;
    }, [details, temporaryPayStubDetailEdits]);

    return hasValidPairs;
};

/**
 * Legacy function maintained for backward compatibility
 * @deprecated Use useSubClassificationVisibility hook instead
 */
export const calculateSubClassificationVisibilityForPayStub = (
    details: ReadonlyArray<PayStubDetail>,
    temporaryPayStubDetailEdits: Map<string, Record<string, any>>
): boolean => {
    console.warn('calculateSubClassificationVisibilityForPayStub is deprecated. Use useSubClassificationVisibility hook instead.');
    
    // Simple logic without hooks - just check if any details have both agreement and classification
    for (const detail of details) {
        const detailId = detail.id;
        if (!detailId) continue;
        
        const tempEditForDetail = temporaryPayStubDetailEdits.get(detailId);
        if (tempEditForDetail?.delete) continue;
        if ('delete' in detail && detail.delete) continue;

        const effectiveAgreementId = tempEditForDetail?.agreementId ?? detail.agreementId;
        const effectiveClassificationId = tempEditForDetail?.classificationId ?? detail.classificationId;
        
        if (effectiveAgreementId != null && effectiveClassificationId != null) {
            return true;
        }
    }
    
    return false;
};

// Future selectors can be added to this file
