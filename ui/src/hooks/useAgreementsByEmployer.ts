import { useMemo } from 'react';
import { useLazyLoadQuery } from 'react-relay';
import { graphql } from 'react-relay';
import type { useAgreementsByEmployerQuery } from '@/src/relay/__generated__/useAgreementsByEmployerQuery.graphql';
import type { Agreement } from '../types/relay-ui-extensions';

/**
 * Configuration for agreement query execution
 */
export interface AgreementQueryConfig {
    /** Employer GUID for filtering agreements */
    employerGuid: string | null | undefined;
    /** Whether to include inactive agreements in results */
    includeInactiveAgreements?: boolean;
    /** Fetch policy for the query */
    fetchPolicy?: 'store-or-network' | 'store-only' | 'network-only';
}

/**
 * Result from agreement query
 */
export interface AgreementQueryResult {
    /** Available agreements for the employer */
    agreements: ReadonlyArray<Agreement>;
    /** Whether the query is currently loading */
    isLoading: boolean;
    /** Whether the query encountered an error */
    hasError: boolean;
    /** Error message if any error occurred */
    errorMessage?: string;
}

/**
 * Custom hook for fetching agreements by employer GUID
 * 
 * This hook abstracts the GraphQL query for signatory agreements and provides
 * consistent error handling and data transformation across components.
 * 
 * @param config - Configuration object with employer GUID and options
 * @returns Object containing agreements, loading state, and error information
 * 
 * @example
 * ```typescript
 * const { agreements, isLoading, hasError } = useAgreementsByEmployer({
 *   employerGuid: 'employer-123',
 *   includeInactiveAgreements: false
 * });
 * ```
 */
export function useAgreementsByEmployer(config: AgreementQueryConfig): AgreementQueryResult {
    const { 
        employerGuid, 
        includeInactiveAgreements = false,
        fetchPolicy = 'store-or-network'
    } = config;

    // Note: Removed deprecated context hooks - now using Relay caching via fetchPolicy

    // GraphQL query for signatory agreements with fragment
    const signatoryAgreementsQuery = graphql`
        query useAgreementsByEmployerQuery($input: SignatoryAgreementInput!) {
            signatoryAgreements(input: $input) {
                nodes {
                    id
                    name
                }
            }
        }
    `;

    // Always use GraphQL query - Relay handles caching automatically
    // Early return for invalid employerGuid
    if (!employerGuid) {
        return useMemo(() => ({
            agreements: [],
            isLoading: false,
            hasError: false
        }), []);
    }

    try {
        const data = useLazyLoadQuery<useAgreementsByEmployerQuery>(
            signatoryAgreementsQuery,
            {
                input: {
                    employerGuid,
                    includeInactiveAgreements
                }
            },
            {
                fetchPolicy
            }
        );

        const result = useMemo(() => {
            // Transform GraphQL response to Agreement format using UI extensions
            const agreements: Agreement[] = data?.signatoryAgreements?.nodes?.map((node) => {
                // Decode base64 Relay Global ID to get "Agreement:12345" format, then extract the numeric ID
                const decodedId = atob(node.id);
                const rawId = decodedId.split(':')[1];
                return {
                    id: node.id,
                    name: node.name,
                    value: rawId,
                    text: node.name
                };
            }) || [];

            return {
                agreements,
                isLoading: false,
                hasError: false
            };
        }, [data]);

        return result;
    } catch (error) {
        // Improved error logging that handles Promise objects from Relay Suspense
        if (error && typeof error === 'object' && 'then' in error) {
            // This is a Relay Suspense promise - don't log it as an error
            // Re-throw it so Suspense can handle it properly
            throw error;
        }
        
        // Only log actual errors, not Suspense promises
        console.error('Error fetching agreements by employer:', 
            error instanceof Error ? error.message : String(error));
        
        return {
            agreements: [],
            isLoading: false,
            hasError: true,
            errorMessage: error instanceof Error ? error.message : 'Unknown error occurred'
        };
    }
}

/**
 * Hook for getting agreements with error boundary support
 * This is a wrapper that provides additional error handling for use with error boundaries
 */
export function useAgreementsByEmployerWithErrorBoundary(config: AgreementQueryConfig): AgreementQueryResult {
    try {
        return useAgreementsByEmployer(config);
    } catch (error) {
        // Re-throw the error to be caught by error boundary
        throw new Error(`Failed to fetch agreements: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}