import { graphql, useLazyLoadQuery, usePaginationFragment, useFragment } from 'react-relay';
import type {
    useEmployeeQueriesSingleEmployeeQuery,
    useEmployeeQueriesSingleEmployeeQuery$data
} from '@/lib/relay/__generated__/useEmployeeQueriesSingleEmployeeQuery.graphql';
import type {
    useEmployeeQueriesEmployeesByEmployerQuery,
    useEmployeeQueriesEmployeesByEmployerQuery$data
} from '@/lib/relay/__generated__/useEmployeeQueriesEmployeesByEmployerQuery.graphql';
import type {
    useEmployeeQueries_employee$data,
    useEmployeeQueries_employee$key
} from '@/lib/relay/__generated__/useEmployeeQueries_employee.graphql';
import type { useEmployeeQueries_employeesPagination$key } from '@/lib/relay/__generated__/useEmployeeQueries_employeesPagination.graphql';

/**
 * GraphQL fragment for employee data
 * Following RELAY_RULES.md: Fragment naming convention <FileName>_<fragmentName>
 */
export const useEmployeeQueries_employee = graphql`
    fragment useEmployeeQueries_employee on Employee {
        id
        externalEmployeeId
        firstName
        middleName
        lastName
        suffix
        dateOfHire
        dateOfTermination
        active
    }
`;

/**
 * Refetchable fragment for employee pagination
 */
export const useEmployeeQueries_employeesPagination = graphql`
    fragment useEmployeeQueries_employeesPagination on Query
    @refetchable(queryName: "useEmployeeQueriesEmployeesByEmployerPaginationQuery") {
        employeesByEmployerGuidAsync(employerGuid: $employerGuid, first: $first, after: $after)
            @connection(key: "useEmployeeQueries_employeesByEmployerGuidAsync") {
            edges {
                node {
                    ...useEmployeeQueries_employee
                }
            }
            pageInfo {
                hasNextPage
                endCursor
            }
            totalCount
        }
    }
`;

/**
 * GraphQL query for fetching a single employee
 */
export const singleEmployeeQuery = graphql`
    query useEmployeeQueriesSingleEmployeeQuery($employeeId: ID!) {
        employeeById(employeeId: $employeeId) {
            ...useEmployeeQueries_employee
        }
    }
`;

/**
 * GraphQL query for fetching employees by employer (initial load)
 */
export const employeesByEmployerQuery = graphql`
    query useEmployeeQueriesEmployeesByEmployerQuery($employerGuid: UUID!, $first: Int, $after: String) {
        ...useEmployeeQueries_employeesPagination
    }
`;

/**
 * Hook for fetching single employee data
 * Following REACT_RULES.md #2: Hooks called unconditionally at top level
 * Following RELAY_PITFALLS.md #5: No type assertions
 */
export function useEmployee(employeeId: string | null): {
    employeeById: useEmployeeQueries_employee$data | null;
} {
    // ✅ CORRECT: Call hook unconditionally (REACT_RULES.md #2)
    const queryResult = useLazyLoadQuery<useEmployeeQueriesSingleEmployeeQuery>(
        singleEmployeeQuery,
        { employeeId: employeeId || '' },
        {
            fetchPolicy: employeeId ? 'store-or-network' : 'store-only',
            UNSTABLE_renderPolicy: 'partial'
        }
    );

    // ✅ CORRECT: Call useFragment unconditionally with null handling (REACT_RULES.md #2)
    const employee = useFragment(useEmployeeQueries_employee, queryResult.employeeById);

    // ✅ CORRECT: Conditional logic after hook calls
    if (!employeeId || !queryResult.employeeById) {
        return { employeeById: null };
    }

    // NOTE: @types/react-relay@18.2.1 has type inference limitations with nullable fragment refs
    // This assertion is safe because we've verified queryResult.employeeById exists above
    // TODO: Remove when @types/react-relay with improved inference becomes available
    return { employeeById: employee as useEmployeeQueries_employee$data };
}

/**
 * Hook for fetching employees by employer with proper pagination
 * Following REACT_RULES.md #2: Hooks called unconditionally
 */
export function useEmployeesByEmployer(
    employerGuid: string | null,
    first: number = 100
): {
    data: useEmployeeQueries_employeesPagination$key | null;
    loadNext: (count: number) => void;
    hasNext: boolean;
    isLoadingNext: boolean;
    refetch: (variables: Partial<useEmployeeQueriesEmployeesByEmployerQuery$data>) => void;
} {
    // ✅ CORRECT: Call hook unconditionally (REACT_RULES.md #2)
    const queryRef = useLazyLoadQuery<useEmployeeQueriesEmployeesByEmployerQuery>(
        employeesByEmployerQuery,
        { employerGuid: employerGuid || '', first },
        {
            fetchPolicy: employerGuid ? 'store-or-network' : 'store-only',
            UNSTABLE_renderPolicy: 'partial'
        }
    );

    const paginationResult = usePaginationFragment(useEmployeeQueries_employeesPagination, queryRef);

    // ✅ CORRECT: Conditional logic after hooks
    if (!employerGuid) {
        return {
            data: null,
            loadNext: () => {},
            hasNext: false,
            isLoadingNext: false,
            refetch: () => {}
        };
    }

    return {
        data: queryRef,
        loadNext: paginationResult.loadNext,
        hasNext: paginationResult.hasNext,
        isLoadingNext: paginationResult.isLoadingNext,
        refetch: paginationResult.refetch
    };
}

/**
 * Interface for processed employee data
 * Following REACT_RULES.md #9: Clear TypeScript typing
 */
export interface ProcessedEmployeeData {
    value: string; // Relay Global ID
    text: string; // Display name
    externalEmployeeId?: string; // Raw employee ID
}

/**
 * Utility function to convert GraphQL employee data to complete Employee interface
 * This function creates a complete Employee object compatible with EmployeeSelector
 */
export function convertEmployeeDataToEmployee(
    employee: useEmployeeQueries_employee$data | null
): import('@/src/types/timesheet').Employee | null {
    if (!employee) return null;

    // Create display text with proper null handling
    const firstName = employee.firstName || '';
    const lastName = employee.lastName || '';
    const text = firstName ? `${lastName}, ${firstName}` : lastName;

    return {
        id: employee.id,
        value: employee.id, // Use Relay Global ID for consistency
        text: text.trim(),
        firstName: employee.firstName,
        lastName: employee.lastName,
        middleName: employee.middleName,
        active: employee.active,
        externalEmployeeId: employee.externalEmployeeId?.trim() || undefined
    };
}
