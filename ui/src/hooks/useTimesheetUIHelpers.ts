/**
 * Helper hooks for TimesheetUI context
 * Separated from the main context file to avoid fast-refresh warnings
 */

import { useCallback } from 'react';
import { useTimesheetUIStore } from '@/src/store/timesheetUIStore';
import type { ModifyPayStubInput } from '@/src/types/graphql-timesheet';

/**
 * usePayStubEditState - Specialized hook for PayStub UI state
 *
 * @param payStubId - The ID of the PayStub to get state for
 * @returns Object containing editing/saving/error state for the specific PayStub
 *
 * Usage:
 * ```tsx
 * const { isEditing, isSaving, error } = usePayStubEditState(payStubId);
 * ```
 */
export function usePayStubEditState(payStubId: string) {
    // TODO: This function needs timesheetId parameter to work properly
    const editingPayStubs = useTimesheetUIStore(state => state.editingPayStubs);
    const savingPayStubIds = useTimesheetUIStore(state => state.savingPayStubIds);
    const errorsByPayStubId = useTimesheetUIStore(state => state.errorsByPayStubId);
    const validationErrorsByPayStubId = useTimesheetUIStore(state => state.validationErrorsByPayStubId);
    const expandedPayStubs = useTimesheetUIStore(state => state.expandedPayStubs);

    return {
        isEditing: editingPayStubs?.has(payStubId) ?? false,
        isSaving: savingPayStubIds?.has(payStubId) ?? false,
        isExpanded: expandedPayStubs?.has(payStubId) ?? false,
        error: errorsByPayStubId?.get(payStubId),
        enhancedError: null, // enhancedErrors not available in new store
        validationErrors: validationErrorsByPayStubId?.get(payStubId) || []
    };
}

/**
 * usePayStubDraft - Specialized hook for managing PayStub draft changes
 *
 * @param payStubId - The ID of the PayStub to manage drafts for
 * @returns Object with draft state and update/clear functions
 *
 * Usage:
 * ```tsx
 * const { draft, updateDraft, clearDraft } = usePayStubDraft(payStubId);
 * updateDraft({ hours: newHours });
 * ```
 */
export function usePayStubDraft(payStubId: string) {
    // TODO: This function needs timesheetId parameter to work properly
    const getDraftForPayStub = useTimesheetUIStore(state => state.getDraftForPayStub);
    const updatePayStubDraft = useTimesheetUIStore(state => state.updatePayStubDraft);
    const clearDraftForPayStub = useTimesheetUIStore(state => state.clearDraftForPayStub);

    return {
        draft: null, // TODO: getDraftForPayStub(timesheetId, payStubId),
        updateDraft: useCallback(
            (changes: Partial<ModifyPayStubInput>) => {
                // TODO: updatePayStubDraft(timesheetId, payStubId, changes);
            },
            [updatePayStubDraft, payStubId]
        ),
        clearDraft: useCallback(() => {
            // TODO: clearDraftForPayStub(timesheetId, payStubId);
        }, [clearDraftForPayStub, payStubId])
    };
}