import { useCallback } from 'react';
import { useRelayEnvironment } from 'react-relay';
import { useTimesheetUIStore } from '@/src/store/timesheetUIStore';
import { useTimesheetSaver, TimesheetValidationError } from './useTimesheetSaver';
import type { RelayTimeSheet } from '@/src/types/timesheet-detail';
import type { ServerPayStub } from '@/src/types/timesheet';

/**
 * Shared timesheet save hook that consolidates validation and mutation logic.
 * This is the single entry point for all save operations in Phase 1.
 *
 * @returns {Object} Save handlers and state
 * @returns {Function} saveTimesheet - Main save function
 * @returns {Function} saveForLater - Save with 'Saved' status
 * @returns {Function} submitTimesheet - Save with 'Submitted' status
 * @returns {boolean} isSaving - Loading state
 * @returns {Error|null} saveError - Error state
 */
export function useSaveTimesheet() {
    const environment = useRelayEnvironment();
    const { saveTimesheet: saveMutation, isSaving, saveError } = useTimesheetSaver();
    const selectModifiablePayStubs = useTimesheetUIStore((state) => state.selectModifiablePayStubs);

    /**
     * Core save function that handles both save and submit operations
     *
     * @param timesheetData - Timesheet data from Relay
     * @param serverPayStubs - Server pay stubs data
     * @param targetStatus - 'Saved' or 'Submitted'
     * @returns Promise<boolean> - Success indicator
     */
    const saveTimesheet = useCallback(
        async (
            timesheetData: RelayTimeSheet | null,
            serverPayStubs: readonly ServerPayStub[],
            targetStatus: 'Saved' | 'Submitted'
        ): Promise<boolean> => {
            if (!timesheetData) {
                throw new Error('Timesheet data is required for save operation');
            }

            // Use the selector to merge server data + drafts
            const payStubs = selectModifiablePayStubs(timesheetData.id, serverPayStubs);

            // Call the mutation with consolidated data
            return saveMutation(timesheetData, payStubs, { targetStatus });
        },
        [saveMutation, selectModifiablePayStubs]
    );

    /**
     * Save timesheet for later (status: 'Saved')
     */
    const saveForLater = useCallback(
        async (timesheetData: RelayTimeSheet | null, serverPayStubs: readonly any[]): Promise<boolean> => {
            return saveTimesheet(timesheetData, serverPayStubs, 'Saved');
        },
        [saveTimesheet]
    );

    /**
     * Submit timesheet (status: 'Submitted')
     */
    const submitTimesheet = useCallback(
        async (timesheetData: RelayTimeSheet | null, serverPayStubs: readonly any[]): Promise<boolean> => {
            return saveTimesheet(timesheetData, serverPayStubs, 'Submitted');
        },
        [saveTimesheet]
    );

    return {
        saveTimesheet,
        saveForLater,
        submitTimesheet,
        isSaving,
        saveError
    };
}
