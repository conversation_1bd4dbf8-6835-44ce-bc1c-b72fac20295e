import { useLazyLoadQuery, graphql } from 'react-relay';
import { useTimesheetDataQuery } from '@/src/relay/__generated__/useTimesheetDataQuery.graphql'; // Corrected import name and path, removed .ts

// Define the GraphQL query inline or import it
const timesheetDetailQuery = graphql`
    query useTimesheetDataQuery($timeSheetId: ID!) {
        # Corrected query name
        timeSheetById(id: $timeSheetId) {
            # Spread the single master fragment for the page
            ...TimesheetDetail_timeSheet
            # Keep other direct fields if necessary (id is included in master fragment)
            # id
            numericId # Keep if needed directly by the hook/container
            oldId
            employerGuid
            name
            hoursWorked
            payPeriodEndDate
            status
            type
            creationDate
            modificationDate
            showBonusColumn
            showCostCenterColumn # Added missing field
            showDTHoursColumn
            showEarningsCodesColumn
            showExpensesColumn
            modifiedByUserId
            payStubs(first: 500) @connection(key: "useTimesheetData_payStubs") {
                edges {
                    node {
                        # Spread the fragment needed by PayStubTable
                        ...PayStubTable_payStub # Corrected spread name
                        # Explicitly request fields needed by mapPayStubsToModifiable
                        id
                        employeeId
                        employee {
                            id
                            firstName
                            lastName
                            active
                        }
                        name
                        totalHours
                        details {
                            id
                            payStubId
                            reportLineItemId
                            workDate
                            name
                            stHours
                            otHours
                            dtHours
                            totalHours
                            jobCode
                            agreementId
                            classificationId
                            subClassificationId
                            costCenter
                            earningsCode
                            hourlyRate
                            bonus
                            expenses
                        }
                    }
                }
            }
        }
    }
`;

// Hook to fetch timesheet data using Relay
export function useTimesheetData(timeSheetId: string) {
    // Fetch data using Relay's hook. timeSheetId is guaranteed to be a non-null string here.
    // Note: This will suspend while loading if data is not cached.
    // Let Suspense handle the loading state and ErrorBoundary handle actual errors.
    const queryData = useLazyLoadQuery<useTimesheetDataQuery>(
        timesheetDetailQuery,
        { timeSheetId: timeSheetId },
        { fetchPolicy: 'store-or-network' }
    );

    // Extract the relevant timesheet data from the query result
    const timeSheetData = queryData?.timeSheetById;

    // Return the data; loading/error are primarily handled by Suspense/ErrorBoundary upstream.
    // The original hook returned loading: false, error: null, implying successful data resolution at this point.
    return { data: timeSheetData, loading: false, error: null };
}
