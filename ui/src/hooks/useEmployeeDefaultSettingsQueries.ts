import { graphql, useLazyLoadQuery, usePaginationFragment } from 'react-relay';
// Import generated types after first relay compilation
// For single employee query:
import type {
    useEmployeeDefaultSettingsQueriesSingleEmployeeQuery,
    useEmployeeDefaultSettingsQueriesSingleEmployeeQuery$data
} from '@/src/relay/__generated__/useEmployeeDefaultSettingsQueriesSingleEmployeeQuery.graphql';
// For employer list entry point query:
import type {
    useEmployeeDefaultSettingsQueriesByEmployerListEntryPointQuery,
    useEmployeeDefaultSettingsQueriesByEmployerListEntryPointQuery$data
} from '@/src/relay/__generated__/useEmployeeDefaultSettingsQueriesByEmployerListEntryPointQuery.graphql';
// For the refetchable fragment (key is used):
import type { useEmployeeDefaultSettingsQueries_EmployerList_RefetchableFragment$key } from '@/src/relay/__generated__/useEmployeeDefaultSettingsQueries_EmployerList_RefetchableFragment.graphql';
// For pagination fragment and query:
import type {
    EmployeeDefaultSettingsByEmployerListPaginationQuery,
    EmployeeDefaultSettingsByEmployerListPaginationQuery$variables
} from '@/src/relay/__generated__/EmployeeDefaultSettingsByEmployerListPaginationQuery.graphql';
import type {
    useEmployeeDefaultSettingsQueries_EmployeeDefaultsNode$data,
    useEmployeeDefaultSettingsQueries_EmployeeDefaultsNode$key
} from '@/src/relay/__generated__/useEmployeeDefaultSettingsQueries_EmployeeDefaultsNode.graphql';

/**
 * GraphQL fragment for employee default settings node
 */
const employeeDefaultSettingsNodeFragment = graphql`
    fragment useEmployeeDefaultSettingsQueries_EmployeeDefaultsNode on EmployeeDefaultSettings {
        employeeId
        defaultAgreementId
        defaultClassificationId
        defaultHourlyRate
    }
`;

/**
 * GraphQL query for fetching a single employee's default settings
 */
export const singleEmployeeDefaultSettingsQuery = graphql`
    query useEmployeeDefaultSettingsQueriesSingleEmployeeQuery($employeeId: ID!) {
        employeeDefaultSettings(employeeId: $employeeId) {
            employeeId
            defaultAgreementId
            defaultClassificationId
            defaultHourlyRate
        }
    }
`;

/**
 * GraphQL query for fetching employee default settings by employer ID (entry point).
 * This query loads the refetchable fragment.
 */
const employeeDefaultsByEmployerEntryPointQuery = graphql`
    query useEmployeeDefaultSettingsQueriesByEmployerListEntryPointQuery($employerId: ID!, $first: Int = 10, $after: String) {
        # Spread the refetchable fragment here
        ...useEmployeeDefaultSettingsQueries_EmployerList_RefetchableFragment
            @arguments(employerId: $employerId, first: $first, after: $after)
    }
`;

/**
 * Refetchable Fragment for paginating employee default settings by employer ID.
 * Assumes the root Query type is named 'Query'. If it's different (e.g., 'RootQuery'),
 * 'on Query' should be changed to 'on YourRootQueryTypeName'.
 */
const employerListRefetchableFragment = graphql`
    fragment useEmployeeDefaultSettingsQueries_EmployerList_RefetchableFragment on Query
    @argumentDefinitions(employerId: { type: "ID!" }, first: { type: "Int", defaultValue: 10 }, after: { type: "String" })
    @refetchable(queryName: "EmployeeDefaultSettingsByEmployerListPaginationQuery") {
        employeeDefaultSettingsByEmployerId(employerId: $employerId, first: $first, after: $after)
            @connection(key: "UseEmployeeDefaultSettingsQueries_employeeDefaultSettingsByEmployerId") {
            edges {
                node {
                    # Explicitly select fields needed by the useEmployerEmployeeDefaultsPagination hook
                    employeeId
                    defaultAgreementId
                    defaultClassificationId
                    defaultHourlyRate
                    # Keep the spread for other potential consumers of this specific node fragment
                    ...useEmployeeDefaultSettingsQueries_EmployeeDefaultsNode
                }
            }
            pageInfo {
                hasNextPage
                endCursor
            }
            totalCount
        }
    }
`;

/**
 * Interface for employee default settings data
 */
export interface EmployeeDefaultSettingsData {
    employeeId?: string | null;
    defaultAgreementId?: number | null;
    defaultClassificationId?: number | null;
    defaultHourlyRate?: string | null; // GraphQL Decimal is often string
}

/**
 * Interface for the result of useEmployerEmployeeDefaultsList
 */
export interface EmployerEmployeeDefaultsListResult {
    // Direct access to the employee default settings data
    nodes: ReadonlyArray<EmployeeDefaultSettingsData | null> | null | undefined;
    // Pagination information
    hasNextPage: boolean | null | undefined;
    endCursor: string | null | undefined;
    totalCount: number | null | undefined;
}

/**
 * Interface for the result of the initial list load.
 * This provides the fragment reference for the pagination hook.
 */
export interface EmployerEmployeeDefaultsListEntryPointResult {
    // Fragment reference to be passed to useEmployerEmployeeDefaultsPagination
    // This key will be for useEmployeeDefaultSettingsQueries_EmployerList_RefetchableFragment
    listRef: useEmployeeDefaultSettingsQueries_EmployerList_RefetchableFragment$key | null | undefined;
}

/**
 * Hook for fetching a single employee's default settings
 * @param employeeId - The ID of the employee (must be a valid ID string)
 * @returns Object containing the employee default settings data
 */
export function useEmployeeDefaultSettings(employeeId: string): {
    data: EmployeeDefaultSettingsData | null | undefined;
} {
    const queryData = useLazyLoadQuery<useEmployeeDefaultSettingsQueriesSingleEmployeeQuery>(
        singleEmployeeDefaultSettingsQuery,
        { employeeId },
        { fetchPolicy: 'store-or-network' }
    );

    if (!queryData?.employeeDefaultSettings) {
        return { data: null };
    }

    const settings = queryData.employeeDefaultSettings;

    return {
        data: settings
            ? {
                  employeeId: settings.employeeId,
                  defaultAgreementId: settings.defaultAgreementId,
                  defaultClassificationId: settings.defaultClassificationId,
                  defaultHourlyRate: settings.defaultHourlyRate
              }
            : null
    };
}

/**
 * Hook for the initial load of employee default settings by employer ID.
 * This hook fetches the first page and provides a fragment reference for pagination.
 * @param employerId - The ID of the employer (must be a valid ID string)
 * @param first - Number of items to fetch for the initial load (default: 10)
 * @returns Object containing the fragment reference for pagination.
 */
export function useEmployerEmployeeDefaultsList(employerId: string, first: number = 10): EmployerEmployeeDefaultsListEntryPointResult {
    const queryData = useLazyLoadQuery<useEmployeeDefaultSettingsQueriesByEmployerListEntryPointQuery>(
        employeeDefaultsByEmployerEntryPointQuery,
        { employerId, first },
        { fetchPolicy: 'store-or-network' }
    );

    if (!queryData) {
        return { listRef: null };
    }

    return {
        listRef: queryData
    };
}

/**
 * Interface for the result of the pagination hook
 */
export interface EmployerEmployeeDefaultsPaginationResult {
    nodes: ReadonlyArray<EmployeeDefaultSettingsData | null>;
    hasNext: boolean;
    loadNext: (count: number) => void;
    isLoadingNext: boolean;
    refetchConnection: (variables: Partial<EmployeeDefaultSettingsByEmployerListPaginationQuery$variables>) => void;
    totalCount?: number | null;
}

/**
 * Hook for paginating through employee default settings by employer ID.
 * @param fragmentRef - The fragment reference obtained from useEmployerEmployeeDefaultsList.
 * @returns Pagination controls and the list of nodes.
 */
export function useEmployerEmployeeDefaultsPagination(
    // The fragmentRef is now for useEmployeeDefaultSettingsQueries_EmployerList_RefetchableFragment
    fragmentRef: useEmployeeDefaultSettingsQueries_EmployerList_RefetchableFragment$key | null | undefined
): EmployerEmployeeDefaultsPaginationResult {
    // usePaginationFragment should be called unconditionally.
    // It will handle a null fragmentRef gracefully (typically by not fetching data or returning an empty state).
    const { data, loadNext, hasNext, isLoadingNext, refetch } = usePaginationFragment<
        EmployeeDefaultSettingsByEmployerListPaginationQuery,
        useEmployeeDefaultSettingsQueries_EmployerList_RefetchableFragment$key
    >(employerListRefetchableFragment, fragmentRef);

    if (!fragmentRef || !data?.employeeDefaultSettingsByEmployerId?.edges) {
        return {
            nodes: [],
            hasNext: false,
            loadNext: (_count: number) => {},
            isLoadingNext: false,
            refetchConnection: (_variables: Partial<EmployeeDefaultSettingsByEmployerListPaginationQuery$variables>) => {},
            totalCount: 0
        };
    }

    const connectionData = data.employeeDefaultSettingsByEmployerId;
    const settingsNodes =
        connectionData.edges
            ?.map((edge) => {
                if (!edge?.node) return null;
                // With the modified fragment, edge.node should now directly have the required fields typed.
                // No cast should be necessary.
                // const nodeData = edge.node; // Or just use edge.node directly

                return {
                    employeeId: edge.node.employeeId, // Directly access from edge.node
                    defaultAgreementId: edge.node.defaultAgreementId, // Directly access from edge.node
                    defaultClassificationId: edge.node.defaultClassificationId, // Directly access from edge.node
                    defaultHourlyRate: edge.node.defaultHourlyRate // Directly access from edge.node
                };
            })
            .filter(Boolean) || []; // Added filter(Boolean) to remove nulls if any edge.node was null

    return {
        nodes: settingsNodes,
        hasNext,
        loadNext,
        isLoadingNext,
        refetchConnection: refetch,
        totalCount: connectionData.totalCount
    };
}
