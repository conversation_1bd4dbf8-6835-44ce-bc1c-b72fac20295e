/**
 * Tests for useTimesheetDirtyFlag hook
 * 
 * This test verifies that the hook correctly scopes dirty flag checks
 * to specific timesheets, preventing false positives from other timesheets.
 */

import { renderHook, act } from '@testing-library/react';
import { useTimesheetDirtyFlag } from '../useTimesheetDirtyFlag';
import { useTimesheetUIStore } from '@/src/store/timesheetUIStore';

// Mock the store
jest.mock('@/src/store/timesheetUIStore');

describe('useTimesheetDirtyFlag', () => {
    const mockHasDraftChanges = jest.fn();
    const mockUseTimesheetUIStore = useTimesheetUIStore as jest.MockedFunction<typeof useTimesheetUIStore>;

    beforeEach(() => {
        jest.clearAllMocks();
        mockUseTimesheetUIStore.mockImplementation((selector) => {
            if (typeof selector === 'function') {
                return selector({
                    hasDraftChanges: mockHasDraftChanges,
                } as any);
            }
            return undefined;
        });
    });

    it('should return false when timesheet has no draft changes', () => {
        mockHasDraftChanges.mockReturnValue(false);

        const { result } = renderHook(() => useTimesheetDirtyFlag('timesheet-123'));

        expect(result.current).toBe(false);
        expect(mockHasDraftChanges).toHaveBeenCalledWith('timesheet-123');
    });

    it('should return true when timesheet has draft changes', () => {
        mockHasDraftChanges.mockReturnValue(true);

        const { result } = renderHook(() => useTimesheetDirtyFlag('timesheet-123'));

        expect(result.current).toBe(true);
        expect(mockHasDraftChanges).toHaveBeenCalledWith('timesheet-123');
    });

    it('should react to changes in draft state', () => {
        mockHasDraftChanges.mockReturnValue(false);

        const { result, rerender } = renderHook(() => useTimesheetDirtyFlag('timesheet-123'));

        expect(result.current).toBe(false);

        // Simulate draft changes being added
        mockHasDraftChanges.mockReturnValue(true);
        rerender();

        expect(result.current).toBe(true);
    });

    it('should be scoped to specific timesheet ID', () => {
        mockHasDraftChanges.mockImplementation((timesheetId) => {
            return timesheetId === 'timesheet-with-drafts';
        });

        const { result: result1 } = renderHook(() => useTimesheetDirtyFlag('timesheet-with-drafts'));
        const { result: result2 } = renderHook(() => useTimesheetDirtyFlag('timesheet-without-drafts'));

        expect(result1.current).toBe(true);
        expect(result2.current).toBe(false);
        expect(mockHasDraftChanges).toHaveBeenCalledWith('timesheet-with-drafts');
        expect(mockHasDraftChanges).toHaveBeenCalledWith('timesheet-without-drafts');
    });

    it('should handle empty string timesheet ID', () => {
        mockHasDraftChanges.mockReturnValue(false);

        const { result } = renderHook(() => useTimesheetDirtyFlag(''));

        expect(result.current).toBe(false);
        expect(mockHasDraftChanges).toHaveBeenCalledWith('');
    });
});