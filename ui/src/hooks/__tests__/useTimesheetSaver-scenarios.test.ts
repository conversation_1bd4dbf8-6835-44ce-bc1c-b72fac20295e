/**
 * PayStub Filtering Scenarios Test Suite
 * 
 * Comprehensive tests for header-only PayStub workflow validation
 * and all critical user scenarios identified by Agent 4
 */

import { renderHook } from '@testing-library/react';
import { useTimesheetSaver } from '../useTimesheetSaver';
import { validatePayStubs, isCompletelyEmptyPayStubPayload } from '../../utils/payStubValidationUtils';
import type { PayStubUI, ModifiablePayStub } from '../../types/timesheet-detail';
import type { PayStubPayload } from '../../utils/payStubValidationUtils';

// Mock dependencies
jest.mock('react-relay', () => ({
  useMutation: () => [jest.fn(), false],
  useRelayEnvironment: () => ({}),
}));

jest.mock('../../store/timesheetUIStore', () => ({
  useTimesheetUIStore: () => ({
    setValidationErrors: jest.fn(),
    clearValidationErrors: jest.fn(),
    getDraftForPayStub: jest.fn(() => null),
    markedForDeletion: new Set(),
    clearAllMarkForDeletion: jest.fn(),
    clearAllDrafts: jest.fn(),
    getDetailDraft: jest.fn(() => null),
  }),
}));

jest.mock('../../store/rosterFilterStore', () => ({
  useTimesheetRosterFilterStore: () => ({
    activeFilters: null,
    activeSortOrder: null,
  }),
}));

// Test data generators with proper Relay fragment types
const createHeaderOnlyPayStub = (employeeId: string, stHours: number = 8, otHours: number = 0, dtHours: number = 0): ModifiablePayStub => ({
  id: `paystub-${employeeId}`,
  employeeId,
  name: `Employee ${employeeId}`,
  stHours,
  otHours,
  dtHours,
  bonus: null,
  expenses: null,
  details: [], // Empty details array for header-only
  delete: false,
  // Required Relay fields for PayStubUI
  employee: {
    id: employeeId,
    name: `Employee ${employeeId}`,
    ' $fragmentType': 'PayStubTable_payStub' as const,
  } as any,
  totalHours: stHours + otHours + dtHours,
  ' $fragmentSpreads': {} as any,
  ' $fragmentType': 'PayStubTable_payStub' as const,
});

const createDetailBasedPayStub = (employeeId: string): ModifiablePayStub => ({
  id: `paystub-${employeeId}`,
  employeeId,
  name: `Employee ${employeeId}`,
  stHours: null, // No header-level data
  otHours: null,
  dtHours: null,
  bonus: null,
  expenses: null,
  details: [
    {
      id: `detail-${employeeId}-1`,
      payStubId: `paystub-${employeeId}`,
      name: `Detail ${employeeId}-1`,
      workDate: '2024-01-01',
      stHours: 8,
      otHours: 0,
      dtHours: 0,
      jobCode: 'TEST',
      earningsCode: null,
      agreementId: 1,
      classificationId: 1,
      subClassificationId: null,
      costCenter: null,
      hourlyRate: 25.00,
      bonus: null,
      expenses: null,
      reportLineItemId: null,
      employeeId,
      ' $fragmentType': 'TimeSheetDetailRow_payStubDetail' as const,
    }
  ],
  delete: false,
  // Required Relay fields for PayStubUI
  employee: {
    id: employeeId,
    name: `Employee ${employeeId}`,
    ' $fragmentType': 'PayStubTable_payStub' as const,
  } as any,
  totalHours: 8,
  ' $fragmentSpreads': {} as any,
  ' $fragmentType': 'PayStubTable_payStub' as const,
});

const createEmptyPayStub = (employeeId: string): ModifiablePayStub => ({
  id: `paystub-${employeeId}`,
  employeeId,
  name: `Employee ${employeeId}`,
  stHours: null,
  otHours: null,
  dtHours: null,
  bonus: null,
  expenses: null,
  details: [],
  delete: false,
  // Required Relay fields for PayStubUI
  employee: {
    id: employeeId,
    name: `Employee ${employeeId}`,
    ' $fragmentType': 'PayStubTable_payStub' as const,
  } as any,
  totalHours: null,
  ' $fragmentSpreads': {} as any,
  ' $fragmentType': 'PayStubTable_payStub' as const,
});

describe('PayStub Filtering Scenarios - Production Readiness Validation', () => {
  describe('Scenario A: Header-Only PayStub Workflow', () => {
    it('should validate header-only PayStubs with meaningful data', () => {
      const headerOnlyPayStub = createHeaderOnlyPayStub('RW1wbG95ZWU6MQ==', 8, 2, 1); // ST=8, OT=2, DT=1
      
      const validationErrors = validatePayStubs([headerOnlyPayStub]);
      
      expect(validationErrors).toHaveLength(0);
    });

    it('should accept header-only PayStub with just standard hours', () => {
      const headerOnlyPayStub = createHeaderOnlyPayStub('RW1wbG95ZWU6MQ==', 8, 0, 0);
      
      const validationErrors = validatePayStubs([headerOnlyPayStub]);
      
      expect(validationErrors).toHaveLength(0);
    });

    it('should accept header-only PayStub with overtime hours', () => {
      const headerOnlyPayStub = createHeaderOnlyPayStub('RW1wbG95ZWU6MQ==', 0, 4, 0);
      
      const validationErrors = validatePayStubs([headerOnlyPayStub]);
      
      expect(validationErrors).toHaveLength(0);
    });

    it('should convert header-only PayStub to proper mutation payload', () => {
      const headerOnlyPayStub = createHeaderOnlyPayStub('RW1wbG95ZWU6MQ==', 8, 2, 0);
      
      // Simulate the payload conversion logic from useTimesheetSaver
      const payload: PayStubPayload = {
        id: headerOnlyPayStub.id,
        employeeId: `Employee:${headerOnlyPayStub.employeeId}`, // Global ID format
        name: headerOnlyPayStub.name,
        stHours: headerOnlyPayStub.stHours,
        otHours: headerOnlyPayStub.otHours,
        dtHours: headerOnlyPayStub.dtHours,
        bonus: headerOnlyPayStub.bonus,
        expenses: headerOnlyPayStub.expenses,
        details: [], // Empty for header-only
        delete: false,
      };
      
      expect(payload.stHours).toBe(8);
      expect(payload.otHours).toBe(2);
      expect(payload.dtHours).toBe(0);
      expect(payload.details).toHaveLength(0);
      expect(isCompletelyEmptyPayStubPayload(payload)).toBe(false);
    });
  });

  describe('Scenario B: Detail-Based PayStub Workflow (Existing)', () => {
    it('should validate traditional detail-based PayStubs', () => {
      const detailBasedPayStub = createDetailBasedPayStub('RW1wbG95ZWU6MQ==');
      
      const validationErrors = validatePayStubs([detailBasedPayStub]);
      
      expect(validationErrors).toHaveLength(0);
    });

    it('should preserve detail-based workflow unchanged', () => {
      const detailBasedPayStub = createDetailBasedPayStub('RW1wbG95ZWU6MQ==');
      
      // Header should be null/empty
      expect(detailBasedPayStub.stHours).toBeNull();
      expect(detailBasedPayStub.otHours).toBeNull();
      expect(detailBasedPayStub.dtHours).toBeNull();
      
      // Details should contain the data
      expect(detailBasedPayStub.details).toHaveLength(1);
      expect(detailBasedPayStub.details[0].stHours).toBe(8);
    });
  });

  describe('Scenario C: Mixed Workflow', () => {
    it('should handle mixed PayStubs (some header-only, some detail-based)', () => {
      const headerOnlyPayStub = createHeaderOnlyPayStub('RW1wbG95ZWU6MQ==', 8, 0, 0);
      const detailBasedPayStub = createDetailBasedPayStub('RW1wbG95ZWU6Mg==');
      
      const validationErrors = validatePayStubs([headerOnlyPayStub, detailBasedPayStub]);
      
      expect(validationErrors).toHaveLength(0);
    });

    it('should correctly identify different PayStub types in mixed scenario', () => {
      const headerOnlyPayStub = createHeaderOnlyPayStub('RW1wbG95ZWU6MQ==', 8, 2, 0);
      const detailBasedPayStub = createDetailBasedPayStub('RW1wbG95ZWU6Mg==');
      
      // Header-only: has header data, no details
      expect(headerOnlyPayStub.stHours).toBe(8);
      expect(headerOnlyPayStub.details).toHaveLength(0);
      
      // Detail-based: no header data, has details
      expect(detailBasedPayStub.stHours).toBeNull();
      expect(detailBasedPayStub.details).toHaveLength(1);
    });
  });

  describe('Scenario D: Empty PayStub Validation', () => {
    it('should detect completely empty PayStubs', () => {
      const emptyPayStub = createEmptyPayStub('RW1wbG95ZWU6MQ==');
      
      const validationErrors = validatePayStubs([emptyPayStub]);
      
      expect(validationErrors).toHaveLength(1);
      expect(validationErrors[0].message).toContain('paystub must have');
    });

    it('should identify empty PayStub payloads correctly', () => {
      const emptyPayload: PayStubPayload = {
        id: 'empty-paystub',
        employeeId: 'Employee:1',
        name: 'Employee 1',
        stHours: null,
        otHours: null,
        dtHours: null,
        bonus: null,
        expenses: null,
        details: [],
        delete: false,
      };
      
      expect(isCompletelyEmptyPayStubPayload(emptyPayload)).toBe(true);
    });

    it('should provide helpful error messages for empty PayStubs', () => {
      const emptyPayStub = createEmptyPayStub('RW1wbG95ZWU6MQ==');
      
      const validationErrors = validatePayStubs([emptyPayStub]);
      
      expect(validationErrors[0].message).toContain('Employee');
      expect(validationErrors[0].message).toContain('paystub must have');
      expect(validationErrors[0].message).toContain('hours');
    });
  });

  describe('Security and Data Integrity', () => {
    it('should handle malicious input safely', () => {
      const maliciousPayStub: PayStubUI = {
        id: '<script>alert("xss")</script>',
        employeeId: 'RW1wbG95ZWU6MQ==',
        name: '<img src=x onerror=alert("xss")>',
        stHours: 8,
        otHours: 0,
        dtHours: 0,
        bonus: null,
        expenses: null,
        details: [],
        delete: false,
        // Required Relay fields
        employee: {
          id: 'RW1wbG95ZWU6MQ==',
          name: '<img src=x onerror=alert("xss")>',
          ' $fragmentType': 'PayStubTable_payStub' as const,
        } as any,
        totalHours: 8,
        ' $fragmentSpreads': {} as any,
        ' $fragmentType': 'PayStubTable_payStub' as const,
      };
      
      // Should not throw errors and should sanitize the input
      expect(() => validatePayStubs([maliciousPayStub])).not.toThrow();
    });

    it('should validate numeric ranges for header-level hours', () => {
      const invalidPayStub = createHeaderOnlyPayStub('RW1wbG95ZWU6MQ==', -5, 25, 15); // Negative hours and excessive hours
      
      // While our validation might accept these (business rules), the mutation should handle validation
      expect(invalidPayStub.stHours).toBe(-5); // Will be caught by business validation
      expect(invalidPayStub.otHours).toBe(25); // Excessive but will be validated
    });
  });

  describe('Performance and Memory', () => {
    it('should handle large numbers of PayStubs efficiently', () => {
      const payStubs: PayStubUI[] = [];
      
      // Create 100 header-only PayStubs
      for (let i = 1; i <= 100; i++) {
        payStubs.push(createHeaderOnlyPayStub(`RW1wbG95ZWU6${i}`, 8, 0, 0));
      }
      
      const startTime = performance.now();
      const validationErrors = validatePayStubs(payStubs);
      const endTime = performance.now();
      
      expect(validationErrors).toHaveLength(0);
      expect(endTime - startTime).toBeLessThan(100); // Should complete within 100ms
    });
  });

  describe('Edge Cases and Boundary Conditions', () => {
    it('should handle PayStub with zero hours correctly', () => {
      const zeroHoursPayStub = createHeaderOnlyPayStub('RW1wbG95ZWU6MQ==', 0, 0, 0);
      
      const validationErrors = validatePayStubs([zeroHoursPayStub]);
      
      expect(validationErrors).toHaveLength(1); // Should be considered empty
    });

    it('should handle PayStub with minimal meaningful data', () => {
      const minimalPayStub = createHeaderOnlyPayStub('RW1wbG95ZWU6MQ==', 0.01, 0, 0); // Minimal ST hours
      
      const validationErrors = validatePayStubs([minimalPayStub]);
      
      expect(validationErrors).toHaveLength(0);
    });

    it('should handle very large hour values', () => {
      const largeHoursPayStub = createHeaderOnlyPayStub('RW1wbG95ZWU6MQ==', 999.99, 999.99, 999.99);
      
      const validationErrors = validatePayStubs([largeHoursPayStub]);
      
      expect(validationErrors).toHaveLength(0); // Validation should pass, business rules may catch
    });
  });

  describe('Backwards Compatibility', () => {
    it('should maintain compatibility with existing detail-based systems', () => {
      const traditionalPayStub = createDetailBasedPayStub('RW1wbG95ZWU6MQ==');
      
      // Should work exactly as before
      expect(traditionalPayStub.stHours).toBeNull();
      expect(traditionalPayStub.details).toHaveLength(1);
      expect(traditionalPayStub.details[0].stHours).toBe(8);
      
      const validationErrors = validatePayStubs([traditionalPayStub]);
      expect(validationErrors).toHaveLength(0);
    });

    it('should not break existing mutation payloads', () => {
      const detailBasedPayStub = createDetailBasedPayStub('RW1wbG95ZWU6MQ==');
      
      const payload: PayStubPayload = {
        id: detailBasedPayStub.id,
        employeeId: `Employee:${detailBasedPayStub.employeeId}`,
        name: detailBasedPayStub.name,
        stHours: detailBasedPayStub.stHours, // null
        otHours: detailBasedPayStub.otHours, // null
        dtHours: detailBasedPayStub.dtHours, // null
        bonus: detailBasedPayStub.bonus,
        expenses: detailBasedPayStub.expenses,
        details: detailBasedPayStub.details.map(detail => ({
          id: detail.id,
          stHours: detail.stHours,
          otHours: detail.otHours,
          dtHours: detail.dtHours,
          jobCode: detail.jobCode,
          earningsCode: detail.earningsCode,
          agreementId: detail.agreementId,
          classificationId: detail.classificationId,
          subClassificationId: detail.subClassificationId,
          costCenter: detail.costCenter,
          hourlyRate: detail.hourlyRate,
          bonus: detail.bonus,
          expenses: detail.expenses,
        })),
        delete: false,
      };
      
      // Header fields should be null for detail-based
      expect(payload.stHours).toBeNull();
      expect(payload.otHours).toBeNull();
      expect(payload.dtHours).toBeNull();
      
      // Details should contain the data
      expect(payload.details).toHaveLength(1);
      expect(payload.details?.[0]?.stHours).toBe(8);
    });
  });
});