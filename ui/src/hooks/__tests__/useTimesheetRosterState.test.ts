/**
 * Comprehensive tests for useTimesheetRosterState hook
 *
 * These tests verify the unified state management functionality, including:
 * - State initialization and transitions
 * - Action handling and predictable state updates
 * - External store synchronization
 * - Performance optimizations
 * - Complex state scenarios
 */

import { renderHook, act } from '@testing-library/react';
import { useTimesheetRosterState } from '../useTimesheetRosterState';
import { TimesheetsRosterViews } from '@/src/constants/views';
import type { ColumnType } from '@/src/types/rosters';
import type { TimeSheetFilterInput, TimeSheetSortInput } from '@/src/relay/__generated__/TimesheetRosterQuery.graphql';

// Mock data for testing
const mockInitialFilter = {
    employerGuid: 'test-employer-guid',
    where: { and: [] },
    order: [],
    customViewsName: 'test-views'
};

const mockInitialColumns: ColumnType[] = [
    {
        key: 'column1',
        label: 'Column 1',
        columnLabel: 'Column 1',
        show: true,
        canHide: true,
        position: 1,
        metaData: { children: 'Column 1' }
    },
    {
        key: 'column2',
        label: 'Column 2',
        columnLabel: 'Column 2',
        show: false,
        canHide: true,
        position: 2,
        metaData: { children: 'Column 2' }
    }
];

const mockExternalStore = {
    currentActiveFilters: null,
    currentActiveSortOrder: null,
    setActiveRosterFilters: jest.fn(),
    setActiveRosterSortOrder: jest.fn()
};

describe('useTimesheetRosterState', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('State Initialization', () => {
        it('should initialize with correct default state', () => {
            const { result } = renderHook(() =>
                useTimesheetRosterState(mockInitialFilter, mockInitialColumns, 'test-employer', mockExternalStore)
            );

            expect(result.current.state).toEqual(
                expect.objectContaining({
                    filter: expect.objectContaining({
                        employerGuid: 'test-employer',
                        where: { and: [] }
                    }),
                    sortOrder: [],
                    columnData: mockInitialColumns,
                    selectedCustomView: null,
                    selectedSystemView: TimesheetsRosterViews.TIMESHEET_ROSTER,
                    defaultViewId: TimesheetsRosterViews.TIMESHEET_ROSTER,
                    pendingCustomViewSelectId: null,
                    externalFilters: null,
                    externalSortOrder: null,
                    isApplyingView: false,
                    lastActionType: 'INITIALIZE'
                })
            );
        });

        it('should properly initialize computed values', () => {
            const { result } = renderHook(() =>
                useTimesheetRosterState(mockInitialFilter, mockInitialColumns, 'test-employer', mockExternalStore)
            );

            expect(result.current.computedValues.isSavedViewDisabled).toBe(true);
            expect(result.current.computedValues.queryVariables).toEqual(
                expect.objectContaining({
                    employerGuid: 'test-employer',
                    order: []
                })
            );
        });
    });

    describe('Filter Management', () => {
        it('should update filter correctly', () => {
            const { result } = renderHook(() =>
                useTimesheetRosterState(mockInitialFilter, mockInitialColumns, 'test-employer', mockExternalStore)
            );

            const newFilter: TimeSheetFilterInput = {
                and: [{ payPeriodEndDate: { eq: new Date('2024-01-01') } }]
            };

            act(() => {
                result.current.actions.updateFilter(newFilter);
            });

            expect(result.current.state.filter.where).toEqual(newFilter);
            expect(result.current.state.lastActionType).toBe('SYNC_EXTERNAL_FILTERS');
        });

        it('should handle empty filter correctly', () => {
            const { result } = renderHook(() =>
                useTimesheetRosterState(mockInitialFilter, mockInitialColumns, 'test-employer', mockExternalStore)
            );

            const emptyFilter: TimeSheetFilterInput = { and: [] };

            act(() => {
                result.current.actions.updateFilter(emptyFilter);
            });

            expect(result.current.state.filter.where).toEqual({ and: [] });
            expect(result.current.state.externalFilters).toBeNull();
        });

        it('should not update state if filter is identical', () => {
            const { result } = renderHook(() =>
                useTimesheetRosterState(mockInitialFilter, mockInitialColumns, 'test-employer', mockExternalStore)
            );

            const initialState = result.current.state;

            act(() => {
                result.current.actions.updateFilter({ and: [] });
            });

            expect(result.current.state).toBe(initialState);
        });
    });

    describe('Sort Order Management', () => {
        it('should update sort order correctly', () => {
            const { result } = renderHook(() =>
                useTimesheetRosterState(mockInitialFilter, mockInitialColumns, 'test-employer', mockExternalStore)
            );

            const newSortOrder: TimeSheetSortInput[] = [{ payPeriodEndDate: 'DESC' }];

            act(() => {
                result.current.actions.updateSortOrder(newSortOrder);
            });

            expect(result.current.state.sortOrder).toEqual(newSortOrder);
            expect(result.current.state.externalSortOrder).toBeNull(); // Hook now returns null for external sort order
            expect(result.current.state.lastActionType).toBe('SYNC_EXTERNAL_SORT_ORDER');
        });

        it('should not update state if sort order is identical', () => {
            const { result } = renderHook(() =>
                useTimesheetRosterState(mockInitialFilter, mockInitialColumns, 'test-employer', mockExternalStore)
            );

            const initialState = result.current.state;

            act(() => {
                result.current.actions.updateSortOrder([]);
            });

            expect(result.current.state).toBe(initialState);
        });
    });

    describe('Column Management', () => {
        it('should update column data correctly', () => {
            const { result } = renderHook(() =>
                useTimesheetRosterState(mockInitialFilter, mockInitialColumns, 'test-employer', mockExternalStore)
            );

            const newColumns = [
                ...mockInitialColumns,
                {
                    key: 'column3',
                    label: 'Column 3',
                    columnLabel: 'Column 3',
                    show: true,
                    canHide: false,
                    position: 3,
                    metaData: { children: 'Column 3' }
                }
            ];

            act(() => {
                result.current.actions.updateColumnData(newColumns);
            });

            expect(result.current.state.columnData).toEqual(newColumns);
            expect(result.current.state.lastActionType).toBe('UPDATE_COLUMN_DATA');
        });

        it('should toggle column visibility correctly', () => {
            const { result } = renderHook(() =>
                useTimesheetRosterState(mockInitialFilter, mockInitialColumns, 'test-employer', mockExternalStore)
            );

            act(() => {
                result.current.actions.toggleColumnVisibility(['Column 1', 'Column 2']);
            });

            const updatedColumns = result.current.state.columnData;
            expect(updatedColumns[0].show).toBe(true); // Column 1 should be visible
            expect(updatedColumns[1].show).toBe(true); // Column 2 should be visible
        });

        it('should not update column visibility if no changes needed', () => {
            const { result } = renderHook(() =>
                useTimesheetRosterState(mockInitialFilter, mockInitialColumns, 'test-employer', mockExternalStore)
            );

            const initialState = result.current.state;

            act(() => {
                result.current.actions.toggleColumnVisibility(['Column 1']); // Already matches current state
            });

            expect(result.current.state).toBe(initialState);
        });
    });

    describe('View Management', () => {
        it('should set selected custom view correctly', () => {
            const { result } = renderHook(() =>
                useTimesheetRosterState(mockInitialFilter, mockInitialColumns, 'test-employer', mockExternalStore)
            );

            const customView = {
                id: 'custom-view-1',
                name: 'My Custom View',
                filter: { where: { and: [] } },
                sortOrder: [],
                columns: mockInitialColumns
            };

            act(() => {
                result.current.actions.setSelectedCustomView(customView);
            });

            expect(result.current.state.selectedCustomView).toEqual(customView);
            expect(result.current.state.lastActionType).toBe('SET_SELECTED_CUSTOM_VIEW');
        });

        it('should set selected system view correctly', () => {
            const { result } = renderHook(() =>
                useTimesheetRosterState(mockInitialFilter, mockInitialColumns, 'test-employer', mockExternalStore)
            );

            act(() => {
                result.current.actions.setSelectedSystemView(TimesheetsRosterViews.TIMESHEET_ROSTER);
            });

            expect(result.current.state.selectedSystemView).toBe(TimesheetsRosterViews.TIMESHEET_ROSTER);
        });

        it('should apply view correctly', () => {
            const { result } = renderHook(() =>
                useTimesheetRosterState(mockInitialFilter, mockInitialColumns, 'test-employer', mockExternalStore)
            );

            const viewToApply = {
                type: TimesheetsRosterViews.TIMESHEET_ROSTER,
                filter: { where: { and: [{ payPeriodEndDate: { eq: new Date('2024-01-01') } }] } },
                sortOrder: [{ payPeriodEndDate: 'DESC' }],
                columns: mockInitialColumns
            };

            const freshColumns = [...mockInitialColumns];

            act(() => {
                result.current.actions.applyView(viewToApply, freshColumns);
            });

            expect(result.current.state.isApplyingView).toBe(true);
            expect(result.current.state.selectedSystemView).toBe(TimesheetsRosterViews.TIMESHEET_ROSTER);
            expect(result.current.state.filter.where).toEqual(viewToApply.filter.where);
            expect(result.current.state.sortOrder).toEqual(viewToApply.sortOrder);
            expect(result.current.state.columnData).toEqual(freshColumns);
        });
    });

    describe('External Store Synchronization', () => {
        it('should sync external filters to local state', () => {
            const externalFilters: TimeSheetFilterInput = {
                and: [{ name: { contains: 'test' } }]
            };

            const externalStore = {
                ...mockExternalStore,
                currentActiveFilters: externalFilters
            };

            const { result } = renderHook(() =>
                useTimesheetRosterState(mockInitialFilter, mockInitialColumns, 'test-employer', externalStore)
            );

            expect(result.current.state.externalFilters).toEqual(externalFilters);
            expect(result.current.state.filter.where).toEqual(externalFilters);
        });

        it('should sync external sort order to local state', () => {
            const externalSortOrder: TimeSheetSortInput[] = [{ payPeriodEndDate: 'ASC' }];

            const externalStore = {
                ...mockExternalStore,
                currentActiveSortOrder: externalSortOrder
            };

            const { result } = renderHook(() =>
                useTimesheetRosterState(mockInitialFilter, mockInitialColumns, 'test-employer', externalStore)
            );

            expect(result.current.state.externalSortOrder).toEqual(externalSortOrder);
            expect(result.current.state.sortOrder).toEqual(externalSortOrder);
        });

        it('should call external store setters when local state changes', () => {
            const { result } = renderHook(() =>
                useTimesheetRosterState(mockInitialFilter, mockInitialColumns, 'test-employer', mockExternalStore)
            );

            const newFilter: TimeSheetFilterInput = {
                and: [{ payPeriodEndDate: { eq: new Date('2024-01-01') } }]
            };

            act(() => {
                result.current.actions.updateFilter(newFilter);
            });

            // External store setters should be called after the action
            expect(mockExternalStore.setActiveRosterFilters).toHaveBeenCalledWith(newFilter);
        });
    });

    describe('Reset to Default', () => {
        it('should reset to default state correctly', () => {
            const { result } = renderHook(() =>
                useTimesheetRosterState(mockInitialFilter, mockInitialColumns, 'test-employer', mockExternalStore)
            );

            // First, change the state
            act(() => {
                result.current.actions.updateFilter({ and: [{ payPeriodEndDate: { eq: new Date('2024-01-01') } }] });
                result.current.actions.setSelectedCustomView({ id: 'test-view' });
            });

            // Then reset to default
            act(() => {
                result.current.actions.resetToDefault(mockInitialFilter, mockInitialColumns);
            });

            expect(result.current.state.filter).toEqual(mockInitialFilter);
            expect(result.current.state.sortOrder).toEqual(mockInitialFilter.order || []);
            expect(result.current.state.columnData).toEqual(mockInitialColumns);
            expect(result.current.state.selectedCustomView).toBeNull();
        });

        it('should not update state when resetting to same values (infinite loop prevention)', () => {
            const { result } = renderHook(() =>
                useTimesheetRosterState(mockInitialFilter, mockInitialColumns, 'test-employer', mockExternalStore)
            );

            // Let initialization complete first
            act(() => {
                // Wait for initialization to complete
            });

            const initialState = result.current.state;

            // Create a reset filter that matches the current initialized state
            const resetFilter = {
                ...mockInitialFilter,
                employerGuid: 'test-employer' // Should match the initialized state
            };

            // Reset to the same values that are already in state
            act(() => {
                result.current.actions.resetToDefault(resetFilter, mockInitialColumns);
            });

            // State reference should remain the same (no re-render triggered)
            expect(result.current.state).toBe(initialState);
            expect(result.current.state.lastActionType).toBe('INITIALIZE'); // Should not change to 'RESET_TO_DEFAULT'
        });
    });

    describe('Employer GUID Updates', () => {
        it('should update employer GUID correctly', () => {
            const { result } = renderHook(() =>
                useTimesheetRosterState(mockInitialFilter, mockInitialColumns, 'test-employer', mockExternalStore)
            );

            const newEmployerGuid = 'new-employer-guid';

            act(() => {
                result.current.actions.updateEmployerGuid(newEmployerGuid);
            });

            expect(result.current.state.filter.employerGuid).toBe(newEmployerGuid);
            expect(result.current.state.lastActionType).toBe('UPDATE_EMPLOYER_GUID');
        });
    });

    describe('Computed Values', () => {
        it('should calculate isSavedViewDisabled correctly for default state', () => {
            const { result } = renderHook(() =>
                useTimesheetRosterState(mockInitialFilter, mockInitialColumns, 'test-employer', mockExternalStore)
            );

            // Should be disabled when state matches initial values
            expect(result.current.computedValues.isSavedViewDisabled).toBe(true);
        });

        it('should calculate isSavedViewDisabled correctly when state differs', () => {
            const { result } = renderHook(() =>
                useTimesheetRosterState(mockInitialFilter, mockInitialColumns, 'test-employer', mockExternalStore)
            );

            act(() => {
                result.current.actions.updateFilter({ and: [{ payPeriodEndDate: { eq: new Date('2024-01-01') } }] });
            });

            // Should be enabled when state differs from initial
            expect(result.current.computedValues.isSavedViewDisabled).toBe(false);
        });

        it('should provide correct query variables', () => {
            const { result } = renderHook(() =>
                useTimesheetRosterState(mockInitialFilter, mockInitialColumns, 'test-employer', mockExternalStore)
            );

            const queryVars = result.current.computedValues.queryVariables;

            expect(queryVars).toEqual(
                expect.objectContaining({
                    employerGuid: 'test-employer',
                    where: { and: [] },
                    order: []
                })
            );
        });
    });

    describe('Performance and Stability', () => {
        it('should maintain stable action references', () => {
            const { result, rerender } = renderHook(() =>
                useTimesheetRosterState(mockInitialFilter, mockInitialColumns, 'test-employer', mockExternalStore)
            );

            const initialActions = result.current.actions;

            rerender();

            expect(result.current.actions).toBe(initialActions);
        });

        it('should not cause unnecessary re-renders on identical actions', () => {
            const { result } = renderHook(() =>
                useTimesheetRosterState(mockInitialFilter, mockInitialColumns, 'test-employer', mockExternalStore)
            );

            // Wait for initial state to stabilize
            act(() => {
                // Trigger a no-op action to let initialization complete
            });

            const stableState = result.current.state;

            act(() => {
                result.current.actions.updateFilter({ and: [] }); // Same as current
            });

            expect(result.current.state).toBe(stableState);
        });
    });

    describe('Complex State Scenarios', () => {
        it('should handle rapid state transitions correctly', () => {
            const { result } = renderHook(() =>
                useTimesheetRosterState(mockInitialFilter, mockInitialColumns, 'test-employer', mockExternalStore)
            );

            act(() => {
                // Rapid sequence of actions
                result.current.actions.updateFilter({ and: [{ payPeriodEndDate: { eq: new Date('2024-01-01') } }] });
                result.current.actions.updateSortOrder([{ payPeriodEndDate: 'DESC' }]);
                result.current.actions.toggleColumnVisibility(['Column 1', 'Column 2']);
                result.current.actions.setDefaultViewId('new-default-view');
            });

            expect(result.current.state.filter.where).toEqual({ and: [{ payPeriodEndDate: { eq: new Date('2024-01-01') } }] });
            expect(result.current.state.sortOrder).toEqual([{ payPeriodEndDate: 'DESC' }]);
            expect(result.current.state.columnData[1].show).toBe(true);
            expect(result.current.state.defaultViewId).toBe('new-default-view');
        });

        it('should handle view application with complex data', () => {
            const { result } = renderHook(() =>
                useTimesheetRosterState(mockInitialFilter, mockInitialColumns, 'test-employer', mockExternalStore)
            );

            const complexView = {
                type: TimesheetsRosterViews.TIMESHEET_ROSTER,
                filter: {
                    where: {
                        and: [{ payPeriodEndDate: { gte: new Date('2024-01-01') } }, { name: { contains: 'test' } }]
                    }
                },
                sortOrder: [{ payPeriodEndDate: 'DESC' }, { modificationDate: 'ASC' }],
                columns: [
                    ...mockInitialColumns,
                    {
                        key: 'newColumn',
                        label: 'New Column',
                        columnLabel: 'New Column',
                        show: true,
                        canHide: true,
                        position: 3,
                        metaData: { children: 'New Column' }
                    }
                ]
            };

            act(() => {
                result.current.actions.applyView(complexView, complexView.columns);
            });

            expect(result.current.state.filter.where).toEqual(complexView.filter.where);
            expect(result.current.state.sortOrder).toEqual(complexView.sortOrder);
            expect(result.current.state.columnData).toEqual(complexView.columns);
            expect(result.current.state.isApplyingView).toBe(true);
        });
    });
});
