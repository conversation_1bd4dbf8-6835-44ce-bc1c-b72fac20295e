/**
 * Comprehensive useMergedPayStub Hook Testing Patterns
 *
 * Phase 3 Implementation: Validate custom hook behavior and ensure proper merge logic
 *
 * This test suite validates:
 * - Basic merge functionality between server data and draft changes
 * - Deep merge behavior for nested objects
 * - Selective subscription performance characteristics
 * - Error state integration and handling
 * - Multi-timesheet scoping isolation
 * - Performance monitoring and debugging features
 * - Edge cases and error conditions
 * - Memory leak prevention
 */

import { renderHook, act } from '@testing-library/react';
import {
    useMergedPayStub,
    useMergedPayStubWithErrors,
    usePayStubDraftStatus,
    useMergePerformanceMonitor,
    type MergedPayStubWithErrors
} from '../useMergedPayStub';
import { useTimesheetUIStore, type TimesheetError } from '../../store/timesheetUIStore';
import type { PayStubDomainModel } from '../../types/timesheet-domain';

// =============================================================================
// TEST SETUP AND UTILITIES
// =============================================================================

const mockTimesheetId = 'timesheet-123';
const mockTimesheetId2 = 'timesheet-456';
const mockPayStubId = 'paystub-123';
const mockPayStubId2 = 'paystub-456';

const mockServerPayStub: PayStubDomainModel = {
    id: mockPayStubId,
    employeeId: 'emp-123',
    employeeName: 'John Doe',
    employee: {
        id: 'emp-123',
        firstName: 'John',
        lastName: 'Doe',
        fullName: 'Doe, John',
        externalEmployeeId: 'EXT-123',
        active: true
    },
    hours: { standard: 40, overtime: 0, doubletime: 0, total: 40 },
    amounts: { bonus: 0, expenses: 0 },
    ui: { expanded: false, isEditing: false, hasErrors: false, isSelected: false, isTemporary: false },
    details: [
        {
            id: 'detail-1',
            payStubId: mockPayStubId,
            workDate: '2023-01-01',
            dayName: 'Monday',
            hours: { standard: 8, overtime: 0, doubletime: 0, total: 8 },
            job: { jobCode: 'JOB001', costCenter: 'CC001', hourlyRate: 25.0 },
            agreements: { agreementId: 1, classificationId: 1, subClassificationId: 1 },
            amounts: { bonus: 0, expenses: 0 },
            earnings: { earningsCode: 'REG', earningsCodeText: 'Regular Hours' },
            employeeId: 'emp-123',
            ui: { isEditing: false, hasErrors: false, isSelected: false, isTemporary: false }
        },
        {
            id: 'detail-2',
            payStubId: mockPayStubId,
            workDate: '2023-01-02',
            dayName: 'Tuesday',
            hours: { standard: 8, overtime: 0, doubletime: 0, total: 8 },
            job: { jobCode: 'JOB001', costCenter: 'CC001', hourlyRate: 25.0 },
            agreements: { agreementId: 1, classificationId: 1, subClassificationId: 1 },
            amounts: { bonus: 0, expenses: 0 },
            earnings: { earningsCode: 'REG', earningsCodeText: 'Regular Hours' },
            employeeId: 'emp-123',
            ui: { isEditing: false, hasErrors: false, isSelected: false, isTemporary: false }
        }
    ]
};

const mockServerPayStub2: PayStubDomainModel = {
    id: mockPayStubId2,
    employeeId: 'emp-456',
    employeeName: 'Jane Smith',
    employee: {
        id: 'emp-456',
        firstName: 'Jane',
        lastName: 'Smith',
        fullName: 'Smith, Jane',
        externalEmployeeId: 'EXT-456',
        active: true
    },
    hours: { standard: 35, overtime: 5, doubletime: 0, total: 40 },
    amounts: { bonus: 100, expenses: 50 },
    ui: { expanded: false, isEditing: false, hasErrors: false, isSelected: false, isTemporary: false },
    details: []
};

// Test helper to assert non-null result and narrow type
function assertValidMergedResult(result: PayStubDomainModel | null): asserts result is PayStubDomainModel {
    expect(result).not.toBeNull();
    expect(result).toBeDefined();
}

// Test helper for MergedPayStubWithErrors return type
function assertValidMergedWithErrorsResult(result: MergedPayStubWithErrors | null): asserts result is MergedPayStubWithErrors {
    expect(result).not.toBeNull();
    expect(result).toBeDefined();
}

// Setup and teardown
beforeEach(() => {
    // Clear Zustand store state
    useTimesheetUIStore.getState().clearAllDrafts();
    useTimesheetUIStore.setState({
        activeTimesheetId: null,
        draftChanges: new Map(),
        expandedPayStubs: new Set(),
        editingPayStubs: new Set(),
        errorsByPayStubId: new Map(),
        isSaving: false,
        lastSaved: null
    });

    // Set active timesheet
    useTimesheetUIStore.getState().setActiveTimesheet(mockTimesheetId);
});

// =============================================================================
// BASIC FUNCTIONALITY TESTS
// =============================================================================

describe('useMergedPayStub - Basic Functionality', () => {
    test('returns server data when no draft changes exist', () => {
        const { result } = renderHook(() => useMergedPayStub(mockServerPayStub, mockTimesheetId));

        expect(result.current).toEqual(mockServerPayStub);
    });

    test('handles null/undefined server data gracefully', () => {
        const { result } = renderHook(() => useMergedPayStub(null, mockTimesheetId));

        expect(result.current).toBeNull();

        const { result: result2 } = renderHook(() => useMergedPayStub(undefined, mockTimesheetId));

        expect(result2.current).toBeNull();
    });

    test('returns same reference when no changes occur', () => {
        const { result, rerender } = renderHook(() => useMergedPayStub(mockServerPayStub, mockTimesheetId));

        const firstResult = result.current;
        rerender();
        const secondResult = result.current;

        expect(firstResult).toBe(secondResult); // Same reference due to memoization
    });

    test('provides fresh reference when data changes', () => {
        const { result } = renderHook(() => useMergedPayStub(mockServerPayStub, mockTimesheetId));

        const originalResult = result.current;

        // Make a draft change
        act(() => {
            useTimesheetUIStore.getState().updatePayStubDraft(mockTimesheetId, mockPayStubId, {
                hours: { standard: 45, overtime: 0, doubletime: 0, total: 45 }
            });
        });

        expect(result.current).not.toBe(originalResult); // Different reference
        assertValidMergedResult(result.current);
        expect(result.current.hours.standard).toBe(45);
    });
});

// =============================================================================
// DEEP MERGE FUNCTIONALITY TESTS
// =============================================================================

describe('useMergedPayStub - Deep Merge Functionality', () => {
    test('performs deep merge preserving nested object properties', () => {
        const { result } = renderHook(() => useMergedPayStub(mockServerPayStub, mockTimesheetId));

        // Update only standard hours - should preserve overtime and doubletime
        act(() => {
            useTimesheetUIStore.getState().updatePayStubDraft(mockTimesheetId, mockPayStubId, {
                hours: { standard: 45, overtime: 0, doubletime: 0, total: 45 } // Only updating standard hours
            });
        });

        assertValidMergedResult(result.current);
        expect(result.current.hours).toEqual({
            standard: 45, // ✅ Updated from draft
            overtime: 0, // ✅ Preserved from server
            doubletime: 0, // ✅ Preserved from server
            total: 45 // ✅ Recalculated
        });
    });

    test('merges multiple nested objects independently', () => {
        const { result } = renderHook(() => useMergedPayStub(mockServerPayStub, mockTimesheetId));

        // Update hours
        act(() => {
            useTimesheetUIStore.getState().updatePayStubDraft(mockTimesheetId, mockPayStubId, {
                hours: { standard: 45, overtime: 5, doubletime: 0, total: 50 }
            });
        });

        // Update amounts (separate nested object)
        act(() => {
            useTimesheetUIStore.getState().updatePayStubDraft(mockTimesheetId, mockPayStubId, {
                amounts: { bonus: 200, expenses: 0 }
            });
        });

        // Update employee info (another nested object)
        act(() => {
            useTimesheetUIStore.getState().updatePayStubDraft(mockTimesheetId, mockPayStubId, {
                employee: { id: 'emp-123', fullName: 'John Updated' }
            });
        });

        assertValidMergedResult(result.current);
        expect(result.current.hours).toEqual({
            standard: 45,
            overtime: 5,
            doubletime: 0,
            total: 50
        });
        expect(result.current.amounts).toEqual({
            bonus: 200,
            expenses: 0
        });
        expect(result.current.employee).toEqual({
            id: 'emp-123',
            firstName: 'John',
            lastName: 'Doe',
            fullName: 'John Updated',
            externalEmployeeId: 'EXT-123',
            active: true
        });
    });

    test('handles incremental updates to same nested object', () => {
        const { result } = renderHook(() => useMergedPayStub(mockServerPayStub, mockTimesheetId));

        // First update: standard hours
        act(() => {
            useTimesheetUIStore.getState().updatePayStubDraft(mockTimesheetId, mockPayStubId, {
                hours: { standard: 45, overtime: 0, doubletime: 0, total: 45 }
            });
        });

        assertValidMergedResult(result.current);
        expect(result.current.hours.standard).toBe(45);
        expect(result.current.hours.overtime).toBe(0); // Preserved

        // Second update: overtime hours (should not affect standard)
        act(() => {
            useTimesheetUIStore.getState().updatePayStubDraft(mockTimesheetId, mockPayStubId, {
                hours: { standard: 45, overtime: 5, doubletime: 0, total: 50 }
            });
        });

        expect(result.current.hours.standard).toBe(45); // Should still be preserved
        expect(result.current.hours.overtime).toBe(5);
        expect(result.current.hours.total).toBe(50); // Recalculated
    });

    test('recalculates total hours correctly', () => {
        const { result } = renderHook(() => useMergedPayStub(mockServerPayStub, mockTimesheetId));

        act(() => {
            useTimesheetUIStore.getState().updatePayStubDraft(mockTimesheetId, mockPayStubId, {
                hours: { standard: 30, overtime: 8, doubletime: 2, total: 40 }
            });
        });

        assertValidMergedResult(result.current);
        expect(result.current.hours.total).toBe(40); // 30 + 8 + 2
    });

    test('handles undefined/null values in nested objects', () => {
        const { result } = renderHook(() => useMergedPayStub(mockServerPayStub, mockTimesheetId));

        // Update with partial data including null values
        act(() => {
            useTimesheetUIStore.getState().updatePayStubDraft(mockTimesheetId, mockPayStubId, {
                hours: { standard: 35, overtime: 0, doubletime: 0, total: 35 }
            });
        });

        assertValidMergedResult(result.current);
        expect(result.current.hours).toEqual({
            standard: 35,
            overtime: 0, // Preserved from server (undefined didn't override)
            doubletime: 0, // Preserved from server (null didn't override)
            total: 35
        });
    });
});

// =============================================================================
// DETAILS ARRAY MERGE TESTS
// =============================================================================

describe('useMergedPayStub - Details Array Merge', () => {
    test('merges details array correctly', () => {
        const { result } = renderHook(() => useMergedPayStub(mockServerPayStub, mockTimesheetId));

        // Update existing detail
        act(() => {
            useTimesheetUIStore.getState().updatePayStubDraft(mockTimesheetId, mockPayStubId, {
                details: [
                    {
                        id: 'detail-1',
                        payStubId: mockPayStubId,
                        workDate: '2023-01-01',
                        dayName: 'Monday',
                        hours: { standard: 9, overtime: 1, doubletime: 0, total: 10 },
                        job: { jobCode: 'JOB001', costCenter: 'CC001', hourlyRate: 25.0 },
                        agreements: { agreementId: 1, classificationId: 1, subClassificationId: 1 },
                        amounts: { bonus: 50, expenses: 0 },
                        earnings: { earningsCode: 'REG', earningsCodeText: 'Regular Hours' },
                        employeeId: 'emp-123',
                        ui: { isEditing: true, hasErrors: false, isSelected: false, isTemporary: false }
                    }
                ]
            });
        });

        assertValidMergedResult(result.current);
        const mergedDetails = result.current.details;
        expect(mergedDetails).toHaveLength(2); // Should still have 2 details

        // First detail should be updated
        expect(mergedDetails[0].hours.standard).toBe(9);
        expect(mergedDetails[0].hours.overtime).toBe(1);
        expect(mergedDetails[0].amounts.bonus).toBe(50);
        expect(mergedDetails[0].ui.isEditing).toBe(true);

        // Second detail should remain unchanged
        expect(mergedDetails[1].hours.standard).toBe(8);
        expect(mergedDetails[1].ui.isEditing).toBe(false);
    });

    test('adds new details to existing array', () => {
        const { result } = renderHook(() => useMergedPayStub(mockServerPayStub, mockTimesheetId));

        // Add a new detail
        act(() => {
            useTimesheetUIStore.getState().updatePayStubDraft(mockTimesheetId, mockPayStubId, {
                details: [
                    {
                        id: 'detail-new',
                        payStubId: mockPayStubId,
                        workDate: '2023-01-03',
                        dayName: 'Wednesday',
                        hours: { standard: 4, overtime: 0, doubletime: 0, total: 4 },
                        job: { jobCode: 'JOB001', costCenter: 'CC001', hourlyRate: 25.0 },
                        agreements: { agreementId: 1, classificationId: 1, subClassificationId: 1 },
                        amounts: { bonus: 0, expenses: 25 },
                        earnings: { earningsCode: 'REG', earningsCodeText: 'Regular Hours' },
                        employeeId: 'emp-123',
                        ui: { isEditing: false, hasErrors: false, isSelected: false, isTemporary: false }
                    }
                ]
            });
        });

        assertValidMergedResult(result.current);
        const mergedDetails = result.current.details;
        expect(mergedDetails).toHaveLength(3); // Should now have 3 details

        // New detail should be added
        const newDetail = mergedDetails.find((d) => d.id === 'detail-new');
        expect(newDetail).toBeDefined();
        expect(newDetail?.hours.standard).toBe(4);
        expect(newDetail?.amounts.expenses).toBe(25);
    });

    test('filters out temporary details', () => {
        const { result } = renderHook(() => useMergedPayStub(mockServerPayStub, mockTimesheetId));

        // Add a temporary detail that should be filtered out
        act(() => {
            useTimesheetUIStore.getState().updatePayStubDraft(mockTimesheetId, mockPayStubId, {
                details: [
                    {
                        id: 'detail-temp',
                        payStubId: mockPayStubId,
                        workDate: '2023-01-03',
                        dayName: 'Wednesday',
                        hours: { standard: 4, overtime: 0, doubletime: 0, total: 4 },
                        job: { jobCode: 'JOB001', costCenter: 'CC001', hourlyRate: 25.0 },
                        agreements: { agreementId: 1, classificationId: 1, subClassificationId: 1 },
                        amounts: { bonus: 0, expenses: 0 },
                        earnings: { earningsCode: 'REG', earningsCodeText: 'Regular Hours' },
                        employeeId: 'emp-123',
                        ui: { isEditing: false, hasErrors: false, isSelected: false, isTemporary: true } // Marked as temporary
                    }
                ]
            });
        });

        assertValidMergedResult(result.current);
        const mergedDetails = result.current.details;
        expect(mergedDetails).toHaveLength(2); // Should still have original 2 details

        // Temporary detail should not be included
        const tempDetail = mergedDetails.find((d) => d.id === 'detail-temp');
        expect(tempDetail).toBeUndefined();
    });
});

// =============================================================================
// MULTI-TIMESHEET SCOPING TESTS
// =============================================================================

describe('useMergedPayStub - Multi-Timesheet Scoping', () => {
    test('isolates data by timesheet ID', () => {
        const { result: result1 } = renderHook(() => useMergedPayStub(mockServerPayStub, mockTimesheetId));

        const { result: result2 } = renderHook(() => useMergedPayStub(mockServerPayStub, mockTimesheetId2));

        // Setup different drafts for same payStub ID in different timesheets
        act(() => {
            useTimesheetUIStore.getState().updatePayStubDraft(mockTimesheetId, mockPayStubId, {
                hours: { standard: 40, overtime: 0, doubletime: 0, total: 40 }
            });
            useTimesheetUIStore.getState().updatePayStubDraft(mockTimesheetId2, mockPayStubId, {
                hours: { standard: 35, overtime: 0, doubletime: 0, total: 35 }
            });
        });

        // Should have different values based on timesheet scope
        assertValidMergedResult(result1.current);
        assertValidMergedResult(result2.current);
        expect(result1.current.hours.standard).toBe(40);
        expect(result2.current.hours.standard).toBe(35);
    });

    test('no cross-contamination between timesheet scopes', () => {
        const { result } = renderHook(() => useMergedPayStub(mockServerPayStub, mockTimesheetId));

        // Change data in different timesheet - should not affect this hook
        act(() => {
            useTimesheetUIStore.getState().updatePayStubDraft(mockTimesheetId2, mockPayStubId, {
                hours: { standard: 99, overtime: 0, doubletime: 0, total: 99 }
            });
        });

        // Should still return original server data
        assertValidMergedResult(result.current);
        expect(result.current.hours.standard).toBe(40);
        expect(result.current).toEqual(mockServerPayStub);
    });
});

// =============================================================================
// SELECTIVE SUBSCRIPTION PERFORMANCE TESTS
// =============================================================================

describe('useMergedPayStub - Selective Subscription Performance', () => {
    test('only re-renders when relevant draft data changes', () => {
        const renderSpy = jest.fn();

        const { result } = renderHook(() => {
            renderSpy();
            return useMergedPayStub(mockServerPayStub, mockTimesheetId);
        });

        expect(renderSpy).toHaveBeenCalledTimes(1);

        // Change unrelated pay stub - should not trigger re-render
        act(() => {
            useTimesheetUIStore.getState().updatePayStubDraft(mockTimesheetId, mockPayStubId2, {
                hours: { standard: 30, overtime: 0, doubletime: 0, total: 30 }
            });
        });

        // Should not have triggered additional render
        expect(renderSpy).toHaveBeenCalledTimes(1);
    });

    test('re-renders when relevant draft data changes', () => {
        const renderSpy = jest.fn();

        const { result } = renderHook(() => {
            renderSpy();
            return useMergedPayStub(mockServerPayStub, mockTimesheetId);
        });

        expect(renderSpy).toHaveBeenCalledTimes(1);

        // Change related pay stub - should trigger re-render
        act(() => {
            useTimesheetUIStore.getState().updatePayStubDraft(mockTimesheetId, mockPayStubId, {
                hours: { standard: 35, overtime: 0, doubletime: 0, total: 35 }
            });
        });

        // Should have re-rendered due to relevant change
        expect(renderSpy).toHaveBeenCalledTimes(2);
    });

    test('memoization prevents unnecessary object recreation', () => {
        const { result, rerender } = renderHook(({ serverData }) => useMergedPayStub(serverData, mockTimesheetId), {
            initialProps: { serverData: mockServerPayStub }
        });

        const firstResult = result.current;

        // Rerender with same server data and no draft changes
        rerender({ serverData: mockServerPayStub });

        // Should return same object reference
        expect(result.current).toBe(firstResult);
    });
});

// =============================================================================
// MERGE OPTIONS TESTS
// =============================================================================

describe('useMergedPayStub - Merge Options', () => {
    test('excludes UI state when includeUIState is false', () => {
        const { result } = renderHook(() => useMergedPayStub(mockServerPayStub, mockTimesheetId, { includeUIState: false }));

        // Make UI state changes
        act(() => {
            useTimesheetUIStore.getState().updatePayStubDraft(mockTimesheetId, mockPayStubId, {
                ui: { expanded: true, isEditing: true, hasErrors: false, isSelected: false, isTemporary: false }
            });
        });

        // UI state should not be merged
        assertValidMergedResult(result.current);
        expect(result.current.ui).toEqual(mockServerPayStub.ui);
        expect(result.current.ui.isEditing).toBe(false); // Should remain server value
    });

    test('includes UI state when includeUIState is true (default)', () => {
        const { result } = renderHook(() => useMergedPayStub(mockServerPayStub, mockTimesheetId, { includeUIState: true }));

        // Make UI state changes
        act(() => {
            useTimesheetUIStore.getState().updatePayStubDraft(mockTimesheetId, mockPayStubId, {
                ui: { expanded: true, isEditing: true, hasErrors: false, isSelected: false, isTemporary: false }
            });
        });

        // UI state should be merged
        assertValidMergedResult(result.current);
        expect(result.current.ui.isEditing).toBe(true);
        expect(result.current.ui.expanded).toBe(true);
    });

    test('handles validation option', () => {
        const consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation();

        const { result } = renderHook(() => useMergedPayStub(mockServerPayStub, mockTimesheetId, { validateOnMerge: true }));

        // Make changes that trigger validation warnings
        act(() => {
            useTimesheetUIStore.getState().updatePayStubDraft(mockTimesheetId, mockPayStubId, {
                hours: { standard: -5, overtime: 0, doubletime: 0, total: -5 } // Invalid negative hours
            });
        });

        expect(consoleWarnSpy).toHaveBeenCalledWith(expect.stringContaining('invalid standard hours'), -5);

        consoleWarnSpy.mockRestore();
    });
});

// =============================================================================
// ERROR STATE INTEGRATION TESTS
// =============================================================================

describe('useMergedPayStubWithErrors', () => {
    test('returns merged data with error state', () => {
        const mockError: TimesheetError = {
            message: 'Invalid hours',
            field: 'stHours',
            severity: 'error',
            timestamp: Date.now()
        };

        const { result } = renderHook(() => useMergedPayStubWithErrors(mockServerPayStub, mockTimesheetId));

        assertValidMergedWithErrorsResult(result.current);
        expect(result.current.hasErrors).toBe(false);
        expect(result.current.hasDraftChanges).toBe(false);

        // Add error and draft
        act(() => {
            useTimesheetUIStore.getState().setError(mockTimesheetId, mockPayStubId, mockError);
            useTimesheetUIStore.getState().updatePayStubDraft(mockTimesheetId, mockPayStubId, {
                hours: { standard: 45, overtime: 0, doubletime: 0, total: 45 }
            });
        });

        assertValidMergedWithErrorsResult(result.current);
        expect(result.current.hasErrors).toBe(true);
        expect(result.current.hasDraftChanges).toBe(true);
        expect(result.current.errors?.message).toBe(mockError.message);
        expect(result.current.errors?.field).toBe(mockError.field);
        expect(result.current.errors?.severity).toBe(mockError.severity);
        expect(result.current.errors?.timestamp).toBeCloseTo(mockError.timestamp, -2); // Allow 100ms tolerance
        expect(result.current.data.hours.standard).toBe(45);
        expect(result.current.lastModified).toBeGreaterThan(Date.now() - 1000);
    });

    test('returns no errors when none exist', () => {
        const { result } = renderHook(() => useMergedPayStubWithErrors(mockServerPayStub, mockTimesheetId));

        expect(result.current).toEqual({
            data: mockServerPayStub,
            hasErrors: false,
            errors: null,
            hasDraftChanges: false,
            lastModified: undefined
        });
    });

    test('isolates errors by timesheet scope', () => {
        const error1: TimesheetError = {
            message: 'Error 1',
            field: 'stHours',
            severity: 'error',
            timestamp: Date.now()
        };
        const error2: TimesheetError = {
            message: 'Error 2',
            field: 'otHours',
            severity: 'warning',
            timestamp: Date.now()
        };

        const { result: result1 } = renderHook(() => useMergedPayStubWithErrors(mockServerPayStub, mockTimesheetId));

        const { result: result2 } = renderHook(() => useMergedPayStubWithErrors(mockServerPayStub, mockTimesheetId2));

        act(() => {
            useTimesheetUIStore.getState().setError(mockTimesheetId, mockPayStubId, error1);
            useTimesheetUIStore.getState().setError(mockTimesheetId2, mockPayStubId, error2);
        });

        assertValidMergedWithErrorsResult(result1.current);
        expect(result1.current.errors?.message).toBe(error1.message);
        expect(result1.current.errors?.field).toBe(error1.field);
        expect(result1.current.errors?.severity).toBe(error1.severity);
        expect(result1.current.errors?.timestamp).toBeCloseTo(error1.timestamp, -2);
        assertValidMergedWithErrorsResult(result2.current);
        expect(result2.current.errors?.message).toBe(error2.message);
        expect(result2.current.errors?.field).toBe(error2.field);
        expect(result2.current.errors?.severity).toBe(error2.severity);
        expect(result2.current.errors?.timestamp).toBeCloseTo(error2.timestamp, -2);
    });
});

// =============================================================================
// DRAFT STATUS HOOK TESTS
// =============================================================================

describe('usePayStubDraftStatus', () => {
    test('returns correct draft and error status', () => {
        const { result } = renderHook(() => usePayStubDraftStatus(mockPayStubId, mockTimesheetId));

        expect(result.current.hasDraftChanges).toBe(false);
        expect(result.current.hasErrors).toBe(false);

        // Add draft changes
        act(() => {
            useTimesheetUIStore.getState().updatePayStubDraft(mockTimesheetId, mockPayStubId, {
                hours: { standard: 45, overtime: 0, doubletime: 0, total: 45 }
            });
        });

        expect(result.current.hasDraftChanges).toBe(true);
        expect(result.current.hasErrors).toBe(false);

        // Add error
        act(() => {
            useTimesheetUIStore.getState().setError(mockTimesheetId, mockPayStubId, {
                message: 'Test error',
                severity: 'error',
                timestamp: Date.now()
            });
        });

        expect(result.current.hasDraftChanges).toBe(true);
        expect(result.current.hasErrors).toBe(true);
    });

    test('is lightweight and performance-optimized', () => {
        const renderSpy = jest.fn();

        const { result } = renderHook(() => {
            renderSpy();
            return usePayStubDraftStatus(mockPayStubId, mockTimesheetId);
        });

        expect(renderSpy).toHaveBeenCalledTimes(1);

        // Change unrelated pay stub
        act(() => {
            useTimesheetUIStore.getState().updatePayStubDraft(mockTimesheetId, mockPayStubId2, {
                hours: { standard: 30, overtime: 0, doubletime: 0, total: 30 }
            });
        });

        // Should not re-render
        expect(renderSpy).toHaveBeenCalledTimes(1);
    });
});

// =============================================================================
// PERFORMANCE MONITORING TESTS
// =============================================================================

describe('useMergePerformanceMonitor', () => {
    test('tracks merge performance correctly', async () => {
        const { result } = renderHook(() => useMergePerformanceMonitor(mockPayStubId, mockTimesheetId));

        expect(result.current.mergeCount).toBe(0);
        expect(result.current.lastMergeTime).toBeNull();
        expect(result.current.averageMergeTime).toBe(0);

        // Trigger merge operations
        act(() => {
            useTimesheetUIStore.getState().updatePayStubDraft(mockTimesheetId, mockPayStubId, {
                hours: { standard: 45, overtime: 0, doubletime: 0, total: 45 }
            });
        });

        // Allow for async effects to complete
        await act(async () => {
            await new Promise((resolve) => setTimeout(resolve, 10));
        });

        expect(result.current.mergeCount).toBeGreaterThan(0);
        expect(result.current.lastMergeTime).toBeGreaterThan(0);
        expect(result.current.averageMergeTime).toBeGreaterThan(0);
    });
});

// =============================================================================
// EDGE CASES AND ERROR CONDITIONS
// =============================================================================

describe('useMergedPayStub - Edge Cases', () => {
    test('handles empty server data gracefully', () => {
        const emptyPayStub = {
            id: 'empty',
            employee: {},
            hours: {},
            amounts: {},
            ui: {}
        } as PayStubDomainModel;

        const { result } = renderHook(() => useMergedPayStub(emptyPayStub, mockTimesheetId));

        act(() => {
            useTimesheetUIStore.getState().updatePayStubDraft(mockTimesheetId, 'empty', {
                hours: { standard: 40, overtime: 0, doubletime: 0, total: 40 }
            });
        });

        assertValidMergedResult(result.current);
        expect(result.current.hours.standard).toBe(40);
        expect(result.current.hours.total).toBe(40);
    });

    test('handles very large numbers correctly', () => {
        const { result } = renderHook(() => useMergedPayStub(mockServerPayStub, mockTimesheetId));

        act(() => {
            useTimesheetUIStore.getState().updatePayStubDraft(mockTimesheetId, mockPayStubId, {
                hours: { standard: 999999.99, overtime: 888888.88, doubletime: 0, total: 1888888.87 },
                amounts: { bonus: 1000000, expenses: 500000 }
            });
        });

        assertValidMergedResult(result.current);
        expect(result.current.hours.standard).toBe(999999.99);
        expect(result.current.hours.overtime).toBe(888888.88);
        expect(result.current.hours.total).toBe(999999.99 + 888888.88);
        expect(result.current.amounts.bonus).toBe(1000000);
        expect(result.current.amounts.expenses).toBe(500000);
    });

    test('handles rapid successive updates correctly', () => {
        const { result } = renderHook(() => useMergedPayStub(mockServerPayStub, mockTimesheetId));

        // Perform rapid updates
        act(() => {
            for (let i = 0; i < 10; i++) {
                useTimesheetUIStore.getState().updatePayStubDraft(mockTimesheetId, mockPayStubId, {
                    hours: { standard: 40 + i, overtime: 0, doubletime: 0, total: 40 + i }
                });
            }
        });

        assertValidMergedResult(result.current);
        expect(result.current.hours.standard).toBe(49); // Last update: 40 + 9
    });

    test('memory efficiency with repeated hook calls', () => {
        // Create multiple hook instances to test memory efficiency
        const hooks = [];
        for (let i = 0; i < 100; i++) {
            hooks.push(renderHook(() => useMergedPayStub(mockServerPayStub, mockTimesheetId)));
        }

        // All should return same memoized result
        const firstResult = hooks[0].result.current;
        hooks.forEach((hook) => {
            expect(hook.result.current).toBe(firstResult);
        });

        // Cleanup
        hooks.forEach((hook) => hook.unmount());
    });
});
