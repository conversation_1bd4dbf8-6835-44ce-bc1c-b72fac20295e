/**
 * Type Safety Tests for useTimesheetSaver
 *
 * Tests to ensure the elimination of unsafe type casts works correctly
 * and that the new MappedPayStubInput interface functions properly
 */

import { renderHook, act } from '@testing-library/react';
import { useTimesheetSaver } from '../useTimesheetSaver';
import type { RelayTimeSheet, ModifiablePayStub } from '@/src/types/timesheet-detail';
import type { MappedPayStubInput } from '../useTimesheetSaver';

// Mock dependencies
jest.mock('react-relay', () => ({
    useMutation: () => [jest.fn(), false],
    useRelayEnvironment: () => ({}),
    graphql: jest.fn()
}));

jest.mock('../../store/timesheetUIStore', () => ({
    useTimesheetUIStore: (selector: any) => {
        const state = {
            setValidationErrors: jest.fn(),
            clearValidationErrors: jest.fn(),
            getDraftForPayStub: jest.fn(() => null),
            markedForDeletion: new Set<string>(),
            clearAllMarkForDeletion: jest.fn(),
            clearAllDrafts: jest.fn(),
            getDetailDraft: jest.fn(() => null)
        };
        return selector ? selector(state) : state;
    }
}));

jest.mock('../../store/rosterFilterStore', () => ({
    useTimesheetRosterFilterStore: () => ({
        activeFilters: null,
        activeSortOrder: null
    })
}));

jest.mock('@/lib', () => ({
    useStore: {
        getState: () => ({
            user: { username: 'testuser', permissions: [] }
        })
    }
}));

describe('useTimesheetSaver Type Safety', () => {
    describe('RelayTimeSheet ID Access', () => {
        it('should access timesheet header ID directly without casting', () => {
            const mockTimesheet: RelayTimeSheet = {
                id: 'timesheet-123',
                numericId: 123,
                employerGuid: 'employer-guid',
                name: 'Test Timesheet',
                payPeriodEndDate: '2024-01-01',
                status: 'Draft',
                type: 'Weekly',
                creationDate: '2024-01-01',
                modificationDate: '2024-01-01',
                modifiedByUserId: 'user-123',
                showBonusColumn: true,
                showCostCenterColumn: false,
                showDTHoursColumn: true,
                showEarningsCodesColumn: false,
                showExpensesColumn: true,
                hoursWorked: 40,
                oldId: null,
                ' $fragmentType': 'TimesheetDetail_timeSheet' as const,
                ' $fragmentSpreads': {} as any
            };

            // This should compile without any type errors
            const headerId = mockTimesheet.id;
            expect(headerId).toBe('timesheet-123');
            expect(typeof headerId).toBe('string');
        });

        it('should handle client-generated IDs correctly', () => {
            const mockTimesheet: RelayTimeSheet = {
                id: 'client:new-timesheet',
                numericId: 0,
                employerGuid: 'employer-guid',
                name: 'New Timesheet',
                payPeriodEndDate: '2024-01-01',
                status: 'New',
                type: 'Weekly',
                creationDate: '2024-01-01',
                modificationDate: '2024-01-01',
                modifiedByUserId: null,
                showBonusColumn: false,
                showCostCenterColumn: false,
                showDTHoursColumn: false,
                showEarningsCodesColumn: false,
                showExpensesColumn: false,
                hoursWorked: 0,
                oldId: null,
                ' $fragmentType': 'TimesheetDetail_timeSheet' as const,
                ' $fragmentSpreads': {} as any
            };

            const headerId = mockTimesheet.id;
            const isNewTimesheet = !headerId || headerId.startsWith('client:');
            expect(isNewTimesheet).toBe(true);
        });
    });

    describe('MappedPayStubInput Type', () => {
        it('should create properly typed MappedPayStubInput objects', () => {
            const mockPayStub: ModifiablePayStub = {
                id: 'paystub-123',
                employeeId: 'emp-123',
                name: 'John Doe',
                totalHours: 40,
                employee: {
                    id: 'emp-123',
                    ' $fragmentSpreads': {
                        EmployeeDisplayFragment_employee: true,
                        TimeSheetDetailRow_employee: true
                    } as const
                },
                ' $fragmentType': 'PayStubTable_payStub' as const,
                ' $fragmentSpreads': {
                    TimeSheetDetailTableView_payStub: true
                } as const,
                details: [
                    {
                        id: 'detail-1',
                        payStubId: 'paystub-123',
                        name: 'Monday',
                        workDate: '2024-01-01',
                        stHours: 8,
                        otHours: 0,
                        dtHours: 0,
                        bonus: null,
                        expenses: null,
                        agreementId: 1,
                        classificationId: 1,
                        costCenter: null,
                        earningsCode: null,
                        hourlyRate: null,
                        jobCode: null,
                        reportLineItemId: null,
                        subClassificationId: null,
                        ' $fragmentType': 'TimeSheetDetailRow_payStubDetail' as const
                    }
                ]
            };

            // Transform to MappedPayStubInput - this should be type-safe
            const mapped: MappedPayStubInput = {
                id: mockPayStub.id,
                employeeId: mockPayStub.employeeId,
                name: mockPayStub.name,
                stHours: null,
                otHours: null,
                dtHours: null,
                bonus: null,
                expenses: null,
                details: mockPayStub.details.map((detail) => ({
                    id: detail.id,
                    name: detail.name || '',
                    workDate: detail.workDate,
                    stHours: detail.stHours,
                    otHours: detail.otHours,
                    dtHours: detail.dtHours,
                    bonus: detail.bonus,
                    expenses: detail.expenses,
                    jobCode: null,
                    earningsCode: null,
                    agreementId: null,
                    classificationId: null,
                    subClassificationId: null,
                    costCenter: null,
                    hourlyRate: null,
                    reportLineItemId: null
                })),
                delete: false
            };

            expect(mapped.employeeId).toBe('emp-123');
            expect(mapped.details).toHaveLength(1);
            expect(mapped.details[0].stHours).toBe(8);
        });

        it('should handle PayStubs marked for deletion', () => {
            const mockPayStub: ModifiablePayStub = {
                id: 'paystub-to-delete',
                employeeId: 'emp-456',
                name: 'Jane Smith',
                totalHours: 0,
                employee: {
                    id: 'emp-456',
                    ' $fragmentSpreads': {
                        EmployeeDisplayFragment_employee: true,
                        TimeSheetDetailRow_employee: true
                    } as const
                },
                ' $fragmentType': 'PayStubTable_payStub' as const,
                ' $fragmentSpreads': {
                    TimeSheetDetailTableView_payStub: true
                } as const,
                details: [],
                delete: true
            };

            const mapped: MappedPayStubInput = {
                id: mockPayStub.id,
                employeeId: mockPayStub.employeeId,
                name: mockPayStub.name,
                stHours: null,
                otHours: null,
                dtHours: null,
                bonus: null,
                expenses: null,
                details: [],
                delete: true
            };

            expect(mapped.delete).toBe(true);
            expect(mapped.details).toHaveLength(0);
        });
    });

    describe('Type-safe conversion to mutation inputs', () => {
        it('should convert MappedPayStubInput array without any casts', () => {
            const mappedPayStubs: MappedPayStubInput[] = [
                {
                    id: 'paystub-1',
                    employeeId: 'emp-1',
                    name: 'Employee 1',
                    stHours: 40,
                    otHours: 0,
                    dtHours: 0,
                    bonus: null,
                    expenses: null,
                    details: [],
                    delete: false
                },
                {
                    id: 'paystub-2',
                    employeeId: 'emp-2',
                    name: 'Employee 2',
                    stHours: 35,
                    otHours: 5,
                    dtHours: 0,
                    bonus: 100,
                    expenses: 50,
                    details: [],
                    delete: false
                }
            ];

            // This conversion should work without any type assertions
            const addPayStubInputs = mappedPayStubs.map((stub) => ({
                id: stub.id,
                employeeId: stub.employeeId,
                name: stub.name,
                stHours: stub.stHours,
                otHours: stub.otHours,
                dtHours: stub.dtHours,
                bonus: stub.bonus,
                expenses: stub.expenses,
                details: stub.details,
                delete: stub.delete
            }));

            expect(addPayStubInputs).toHaveLength(2);
            expect(addPayStubInputs[0].employeeId).toBe('emp-1');
            expect(addPayStubInputs[1].bonus).toBe(100);
        });
    });

    describe('Relay ID conversion', () => {
        it('should handle numeric employee IDs correctly', () => {
            const { result } = renderHook(() => useTimesheetSaver());

            // Test that the hook is properly initialized
            expect(result.current.isSaving).toBe(false);
            expect(result.current.saveError).toBe(null);
            expect(typeof result.current.saveTimesheet).toBe('function');
        });
    });
});
