import { renderHook, act } from '@testing-library/react';
import { useTimesheetSaver } from '../useTimesheetSaver';
import { useMutation } from 'react-relay';

// Mock dependencies
jest.mock('react-relay');

// Mock the timesheetUIStore with all required functions
jest.mock('@/src/store/timesheetUIStore', () => ({
    useTimesheetUIStore: (selector: any) => {
        const state = {
            setValidationErrors: jest.fn(),
            clearValidationErrors: jest.fn(),
            getDraftForPayStub: jest.fn(() => null),
            markedForDeletion: new Set<string>(),
            clearAllMarkForDeletion: jest.fn(),
            clearAllDrafts: jest.fn(),
            getDetailDraft: jest.fn(() => null),
            getAllValidationErrors: jest.fn(() => new Map())
        };
        return selector ? selector(state) : state;
    }
}));

jest.mock('@/src/store/rosterFilterStore', () => ({
    useTimesheetRosterFilterStore: () => ({
        activeFilters: null,
        activeSortOrder: null
    })
}));

jest.mock('@/lib', () => ({
    useStore: {
        getState: () => ({
            user: { username: 'test-user', permissions: [] }
        })
    }
}));

describe('useTimesheetSaver - Error Display', () => {
    const mockCommitMutation = jest.fn();

    beforeEach(() => {
        jest.clearAllMocks();
        (useMutation as jest.Mock).mockReturnValue([mockCommitMutation, false]);
    });

    it('should extract and display actual GraphQL error message instead of generic network error', async () => {
        const { result } = renderHook(() => useTimesheetSaver());

        const mockTimesheet: any = {
            id: 'test-id',
            numericId: 123,
            employerGuid: 'test-guid',
            name: 'Test Timesheet',
            payPeriodEndDate: '2024-01-01',
            status: 'Saved',
            type: 'Regular',
            showBonusColumn: false,
            showCostCenterColumn: false,
            showDTHoursColumn: false,
            showEarningsCodesColumn: false,
            showExpensesColumn: false
        };

        const mockPayStubs: any = [
            {
                id: 'paystub-1',
                employeeId: 'emp-1',
                name: 'Test Employee',
                details: []
            }
        ];

        // Simulate GraphQL error with proper structure
        const graphQLError = {
            source: {
                errors: [
                    {
                        message: 'The required input field `workDate` is missing.',
                        extensions: { code: 'BAD_USER_INPUT' }
                    }
                ]
            }
        };

        // Setup mutation to call onError with GraphQL error
        mockCommitMutation.mockImplementation(({ onError }) => {
            setTimeout(() => {
                onError(graphQLError);
            }, 0);
        });

        let savePromise: Promise<boolean>;

        await act(async () => {
            savePromise = result.current.saveTimesheet(mockTimesheet, mockPayStubs, {
                targetStatus: 'Submitted'
            });
        });

        // Wait for the promise to reject
        await expect(savePromise!).rejects.toThrow('The required input field `workDate` is missing.');

        // Verify the error in the hook state
        expect(result.current.saveError).toBeTruthy();
        expect(result.current.saveError?.message).toBe('The required input field `workDate` is missing.');

        // Verify the error has graphQLErrors attached for ErrorHandlingService
        expect((result.current.saveError as any)?.graphQLErrors).toEqual([
            {
                message: 'The required input field `workDate` is missing.',
                extensions: { code: 'BAD_USER_INPUT' }
            }
        ]);
    });

    it('should handle generic errors without GraphQL structure', async () => {
        const { result } = renderHook(() => useTimesheetSaver());

        const mockTimesheet: any = {
            id: 'test-id',
            numericId: 123,
            employerGuid: 'test-guid',
            name: 'Test Timesheet',
            payPeriodEndDate: '2024-01-01',
            status: 'Saved',
            type: 'Regular',
            showBonusColumn: false,
            showCostCenterColumn: false,
            showDTHoursColumn: false,
            showEarningsCodesColumn: false,
            showExpensesColumn: false
        };

        const mockPayStubs: any = [
            {
                id: 'paystub-1',
                employeeId: 'emp-1',
                name: 'Test Employee',
                details: []
            }
        ];

        // Simulate generic network error
        const networkError = new Error('Network request failed');

        // Setup mutation to call onError with network error
        mockCommitMutation.mockImplementation(({ onError }) => {
            setTimeout(() => {
                onError(networkError);
            }, 0);
        });

        let savePromise: Promise<boolean>;

        await act(async () => {
            savePromise = result.current.saveTimesheet(mockTimesheet, mockPayStubs, {
                targetStatus: 'Submitted'
            });
        });

        // Wait for the promise to reject
        await expect(savePromise!).rejects.toThrow('Network request failed');

        // Verify the error remains unchanged for non-GraphQL errors
        expect(result.current.saveError).toBe(networkError);
    });
});
