import { renderHook, act } from '@testing-library/react';
import { useRelayEnvironment } from 'react-relay';
import { useTimesheetUIStore } from '@/src/store/timesheetUIStore';
import { useSaveTimesheet } from '../useSaveTimesheet';
import { useTimesheetSaver } from '../useTimesheetSaver';
import type { RelayTimeSheet } from '@/src/types/timesheet-detail';
import type { ServerPayStub } from '@/src/types/timesheet';

// Mock dependencies
jest.mock('react-relay');
jest.mock('@/src/store/timesheetUIStore');
jest.mock('../useTimesheetSaver');

describe('useSaveTimesheet', () => {
    const mockEnvironment = {} as any;
    const mockSelectModifiablePayStubs = jest.fn();
    const mockSaveTimesheet = jest.fn();
    
    beforeEach(() => {
        jest.clearAllMocks();
        
        // Setup mocks
        (useRelayEnvironment as jest.Mock).mockReturnValue(mockEnvironment);
        (useTimesheetUIStore as unknown as jest.Mock).mockReturnValue(mockSelectModifiablePayStubs);
        (useTimesheetSaver as jest.Mock).mockReturnValue({
            saveTimesheet: mockSaveTimesheet,
            isSaving: false,
            saveError: null
        });
    });
    
    describe('saveForLater', () => {
        it('should handle saveForLater with correct status', async () => {
            const mockTimesheetData: RelayTimeSheet = {
                id: 'timesheet-1',
                status: 'Draft'
            } as RelayTimeSheet;
            
            const mockServerPayStubs: ServerPayStub[] = [
                {
                    id: 'paystub-1',
                    employeeId: 'emp-1',
                    name: 'John Doe',
                    totalHours: 40,
                    details: []
                }
            ];
            
            const mockModifiablePayStubs = [
                { id: 'paystub-1', name: 'John Doe Modified' }
            ];
            
            mockSelectModifiablePayStubs.mockReturnValue(mockModifiablePayStubs);
            mockSaveTimesheet.mockResolvedValue(true);
            
            const { result } = renderHook(() => useSaveTimesheet());
            
            let saveResult: boolean | undefined;
            await act(async () => {
                saveResult = await result.current.saveForLater(mockTimesheetData, mockServerPayStubs);
            });
            
            expect(mockSelectModifiablePayStubs).toHaveBeenCalledWith('timesheet-1', mockServerPayStubs);
            expect(mockSaveTimesheet).toHaveBeenCalledWith(
                mockTimesheetData,
                mockModifiablePayStubs,
                { targetStatus: 'Saved' }
            );
            expect(saveResult).toBe(true);
        });
        
        it('should throw error when timesheet data is null', async () => {
            const { result } = renderHook(() => useSaveTimesheet());
            
            await expect(
                result.current.saveForLater(null, [])
            ).rejects.toThrow('Timesheet data is required for save operation');
        });
    });
    
    describe('submitTimesheet', () => {
        it('should handle submitTimesheet with correct status', async () => {
            const mockTimesheetData: RelayTimeSheet = {
                id: 'timesheet-1',
                status: 'Saved'
            } as RelayTimeSheet;
            
            const mockServerPayStubs: ServerPayStub[] = [];
            const mockModifiablePayStubs: any[] = [];
            
            mockSelectModifiablePayStubs.mockReturnValue(mockModifiablePayStubs);
            mockSaveTimesheet.mockResolvedValue(true);
            
            const { result } = renderHook(() => useSaveTimesheet());
            
            let submitResult: boolean | undefined;
            await act(async () => {
                submitResult = await result.current.submitTimesheet(mockTimesheetData, mockServerPayStubs);
            });
            
            expect(mockSelectModifiablePayStubs).toHaveBeenCalledWith('timesheet-1', mockServerPayStubs);
            expect(mockSaveTimesheet).toHaveBeenCalledWith(
                mockTimesheetData,
                mockModifiablePayStubs,
                { targetStatus: 'Submitted' }
            );
            expect(submitResult).toBe(true);
        });
    });
    
    describe('loading and error states', () => {
        it('should propagate loading state', () => {
            (useTimesheetSaver as jest.Mock).mockReturnValue({
                saveTimesheet: mockSaveTimesheet,
                isSaving: true,
                saveError: null
            });
            
            const { result } = renderHook(() => useSaveTimesheet());
            
            expect(result.current.isSaving).toBe(true);
        });
        
        it('should propagate error state', () => {
            const mockError = new Error('Save failed');
            (useTimesheetSaver as jest.Mock).mockReturnValue({
                saveTimesheet: mockSaveTimesheet,
                isSaving: false,
                saveError: mockError
            });
            
            const { result } = renderHook(() => useSaveTimesheet());
            
            expect(result.current.saveError).toBe(mockError);
        });
    });
    
    describe('selectModifiablePayStubs integration', () => {
        it('should call selectModifiablePayStubs with correct params', async () => {
            const mockTimesheetData: RelayTimeSheet = {
                id: 'timesheet-123',
                status: 'Draft'
            } as RelayTimeSheet;
            
            const mockServerPayStubs: ServerPayStub[] = [
                {
                    id: 'ps-1',
                    employeeId: 'emp-1',
                    name: 'Employee 1',
                    details: [
                        {
                            id: 'detail-1',
                            payStubId: 'ps-1',
                            stHours: 8,
                            otHours: 0,
                            dtHours: 0
                        }
                    ]
                }
            ];
            
            mockSelectModifiablePayStubs.mockReturnValue([]);
            mockSaveTimesheet.mockResolvedValue(true);
            
            const { result } = renderHook(() => useSaveTimesheet());
            
            await act(async () => {
                await result.current.saveTimesheet(mockTimesheetData, mockServerPayStubs, 'Saved');
            });
            
            expect(mockSelectModifiablePayStubs).toHaveBeenCalledWith('timesheet-123', mockServerPayStubs);
        });
    });
});