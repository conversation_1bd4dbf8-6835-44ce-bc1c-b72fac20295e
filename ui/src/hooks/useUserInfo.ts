import { useCallback, useRef } from 'react';
import { instance } from '@/src/core/http/axios-config';
import { UserInfoApi } from '@/src/constants/api-urls';
import { ClientUtils } from "@/lib/core/ClientUtils";
import { useStore, StoreState } from '@/lib';

// Global flag to prevent duplicate calls across all components
let globalUserInfoLoaded = false;
let globalUserInfoPromise: Promise<any> | null = null;

export const useUserInfo = () => {
    const setUser = useStore((state: StoreState) => state.setUser);

    const loadUserInfo = useCallback(async () => {
        // If already loaded, return immediately
        if (globalUserInfoLoaded) {
            return;
        }

        // If a request is already in progress, wait for it
        if (globalUserInfoPromise) {
            return globalUserInfoPromise;
        }

        // Start new request
        globalUserInfoPromise = (async () => {
            try {
                // Don't set Authorization header manually - let the CookieToHeaderMiddleware handle it
                // The instance already has withCredentials: true to send HttpOnly cookies
                const response = await instance.get(UserInfoApi.GET_USER_INFO());

                const userData = response.data;

                // Store user data in global store
                setUser({
                    username: userData.username,
                    ...userData,
                    roles: userData.role || [], // Map API's 'role' to 'roles'
                    permissions: userData.permission || [] // Map API's 'permission' to 'permissions'
                });
                globalUserInfoLoaded = true;
                return userData;
            } catch (error) {
                console.error('Failed to load user info:', error);
                throw error;
            } finally {
                globalUserInfoPromise = null;
            }
        })();

        return globalUserInfoPromise;
    }, [setUser]);

    const resetUserInfo = useCallback(() => {
        globalUserInfoLoaded = false;
        globalUserInfoPromise = null;
    }, []);

    return {
        loadUserInfo,
        resetUserInfo,
        isLoaded: globalUserInfoLoaded
    };
};
