/**
 * Unified State Management Hook for TimesheetRoster
 * 
 * This hook replaces the complex multi-layer state management in TimesheetRoster
 * with a single, unified useReducer-based approach. It eliminates the need for
 * multiple useEffect hooks, ref workarounds, and complex synchronization logic.
 * 
 * Key benefits:
 * - Single source of truth for all roster state
 * - Predictable state updates through actions
 * - No circular dependencies or infinite loops
 * - Better performance with reduced re-renders
 * - Clearer separation of concerns
 */

import { useReducer, useCallback, useMemo, useEffect } from 'react';
import { isEqual } from 'lodash';
import { TimeSheetFilterInput, TimeSheetSortInput } from '@/src/relay/__generated__/TimesheetRosterQuery.graphql';
import { TimesheetRosterQuery$variables } from '@/src/relay/__generated__/TimesheetRosterQuery.graphql';
import { TimesheetsRosterViews } from '@/src/constants/views';
import { ColumnType } from '@/src/types/rosters';

/**
 * Complete state interface for TimesheetRoster
 */
export interface TimesheetRosterState {
    // Core query state
    filter: TimesheetRosterQuery$variables;
    sortOrder: readonly TimeSheetSortInput[];
    columnData: ColumnType[];
    
    // View management state
    selectedCustomView: any | null;
    selectedSystemView: TimesheetsRosterViews;
    defaultViewId: string;
    pendingCustomViewSelectId: string | null;
    
    // External store state (mirrors Zustand store)
    externalFilters: TimeSheetFilterInput | null;
    externalSortOrder: readonly TimeSheetSortInput[] | null;
    
    // UI state flags
    isApplyingView: boolean;
    lastActionType: string | null;
}

/**
 * Actions for state management
 */
export type TimesheetRosterAction =
    | { type: 'INITIALIZE'; payload: { initialFilter: TimesheetRosterQuery$variables; initialColumns: ColumnType[]; employerGuid: string } }
    | { type: 'UPDATE_FILTER'; payload: TimeSheetFilterInput | null | undefined }
    | { type: 'UPDATE_SORT_ORDER'; payload: readonly TimeSheetSortInput[] }
    | { type: 'UPDATE_COLUMN_DATA'; payload: ColumnType[] }
    | { type: 'TOGGLE_COLUMN_VISIBILITY'; payload: string[] }
    | { type: 'SET_SELECTED_CUSTOM_VIEW'; payload: any | null }
    | { type: 'SET_SELECTED_SYSTEM_VIEW'; payload: TimesheetsRosterViews }
    | { type: 'SET_DEFAULT_VIEW_ID'; payload: string }
    | { type: 'SET_PENDING_CUSTOM_VIEW_SELECT_ID'; payload: string | null }
    | { type: 'SYNC_EXTERNAL_FILTERS'; payload: TimeSheetFilterInput | null }
    | { type: 'SYNC_EXTERNAL_SORT_ORDER'; payload: readonly TimeSheetSortInput[] | null }
    | { type: 'APPLY_VIEW_START'; payload: { view: any; freshColumns: ColumnType[] } }
    | { type: 'APPLY_VIEW_COMPLETE' }
    | { type: 'RESET_TO_DEFAULT'; payload: { defaultFilter: TimesheetRosterQuery$variables; defaultColumns: ColumnType[] } }
    | { type: 'UPDATE_EMPLOYER_GUID'; payload: string };

/**
 * State reducer with predictable state transitions
 */
function timesheetRosterStateReducer(state: TimesheetRosterState, action: TimesheetRosterAction): TimesheetRosterState {
    switch (action.type) {
        case 'INITIALIZE': {
            const { initialFilter, initialColumns, employerGuid } = action.payload;
            return {
                ...state,
                filter: { ...initialFilter, employerGuid },
                sortOrder: initialFilter.order || [],
                columnData: initialColumns,
                lastActionType: action.type
            };
        }

        case 'UPDATE_FILTER': {
            const { payload: filterData } = action;
            
            // Handle empty filter case (clear all)
            const isEmptyFilter = filterData && (!filterData.and || (Array.isArray(filterData.and) && filterData.and.length === 0));
            
            if (isEmptyFilter) {
                const newFilterWhere = { and: [] };
                // Only update if current filter is different
                if (!isEqual(state.filter.where, newFilterWhere)) {
                    return {
                        ...state,
                        filter: {
                            ...state.filter,
                            where: newFilterWhere
                        },
                        externalFilters: null,
                        lastActionType: action.type
                    };
                }
                return state;
            }
            
            // Only update if filter data is different
            if (!isEqual(state.filter.where, filterData)) {
                return {
                    ...state,
                    filter: {
                        ...state.filter,
                        where: { ...state.filter.where, ...filterData }
                    },
                    externalFilters: filterData || null,
                    lastActionType: action.type
                };
            }
            
            return state;
        }

        case 'UPDATE_SORT_ORDER': {
            const { payload: sortData } = action;
            
            // Only update if sort data is different
            if (!isEqual(state.sortOrder, sortData)) {
                return {
                    ...state,
                    sortOrder: sortData,
                    externalSortOrder: sortData,
                    lastActionType: action.type
                };
            }
            
            return state;
        }

        case 'UPDATE_COLUMN_DATA': {
            const { payload: newColumnData } = action;
            
            // Only update if column data is different
            if (!isEqual(state.columnData, newColumnData)) {
                return {
                    ...state,
                    columnData: newColumnData,
                    lastActionType: action.type
                };
            }
            
            return state;
        }

        case 'TOGGLE_COLUMN_VISIBILITY': {
            const { payload: visibleColumnLabels } = action;
            
            const updatedColumnData = state.columnData.map((column) => ({
                ...column,
                show: !column.canHide || visibleColumnLabels.includes(column.columnLabel)
            }));
            
            // Only update if visibility actually changed
            const visibilityChanged = !isEqual(
                state.columnData.map((col) => ({ key: col.key, show: col.show })),
                updatedColumnData.map((col) => ({ key: col.key, show: col.show }))
            );
            
            if (visibilityChanged) {
                return {
                    ...state,
                    columnData: updatedColumnData,
                    lastActionType: action.type
                };
            }
            
            return state;
        }

        case 'SET_SELECTED_CUSTOM_VIEW': {
            return {
                ...state,
                selectedCustomView: action.payload,
                lastActionType: action.type
            };
        }

        case 'SET_SELECTED_SYSTEM_VIEW': {
            return {
                ...state,
                selectedSystemView: action.payload,
                lastActionType: action.type
            };
        }

        case 'SET_DEFAULT_VIEW_ID': {
            return {
                ...state,
                defaultViewId: action.payload,
                lastActionType: action.type
            };
        }

        case 'SET_PENDING_CUSTOM_VIEW_SELECT_ID': {
            return {
                ...state,
                pendingCustomViewSelectId: action.payload,
                lastActionType: action.type
            };
        }

        case 'SYNC_EXTERNAL_FILTERS': {
            const { payload: externalFilters } = action;
            
            // Update local filter state from external store only if different
            if (!isEqual(state.externalFilters, externalFilters)) {
                return {
                    ...state,
                    externalFilters,
                    filter: externalFilters ? {
                        ...state.filter,
                        where: externalFilters
                    } : state.filter,
                    lastActionType: action.type
                };
            }
            
            return state;
        }

        case 'SYNC_EXTERNAL_SORT_ORDER': {
            const { payload: externalSortOrder } = action;
            
            // Update local sort order from external store only if different
            if (!isEqual(state.externalSortOrder, externalSortOrder)) {
                return {
                    ...state,
                    externalSortOrder,
                    sortOrder: externalSortOrder || state.sortOrder,
                    lastActionType: action.type
                };
            }
            
            return state;
        }

        case 'APPLY_VIEW_START': {
            const { view, freshColumns } = action.payload;
            
            // Check if we actually need to update anything to prevent unnecessary re-renders
            const needsFilterUpdate = !isEqual(state.filter.where, view.filter.where);
            const needsSortUpdate = !isEqual(state.sortOrder, view.sortOrder);
            const needsColumnsUpdate = !isEqual(state.columnData, freshColumns);
            const needsSystemViewUpdate = state.selectedSystemView !== view.type;
            
            // If nothing needs to change, return current state
            if (!needsFilterUpdate && !needsSortUpdate && !needsColumnsUpdate && !needsSystemViewUpdate && state.isApplyingView) {
                return state;
            }
            
            return {
                ...state,
                isApplyingView: true,
                selectedSystemView: needsSystemViewUpdate ? view.type : state.selectedSystemView,
                filter: needsFilterUpdate ? {
                    ...state.filter,
                    where: view.filter.where
                } : state.filter,
                sortOrder: needsSortUpdate ? view.sortOrder : state.sortOrder,
                columnData: needsColumnsUpdate ? freshColumns : state.columnData,
                lastActionType: action.type
            };
        }

        case 'APPLY_VIEW_COMPLETE': {
            return {
                ...state,
                isApplyingView: false,
                lastActionType: action.type
            };
        }

        case 'RESET_TO_DEFAULT': {
            const { defaultFilter, defaultColumns } = action.payload;
            
            // Only update if state actually differs from defaults to prevent unnecessary re-renders
            const needsFilterReset = !isEqual(state.filter, defaultFilter);
            const needsSortReset = !isEqual(state.sortOrder, defaultFilter.order || []);
            const needsColumnsReset = !isEqual(state.columnData, defaultColumns);
            const needsCustomViewReset = state.selectedCustomView !== null;
            
            if (!needsFilterReset && !needsSortReset && !needsColumnsReset && !needsCustomViewReset) {
                return state; // No changes needed, return current state to prevent re-render
            }
            
            return {
                ...state,
                filter: needsFilterReset ? defaultFilter : state.filter,
                sortOrder: needsSortReset ? (defaultFilter.order || []) : state.sortOrder,
                columnData: needsColumnsReset ? defaultColumns : state.columnData,
                selectedCustomView: needsCustomViewReset ? null : state.selectedCustomView,
                lastActionType: action.type
            };
        }

        case 'UPDATE_EMPLOYER_GUID': {
            return {
                ...state,
                filter: {
                    ...state.filter,
                    employerGuid: action.payload
                },
                lastActionType: action.type
            };
        }

        default:
            return state;
    }
}

/**
 * Hook interface for external store synchronization
 */
interface ExternalStoreSync {
    currentActiveFilters: TimeSheetFilterInput | null;
    currentActiveSortOrder: readonly TimeSheetSortInput[] | null;
    setActiveRosterFilters: (filters: TimeSheetFilterInput | null) => void;
    setActiveRosterSortOrder: (sortOrder: TimeSheetSortInput[] | null) => void;
}

/**
 * Main hook for TimesheetRoster state management
 */
export function useTimesheetRosterState(
    initialFilter: TimesheetRosterQuery$variables,
    initialColumns: ColumnType[],
    employerGuid: string,
    externalStore: ExternalStoreSync
) {
    // Initialize state
    const [state, dispatch] = useReducer(timesheetRosterStateReducer, {
        filter: { ...initialFilter, employerGuid },
        sortOrder: initialFilter.order || [],
        columnData: initialColumns,
        selectedCustomView: null,
        selectedSystemView: TimesheetsRosterViews.TIMESHEET_ROSTER,
        defaultViewId: TimesheetsRosterViews.TIMESHEET_ROSTER,
        pendingCustomViewSelectId: null,
        externalFilters: null,
        externalSortOrder: null,
        isApplyingView: false,
        lastActionType: null
    });

    // Initialize state on mount
    useEffect(() => {
        dispatch({
            type: 'INITIALIZE',
            payload: { initialFilter, initialColumns, employerGuid }
        });
    }, [initialFilter, initialColumns, employerGuid]);

    // Sync with external store (Zustand) - only when external store changes
    useEffect(() => {
        if (externalStore.currentActiveFilters !== state.externalFilters) {
            dispatch({
                type: 'SYNC_EXTERNAL_FILTERS',
                payload: externalStore.currentActiveFilters
            });
        }
    }, [externalStore.currentActiveFilters, state.externalFilters]);

    useEffect(() => {
        if (externalStore.currentActiveSortOrder !== state.externalSortOrder) {
            dispatch({
                type: 'SYNC_EXTERNAL_SORT_ORDER',
                payload: externalStore.currentActiveSortOrder
            });
        }
    }, [externalStore.currentActiveSortOrder, state.externalSortOrder]);

    // Update external store when local state changes (only for user-initiated actions)
    useEffect(() => {
        const shouldUpdateExternalFilters = 
            state.lastActionType === 'UPDATE_FILTER' && 
            !isEqual(externalStore.currentActiveFilters, state.externalFilters);
            
        if (shouldUpdateExternalFilters) {
            externalStore.setActiveRosterFilters(state.externalFilters);
        }
    }, [state.externalFilters, state.lastActionType, externalStore]);

    useEffect(() => {
        const shouldUpdateExternalSortOrder = 
            state.lastActionType === 'UPDATE_SORT_ORDER' && 
            !isEqual(externalStore.currentActiveSortOrder, state.externalSortOrder);
            
        if (shouldUpdateExternalSortOrder) {
            externalStore.setActiveRosterSortOrder(state.externalSortOrder ? [...state.externalSortOrder] : null);
        }
    }, [state.externalSortOrder, state.lastActionType, externalStore]);

    // Action creators (memoized for performance)
    const actions = useMemo(() => ({
        updateFilter: (filterData: TimeSheetFilterInput | null | undefined) => {
            dispatch({ type: 'UPDATE_FILTER', payload: filterData });
        },

        updateSortOrder: (sortData: readonly TimeSheetSortInput[]) => {
            dispatch({ type: 'UPDATE_SORT_ORDER', payload: sortData });
        },

        updateColumnData: (columnData: ColumnType[]) => {
            dispatch({ type: 'UPDATE_COLUMN_DATA', payload: columnData });
        },

        toggleColumnVisibility: (visibleColumns: string[]) => {
            dispatch({ type: 'TOGGLE_COLUMN_VISIBILITY', payload: visibleColumns });
        },

        setSelectedCustomView: (view: any | null) => {
            dispatch({ type: 'SET_SELECTED_CUSTOM_VIEW', payload: view });
        },

        setSelectedSystemView: (view: TimesheetsRosterViews) => {
            dispatch({ type: 'SET_SELECTED_SYSTEM_VIEW', payload: view });
        },

        setDefaultViewId: (id: string) => {
            dispatch({ type: 'SET_DEFAULT_VIEW_ID', payload: id });
        },

        setPendingCustomViewSelectId: (id: string | null) => {
            dispatch({ type: 'SET_PENDING_CUSTOM_VIEW_SELECT_ID', payload: id });
        },

        applyView: (view: any, freshColumns: ColumnType[]) => {
            dispatch({ type: 'APPLY_VIEW_START', payload: { view, freshColumns } });
            // Complete the view application in next tick to ensure state consistency
            setTimeout(() => {
                dispatch({ type: 'APPLY_VIEW_COMPLETE' });
            }, 0);
        },

        resetToDefault: (defaultFilter: TimesheetRosterQuery$variables, defaultColumns: ColumnType[]) => {
            dispatch({ type: 'RESET_TO_DEFAULT', payload: { defaultFilter, defaultColumns } });
        },

        updateEmployerGuid: (guid: string) => {
            dispatch({ type: 'UPDATE_EMPLOYER_GUID', payload: guid });
        }
    }), []);

    // Computed values (memoized for performance)
    const computedValues = useMemo(() => ({
        // Query variables for GraphQL
        queryVariables: {
            ...state.filter,
            order: state.sortOrder
        },

        // Check if current state differs from selected view
        isSavedViewDisabled: (() => {
            if (!state.selectedCustomView) {
                return (
                    isEqual(state.filter.where, initialFilter.where) &&
                    isEqual(state.sortOrder, initialFilter.order || []) &&
                    isEqual(state.columnData, initialColumns)
                );
            }

            return (
                isEqual(state.filter.where, state.selectedCustomView.filter?.where) &&
                isEqual(state.sortOrder, state.selectedCustomView.sortOrder) &&
                isEqual(state.columnData, state.selectedCustomView.columns)
            );
        })(),

        // Current view state for comparisons
        currentViewState: {
            filter: state.filter.where,
            sortOrder: state.sortOrder,
            columns: state.columnData
        }
    }), [state, initialFilter, initialColumns]);

    return {
        state,
        actions,
        computedValues
    };
}

