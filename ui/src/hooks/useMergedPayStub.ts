/**
 * useMergedPayStub Custom Hook
 *
 * Phase 0 Implementation: Centralized Data Merging for PayStub Components
 *
 * This hook provides a stable, memoized representation of a pay stub by merging
 * authoritative server data with pending draft changes from the Zustand store.
 * It eliminates scattered merge logic across components (DRY compliance) and
 * provides selective subscription for optimal performance.
 *
 * Key Features:
 * - Selective subscription to only relevant draft changes
 * - Deep merging for nested objects to prevent data loss
 * - Automatic memoization for performance
 * - Error state integration
 * - Multi-timesheet scoping support
 * - Type-safe interfaces
 */

import React, { useMemo, useState, useEffect } from 'react';
import { useTimesheetUIStore } from '../store/timesheetUIStore';
import type { PayStubDomainModel } from '../types/timesheet-domain';
import type { TimesheetError } from '../store/timesheetUIStore';
import type { FlatPayStubDraft } from '@/src/types';
import { flatToNestedPayStubDetail } from '../utils/schema-aware-converters';
import type { PayStubTable_payStub$data } from '@/src/relay/__generated__/PayStubTable_payStub.graphql';
import { getFullNameFromFirstAndLastName } from '@/src/services/employer-roster';

// =============================================================================
// HOOK INTERFACES
// =============================================================================

/**
 * Extended result interface that includes error state
 */
export interface MergedPayStubWithErrors {
    data: PayStubDomainModel;
    hasErrors: boolean;
    errors: TimesheetError | null;
    hasDraftChanges: boolean;
    lastModified?: number;
}

/**
 * Options for controlling merge behavior
 */
export interface MergeOptions {
    includeUIState?: boolean; // Whether to merge UI state fields
    preserveServerDefaults?: boolean; // Whether to preserve server defaults for undefined draft values
    validateOnMerge?: boolean; // Whether to validate the merged result
}

// =============================================================================
// CORE HOOK IMPLEMENTATION
// =============================================================================

/**
 * Phase 3 Migration: Updated hook that accepts GraphQL fragments and flat drafts
 * A hook that provides a stable, memoized representation of a pay stub,
 * by merging the authoritative server data with any pending draft changes from the Zustand store.
 *
 * MIGRATION NOTE: Now accepts GraphQL fragment data instead of domain models,
 * uses flat types internally, but still returns domain models for component compatibility.
 *
 * It subscribes selectively to only the draft changes for the specified pay stub ID,
 * ensuring the component only re-renders when its specific data changes.
 *
 * @param serverPayStub The authoritative pay stub data from Relay (GraphQL fragment)
 * @param timesheetId The timesheet ID for scoping
 * @param options Optional configuration for merge behavior
 * @returns A memoized PayStubDomainModel object with draft changes applied
 */
export function useMergedPayStub(
    serverPayStub: PayStubTable_payStub$data | PayStubDomainModel | null | undefined,
    timesheetId: string,
    options: MergeOptions = {}
): PayStubDomainModel | null {
    const { includeUIState = true, preserveServerDefaults = true, validateOnMerge = false } = options;

    // Handle null/undefined server data gracefully
    if (!serverPayStub || !serverPayStub.id) {
        return null;
    }

    // PHASE 4: Use reliable discriminator for fragment vs domain detection
    // GraphQL fragments have $fragmentType, domain models have nested structure like hours.standard
    const isGraphQLFragment = '$fragmentType' in serverPayStub;
    const payStubId = serverPayStub.id;

    // 1. Subscribe ONLY to the flat draft data for this specific pay stub.
    // This is highly performant. If another pay stub's draft changes, this hook will NOT trigger a re-render.
    const flatDraftData = useTimesheetUIStore((state) => state.getDraftForPayStub(timesheetId, payStubId));
    
    // 2. Subscribe to deletion state for this specific pay stub
    const isMarkedForDeletion = useTimesheetUIStore((state) => state.isMarkedForDeletion(timesheetId, payStubId));

    // PHASE 4: Extract scalar values for proper memo dependency tracking
    // This prevents stale UI when Relay mutates edges in-place without changing the ref
    const serverDataScalars = useMemo(() => {
        if (!serverPayStub) return null;
        return {
            id: serverPayStub.id,
            employeeId: (serverPayStub as any).employeeId,
            name: (serverPayStub as any).name,
            totalHours: (serverPayStub as any).totalHours,
            detailsLength: (serverPayStub as any).details?.length || 0,
            isFragment: isGraphQLFragment
        };
    }, [serverPayStub, isGraphQLFragment]);

    // 3. Memoize the merge operation - PHASE 3: Now handles both GraphQL fragments and domain models
    // The merged result is only recalculated if the server data scalars or the specific draft data changes.
    const mergedData = useMemo(() => {
        // Convert GraphQL fragment to domain model if needed
        let baseDomainModel: PayStubDomainModel;
        
        if (isGraphQLFragment) {
            // Convert GraphQL fragment to domain model structure
            const fragment = serverPayStub as PayStubTable_payStub$data;
            baseDomainModel = {
                id: fragment.id,
                employeeId: String(fragment.employeeId), // Convert number to string for domain model
                name: fragment.name || '',
                employeeName: fragment.name || '', // Use PayStub name as employee name
                
                // Create nested structure expected by domain model
                hours: {
                    standard: 0, // GraphQL fragment doesn't have individual hours - calculated from details
                    overtime: 0,
                    doubletime: 0,
                    total: fragment.totalHours || 0
                },
                
                amounts: {
                    bonus: 0, // PayStub-level amounts not in GraphQL fragment - calculated from details
                    expenses: 0
                },
                
                employee: {
                    id: String(fragment.employeeId),
                    firstName: '', // Employee details not available in this fragment
                    lastName: '', // Employee details not available in this fragment
                    fullName: fragment.name || 'Unknown Employee' // Use PayStub name as fallback
                },
                
                ui: {
                    isEditing: false,
                    hasErrors: false,
                    isSelected: false,
                    isTemporary: false, // Keep separate from deletion state
                    expanded: false
                },
                
                // Details come from GraphQL fragment
                // Details come from GraphQL fragment - for compatibility use empty array
                // Details are managed separately in the new flat types system
                details: []
            };
        } else {
            // Already a domain model
            baseDomainModel = serverPayStub as PayStubDomainModel;
        }

        // CRITICAL FIX: Always inject deletion state, even when no draft data exists
        if (!flatDraftData) {
            // Still need to inject deletion state into UI when no draft exists
            return {
                ...baseDomainModel,
                ui: includeUIState 
                    ? {
                          ...baseDomainModel.ui,
                          isTemporary: baseDomainModel.ui?.isTemporary || false // Keep original temporary status, deletion is separate
                      }
                    : baseDomainModel.ui
            };
        }

        // PHASE 3: Convert flat draft to domain model structure for merging
        const domainDraft = convertFlatToDomainStructure(flatDraftData);

        // Perform deep merge with careful handling of nested objects
        const merged: PayStubDomainModel = {
            ...baseDomainModel,
            ...domainDraft,

            // Deep merge for hours object
            hours: {
                ...baseDomainModel.hours,
                ...domainDraft.hours,
                // Recalculate total if any component changed
                total: calculateTotalHours({
                    ...baseDomainModel.hours,
                    ...domainDraft.hours
                })
            },

            // Deep merge for amounts object
            amounts: {
                ...baseDomainModel.amounts,
                ...domainDraft.amounts
            },

            // Deep merge for employee object
            employee: {
                ...baseDomainModel.employee,
                ...domainDraft.employee
            },

            // Deep merge for UI state (if enabled)
            ui: includeUIState
                ? {
                      ...baseDomainModel.ui,
                      ...domainDraft.ui,
                      // Keep temporary status separate from deletion state
                      isTemporary: domainDraft.ui?.isTemporary || baseDomainModel.ui?.isTemporary || false
                  }
                : baseDomainModel.ui,

            // Merge details array with proper handling
            details: mergePayStubDetails(baseDomainModel.details || [], domainDraft.details || [], preserveServerDefaults)
        };

        // Optional validation
        if (validateOnMerge) {
            validateMergedPayStub(merged);
        }

        return merged;
    }, [serverDataScalars, flatDraftData, includeUIState, preserveServerDefaults, validateOnMerge, isMarkedForDeletion]);

    return mergedData;
}

/**
 * Extended version that also provides error state and metadata for the pay stub
 * Useful for components that need to display validation errors or draft indicators
 */
export function useMergedPayStubWithErrors(
    serverPayStub: PayStubTable_payStub$data | PayStubDomainModel | null | undefined,
    timesheetId: string,
    options: MergeOptions = {}
): MergedPayStubWithErrors | null {
    // Get merged data using the core hook
    const mergedData = useMergedPayStub(serverPayStub, timesheetId, options);

    // Handle null server data
    if (!serverPayStub || !mergedData) {
        return null;
    }

    // Get draft data to check for changes
    const draftData = useTimesheetUIStore((state) => state.getDraftForPayStub(timesheetId, serverPayStub.id));
    
    // Get error state
    const errors = useTimesheetUIStore((state) => state.getErrorForPayStub(timesheetId, serverPayStub.id));

    return useMemo(
        () => ({
            data: mergedData,
            hasErrors: !!errors,
            errors,
            hasDraftChanges: !!draftData,
            lastModified: undefined // Not available in domain model
        }),
        [mergedData, errors, draftData]
    );
}

/**
 * Lightweight hook that only returns whether the pay stub has draft changes
 * Useful for performance-sensitive components that only need to show draft indicators
 */
export function usePayStubDraftStatus(payStubId: string, timesheetId: string): { hasDraftChanges: boolean; hasErrors: boolean } {
    const hasDraftChanges = useTimesheetUIStore((state) => !!state.getDraftForPayStub(timesheetId, payStubId));
    const hasErrors = useTimesheetUIStore((state) => !!state.getErrorForPayStub(timesheetId, payStubId));

    return { hasDraftChanges, hasErrors };
}

// =============================================================================
// UTILITY FUNCTIONS
// =============================================================================

/**
 * Calculate total hours from individual hour components
 * Handles null/undefined values gracefully
 */
function calculateTotalHours(hours: { standard?: number; overtime?: number; doubletime?: number }): number {
    const standard = hours.standard || 0;
    const overtime = hours.overtime || 0;
    const doubletime = hours.doubletime || 0;

    return standard + overtime + doubletime;
}


/**
 * Merge PayStub details array with draft changes
 * Performs element-wise merging for details that exist in both arrays
 */
function mergePayStubDetails(
    serverDetails: PayStubDomainModel['details'],
    draftDetails: PayStubDomainModel['details'],
    preserveServerDefaults: boolean
): PayStubDomainModel['details'] {
    if (!draftDetails || draftDetails.length === 0) {
        return serverDetails || [];
    }

    if (!serverDetails || serverDetails.length === 0) {
        return draftDetails;
    }

    // Create a Map for efficient lookups
    const draftMap = new Map(draftDetails.map((detail) => [detail.id, detail]));

    // Merge server details with draft changes
    const merged = serverDetails.map((serverDetail) => {
        const draftDetail = draftMap.get(serverDetail.id);
        if (!draftDetail) {
            return serverDetail;
        }

        // Perform deep merge for nested objects
        return {
            ...serverDetail,
            ...draftDetail,
            hours: {
                ...serverDetail.hours,
                ...draftDetail.hours,
                total: calculateTotalHours({
                    ...serverDetail.hours,
                    ...draftDetail.hours
                })
            },
            amounts: {
                ...serverDetail.amounts,
                ...draftDetail.amounts
            },
            job: {
                ...serverDetail.job,
                ...draftDetail.job
            },
            agreements: {
                ...serverDetail.agreements,
                ...draftDetail.agreements
            },
            earnings: {
                ...serverDetail.earnings,
                ...draftDetail.earnings
            },
            ui: {
                ...serverDetail.ui,
                ...draftDetail.ui
            }
        };
    });

    // Add any new details from draft that don't exist in server
    const serverIds = new Set(serverDetails.map((detail) => detail.id));
    const newDraftDetails = draftDetails.filter((detail) => !serverIds.has(detail.id));
    
    return [...merged, ...newDraftDetails];
}

/**
 * Validate merged pay stub data
 * Can be extended with business rules validation
 */
function validateMergedPayStub(payStub: PayStubDomainModel): void {
    // Basic validation
    if (!payStub.id) {
        console.warn('Merged pay stub missing ID');
    }

    if (!payStub.employee?.id) {
        console.warn('Merged pay stub missing employee ID');
    }

    // Validate hours are within reasonable ranges
    if (payStub.hours.standard < 0 || payStub.hours.standard > 24) {
        console.warn('Merged pay stub has invalid standard hours:', payStub.hours.standard);
    }

    if (payStub.hours.overtime < 0 || payStub.hours.overtime > 24) {
        console.warn('Merged pay stub has invalid overtime hours:', payStub.hours.overtime);
    }

    if (payStub.hours.doubletime < 0 || payStub.hours.doubletime > 24) {
        console.warn('Merged pay stub has invalid doubletime hours:', payStub.hours.doubletime);
    }

    // Validate amounts are not negative
    if (payStub.amounts.bonus < 0) {
        console.warn('Merged pay stub has negative bonus amount:', payStub.amounts.bonus);
    }

    if (payStub.amounts.expenses < 0) {
        console.warn('Merged pay stub has negative expenses amount:', payStub.amounts.expenses);
    }
}

// =============================================================================
// PERFORMANCE HOOKS
// =============================================================================

/**
 * Hook for monitoring merge performance and debugging
 * Useful during development to identify performance bottlenecks
 */
export function useMergePerformanceMonitor(
    payStubId: string,
    timesheetId: string
): {
    mergeCount: number;
    lastMergeTime: number | null;
    averageMergeTime: number;
} {
    const [mergeStats, setMergeStats] = useState({
        count: 0,
        totalTime: 0,
        lastTime: null as number | null
    });

    const draftData = useTimesheetUIStore((state) => state.getDraftForPayStub(timesheetId, payStubId));

    useEffect(() => {
        const startTime = performance.now();

        return () => {
            const endTime = performance.now();
            const duration = endTime - startTime;

            setMergeStats((prev) => ({
                count: prev.count + 1,
                totalTime: prev.totalTime + duration,
                lastTime: duration
            }));
        };
    }, [draftData]);

    return {
        mergeCount: mergeStats.count,
        lastMergeTime: mergeStats.lastTime,
        averageMergeTime: mergeStats.count > 0 ? mergeStats.totalTime / mergeStats.count : 0
    };
}

// =============================================================================
// TYPE EXPORTS
// =============================================================================

// Type exports are already included in interface declarations above


// =============================================================================
// DEFAULT EXPORT
// =============================================================================

/**
 * PHASE 3: Convert flat draft structure to domain model structure
 * Used for merging flat drafts with domain model data
 */
function convertFlatToDomainStructure(flatDraft: FlatPayStubDraft): Partial<PayStubDomainModel> {
    return {
        id: flatDraft.id || '',
        employeeId: String(flatDraft.employeeId || ''), // Convert number to string
        name: flatDraft.name || '',
        
        // UI fields from flat draft
        ui: {
            isEditing: flatDraft._uiEditingMode === 'edit',
            hasErrors: (flatDraft._uiValidationErrors?.length || 0) > 0,
            isSelected: flatDraft._uiSelected || false,
            isTemporary: flatDraft._uiIsTemporary || false,
            expanded: flatDraft._uiExpanded || false
        }
        
        // Note: hours, amounts, employee, details are not included in FlatPayStubDraft
        // They are managed at the detail level or computed from server data
    };
}

export default useMergedPayStub;
