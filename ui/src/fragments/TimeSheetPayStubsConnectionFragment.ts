/**
 * TimeSheet PayStubs Connection Fragment
 *
 * Provides a connection-based fragment for TimeSheet pay stubs that follows
 * Relay best practices for pagination and cache management. This fragment
 * enables proper use of usePaginationFragment and connection-based mutations.
 */

import { graphql } from 'react-relay';

/**
 * Fragment for TimeSheet with PayStubs connection
 * 
 * Uses @connection directive for proper Relay connection handling.
 * Includes pagination arguments with sensible defaults.
 */
export const TimeSheetPayStubsConnectionFragment = graphql`
    fragment TimeSheetPayStubsConnectionFragment_timeSheet on TimeSheet
    @argumentDefinitions(
        first: { type: "Int", defaultValue: 500 }
        after: { type: "String" }
    )
    @refetchable(queryName: "TimeSheetPayStubsConnectionRefetchQuery") {
        id
        payStubsConnection: payStubs(first: $first, after: $after) 
            @connection(key: "TimeSheetPayStubsConnectionFragment_payStubsConnection") {
            edges {
                cursor
                node {
                    id
                    employeeId
                    # totalHours is computed from individual hour fields
                    employee {
                        id
                        firstName
                        lastName
                        externalEmployeeId
                        active
                    }
                    details {
                        id
                        payStubId
                        reportLineItemId
                        workDate
                        name
                        stHours
                        otHours
                        dtHours
                        # totalHours is computed from individual hour fields
                        jobCode
                        earningsCode
                        agreementId
                        classificationId
                        subClassificationId
                        costCenter
                        hourlyRate
                        bonus
                        expenses
                    }
                }
            }
            pageInfo {
                hasNextPage
                hasPreviousPage
                startCursor
                endCursor
            }
            totalCount
        }
        payStubCount
    }
`;

export default TimeSheetPayStubsConnectionFragment;