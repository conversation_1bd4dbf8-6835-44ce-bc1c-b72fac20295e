import { create } from 'zustand';
import { persist, PersistOptions, StateStorage, StorageValue } from 'zustand/middleware';
import { TimeSheetFilterInput, TimeSheetSortInput } from '@/src/relay/__generated__/TimesheetRosterQuery.graphql';
import { sanitizeDateFieldsInFilterNodeRecursive } from '@/lib/utils/filterUtils';

// --- Generic State Definition ---
interface RosterState<FilterType, SortType> {
    activeFilters: FilterType | null;
    activeSortOrder: SortType[] | null;
    setActiveFilters: (filters: FilterType | null) => void;
    setActiveSortOrder: (sortOrder: SortType[] | null) => void;
}

// --- Factory Function ---
export function createRosterFilterStore<FilterType, SortType>(storageKey: string) {
    // Type definition for the persist middleware options, now generic
    type RosterPersist = PersistOptions<
        RosterState<FilterType, SortType>,
        Pick<RosterState<FilterType, SortType>, 'activeFilters' | 'activeSortOrder'>
    >;

    const persistOptions: RosterPersist = {
        name: storageKey, // Use the provided key for localStorage
        partialize: (state) => ({
            activeFilters: state.activeFilters,
            activeSortOrder: state.activeSortOrder
        }),
        storage: {
            getItem: (
                name
            ): StorageValue<Pick<RosterState<FilterType, SortType>, 'activeFilters' | 'activeSortOrder'>> | null => {
                const str = localStorage.getItem(name);
                if (!str) {
                    return null;
                }
                try {
                    const parsed = JSON.parse(str) as StorageValue<Pick<RosterState<FilterType, SortType>, 'activeFilters' | 'activeSortOrder'>>;

                    // A flag to identify if this store is for TimesheetRoster filters
                    const isTimesheetRosterStore = storageKey === 'timesheet-roster-filter-storage';

                    // Sanitize activeFilters if it's the TimesheetRoster store and activeFilters exist
                    if (isTimesheetRosterStore && parsed.state && parsed.state.activeFilters) {
                        const currentFilterContent = parsed.state.activeFilters;
                        
                        // Define the date keys specific to Timesheet filters
                        const timesheetDateKeys = ['payPeriodEndDate', 'workDate'];
                        // Directly call the generalized sanitize function with the specific keys
                        const sanitizedFilterContent = sanitizeDateFieldsInFilterNodeRecursive(currentFilterContent, timesheetDateKeys); 
                        
                        // Update the state with sanitized filters
                        // Ensure we don't mutate the original parsed.state if it's used elsewhere, by creating a new state object.
                        parsed.state = {
                            ...parsed.state,
                            activeFilters: sanitizedFilterContent as FilterType | null
                        };
                    }
                    return parsed;
                } catch (e) {
                    console.error(`[RosterFilterStore (${name})] Error parsing or sanitizing stored value:`, e);
                    // Optionally remove corrupted item
                    // localStorage.removeItem(name);
                    return null;
                }
            },
            setItem: (
                name,
                value: StorageValue<Pick<RosterState<FilterType, SortType>, 'activeFilters' | 'activeSortOrder'>>
            ): void => {
                try {
                    // Stringify the entire StorageValue object (including state and version)
                    const str = JSON.stringify(value);
                    localStorage.setItem(name, str);
                } catch (e) {
                    console.error(`[RosterFilterStore (${name})] Error setting item:`, e);
                }
            },
            removeItem: (name): void => {
                localStorage.removeItem(name);
            }
        }
    };

    // Create and return the store using the generic types and specific persist options
    return create<RosterState<FilterType, SortType>>()(
        persist(
            (set) => ({
                activeFilters: null,
                activeSortOrder: null,
                setActiveFilters: (filters: FilterType | null) => {
                    set({ activeFilters: filters });
                },
                setActiveSortOrder: (sortOrder: SortType[] | null) => {
                    set({ activeSortOrder: sortOrder });
                }
            }),
            persistOptions
        )
    );
}

// --- Example Usage (Timesheet Specific Store) ---
// You would typically put this in a separate file or where it's needed
export const useTimesheetRosterFilterStore = createRosterFilterStore<TimeSheetFilterInput, TimeSheetSortInput>(
    'timesheet-roster-filter-storage'
);
