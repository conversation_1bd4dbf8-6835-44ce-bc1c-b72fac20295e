import { TimesheetError, ErrorSeverity, ErrorType } from '@/errorHandling';
import { sanitizeMutationVariables, sanitizePayload, createSafeErrorContext } from './pii-sanitizer';

/**
 * Error reporting configuration
 */
interface ErrorReportingConfig {
  apiEndpoint?: string;
  apiKey?: string;
  enableConsoleLogging: boolean;
  enableRemoteLogging: boolean;
  batchSize: number;
  flushInterval: number;
  maxRetries: number;
}

/**
 * Error report interface
 */
interface ErrorReport {
  id: string;
  timestamp: Date;
  error: TimesheetError;
  userAgent: string;
  url: string;
  userId?: string;
  sessionId?: string;
  buildVersion?: string;
  environment: string;
}

/**
 * Error reporting service for timesheet errors
 */
class ErrorReportingService {
  private config: ErrorReportingConfig;
  private errorQueue: ErrorReport[] = [];
  private flushTimer: ReturnType<typeof setTimeout> | null = null;
  private sessionId: string;

  constructor(config: Partial<ErrorReportingConfig> = {}) {
    this.config = {
      enableConsoleLogging: true,
      enableRemoteLogging: process.env.NODE_ENV === 'production',
      batchSize: 10,
      flushInterval: 30000, // 30 seconds
      maxRetries: 3,
      ...config
    };
    
    this.sessionId = this.generateSessionId();
    this.startFlushTimer();
  }

  /**
   * Reports an error to the configured reporting systems
   */
  public reportError(error: TimesheetError, context: {
    userId?: string;
    action?: string;
    component?: string;
    additionalContext?: Record<string, unknown>;
  } = {}): void {
    // Phase 5: Sanitize all context data
    const sanitizedAdditionalContext = context.additionalContext 
      ? sanitizePayload(context.additionalContext) 
      : undefined;
    
    const sanitizedContext = {
      ...context,
      additionalContext: sanitizedAdditionalContext
    };
    
    // Sanitize the error context as well
    const sanitizedErrorContext = error.enhancedError.context 
      ? sanitizePayload(error.enhancedError.context)
      : {};
    
    const enhancedErrorWithContext = new TimesheetError({
      ...error.enhancedError,
      context: {
        ...sanitizedErrorContext,
        reportingContext: sanitizedContext
      }
    });

    const report: ErrorReport = {
      id: `report_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      error: enhancedErrorWithContext,
      userAgent: navigator.userAgent,
      url: window.location.href,
      userId: context.userId,
      sessionId: this.sessionId,
      buildVersion: process.env.REACT_APP_VERSION || 'unknown',
      environment: process.env.NODE_ENV || 'development'
    };

    // Console logging
    if (this.config.enableConsoleLogging) {
      this.logToConsole(report);
    }

    // Add to queue for batch processing
    if (this.config.enableRemoteLogging) {
      this.addToQueue(report);
    }

    // Immediate reporting for critical errors
    if (error.enhancedError.severity === ErrorSeverity.CRITICAL) {
      this.flushImmediately();
    }
  }

  /**
   * Reports a fragment reference error specifically
   */
  public reportFragmentError(
    fragmentName: string,
    componentName: string,
    error: Error,
    additionalContext: Record<string, unknown> = {}
  ): void {
    const timesheetError = new TimesheetError({
      type: ErrorType.FRAGMENT,
      severity: ErrorSeverity.HIGH,
      message: `Fragment reference error in ${componentName}: ${fragmentName}`,
      userMessage: 'Data loading error occurred. Please refresh the page.',
      technical: `Fragment ${fragmentName} in component ${componentName}: ${error.message}`,
      suggestions: [
        'Refresh the page',
        'Clear browser cache',
        'Contact support if problem persists'
      ],
      canRetry: true,
      context: {
        fragmentName,
        componentName,
        originalError: error,
        stack: error.stack,
        ...additionalContext
      }
    });

    this.reportError(timesheetError, {
      action: 'fragment-reference',
      component: componentName,
      additionalContext: { fragmentName, ...additionalContext }
    });
  }

  /**
   * Reports a network error specifically
   */
  public reportNetworkError(
    operation: string,
    error: Error,
    additionalContext: Record<string, unknown> = {}
  ): void {
    const timesheetError = new TimesheetError({
      type: ErrorType.NETWORK,
      severity: ErrorSeverity.MEDIUM,
      message: `Network error during ${operation}: ${error.message}`,
      userMessage: 'Connection issue occurred. Please check your internet connection.',
      technical: `Network operation ${operation} failed: ${error.message}`,
      canRetry: true,
      context: {
        operation,
        originalError: error,
        stack: error.stack,
        ...additionalContext
      }
    });

    this.reportError(timesheetError, {
      action: 'network-operation',
      additionalContext: { operation, ...additionalContext }
    });
  }

  /**
   * Reports a mutation error specifically
   */
  public reportMutationError(
    mutationName: string,
    variables: Record<string, unknown>,
    error: Error,
    additionalContext: Record<string, unknown> = {}
  ): void {
    // Phase 5: Sanitize mutation variables to remove PII
    const sanitizedVariables = sanitizeMutationVariables(variables);
    const sanitizedContext = sanitizePayload(additionalContext);
    
    const timesheetError = new TimesheetError({
      type: ErrorType.MUTATION,
      severity: ErrorSeverity.HIGH,
      message: `Mutation error in ${mutationName}: ${error.message}`,
      userMessage: 'Unable to save changes at this time.',
      technical: `Mutation ${mutationName} failed: ${error.message}`,
      canRetry: true,
      context: {
        mutationName,
        variables: sanitizedVariables,
        originalError: createSafeErrorContext(error),
        stack: error.stack,
        ...sanitizedContext
      }
    });

    this.reportError(timesheetError, {
      action: 'mutation-execution',
      additionalContext: { mutationName, variables: sanitizedVariables, ...sanitizedContext }
    });
  }

  /**
   * Flushes the error queue immediately
   */
  public flushImmediately(): void {
    if (this.errorQueue.length > 0) {
      this.sendBatch([...this.errorQueue]);
      this.errorQueue = [];
    }
  }

  /**
   * Destroys the service and cleans up resources
   */
  public destroy(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = null;
    }
    this.flushImmediately();
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private logToConsole(report: ErrorReport): void {
    const { error } = report;
    const logMethod = this.getConsoleLogMethod(error.enhancedError.severity);
    
    logMethod(`[ErrorReporting] ${error.enhancedError.type} - ${error.enhancedError.userMessage}`, {
      id: report.id,
      severity: error.enhancedError.severity,
      technical: error.enhancedError.technical,
      suggestions: error.enhancedError.suggestions,
      canRetry: error.enhancedError.canRetry,
      context: error.enhancedError.context,
      url: report.url,
      timestamp: report.timestamp
    });
  }

  private getConsoleLogMethod(severity: ErrorSeverity): typeof console.log {
    switch (severity) {
      case ErrorSeverity.LOW:
        return console.info;
      case ErrorSeverity.MEDIUM:
        return console.warn;
      case ErrorSeverity.HIGH:
      case ErrorSeverity.CRITICAL:
        return console.error;
      default:
        return console.log;
    }
  }

  private addToQueue(report: ErrorReport): void {
    this.errorQueue.push(report);
    
    if (this.errorQueue.length >= this.config.batchSize) {
      this.flushImmediately();
    }
  }

  private startFlushTimer(): void {
    this.flushTimer = setInterval(() => {
      if (this.errorQueue.length > 0) {
        this.sendBatch([...this.errorQueue]);
        this.errorQueue = [];
      }
    }, this.config.flushInterval);
  }

  private async sendBatch(reports: ErrorReport[], retryCount: number = 0): Promise<void> {
    if (!this.config.apiEndpoint || reports.length === 0) {
      return;
    }

    try {
      const response = await fetch(this.config.apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(this.config.apiKey && { 'Authorization': `Bearer ${this.config.apiKey}` })
        },
        body: JSON.stringify({
          reports,
          metadata: {
            serviceVersion: '1.0.0',
            timestamp: new Date().toISOString(),
            batchSize: reports.length
          }
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      console.log(`[ErrorReporting] Successfully sent batch of ${reports.length} error reports`);
    } catch (error) {
      console.error('[ErrorReporting] Failed to send error reports:', error);
      
      // Retry with exponential backoff
      if (retryCount < this.config.maxRetries) {
        const delay = Math.pow(2, retryCount) * 1000;
        setTimeout(() => {
          this.sendBatch(reports, retryCount + 1);
        }, delay);
      }
    }
  }
}

/**
 * Global error reporting service instance
 */
export const errorReportingService = new ErrorReportingService({
  apiEndpoint: process.env.REACT_APP_ERROR_REPORTING_ENDPOINT,
  apiKey: process.env.REACT_APP_ERROR_REPORTING_API_KEY,
  enableRemoteLogging: process.env.NODE_ENV === 'production'
});

/**
 * Convenience function for reporting errors
 */
export function reportError(error: TimesheetError, context?: {
  userId?: string;
  action?: string;
  component?: string;
  additionalContext?: Record<string, unknown>;
}): void {
  errorReportingService.reportError(error, context);
}

/**
 * Convenience function for reporting fragment errors
 */
export function reportFragmentError(
  fragmentName: string,
  componentName: string,
  error: Error,
  additionalContext?: Record<string, unknown>
): void {
  errorReportingService.reportFragmentError(fragmentName, componentName, error, additionalContext);
}

/**
 * Convenience function for reporting network errors
 */
export function reportNetworkError(
  operation: string,
  error: Error,
  additionalContext?: Record<string, unknown>
): void {
  errorReportingService.reportNetworkError(operation, error, additionalContext);
}

/**
 * Convenience function for reporting mutation errors
 */
export function reportMutationError(
  mutationName: string,
  variables: Record<string, unknown>,
  error: Error,
  additionalContext?: Record<string, unknown>
): void {
  errorReportingService.reportMutationError(mutationName, variables, error, additionalContext);
}

// Clean up on window unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    errorReportingService.destroy();
  });
}