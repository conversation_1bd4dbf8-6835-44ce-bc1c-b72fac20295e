import { GlobalID, GlobalIDGuards, GlobalIDAssertions } from '../types/GlobalID';

/**
 * Type mapping for Relay Global ID types
 * Add new types as they are discovered in the GraphQL schema
 */
export type RelayNodeType =
    | 'Agreement'
    | 'ClassificationName'
    | 'SubClassification'
    | 'Employee'
    | 'Employer'
    | 'TimeSheet'
    | 'PayStub'
    | 'PayStubDetail'
    | 'Organization'
    | 'User'
    | 'Chapter'
    | 'Union'
    | 'Contract'
    | 'Benefit';

/**
 * Result from parsing a Relay Global ID
 */
export interface ParsedGlobalId {
    /** The type of the entity */
    type: string;
    /** The raw ID of the entity */
    id: string;
}

/**
 * Validation result for ID operations
 */
export interface IdValidationResult {
    /** Whether the ID is valid */
    isValid: boolean;
    /** Error message if validation failed */
    error?: string;
    /** Normalized ID if validation succeeded */
    normalizedId?: string;
}

/**
 * Centralized service for handling Relay Global ID conversions and operations
 *
 * This service provides a consistent interface for working with Relay Global IDs
 * throughout the application, ensuring proper encoding/decoding and type safety.
 *
 * Relay Global IDs follow the format: base64(TypeName:rawId)
 * For example: base64("Agreement:123") -> "QWdyZWVtZW50OjEyMw=="
 */
export class RelayIdService {
    /**
     * Converts a type and raw ID to a Relay Global ID
     *
     * @param type - The GraphQL type name
     * @param id - The raw ID (string or number)
     * @returns Base64 encoded global ID
     *
     * @example
     * ```typescript
     * const globalId = RelayIdService.toGlobalId('Agreement', '123');
     * // Returns: "QWdyZWVtZW50OjEyMw=="
     * ```
     */
    static toGlobalId(type: RelayNodeType, id: string | number | null | undefined): string {
        // Handle null/undefined/empty values with default fallback
        if (id === null || id === undefined || id === '') {
            return this.toGlobalId(type, '0');
        }

        // Ensure ID is a string
        const stringId = String(id);

        // Validate the type and ID
        const validation = this.validateTypeAndId(type, stringId);
        if (!validation.isValid) {
            console.warn(`Invalid ID conversion: ${validation.error}`);
            return this.toGlobalId(type, '0');
        }

        // Create the global ID string and encode it
        const globalIdString = `${type}:${stringId}`;
        return btoa(globalIdString);
    }

    /**
     * Parses a Relay Global ID back to its type and raw ID
     *
     * @param globalId - The base64 encoded global ID
     * @returns Object containing type and raw ID
     *
     * @example
     * ```typescript
     * const { type, id } = RelayIdService.fromGlobalId("QWdyZWVtZW50OjEyMw==");
     * // Returns: { type: "Agreement", id: "123" }
     * ```
     */
    static fromGlobalId(globalId: string): ParsedGlobalId {
        try {
            if (!globalId || typeof globalId !== 'string') {
                throw new Error('Invalid global ID: must be a non-empty string');
            }

            // Decode the base64 string
            const decodedString = atob(globalId);

            // Split on the first colon to separate type and ID
            const colonIndex = decodedString.indexOf(':');
            if (colonIndex === -1) {
                throw new Error('Invalid global ID format: missing type separator');
            }

            const type = decodedString.substring(0, colonIndex);
            const id = decodedString.substring(colonIndex + 1);

            if (!type || !id) {
                throw new Error('Invalid global ID format: empty type or ID');
            }

            return { type, id };
        } catch (error) {
            console.error('Error parsing global ID:', error);
            return { type: 'Unknown', id: '0' };
        }
    }

    /**
     * Converts an ID to numeric format, handling various input types
     *
     * @param id - The ID to convert (string, number, or global ID)
     * @returns Numeric ID or 0 if conversion fails
     *
     * @example
     * ```typescript
     * const numericId = RelayIdService.toNumericId("123"); // Returns: 123
     * const numericId2 = RelayIdService.toNumericId("QWdyZWVtZW50OjEyMw=="); // Returns: 123
     * ```
     */
    static toNumericId(id: string | number | null | undefined): number {
        if (id === null || id === undefined) {
            return 0;
        }

        // If it's already a number, return it
        if (typeof id === 'number') {
            return Math.max(0, Math.floor(id));
        }

        // If it's a string, try to parse it
        const stringId = String(id);

        // Check if it looks like a base64 encoded global ID
        if (this.isGlobalId(stringId)) {
            const { id: rawId } = this.fromGlobalId(stringId);
            return this.toNumericId(rawId);
        }

        // Try to parse as a regular number
        const parsed = parseInt(stringId, 10);
        return isNaN(parsed) ? 0 : Math.max(0, parsed);
    }

    /**
     * Checks if the supplied string represents a positive (non-zero) integer.
     * This intentionally rejects 0, negative numbers, decimals, and any
     * non-digit characters.
     */
    static isPositiveNumericString(value: unknown): value is string {
        return typeof value === 'string' && /^[1-9]\d*$/.test(value);
    }

    /**
     * Attempts to extract a positive numeric ID from the given value. Returns
     * null if the value is not a valid positive integer or a valid Relay Global
     * ID encoding one.
     */
    static parseNumericId(value: string | number | null | undefined): number | null {
        if (value === null || value === undefined) return null;

        // Accept raw numbers directly
        if (typeof value === 'number' && Number.isInteger(value) && value > 0) {
            return value;
        }

        const str = String(value).trim();
        if (this.isPositiveNumericString(str)) {
            return parseInt(str, 10);
        }

        // If it looks like a Global ID, decode then validate the numeric part
        if (this.isGlobalId(str)) {
            const { id: raw } = this.fromGlobalId(str);
            return this.isPositiveNumericString(raw) ? parseInt(raw, 10) : null;
        }

        return null;
    }

    /**
     * Validates if a string is a valid Relay Global ID
     *
     * @param value - The value to validate
     * @returns True if the value is a valid global ID
     */
    static isGlobalId(value: string): boolean {
        // Quick sanity checks to avoid expensive/invalid base64 decode attempts
        if (!value || typeof value !== 'string') {
            return false;
        }

        // Base64-encoded IDs must have length divisible by 4 and contain only valid chars
        if (value.length % 4 !== 0 || !/^[A-Za-z0-9+/]+={0,2}$/.test(value)) {
            return false;
        }

        try {
            const { type, id } = this.fromGlobalId(value);
            return type !== 'Unknown' && id !== '0';
        } catch {
            return false;
        }
    }

    /**
     * Checks if a Relay Global ID has a numeric raw ID part
     *
     * @param id - The Relay Global ID to check
     * @returns True if the ID is a valid global ID whose decoded raw part is all digits
     */
    static isNumericGlobalId(id: string): boolean {
        if (!this.isGlobalId(id)) {
            return false;
        }

        const { id: raw } = this.fromGlobalId(id);
        return /^\d+$/.test(raw);
    }

    /**
     * Safely converts to global ID only if the value represents a numeric ID
     *
     * @param type - The GraphQL type name
     * @param value - The value to convert (string or number)
     * @returns Global ID if value is numeric, undefined otherwise
     */
    static safeToGlobalId(type: RelayNodeType, value: string | number | null | undefined): string | undefined {
        if (value === null || value === undefined || value === '') {
            return undefined;
        }

        // Convert to string for checking
        const stringValue = String(value);

        // Check if it's a numeric value
        if (typeof value === 'number' && Number.isInteger(value) && value > 0) {
            return this.toGlobalId(type, value);
        }

        // Check if string represents a positive integer
        if (typeof value === 'string' && /^\d+$/.test(stringValue) && parseInt(stringValue, 10) > 0) {
            return this.toGlobalId(type, value);
        }

        return undefined;
    }

    /**
     * Validates a type and ID combination
     *
     * @param type - The GraphQL type name
     * @param id - The raw ID
     * @returns Validation result with error details
     */
    static validateTypeAndId(type: string, id: string): IdValidationResult {
        // Check type
        if (!type || typeof type !== 'string') {
            return {
                isValid: false,
                error: 'Type must be a non-empty string'
            };
        }

        // Check ID
        if (!id || typeof id !== 'string') {
            return {
                isValid: false,
                error: 'ID must be a non-empty string'
            };
        }

        // Check for invalid characters in type (should be alphanumeric)
        if (!/^[a-zA-Z][a-zA-Z0-9]*$/.test(type)) {
            return {
                isValid: false,
                error: 'Type must start with a letter and contain only alphanumeric characters'
            };
        }

        return {
            isValid: true,
            normalizedId: id
        };
    }

    /**
     * Batch converts multiple IDs to global IDs
     *
     * @param type - The GraphQL type name
     * @param ids - Array of raw IDs
     * @returns Array of global IDs in the same order
     */
    static batchToGlobalId(type: RelayNodeType, ids: (string | number)[]): string[] {
        return ids.map((id) => this.toGlobalId(type, id));
    }

    /**
     * Batch parses multiple global IDs
     *
     * @param globalIds - Array of global IDs
     * @returns Array of parsed results
     */
    static batchFromGlobalId(globalIds: string[]): ParsedGlobalId[] {
        return globalIds.map((globalId) => this.fromGlobalId(globalId));
    }

    /**
     * Safely extracts the raw ID from a global ID, with fallback
     *
     * @param globalId - The global ID
     * @param fallback - Fallback value if parsing fails
     * @returns Raw ID or fallback
     */
    static safeExtractId(globalId: string | null | undefined, fallback = '0'): string {
        if (!globalId) {
            return fallback;
        }

        try {
            const { id } = this.fromGlobalId(globalId);
            return id !== '0' ? id : fallback;
        } catch {
            return fallback;
        }
    }

    /**
     * Creates a comparison function for global IDs of the same type
     * Useful for sorting arrays of global IDs
     *
     * @param type - The expected type for all IDs
     * @returns Comparison function for sorting
     */
    static createComparator(type: RelayNodeType) {
        return (a: string, b: string): number => {
            const parsedA = this.fromGlobalId(a);
            const parsedB = this.fromGlobalId(b);

            // Type check
            if (parsedA.type !== type || parsedB.type !== type) {
                console.warn(`ID type mismatch in comparator. Expected: ${type}`);
            }

            // Compare numeric IDs
            const numericA = this.toNumericId(parsedA.id);
            const numericB = this.toNumericId(parsedB.id);

            return numericA - numericB;
        };
    }

    /**
     * Type-safe conversion to PayStub Global ID
     * @param id - Raw ID or existing global ID
     * @returns Typed PayStub Global ID
     */
    static toPayStubGlobalId(id: string | number): GlobalID<'PayStub'> {
        const globalId = this.toGlobalId('PayStub', id);
        return globalId as GlobalID<'PayStub'>;
    }

    /**
     * Type-safe conversion to PayStubDetail Global ID
     * @param id - Raw ID or existing global ID
     * @returns Typed PayStubDetail Global ID
     */
    static toPayStubDetailGlobalId(id: string | number): GlobalID<'PayStubDetail'> {
        const globalId = this.toGlobalId('PayStubDetail', id);
        return globalId as GlobalID<'PayStubDetail'>;
    }

    /**
     * Type-safe conversion to Employee Global ID
     * @param id - Raw ID or existing global ID
     * @returns Typed Employee Global ID
     */
    static toEmployeeGlobalId(id: string | number): GlobalID<'Employee'> {
        const globalId = this.toGlobalId('Employee', id);
        return globalId as GlobalID<'Employee'>;
    }

    /**
     * Type-safe conversion to TimeSheet Global ID
     * @param id - Raw ID or existing global ID
     * @returns Typed TimeSheet Global ID
     */
    static toTimeSheetGlobalId(id: string | number): GlobalID<'TimeSheet'> {
        const globalId = this.toGlobalId('TimeSheet', id);
        return globalId as GlobalID<'TimeSheet'>;
    }

    /**
     * Validates and converts unknown value to typed Global ID
     * @param value - Unknown value to convert
     * @param expectedType - Expected entity type
     * @returns Typed Global ID
     * @throws Error if conversion fails
     */
    static toTypedGlobalId<T extends RelayNodeType>(value: unknown, expectedType: T): GlobalID<T> {
        if (typeof value !== 'string' && typeof value !== 'number') {
            throw new Error(`Cannot convert ${typeof value} to Global ID. Expected string or number.`);
        }

        const globalId = this.toGlobalId(expectedType, value);
        
        // Validate the result
        if (GlobalIDGuards.isGlobalIDFormat(globalId)) {
            const { type } = this.fromGlobalId(globalId);
            if (type !== expectedType) {
                throw new Error(`Expected ${expectedType} Global ID, got ${type}`);
            }
        }

        return globalId as GlobalID<T>;
    }
}

/**
 * Legacy compatibility: Export individual functions for backward compatibility
 * @deprecated Use RelayIdService.toGlobalId instead
 */
export const toGlobalId = RelayIdService.toGlobalId.bind(RelayIdService);

/**
 * Legacy compatibility: Export individual functions for backward compatibility
 * @deprecated Use RelayIdService.fromGlobalId instead
 */
export const fromGlobalId = RelayIdService.fromGlobalId.bind(RelayIdService);

/**
 * Legacy compatibility: Export individual functions for backward compatibility
 * @deprecated Use RelayIdService.toNumericId instead
 */
export const toNumericId = RelayIdService.toNumericId.bind(RelayIdService);
