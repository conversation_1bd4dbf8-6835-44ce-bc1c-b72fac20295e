/**
 * Type Guards and Runtime Validation Utilities
 *
 * Provides comprehensive type guards and runtime validation functions to eliminate
 * unsafe type assertions throughout the timesheet codebase. These guards ensure
 * type safety at runtime and provide meaningful error messages for debugging.
 */

import type { ModifiablePayStub, ModifiablePayStubDetail, ValidationError } from '../types/timesheet-detail';
import type { PayStub, PayStubDetail } from '../types/relay-ui-extensions';
import type { ModifyPayStubInput, ModifyPayStubDetailInput } from '@/src/relay/__generated__/useTimesheetSaverModifyMutation.graphql';
import type { FragmentRefs } from 'relay-runtime';
import type { ConversionResult } from '../utils/validationUtils';

/**
 * Type guard for PayStub validation compatibility with enhanced error handling
 *
 * Validates that an object has the minimum structure required for validation.
 * This is more lenient than strict PayStub type checking but includes comprehensive validation.
 *
 * @param obj Object to validate
 * @param errorCollector Optional array to collect detailed validation errors
 * @returns true if object can be safely used for validation
 */
export function isValidationCompatiblePayStub(obj: unknown, errorCollector?: string[]): obj is PayStub {
    if (!obj || typeof obj !== 'object') {
        errorCollector?.push('PayStub must be a non-null object');
        return false;
    }

    const candidate = obj as Record<string, unknown>;
    let isValid = true;

    // Check required ID field
    if (typeof candidate.id !== 'string' || candidate.id.trim() === '') {
        errorCollector?.push('PayStub.id must be a non-empty string');
        isValid = false;
    }

    // Check details array
    if (!Array.isArray(candidate.details)) {
        errorCollector?.push('PayStub.details must be an array');
        isValid = false;
    } else {
        // Validate each detail for basic structure
        candidate.details.forEach((detail, index) => {
            if (!detail || typeof detail !== 'object') {
                errorCollector?.push(`PayStub.details[${index}] must be an object`);
                isValid = false;
            } else {
                const detailObj = detail as Record<string, unknown>;
                if (typeof detailObj.id !== 'string') {
                    errorCollector?.push(`PayStub.details[${index}].id must be a string`);
                    isValid = false;
                }
            }
        });
    }

    // Check employee ID with enhanced validation
    if (
        candidate.employeeId !== null &&
        candidate.employeeId !== undefined &&
        typeof candidate.employeeId !== 'string' &&
        typeof candidate.employeeId !== 'number'
    ) {
        errorCollector?.push('PayStub.employeeId must be string, number, null, or undefined');
        isValid = false;
    }

    // Check employee ID format if it's a string (should be non-empty)
    if (typeof candidate.employeeId === 'string' && candidate.employeeId.trim() === '') {
        errorCollector?.push('PayStub.employeeId string must be non-empty');
        isValid = false;
    }

    // Check employee ID if it's a number (should be positive)
    if (typeof candidate.employeeId === 'number' && candidate.employeeId <= 0) {
        errorCollector?.push('PayStub.employeeId number must be positive');
        isValid = false;
    }

    return isValid;
}

/**
 * Type guard for PayStubDetail validation compatibility with enhanced validation
 *
 * Validates that an object has the minimum structure required for validation.
 * Includes comprehensive checks for edge cases and provides detailed error reporting.
 *
 * @param obj Object to validate
 * @param errorCollector Optional array to collect detailed validation errors
 * @returns true if object can be safely used for validation
 */
export function isValidationCompatiblePayStubDetail(obj: unknown, errorCollector?: string[]): obj is PayStubDetail {
    if (!obj || typeof obj !== 'object') {
        errorCollector?.push('PayStubDetail must be a non-null object');
        return false;
    }

    const candidate = obj as Record<string, unknown>;
    let isValid = true;

    // Check required ID field
    if (typeof candidate.id !== 'string' || candidate.id.trim() === '') {
        errorCollector?.push('PayStubDetail.id must be a non-empty string');
        isValid = false;
    }

    // For validation compatibility, object must have more than just an ID
    // Validation function expects to access various fields, so we need at least basic structure
    const hasMinimumStructure =
        Object.keys(candidate).length > 1 ||
        'workDate' in candidate ||
        'stHours' in candidate ||
        'otHours' in candidate ||
        'dtHours' in candidate ||
        'payStubId' in candidate ||
        'employeeId' in candidate;

    if (!hasMinimumStructure) {
        errorCollector?.push('PayStubDetail must have fields beyond just id for validation compatibility');
        isValid = false;
    }

    // Check payStubId with enhanced validation
    if (candidate.payStubId !== null && candidate.payStubId !== undefined) {
        if (typeof candidate.payStubId !== 'string' || candidate.payStubId.trim() === '') {
            errorCollector?.push('PayStubDetail.payStubId must be null or a non-empty string');
            isValid = false;
        }
    }

    // Check workDate with enhanced validation
    if (candidate.workDate !== null && candidate.workDate !== undefined) {
        if (typeof candidate.workDate !== 'string') {
            errorCollector?.push('PayStubDetail.workDate must be null or a string');
            isValid = false;
        } else {
            // Basic date format validation
            const dateObj = new Date(candidate.workDate);
            if (isNaN(dateObj.getTime())) {
                errorCollector?.push('PayStubDetail.workDate must be a valid date string');
                isValid = false;
            }
        }
    }

    // Check employeeId with enhanced validation
    if (candidate.employeeId !== null && candidate.employeeId !== undefined) {
        if (typeof candidate.employeeId !== 'string' || candidate.employeeId.trim() === '') {
            errorCollector?.push('PayStubDetail.employeeId must be null or a non-empty string');
            isValid = false;
        }
    }

    // Validate numeric fields (hours, rates, amounts)
    const numericFields = ['stHours', 'otHours', 'dtHours', 'hourlyRate', 'bonus', 'expenses', 'totalHours'] as const;
    numericFields.forEach((field) => {
        const value = candidate[field];
        if (value !== null && value !== undefined) {
            if (typeof value !== 'number' || isNaN(value)) {
                errorCollector?.push(`PayStubDetail.${field} must be null, undefined, or a valid number`);
                isValid = false;
            } else if (value < 0) {
                errorCollector?.push(`PayStubDetail.${field} must not be negative`);
                isValid = false;
            }
        }
    });

    // Validate ID fields (classification, agreement, etc.)
    const idFields = ['agreementId', 'classificationId', 'subClassificationId', 'reportLineItemId'] as const;
    idFields.forEach((field) => {
        const value = candidate[field];
        if (value !== null && value !== undefined) {
            if (typeof value !== 'number' || !Number.isInteger(value) || value < 0) {
                errorCollector?.push(`PayStubDetail.${field} must be null, undefined, or a non-negative integer`);
                isValid = false;
            }
        }
    });

    // Validate string fields
    const stringFields = ['jobCode', 'costCenter', 'earningsCode', 'name'] as const;
    stringFields.forEach((field) => {
        const value = candidate[field];
        if (value !== null && value !== undefined && typeof value !== 'string') {
            errorCollector?.push(`PayStubDetail.${field} must be null, undefined, or a string`);
            isValid = false;
        }
    });

    // Validate boolean fields
    const booleanFields = ['delete', 'isTemporary'] as const;
    booleanFields.forEach((field) => {
        const value = candidate[field];
        if (value !== null && value !== undefined && typeof value !== 'boolean') {
            errorCollector?.push(`PayStubDetail.${field} must be null, undefined, or a boolean`);
            isValid = false;
        }
    });

    return isValid;
}

/**
 * Type guard for ModifyPayStubInput validation
 *
 * Validates that an object conforms to the ModifyPayStubInput interface.
 *
 * @param obj Object to validate
 * @returns true if object is a valid ModifyPayStubInput
 */
export function isModifyPayStubInput(obj: unknown): obj is ModifyPayStubInput {
    if (!obj || typeof obj !== 'object') {
        return false;
    }

    const candidate = obj as Record<string, unknown>;

    // Check required fields for ModifyPayStubInput
    // According to the GraphQL schema, employeeId is required string (not nullable)
    return (
        typeof candidate.id === 'string' &&
        typeof candidate.employeeId === 'string' && // Required string, not nullable
        Array.isArray(candidate.details) &&
        // All details must be valid ModifyPayStubDetailInput
        candidate.details.every((detail: unknown) => isModifyPayStubDetailInput(detail))
    );
}

/**
 * Type guard for ModifyPayStubDetailInput validation
 *
 * Validates that an object conforms to the ModifyPayStubDetailInput interface.
 *
 * @param obj Object to validate
 * @returns true if object is a valid ModifyPayStubDetailInput
 */
export function isModifyPayStubDetailInput(obj: unknown): obj is ModifyPayStubDetailInput {
    if (!obj || typeof obj !== 'object') {
        return false;
    }

    const candidate = obj as Record<string, unknown>;

    // Check required fields for ModifyPayStubDetailInput
    return (
        typeof candidate.id === 'string' &&
        (typeof candidate.workDate === 'string' || candidate.workDate === null) &&
        (typeof candidate.reportLineItemId === 'number' || candidate.reportLineItemId === null) &&
        // Check optional numeric fields
        (typeof candidate.stHours === 'number' || candidate.stHours === null || candidate.stHours === undefined) &&
        (typeof candidate.otHours === 'number' || candidate.otHours === null || candidate.otHours === undefined) &&
        (typeof candidate.dtHours === 'number' || candidate.dtHours === null || candidate.dtHours === undefined) &&
        (typeof candidate.agreementId === 'number' || candidate.agreementId === null || candidate.agreementId === undefined) &&
        (typeof candidate.classificationId === 'number' ||
            candidate.classificationId === null ||
            candidate.classificationId === undefined) &&
        (typeof candidate.hourlyRate === 'number' || candidate.hourlyRate === null || candidate.hourlyRate === undefined)
    );
}

/**
 * Type guard for ModifiablePayStub
 *
 * Validates that an object conforms to the ModifiablePayStub interface.
 *
 * @param obj Object to validate
 * @returns true if object is a valid ModifiablePayStub
 */
export function isModifiablePayStub(obj: unknown): obj is ModifiablePayStub {
    if (!obj || typeof obj !== 'object') {
        return false;
    }

    const candidate = obj as Record<string, unknown>;

    return (
        typeof candidate.id === 'string' &&
        (typeof candidate.name === 'string' || candidate.name === null) &&
        (typeof candidate.employeeId === 'string' || candidate.employeeId === null) &&
        (typeof candidate.totalHours === 'number' || candidate.totalHours === null) &&
        Array.isArray(candidate.details) &&
        // All details must be valid ModifiablePayStubDetail
        candidate.details.every((detail: unknown) => isModifiablePayStubDetail(detail))
    );
}

/**
 * Type guard for ModifiablePayStubDetail
 *
 * Validates that an object conforms to the ModifiablePayStubDetail interface.
 *
 * @param obj Object to validate
 * @returns true if object is a valid ModifiablePayStubDetail
 */
export function isModifiablePayStubDetail(obj: unknown): obj is ModifiablePayStubDetail {
    if (!obj || typeof obj !== 'object') {
        return false;
    }

    const candidate = obj as Record<string, unknown>;

    return (
        typeof candidate.id === 'string' &&
        (typeof candidate.workDate === 'string' || candidate.workDate === null) &&
        (typeof candidate.stHours === 'number' || candidate.stHours === null || candidate.stHours === undefined) &&
        (typeof candidate.otHours === 'number' || candidate.otHours === null || candidate.otHours === undefined) &&
        (typeof candidate.dtHours === 'number' || candidate.dtHours === null || candidate.dtHours === undefined)
    );
}

/**
 * Converts modifiable pay stubs to validation-compatible format with enhanced error handling.
 *
 * This function safely transforms modifiable pay stubs to the format expected
 * by the validation function, collecting conversion errors instead of throwing.
 * It accepts nullable business fields and emits ValidationError objects for UI highlighting.
 * Enhanced with comprehensive type checking and edge case handling.
 *
 * @param modifiablePayStubs Array of modifiable pay stubs
 * @returns ConversionResult with successfully converted pay stubs and any conversion errors
 */
export function convertToValidationPayStubs(modifiablePayStubs: ReadonlyArray<ModifiablePayStub>): ConversionResult {
    const conversionErrors: ValidationError[] = [];
    const validationPayStubs: PayStub[] = [];

    // Validate input array
    if (!Array.isArray(modifiablePayStubs)) {
        conversionErrors.push({
            field: 'modifiablePayStubs',
            message: 'Input must be an array of modifiable pay stubs',
            payStubId: 'unknown',
            columnUid: 'conversion',
            employeeName: undefined,
            workDate: undefined,
            severity: 'error'
        });
        return { payStubs: [], conversionErrors };
    }

    modifiablePayStubs.forEach((stub, index) => {
        const stubErrorCollector: string[] = [];

        try {
            // Enhanced validation of the stub object
            if (!stub || typeof stub !== 'object') {
                conversionErrors.push({
                    field: `payStub[${index}]`,
                    message: 'Pay stub must be a non-null object',
                    payStubId: `stub-${index}`,
                    columnUid: 'conversion',
                    employeeName: undefined,
                    workDate: undefined,
                    severity: 'error'
                });
                return;
            }

            // Validate and safely extract ID
            let stubId: string;
            if (typeof stub.id === 'string' && stub.id.trim() !== '') {
                stubId = stub.id;
            } else if (typeof stub.id === 'number') {
                stubId = String(stub.id);
            } else {
                stubId = `stub-${index}`;
                stubErrorCollector.push(`Invalid or missing ID, using fallback: ${stubId}`);
            }

            // Enhanced employee ID validation and conversion
            let employeeId: string | null = null;
            if (stub.employeeId !== null && stub.employeeId !== undefined) {
                if (typeof stub.employeeId === 'string' && stub.employeeId.trim() !== '') {
                    employeeId = stub.employeeId;
                } else if (typeof stub.employeeId === 'number' && stub.employeeId > 0) {
                    employeeId = String(stub.employeeId);
                } else {
                    stubErrorCollector.push('Invalid employeeId format - must be non-empty string or positive number');
                }
            }

            // Create validation-compatible details with enhanced conversion
            const validationDetails: PayStubDetail[] = [];
            const details = Array.isArray(stub.details) ? stub.details : [];

            details.forEach((detail: any, detailIndex: number) => {
                const detailErrorCollector: string[] = [];

                try {
                    // Enhanced detail validation
                    if (!detail || typeof detail !== 'object') {
                        conversionErrors.push({
                            field: `payStub[${index}].details[${detailIndex}]`,
                            message: 'Detail must be a non-null object',
                            payStubId: stubId,
                            detailId: `detail-${index}-${detailIndex}`,
                            columnUid: 'conversion',
                            employeeName: stub.name || undefined,
                            workDate: undefined,
                            severity: 'error'
                        });
                        return;
                    }

                    // Safely extract and validate detail ID
                    let detailId: string;
                    if (typeof detail.id === 'string' && detail.id.trim() !== '') {
                        detailId = detail.id;
                    } else if (typeof detail.id === 'number') {
                        detailId = String(detail.id);
                    } else {
                        detailId = `detail-${index}-${detailIndex}`;
                        detailErrorCollector.push(`Invalid or missing detail ID, using fallback: ${detailId}`);
                    }

                    // Enhanced numeric field conversion with validation
                    const convertNumericField = (fieldName: string, value: unknown): number | null => {
                        if (value === null || value === undefined || value === '') {
                            return null;
                        }

                        if (typeof value === 'number') {
                            if (isNaN(value)) {
                                detailErrorCollector.push(`${fieldName} is NaN, converting to null`);
                                return null;
                            }
                            if (value < 0) {
                                detailErrorCollector.push(`${fieldName} is negative (${value}), but allowing for validation to handle`);
                            }
                            return value;
                        }

                        if (typeof value === 'string') {
                            const parsed = parseFloat(value);
                            if (isNaN(parsed)) {
                                detailErrorCollector.push(`${fieldName} string "${value}" cannot be converted to number, using null`);
                                return null;
                            }
                            return parsed;
                        }

                        detailErrorCollector.push(`${fieldName} has invalid type ${typeof value}, using null`);
                        return null;
                    };

                    // Enhanced string field conversion
                    const convertStringField = (fieldName: string, value: unknown): string | null => {
                        if (value === null || value === undefined) {
                            return null;
                        }

                        if (typeof value === 'string') {
                            return value; // Keep empty strings as-is for validation to handle
                        }

                        if (typeof value === 'number' || typeof value === 'boolean') {
                            return String(value);
                        }

                        detailErrorCollector.push(`${fieldName} has invalid type ${typeof value}, converting to string`);
                        return String(value);
                    };

                    // Enhanced ID field conversion
                    const convertIdField = (fieldName: string, value: unknown): number | null => {
                        if (value === null || value === undefined) {
                            return null;
                        }

                        if (typeof value === 'number') {
                            if (!Number.isInteger(value)) {
                                detailErrorCollector.push(`${fieldName} is not an integer (${value}), rounding`);
                                return Math.round(value);
                            }
                            if (value < 0) {
                                detailErrorCollector.push(`${fieldName} is negative (${value}), but allowing for validation to handle`);
                            }
                            return value;
                        }

                        if (typeof value === 'string') {
                            const parsed = parseInt(value, 10);
                            if (isNaN(parsed)) {
                                detailErrorCollector.push(`${fieldName} string "${value}" cannot be converted to integer, using null`);
                                return null;
                            }
                            return parsed;
                        }

                        detailErrorCollector.push(`${fieldName} has invalid type ${typeof value}, using null`);
                        return null;
                    };

                    // Enhanced date field conversion
                    const convertDateField = (value: unknown): string | null => {
                        if (value === null || value === undefined) {
                            return null;
                        }

                        if (typeof value === 'string') {
                            // Validate date format
                            const dateObj = new Date(value);
                            if (isNaN(dateObj.getTime())) {
                                detailErrorCollector.push(`workDate "${value}" is not a valid date, but keeping for validation to handle`);
                            }
                            return value;
                        }

                        if (value instanceof Date) {
                            return value.toISOString();
                        }

                        if (typeof value === 'number') {
                            return new Date(value).toISOString();
                        }

                        detailErrorCollector.push(`workDate has invalid type ${typeof value}, converting to string`);
                        return String(value);
                    };

                    // Create validation detail with enhanced type conversion
                    const validationDetail: PayStubDetail = {
                        id: detailId,
                        payStubId: convertStringField('payStubId', detail.payStubId) || stubId,
                        reportLineItemId: convertIdField('reportLineItemId', detail.reportLineItemId),
                        workDate: convertDateField(detail.workDate),
                        name: convertStringField('name', detail.name) || '',
                        stHours: convertNumericField('stHours', detail.stHours),
                        otHours: convertNumericField('otHours', detail.otHours),
                        dtHours: convertNumericField('dtHours', detail.dtHours),
                        totalHours: convertNumericField('totalHours', detail.totalHours),
                        jobCode: convertStringField('jobCode', detail.jobCode),
                        agreementId: convertIdField('agreementId', detail.agreementId),
                        classificationId: convertIdField('classificationId', detail.classificationId),
                        subClassificationId: convertIdField('subClassificationId', detail.subClassificationId),
                        costCenter: convertStringField('costCenter', detail.costCenter),
                        hourlyRate: convertNumericField('hourlyRate', detail.hourlyRate),
                        bonus: convertNumericField('bonus', detail.bonus),
                        expenses: convertNumericField('expenses', detail.expenses),
                        earningsCode: convertStringField('earningsCode', detail.earningsCode),
                        delete: Boolean(detail.delete),
                        employeeId: employeeId,
                        isTemporary: Boolean(detail.isTemporary),
                        ' $fragmentType': 'TimeSheetDetailRow_payStubDetail' as const
                    };

                    // Report conversion warnings if any
                    if (detailErrorCollector.length > 0) {
                        conversionErrors.push({
                            field: `payStub[${index}].details[${detailIndex}]`,
                            message: `Conversion warnings: ${detailErrorCollector.join('; ')}`,
                            payStubId: stubId,
                            detailId: detailId,
                            columnUid: 'conversion',
                            employeeName: stub.name || undefined,
                            workDate: convertDateField(detail.workDate) || undefined,
                            severity: 'warning'
                        });
                    }

                    validationDetails.push(validationDetail);
                } catch (error) {
                    // Emit conversion error for UI highlighting
                    conversionErrors.push({
                        field: `payStub[${index}].details[${detailIndex}]`,
                        message: `Failed to convert detail: ${error instanceof Error ? error.message : 'Unknown error'}`,
                        payStubId: stubId,
                        detailId: String(detail.id || `detail-${index}-${detailIndex}`),
                        columnUid: 'conversion',
                        employeeName: stub.name || undefined,
                        workDate: detail.workDate ? String(detail.workDate) : undefined,
                        severity: 'error'
                    });
                }
            });

            // Create validation-compatible pay stub with lenient employee handling
            const validationPayStub: PayStub = {
                id: stubId,
                name: stub.name || undefined, // Convert null to undefined for PayStub interface
                employeeId: employeeId || '',
                totalHours: stub.totalHours ?? null,
                details: validationDetails,
                expanded: false, // UI state not needed for validation
                delete: false, // UI state not needed for validation
                inError: false, // UI state not needed for validation
                employee: employeeId
                    ? {
                          id: String(employeeId),
                          ' $fragmentSpreads': {
                              EmployeeDisplayFragment_employee: true,
                              TimeSheetDetailRow_employee: true
                          } as FragmentRefs<'EmployeeDisplayFragment_employee' | 'TimeSheetDetailRow_employee'>
                      }
                    : {
                          id: '',
                          ' $fragmentSpreads': {
                              EmployeeDisplayFragment_employee: true,
                              TimeSheetDetailRow_employee: true
                          } as FragmentRefs<'EmployeeDisplayFragment_employee' | 'TimeSheetDetailRow_employee'>
                      },
                ' $fragmentType': 'PayStubTable_payStub' as const,
                ' $fragmentSpreads': {
                    TimeSheetDetailTableView_payStub: true
                } as FragmentRefs<'TimeSheetDetailTableView_payStub'>
            };

            validationPayStubs.push(validationPayStub);
        } catch (error) {
            // Emit conversion error for critical pay stub failures
            conversionErrors.push({
                field: `payStub[${index}]`,
                message: `Failed to convert pay stub: ${error instanceof Error ? error.message : 'Unknown error'}`,
                payStubId: String(stub.id || `stub-${index}`),
                columnUid: 'conversion',
                employeeName: stub.name || undefined,
                workDate: undefined,
                severity: 'error'
            });
        }
    });

    return {
        payStubs: validationPayStubs,
        conversionErrors
    };
}

/**
 * Converts modifiable pay stubs to mutation input format
 *
 * This function safely transforms modifiable pay stubs to the format expected
 * by GraphQL mutations, eliminating the need for type assertion.
 *
 * **Important**: This function throws an error when encountering pay stubs with null or invalid
 * employeeId values, as they are required for GraphQL mutations. Empty pay stubs should be
 * filtered out before calling this function using isCompletelyEmptyPayStubPayload().
 *
 * @param payStubs Array of pay stubs (modifiable or already in mutation format)
 * @returns Array of mutation-compatible pay stub inputs
 * @throws Error if any pay stub has null/invalid employeeId or is otherwise invalid for mutation
 */
export function convertToMutationInputs(payStubs: ReadonlyArray<unknown>): ModifyPayStubInput[] {
    const conversionErrors: string[] = [];

    const mutationInputs = payStubs
        .map((stub, index) => {
            if (!stub || typeof stub !== 'object') {
                conversionErrors.push(`PayStub at index ${index} is not an object`);
                return null;
            }

            const candidate = stub as Record<string, unknown>;

            // Check if it's already in mutation format
            if (isModifyPayStubInput(candidate)) {
                return candidate;
            }

            // Check if it's a modifiable pay stub that we can convert
            if (isModifiablePayStub(candidate)) {
                // Convert details to mutation input format
                const mutationDetails = candidate.details
                    .filter((detail) => !detail.delete) // Exclude deleted details
                    .map((detail, detailIndex) => {
                        if (!isModifiablePayStubDetail(detail)) {
                            conversionErrors.push(`PayStubDetail at payStub[${index}].details[${detailIndex}] is invalid for mutation`);
                            return null;
                        }

                        const mutationDetail: ModifyPayStubDetailInput = {
                            id: String(detail.id),
                            workDate: String(detail.workDate),
                            reportLineItemId: detail.reportLineItemId,
                            stHours: detail.stHours,
                            otHours: detail.otHours,
                            dtHours: detail.dtHours,
                            jobCode: detail.jobCode,
                            agreementId: detail.agreementId,
                            classificationId: detail.classificationId,
                            subClassificationId: detail.subClassificationId,
                            costCenter: detail.costCenter,
                            hourlyRate: detail.hourlyRate,
                            bonus: detail.bonus,
                            expenses: detail.expenses,
                            earningsCode: detail.earningsCode
                        };

                        return mutationDetail;
                    })
                    .filter((detail): detail is ModifyPayStubDetailInput => detail !== null);

                // Skip pay stubs without valid employeeId since it's required
                if (typeof candidate.employeeId !== 'string' || candidate.employeeId === null) {
                    conversionErrors.push(`PayStub at index ${index} has invalid employeeId (required Global ID string)`);
                    return null;
                }

                const mutationInput: ModifyPayStubInput = {
                    id: String(candidate.id),
                    employeeId: candidate.employeeId,
                    details: mutationDetails
                };

                return mutationInput;
            }

            // Try to convert from a generic object structure (from mappedPayStubs)
            if (
                typeof candidate.id === 'string' &&
                (typeof candidate.employeeId === 'number' || candidate.employeeId === null) &&
                Array.isArray(candidate.details)
            ) {
                const mutationDetails = candidate.details
                    .map((detail: unknown, detailIndex: number) => {
                        if (!detail || typeof detail !== 'object') {
                            conversionErrors.push(`PayStubDetail at payStub[${index}].details[${detailIndex}] is not an object`);
                            return null;
                        }

                        const detailCandidate = detail as Record<string, unknown>;

                        // Create mutation detail from generic object
                        const mutationDetail: ModifyPayStubDetailInput = {
                            id: typeof detailCandidate.id === 'string' ? detailCandidate.id : '',
                            workDate: typeof detailCandidate.workDate === 'string' ? detailCandidate.workDate : null,
                            reportLineItemId:
                                typeof detailCandidate.reportLineItemId === 'number' ? detailCandidate.reportLineItemId : null,
                            stHours: typeof detailCandidate.stHours === 'number' ? detailCandidate.stHours : null,
                            otHours: typeof detailCandidate.otHours === 'number' ? detailCandidate.otHours : null,
                            dtHours: typeof detailCandidate.dtHours === 'number' ? detailCandidate.dtHours : null,
                            jobCode: typeof detailCandidate.jobCode === 'string' ? detailCandidate.jobCode : null,
                            agreementId: typeof detailCandidate.agreementId === 'number' ? detailCandidate.agreementId : null,
                            classificationId:
                                typeof detailCandidate.classificationId === 'number' ? detailCandidate.classificationId : null,
                            subClassificationId:
                                typeof detailCandidate.subClassificationId === 'number' ? detailCandidate.subClassificationId : null,
                            costCenter: typeof detailCandidate.costCenter === 'string' ? detailCandidate.costCenter : null,
                            hourlyRate: typeof detailCandidate.hourlyRate === 'number' ? detailCandidate.hourlyRate : null,
                            bonus: typeof detailCandidate.bonus === 'number' ? detailCandidate.bonus : null,
                            expenses: typeof detailCandidate.expenses === 'number' ? detailCandidate.expenses : null,
                            earningsCode: typeof detailCandidate.earningsCode === 'string' ? detailCandidate.earningsCode : null
                        };

                        return mutationDetail;
                    })
                    .filter((detail): detail is ModifyPayStubDetailInput => detail !== null);

                // Skip pay stubs without valid employeeId since it's required
                if (typeof candidate.employeeId !== 'string' || candidate.employeeId === null) {
                    conversionErrors.push(`PayStub at index ${index} has invalid employeeId (required Global ID string)`);
                    return null;
                }

                const mutationInput: ModifyPayStubInput = {
                    id: candidate.id,
                    employeeId: candidate.employeeId,
                    details: mutationDetails
                };

                return mutationInput;
            }

            conversionErrors.push(`PayStub at index ${index} cannot be converted to mutation format`);
            return null;
        })
        .filter((input): input is ModifyPayStubInput => input !== null);

    if (conversionErrors.length > 0) {
        throw new Error(`Mutation conversion failed: ${conversionErrors.join('; ')}`);
    }

    // Final schema validation guard to prevent malformed IDs reaching backend
    const validationResult = validateModifyPayStubInputs(mutationInputs);
    if (!validationResult.isValid) {
        const validationMessages = validationResult.errors.map((err) => err.message).join('; ');
        throw new Error(`Mutation schema validation failed: ${validationMessages}`);
    }

    return mutationInputs;
}

/**
 * Validates an array of objects as PayStub-compatible for validation
 *
 * This function provides detailed validation results for debugging.
 *
 * @param payStubs Array of objects to validate
 * @returns Validation result with detailed error information
 */
export function validatePayStubsForValidation(payStubs: unknown[]): {
    isValid: boolean;
    errors: ValidationError[];
    validPayStubs: PayStub[];
} {
    const errors: ValidationError[] = [];
    const validPayStubs: PayStub[] = [];

    payStubs.forEach((stub, index) => {
        if (!isValidationCompatiblePayStub(stub)) {
            errors.push({
                field: `payStub[${index}]`,
                message: `PayStub at index ${index} is not compatible with validation requirements`,
                severity: 'error',
                payStubId: 'unknown',
                columnUid: 'payStub'
            });
            return;
        }

        validPayStubs.push(stub);
    });

    return {
        isValid: errors.length === 0,
        errors,
        validPayStubs
    };
}

/**
 * Validates an array of objects as ModifyPayStubInput-compatible
 *
 * This function provides detailed validation results for debugging.
 *
 * @param inputs Array of objects to validate
 * @returns Validation result with detailed error information
 */
export function validateModifyPayStubInputs(inputs: unknown[]): {
    isValid: boolean;
    errors: ValidationError[];
    validInputs: ModifyPayStubInput[];
} {
    const errors: ValidationError[] = [];
    const validInputs: ModifyPayStubInput[] = [];

    inputs.forEach((input, index) => {
        if (!isModifyPayStubInput(input)) {
            errors.push({
                field: `payStubInput[${index}]`,
                message: `PayStub input at index ${index} is not compatible with mutation requirements`,
                severity: 'error',
                payStubId: 'unknown',
                columnUid: 'payStubInput'
            });
            return;
        }

        validInputs.push(input);
    });

    return {
        isValid: errors.length === 0,
        errors,
        validInputs
    };
}

/**
 * Utility function to create type-safe validation error
 *
 * @param field Field name where error occurred
 * @param message Error message
 * @param severity Error severity
 * @param payStubId PayStub ID for context
 * @param columnUid Column UID for UI highlighting
 * @returns Properly typed validation error
 */
export function createValidationError(
    field: string,
    message: string,
    severity: 'error' | 'warning' = 'error',
    payStubId: string = 'unknown',
    columnUid: string = 'header'
): ValidationError {
    return {
        field,
        message,
        severity,
        payStubId,
        columnUid
    };
}

/**
 * Type guard to check if an error is a type validation error
 *
 * @param error Error to check
 * @returns true if error is related to type validation
 */
export function isTypeValidationError(error: unknown): error is Error {
    return (
        error instanceof Error &&
        (error.message.includes('Validation conversion failed') ||
            error.message.includes('Mutation conversion failed') ||
            error.message.includes('is not compatible with'))
    );
}
