import {
  TimesheetError,
  ErrorType,
  ErrorSeverity,
  parseRelayError,
  shouldRetryError,
  incrementRetryCount
} from '@/errorHandling';

describe('TimesheetError', () => {
  it('should create error with minimal data', () => {
    const error = new TimesheetError({
      message: 'Test error'
    });

    expect(error.name).toBe('TimesheetError');
    expect(error.message).toBe('Test error');
    expect(error.enhancedError.type).toBe(ErrorType.UNKNOWN);
    expect(error.enhancedError.severity).toBe(ErrorSeverity.MEDIUM);
    expect(error.enhancedError.userMessage).toBe('An unexpected error occurred.');
    expect(error.enhancedError.canRetry).toBe(true);
    expect(error.enhancedError.retryCount).toBe(0);
    expect(error.enhancedError.maxRetries).toBe(3);
  });

  it('should create error with full data', () => {
    const error = new TimesheetError({
      type: ErrorType.VALIDATION,
      severity: ErrorSeverity.HIGH,
      message: 'Validation failed',
      userMessage: 'Please fix the errors',
      technical: 'Field validation failed',
      suggestions: ['Check input', 'Try again'],
      canRetry: false,
      retryCount: 1,
      maxRetries: 5,
      context: { field: 'hours' }
    });

    expect(error.enhancedError.type).toBe(ErrorType.VALIDATION);
    expect(error.enhancedError.severity).toBe(ErrorSeverity.HIGH);
    expect(error.enhancedError.userMessage).toBe('Please fix the errors');
    expect(error.enhancedError.technical).toBe('Field validation failed');
    expect(error.enhancedError.suggestions).toEqual(['Check input', 'Try again']);
    expect(error.enhancedError.canRetry).toBe(false);
    expect(error.enhancedError.retryCount).toBe(1);
    expect(error.enhancedError.maxRetries).toBe(5);
    expect(error.enhancedError.context).toEqual({ field: 'hours' });
  });

  it('should generate default user messages based on error type', () => {
    const networkError = new TimesheetError({
      type: ErrorType.NETWORK,
      message: 'Network failed'
    });
    expect(networkError.enhancedError.userMessage).toBe('Connection issue occurred. Please check your internet connection.');

    const validationError = new TimesheetError({
      type: ErrorType.VALIDATION,
      message: 'Validation failed'
    });
    expect(validationError.enhancedError.userMessage).toBe('Please check your input and try again.');

    const permissionError = new TimesheetError({
      type: ErrorType.PERMISSION,
      message: 'Unauthorized'
    });
    expect(permissionError.enhancedError.userMessage).toBe('You don\'t have permission to perform this action.');
  });

  it('should generate default suggestions based on error type', () => {
    const fragmentError = new TimesheetError({
      type: ErrorType.FRAGMENT,
      message: 'Fragment error'
    });
    expect(fragmentError.enhancedError.suggestions).toContain('Refresh the page');
    expect(fragmentError.enhancedError.suggestions).toContain('Clear browser cache if problem persists');

    const mutationError = new TimesheetError({
      type: ErrorType.MUTATION,
      message: 'Mutation failed'
    });
    expect(mutationError.enhancedError.suggestions).toContain('Try saving again');
    expect(mutationError.enhancedError.suggestions).toContain('Refresh the page and retry');
  });

  it('should set default retryability based on error type', () => {
    const networkError = new TimesheetError({
      type: ErrorType.NETWORK,
      message: 'Network failed'
    });
    expect(networkError.enhancedError.canRetry).toBe(true);

    const validationError = new TimesheetError({
      type: ErrorType.VALIDATION,
      message: 'Validation failed'
    });
    expect(validationError.enhancedError.canRetry).toBe(false);

    const permissionError = new TimesheetError({
      type: ErrorType.PERMISSION,
      message: 'Unauthorized'
    });
    expect(permissionError.enhancedError.canRetry).toBe(false);
  });
});

describe('parseRelayError', () => {
  it('should parse fragment reference errors', () => {
    const relayError = {
      message: 'fragment reference not found for PayStub_timeSheet'
    };

    const error = parseRelayError(relayError);

    expect(error.enhancedError.type).toBe(ErrorType.FRAGMENT);
    expect(error.enhancedError.severity).toBe(ErrorSeverity.HIGH);
    expect(error.enhancedError.userMessage).toBe('Data loading error occurred. Please refresh the page.');
    expect(error.enhancedError.canRetry).toBe(true);
    expect(error.enhancedError.context.originalError).toBe(relayError);
  });

  it('should parse GraphQL validation errors', () => {
    const relayError = {
      source: {
        errors: [{
          message: 'Validation failed for field hours',
          extensions: {
            code: 'VALIDATION_ERROR',
            validationErrors: [{ field: 'hours', message: 'Required' }]
          }
        }]
      }
    };

    const error = parseRelayError(relayError);

    expect(error.enhancedError.type).toBe(ErrorType.VALIDATION);
    expect(error.enhancedError.severity).toBe(ErrorSeverity.MEDIUM);
    expect(error.enhancedError.userMessage).toBe('Please check your input and correct any errors.');
    expect(error.enhancedError.canRetry).toBe(false);
    expect(error.enhancedError.context.validationErrors).toEqual([{ field: 'hours', message: 'Required' }]);
  });

  it('should parse GraphQL authorization errors', () => {
    const relayError = {
      source: {
        errors: [{
          message: 'Unauthorized access',
          extensions: {
            code: 'UNAUTHORIZED'
          }
        }]
      }
    };

    const error = parseRelayError(relayError);

    expect(error.enhancedError.type).toBe(ErrorType.PERMISSION);
    expect(error.enhancedError.severity).toBe(ErrorSeverity.HIGH);
    expect(error.enhancedError.userMessage).toBe('You don\'t have permission to perform this action.');
    expect(error.enhancedError.canRetry).toBe(false);
  });

  it('should parse network errors', () => {
    const relayError = {
      name: 'TypeError',
      message: 'Failed to fetch'
    };

    const error = parseRelayError(relayError);

    expect(error.enhancedError.type).toBe(ErrorType.NETWORK);
    expect(error.enhancedError.severity).toBe(ErrorSeverity.MEDIUM);
    expect(error.enhancedError.userMessage).toBe('Connection issue occurred. Please check your internet connection.');
    expect(error.enhancedError.canRetry).toBe(true);
  });

  it('should handle unknown errors', () => {
    const relayError = {
      message: 'Something went wrong'
    };

    const error = parseRelayError(relayError);

    expect(error.enhancedError.type).toBe(ErrorType.UNKNOWN);
    expect(error.enhancedError.severity).toBe(ErrorSeverity.MEDIUM);
    expect(error.enhancedError.userMessage).toBe('An unexpected error occurred.');
    expect(error.enhancedError.canRetry).toBe(true);
  });

  it('should handle errors without messages', () => {
    const relayError = {};

    const error = parseRelayError(relayError);

    expect(error.enhancedError.type).toBe(ErrorType.UNKNOWN);
    expect(error.enhancedError.message).toBe('Unknown error occurred');
    expect(error.enhancedError.technical).toBe('{}');
  });
});

describe('shouldRetryError', () => {
  it('should return true for retryable errors within retry limit', () => {
    const error = new TimesheetError({
      type: ErrorType.NETWORK,
      message: 'Network failed',
      canRetry: true,
      retryCount: 1,
      maxRetries: 3
    });

    expect(shouldRetryError(error)).toBe(true);
  });

  it('should return false for non-retryable errors', () => {
    const error = new TimesheetError({
      type: ErrorType.VALIDATION,
      message: 'Validation failed',
      canRetry: false,
      retryCount: 0,
      maxRetries: 3
    });

    expect(shouldRetryError(error)).toBe(false);
  });

  it('should return false when retry count exceeds max retries', () => {
    const error = new TimesheetError({
      type: ErrorType.NETWORK,
      message: 'Network failed',
      canRetry: true,
      retryCount: 3,
      maxRetries: 3
    });

    expect(shouldRetryError(error)).toBe(false);
  });

  it('should return false for critical errors', () => {
    const error = new TimesheetError({
      type: ErrorType.UNKNOWN,
      severity: ErrorSeverity.CRITICAL,
      message: 'Critical error',
      canRetry: true,
      retryCount: 0,
      maxRetries: 3
    });

    expect(shouldRetryError(error)).toBe(false);
  });
});

describe('incrementRetryCount', () => {
  it('should increment retry count and update timestamp', async () => {
    const originalError = new TimesheetError({
      type: ErrorType.NETWORK,
      message: 'Network failed',
      retryCount: 1
    });

    const originalTimestamp = originalError.enhancedError.timestamp;
    
    // Wait a bit to ensure timestamp difference
    await new Promise(resolve => setTimeout(resolve, 1));
    const incrementedError = incrementRetryCount(originalError);

    expect(incrementedError.enhancedError.retryCount).toBe(2);
    expect(incrementedError.enhancedError.timestamp.getTime()).toBeGreaterThanOrEqual(originalTimestamp.getTime());
    expect(incrementedError.enhancedError.type).toBe(ErrorType.NETWORK);
    expect(incrementedError.enhancedError.message).toBe('Network failed');
  });

  it('should preserve all other error properties', () => {
    const originalError = new TimesheetError({
      type: ErrorType.MUTATION,
      severity: ErrorSeverity.HIGH,
      message: 'Mutation failed',
      userMessage: 'Save failed',
      technical: 'GraphQL mutation error',
      suggestions: ['Try again', 'Refresh page'],
      canRetry: true,
      retryCount: 0,
      maxRetries: 5,
      context: { mutationName: 'updatePayStub' }
    });

    const incrementedError = incrementRetryCount(originalError);

    expect(incrementedError.enhancedError.type).toBe(ErrorType.MUTATION);
    expect(incrementedError.enhancedError.severity).toBe(ErrorSeverity.HIGH);
    expect(incrementedError.enhancedError.userMessage).toBe('Save failed');
    expect(incrementedError.enhancedError.technical).toBe('GraphQL mutation error');
    expect(incrementedError.enhancedError.suggestions).toEqual(['Try again', 'Refresh page']);
    expect(incrementedError.enhancedError.canRetry).toBe(true);
    expect(incrementedError.enhancedError.maxRetries).toBe(5);
    expect(incrementedError.enhancedError.context).toEqual({ mutationName: 'updatePayStub' });
  });
});

describe('Error Type Integration', () => {
  it('should have consistent error type behavior', () => {
    // Test all error types for consistency
    const errorTypes = Object.values(ErrorType);
    
    errorTypes.forEach(type => {
      const error = new TimesheetError({
        type,
        message: `Test ${type} error`
      });

      expect(error.enhancedError.type).toBe(type);
      expect(error.enhancedError.userMessage).toBeTruthy();
      expect(error.enhancedError.suggestions.length).toBeGreaterThan(0);
      expect(typeof error.enhancedError.canRetry).toBe('boolean');
    });
  });

  it('should have consistent error severity behavior', () => {
    // Test all error severities for consistency
    const errorSeverities = Object.values(ErrorSeverity);
    
    errorSeverities.forEach(severity => {
      const error = new TimesheetError({
        severity,
        message: `Test ${severity} error`
      });

      expect(error.enhancedError.severity).toBe(severity);
      
      // Critical errors should not be retryable by default
      if (severity === ErrorSeverity.CRITICAL) {
        expect(shouldRetryError(error)).toBe(false);
      }
    });
  });
});