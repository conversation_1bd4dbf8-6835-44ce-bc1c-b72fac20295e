export const LABEL_TEXT = {
    // Common Labels
    TYPE: 'Type',
    STATUS: 'Status',
    PERIOD: 'Period',
    DATE: 'Date',
    FEIN: 'FEIN',
    NAME: 'Name',
    DESCRIPTION: 'Description',

    // Entity Labels
    EMPLOYER: 'Employer',
    EMPLOYERS: 'Employers',
    EMPLOYEES: 'Employees',
    TIMESHEET: 'Timesheet',
    TIMESHEETS: 'Timesheets',

    // Table Column Labels
    ID_LABEL: 'ID',
    BUSINESS_NAME_LABEL: 'Business Name',
    DBA_LABEL: 'Doing Business As (DBA)',
    NECA_ID_LABEL: 'NECA ID',
    PAYROLL_CONTACT_LABEL: 'Payroll contact',
    LATEST_REPORT_LABEL: 'Latest report',
    OVERRIDE_RATE_LABEL: 'Override rate',
    ASSN_ID_LABEL: 'Assn ID',
    BENEFIT_LABEL: 'Benefit',
    MULTI_NECA_LABEL: 'Multi-NECA',
    DATES_LABEL: 'Dates',
    ACTIONS_LABEL: ' ',
    TYPE_LABEL: 'Type',
    HOURS_WORKED_LABEL: 'Hours Worked',
    HOURS_WKD_LABEL: 'Hours Worked',
    STUBS_LABEL: 'Pay Stubs',
    STATUS_LABEL: 'Status',
    LAST_MODIFIED_BY_LABEL: 'Last Modified By',
    PERIOD_LABEL: 'Period',
    TIMESHEET_ID_LABEL: 'Timesheet ID#',
    LAST_MODIFIED_LABEL: 'Last Modified',
    EMPLOYER_NAME_LABEL: 'Employer name',
    WEEK_END_DATE_LABEL: 'Week End Date',
    TIMESHEET_MODIFIED_LABEL: 'Timesheet Last Modified Date',
    FEDERAL_EMPLOYER_ID_LABEL: 'Federal Employer ID (FEIN)',
    REMOVE_EMPLOYER_LABEL: 'Remove employer',
    NAME_VIEW_LABEL: 'Name View',

    // Common Actions
    SAVE: 'Save',
    CANCEL: 'Cancel',
    CONFIRM: 'Confirm',
    APPLY: 'Apply',
    CLOSE: 'Close',
    DELETE: 'Delete',
    DOWNLOAD: 'Download',
    COPY: 'Copy',
    UPLOAD: 'Upload',
    RESET: 'Reset',
    YES_DELETE: 'Yes, Delete',

    // Navigation/Menu Items
    SETTINGS: 'Settings',
    PROFILE: 'Profile',
    LOGOUT: 'Logout',

    // View Management
    VIEWS: 'Views',
    VIEW: 'View',
    DETAILS: 'Details',
    SAVE_VIEW: 'Save View',
    CREATE_NEW: 'Create new',
    UPDATE_EXISTING: 'Update existing',
    DELETE_VIEW: 'Delete view',
    MAKE_DEFAULT: 'Make default',
    RENAME: 'Rename',
    RESTORE_AS_DEFAULT: 'Restore as default',
    DEFAULT: 'default',
    SYSTEM_DEFAULT: 'system default',
    VIEWS_IN_EPRLIVE: 'Views in EPRLive',

    // Action Buttons
    ADD_EMPLOYER_BTN: 'Add Employer',
    ADD_EMPLOYEE_BTN: 'Add Employee',
    ADD_TIMESHEET_BTN: 'Add Timesheet',
    CHECK_SITE_BTN: 'Check Site',
    END_RELATIONSHIP_BTN: 'Yes, end relationship',
    CLEAR_ALL_BTN: 'Clear All',

    // Table Actions
    VIEW_EMPLOYER_PROFILE_ACTION: 'View employer profile',
    VIEW_EMPLOYEE_ROSTER_ACTION: 'View employee roster',
    VIEW_PAYROLL_REPORTS_ACTION: 'View payroll reports',
    ARCHIVE_EMPLOYER_ACTION: 'Archive Employer',
    REMOVE_BENEFIT_ACTION: 'Remove Benefit',

    // Filter Labels & Hints
    INCLUDE_INACTIVE_LABEL: 'Include inactive employers',
    INACTIVE_EMPLOYERS_HINT: 'Employers that are marked as inactive.',
    EMPLOYER_NAME_HINT: "An employer's regular business name.",
    FEIN_HINT: "An employer's Federal Employer ID Number (FEIN)",
    LATEST_REPORT_HINT: "Each employer's latest non zero-hour Payroll Report received.",

    // Prefixes & Formatting
    START_DATE_PREFIX: 'S:',
    END_DATE_PREFIX: 'E:',
    YES_DISCOUNT: 'Yes,',
    DISCOUNT_PERCENTAGE: '45% disc',
    HAS_BEEN: 'has been',

    // Validation Messages
    FEIN_VALIDATION_MESSAGE: 'FEIN must be a 9-digit number value (XX-XXXXXXX or XXXXXXXXX)',
    FEIN_MIN_LENGTH_MESSAGE: 'FEIN must be at least 9 digits. Ex: (XX-XXXXXXX or XXXXXXXXX)',
    FEIN_MAX_LENGTH_MESSAGE: 'FEIN can be maximum 10 digits. Ex: (XX-XXXXXXX or XXXXXXXXX)',
    VIEW_NAME_MIN_LENGTH_MESSAGE: 'Name must include at least one letter or number',
    VIEW_NAME_MAX_LENGTH_MESSAGE: 'Name can have maximum 30 letters or numbers',
    VIEW_NAME_SYSTEM_CONFLICT_MESSAGE: 'Name matches a system view name, please choose a different name',
    REQUIRED_FIELD_MESSAGE: 'Please enter a value',
    
    // PayStub Validation Messages
    PAYSTUB_MEANINGFUL_DATA_MESSAGE: 'PayStub must have either detailed hours entries or a total hours value greater than 0',
    PAYSTUB_HEADER_ONLY_VALIDATION_FAILED: 'Header-only PayStub validation failed',
    PAYSTUB_DETAIL_VALIDATION_FAILED: 'PayStub detail validation failed',
    PAYSTUB_VALIDATION_FAILED: 'PayStub validation failed',
    PAYSTUB_NO_MEANINGFUL_DATA_HINT: 'Try adding hours, job codes, or cost center information to make the PayStub valid',
    PAYSTUB_VALIDATION_HELP: 'Each PayStub needs either: (1) Detail rows with hours/job information, or (2) A total hours value greater than 0',

    // Descriptions
    VIEW_COLLECTION_DESCRIPTION:
        'A "View" is a collection settings for displaying data in a table. Those settings include columns shown, column order, sorting, and any filters.',
    VIEW_BASIC_DESCRIPTION: 'EPRLive ships with a basic set of Views for each page that cannot be renamed, modified, or deleted.',
    VIEWS_DESCRIPTION: 'Views created in EPRLive can be renamed, deleted, or modified at any time.',
    FEIN_CHECK_DESCRIPTION:
        'Enter the Federal Employer ID (FEIN) for the business you want to add. We\'ll check our system to see if it already exists in EPRLive.',

    // Status & Info Messages
    NO_RESULTS_MESSAGE: 'No results found.',
    NO_MATCHING_RESULTS_MESSAGE: 'No matching results',
    CLEAR_FILTERS_MESSAGE: 'Try clearing or changing your active filters.',
    NO_CONTACT_INFO_MESSAGE: 'No contact information on file.',
    DELETE_VIEW_CONFIRMATION_MESSAGE: 'Are you sure you want to delete the view',

    // Employer Management Messages
    EMPLOYER_DELETE_CONFIRMATION: 'may be deleted.',
    EMPLOYER_DELETE_WARNING: 'This action cannot be un-done. Selecting "Yes" will delete the employer profile for',
    EMPLOYER_DELETE_ACCOUNTS_WARNING: 'in EPRLive, in addition to deleting all user accounts tied to',
    EMPLOYER_DELETED: 'has been deleted from EPRLive.',
    EMPLOYER_CANNOT_DELETE: 'cannot be deleted from EPRLive.',
    EMPLOYER_STATUS_MESSAGE: 'has not been marked "Inactive" you can change that setting under "Status," on their Employer Detail page.',
    EMPLOYER_RELATIONSHIP_MESSAGE: 'cannot be deleted from EPRLive, because it is in a relationship with other site sponsors.',
    EMPLOYER_END_RELATIONSHIP_QUESTION: 'Would you like to end your relationship with',
    EMPLOYER_RELATIONSHIP_ENDED: 'Your relationship with',
    EMPLOYER_RELATIONSHIP_ENDED_STATUS: 'has been ended.',
    EMPLOYER_RELATIONSHIP_REESTABLISH: 'They are still on EPRLive, and you may re-establish a relationship with them at any time.',
    CONTINUE_CONFIRMATION: 'Would you like to continue?',

    // Page Titles
    EMPLOYER_ROSTER_TITLE: 'Employer Roster'
};

export const TIMESHEET_TEXT = {
    WEEK_END_DATE_LABEL: LABEL_TEXT.WEEK_END_DATE_LABEL,
    LAST_MODIFIED_DATE_LABEL: LABEL_TEXT.TIMESHEET_MODIFIED_LABEL,
    ADD_DIALOG_DESCRIPTION: 'Please enter a week end date for the new timesheet.'
};

export const TABLE_TEXT = {
    EMPLOYER_TABLE_LABEL: 'Employer Roster Table',
    TIMESHEET_TABLE_LABEL: 'Timesheet Roster Table',
    BENEFIT_TABLE_LABEL: 'Benefit Elections Table'
};

export const BUTTON_TEXT = {
    SAVE: LABEL_TEXT.SAVE,
    CANCEL: LABEL_TEXT.CANCEL,
    CONFIRM: LABEL_TEXT.CONFIRM,
    APPLY: LABEL_TEXT.APPLY,
    CLOSE: LABEL_TEXT.CLOSE,
    CHECK_SITE: LABEL_TEXT.CHECK_SITE_BTN,
    DELETE: LABEL_TEXT.DELETE,
    YES_DELETE: LABEL_TEXT.YES_DELETE
};
