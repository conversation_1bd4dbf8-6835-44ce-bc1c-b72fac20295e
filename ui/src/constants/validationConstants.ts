/**
 * Validation Constants
 * 
 * This file contains all hard-coded values used in validation logic.
 * Centralizing these constants improves maintainability and makes
 * business rules more transparent and easier to update.
 */

/**
 * Performance thresholds for optimization strategies
 */
export const PERFORMANCE_THRESHOLDS = {
    /** Number of paystubs that triggers performance optimizations */
    LARGE_DATASET_SIZE: 100,
    /** Maximum errors to collect before stopping validation */
    MAX_ERRORS_DEFAULT: 1000,
} as const;

/**
 * Date validation constants
 */
export const DATE_VALIDATION = {
    /** Maximum years in the past for work date validation */
    MAX_YEARS_PAST: 1,
    /** Maximum years in the future for work date validation */
    MAX_YEARS_FUTURE: 1,
} as const;

/**
 * Hours validation constants
 */
export const HOURS_VALIDATION = {
    /** Maximum hours allowed per day */
    MAX_HOURS_PER_DAY: 24,
    /** Warning threshold for total hours per pay period */
    HIGH_HOURS_WARNING_THRESHOLD: 60,
} as const;

/**
 * Financial validation constants
 */
export const FINANCIAL_VALIDATION = {
    /** Warning threshold for bonus amounts */
    HIGH_BONUS_WARNING_THRESHOLD: 10000,
    /** Warning threshold for expense amounts */
    HIGH_EXPENSES_WARNING_THRESHOLD: 5000,
    /** Warning threshold for high hourly rates */
    HIGH_HOURLY_RATE_WARNING: 500,
    /** Federal minimum wage for warning validation */
    FEDERAL_MINIMUM_WAGE: 7.25,
} as const;

/**
 * Text field validation constants
 */
export const TEXT_FIELD_VALIDATION = {
    /** Maximum length for job code */
    JOB_CODE_MAX_LENGTH: 20,
    /** Maximum length for cost center */
    COST_CENTER_MAX_LENGTH: 20,
} as const;

/**
 * UI behavior constants
 */
export const UI_BEHAVIOR = {
    /** Default debounce delay for field validation (milliseconds) */
    DEFAULT_DEBOUNCE_DELAY: 300,
    /** Maximum debounce delay allowed (milliseconds) */
    MAX_DEBOUNCE_DELAY: 2000,
} as const;

/**
 * Agreement ID constants
 */
export const AGREEMENT_IDS = {
    /** Special ID representing "No Agreement" selection */
    NO_AGREEMENT: 0,
} as const;

/**
 * Type guard to ensure all constants are readonly
 */
type DeepReadonly<T> = {
    readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P];
};

/**
 * Combined validation constants for easy import
 */
export const VALIDATION_CONSTANTS = {
    PERFORMANCE: PERFORMANCE_THRESHOLDS,
    DATE: DATE_VALIDATION,
    HOURS: HOURS_VALIDATION,
    FINANCIAL: FINANCIAL_VALIDATION,
    TEXT: TEXT_FIELD_VALIDATION,
    UI: UI_BEHAVIOR,
    AGREEMENT: AGREEMENT_IDS,
} as const satisfies DeepReadonly<Record<string, unknown>>;

/**
 * Type definitions for validation constants
 */
export type ValidationConstants = typeof VALIDATION_CONSTANTS;
export type PerformanceThresholds = typeof PERFORMANCE_THRESHOLDS;
export type DateValidation = typeof DATE_VALIDATION;
export type HoursValidation = typeof HOURS_VALIDATION;
export type FinancialValidation = typeof FINANCIAL_VALIDATION;
export type TextFieldValidation = typeof TEXT_FIELD_VALIDATION;
export type UIBehavior = typeof UI_BEHAVIOR;
export type AgreementIds = typeof AGREEMENT_IDS;