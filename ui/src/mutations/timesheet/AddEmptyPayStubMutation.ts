/**
 * Add Empty PayStub Mutation
 *
 * This mutation creates a new empty PayStub record immediately upon employee selection.
 * It replaces the temporary PayStub system with immediate database persistence,
 * leveraging Relay's predictable data patterns.
 */

import { graphql, useMutation, ConnectionHandler, commitMutation } from 'react-relay';
import type { Environment } from 'relay-runtime';
import type { AddEmptyPayStubMutation$variables, AddEmptyPayStubMutation$data, AddEmptyPayStubMutation } from '@/src/types/graphql-timesheet';
import { RelayIdService } from '@/src/services/RelayIdService';

/**
 * GraphQL mutation for creating an empty PayStub
 *
 * Creates a minimal PayStub record with edge-based payload for proper
 * Relay connection integration. Uses @prependEdge directive for automatic
 * connection management following RELAY-RULES.md Rule 11.
 */
const AddEmptyPayStubMutationGraphQL = graphql`
    mutation AddEmptyPayStubMutation($input: AddEmptyPayStubInput!, $connections: [ID!]!) {
        addEmptyPayStub(input: $input) {
            payStubEdge @prependEdge(connections: $connections) {
                cursor
                node {
                    id
                    employeeId
                    # totalHours is computed from individual hour fields
                    employee {
                        id
                    }
                    details {
                        id
                        workDate
                        stHours
                        otHours
                        dtHours
                        # totalHours is computed from individual hour fields
                        agreementId
                        classificationId
                        subClassificationId
                    }
                }
            }
            errors
        }
    }
`;

/**
 * Input interface for adding an empty PayStub
 */
export interface AddEmptyPayStubInput {
    timeSheetId: string; // Relay Global ID
    employeeId: string; // Relay Global ID
}

/**
 * Hook for adding an empty PayStub using Relay connections
 *
 * Provides a simple interface for creating empty PayStubs with proper
 * Relay connection integration. Uses @prependEdge directive for automatic
 * cache updates, eliminating the need for manual store manipulation.
 *
 * @returns Object with addEmptyPayStub function and loading state
 */
export function useAddEmptyPayStub() {
    // Always call the hook to satisfy React Hook rules
    const [commitMutation, isMutationInFlight] = useMutation<AddEmptyPayStubMutation>(AddEmptyPayStubMutationGraphQL);

    /**
     * Adds an empty PayStub for the specified employee using Relay connections
     *
     * @param timeSheetId - The Relay Global ID of the timesheet to add the PayStub to
     * @param employeeId - The Relay Global ID of the employee to create the PayStub for
     * @returns Promise that resolves to the created PayStub ID or rejects with error
     */
    const addEmptyPayStub = (timeSheetId: string, employeeId: string): Promise<string> => {
        return new Promise((resolve, reject) => {
            // Validate input before sending to server
            try {
                validateAddEmptyPayStubInput({ timeSheetId, employeeId });
            } catch (validationError) {
                reject(validationError);
                return;
            }

            // Generate connection ID for @prependEdge directive
            const connectionId = ConnectionHandler.getConnectionID(timeSheetId, 'PayStubTable_connectionFragment__payStubs');

            // Pass Global IDs directly to the mutation - backend now accepts ID! types
            const input = { timeSheetId, employeeId };

            commitMutation({
                variables: {
                    input,
                    connections: [connectionId] // For @prependEdge directive
                },
                optimisticResponse: {
                    addEmptyPayStub: {
                        payStubEdge: {
                            cursor: '', // Temporary cursor
                            node: {
                                id: `client:temp:paystub:${employeeId}:${Date.now()}`,
                                employeeId: employeeId,
                                employee: {
                                    id: employeeId
                                },
                                details: [] // Empty array for details
                            }
                        },
                        errors: []
                    }
                } as any,
                onCompleted: (response, errors) => {
                    // Check for network/GraphQL errors first
                    if (errors?.length) {
                        reject(new Error(`GraphQL Error: ${errors.map((e) => e.message).join(', ')}`));
                        return;
                    }

                    // Check for mutation-specific errors
                    if (response.addEmptyPayStub.errors?.length) {
                        const errorMessages = response.addEmptyPayStub.errors.join(', ');
                        reject(new Error(`Mutation Error: ${errorMessages}`));
                        return;
                    }

                    // Check if we got a valid PayStubEdge back
                    if (!response.addEmptyPayStub.payStubEdge?.node?.id) {
                        reject(new Error('Failed to create PayStub: No PayStubEdge returned from server'));
                        return;
                    }

                    // Success - return the new PayStub ID
                    resolve(response.addEmptyPayStub.payStubEdge.node.id);
                },
                onError: (error) => {
                    reject(new Error(`Network Error: ${error.message}`));
                }
            });
        });
    };

    return {
        addEmptyPayStub,
        isLoading: isMutationInFlight
    };
}

/**
 * Validation function for AddEmptyPayStub input
 *
 * @param input - Input to validate
 * @throws Error if validation fails
 */
export function validateAddEmptyPayStubInput(input: AddEmptyPayStubInput): void {
    if (!input.timeSheetId || typeof input.timeSheetId !== 'string' || input.timeSheetId.trim() === '') {
        const error = new Error('timeSheetId is required and must be a non-empty string');
        console.error('AddEmptyPayStubMutation: Validation failed - timeSheetId is required:', input);
        throw error;
    }

    if (!input.employeeId || typeof input.employeeId !== 'string' || input.employeeId.trim() === '') {
        const error = new Error('employeeId is required and must be a non-empty string');
        console.error('AddEmptyPayStubMutation: Validation failed - employeeId is required:', input);
        throw error;
    }
}

/**
 * Safe wrapper around addEmptyPayStub with validation
 *
 * @param addEmptyPayStubFn - The mutation function
 * @param timeSheetId - The Relay Global ID of the timesheet
 * @param employeeId - The Relay Global ID of the employee
 * @param connectionId - The connection ID for the payStubs connection
 * @returns Promise that resolves to the PayStub ID
 */
export async function safeAddEmptyPayStub(
    addEmptyPayStubFn: (timeSheetId: string, employeeId: string, connectionId: string) => Promise<string>,
    timeSheetId: string,
    employeeId: string,
    connectionId: string
): Promise<string> {
    validateAddEmptyPayStubInput({ timeSheetId, employeeId });
    if (!connectionId || typeof connectionId !== 'string') {
        throw new Error('connectionId is required for Relay connection updates');
    }
    return addEmptyPayStubFn(timeSheetId, employeeId, connectionId);
}

/**
 * Non-hook version of addEmptyPayStub for use outside React components
 * (e.g., in Zustand stores or utility functions)
 * 
 * @param environment - Relay environment
 * @param timeSheetId - The Relay Global ID of the timesheet
 * @param employeeId - The Relay Global ID of the employee
 * @returns Promise that resolves to the created PayStub ID
 */
export function addEmptyPayStubMutation(
    environment: Environment, 
    timeSheetId: string, 
    employeeId: string
): Promise<string> {
    return new Promise((resolve, reject) => {
        // Validate input
        try {
            validateAddEmptyPayStubInput({ timeSheetId, employeeId });
        } catch (validationError) {
            reject(validationError);
            return;
        }

        // Generate connection ID for @prependEdge directive
        const connectionId = ConnectionHandler.getConnectionID(timeSheetId, 'PayStubTable_connectionFragment__payStubs');
        
        const input = { timeSheetId, employeeId };

        commitMutation<AddEmptyPayStubMutation>(environment, {
            mutation: AddEmptyPayStubMutationGraphQL,
            variables: {
                input,
                connections: [connectionId]
            },
            optimisticResponse: {
                addEmptyPayStub: {
                    payStubEdge: {
                        cursor: '', // Temporary cursor
                        node: {
                            id: `client:temp:paystub:${employeeId}:${Date.now()}`,
                            employeeId: employeeId,
                            employee: {
                                id: employeeId
                            },
                            details: [] // Empty array for details
                        }
                    },
                    errors: []
                }
            } as any,
            onCompleted: (response, errors) => {
                // Check for network/GraphQL errors first
                if (errors?.length) {
                    reject(new Error(`GraphQL Error: ${errors.map((e) => e.message).join(', ')}`));
                    return;
                }

                // Check for mutation-specific errors
                if (response.addEmptyPayStub.errors?.length) {
                    const errorMessages = response.addEmptyPayStub.errors.join(', ');
                    reject(new Error(`Mutation Error: ${errorMessages}`));
                    return;
                }

                // Check if we got a valid PayStubEdge back
                if (!response.addEmptyPayStub.payStubEdge?.node?.id) {
                    reject(new Error('Failed to create PayStub: No PayStubEdge returned from server'));
                    return;
                }

                // Success - return the new PayStub ID
                resolve(response.addEmptyPayStub.payStubEdge.node.id);
            },
            onError: (error) => {
                reject(new Error(`Network Error: ${error.message}`));
            }
        });
    });
}

export default AddEmptyPayStubMutationGraphQL;
