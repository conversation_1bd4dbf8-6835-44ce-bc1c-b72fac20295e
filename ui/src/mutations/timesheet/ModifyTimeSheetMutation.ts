import { commitMutation, graphql } from 'react-relay';
import type { Environment } from 'relay-runtime';
import type {
    ModifyTimeSheetMutation$variables,
    ModifyTimeSheetMutation$data,
    ModifyPayStubInput,
    ModifyPayStubDetailInput
} from '@/src/types/graphql-timesheet';

const mutation = graphql`
    mutation ModifyTimeSheetMutation($input: ModifyTimeSheetInput!) {
        modifyTimeSheet(input: $input) {
            timeSheet {
                id
                ...TimesheetDetail_timeSheet
            }
        }
    }
`;

export function modifyTimeSheet(environment: Environment, input: ModifyTimeSheetMutation$variables['input']) {
    return commitMutation(environment, {
        mutation,
        variables: { input },
        optimisticUpdater: (store) => {
            const timeSheet = store.get(input.timeSheetId);
            
            // Handle modifyPayStubs (existing PayStubs being updated)
            if (timeSheet && input.modifyPayStubs) {
                input.modifyPayStubs.forEach((payStubUpdate: ModifyPayStubInput) => {
                    if (payStubUpdate.id) {
                        const payStub = store.get(payStubUpdate.id);
                        if (payStub) {
                            // Update GraphQL fields only (UI state fields like expanded/inEdit are not included)
                            if (payStubUpdate.name !== undefined) payStub.setValue(payStubUpdate.name, 'name');
                            if (payStubUpdate.employeeId !== undefined) payStub.setValue(payStubUpdate.employeeId, 'employeeId');
                            // totalHours is now computed automatically, no need to set it
                            if (payStubUpdate.stHours !== undefined) payStub.setValue(payStubUpdate.stHours, 'stHours');
                            if (payStubUpdate.otHours !== undefined) payStub.setValue(payStubUpdate.otHours, 'otHours');
                            if (payStubUpdate.dtHours !== undefined) payStub.setValue(payStubUpdate.dtHours, 'dtHours');
                            if (payStubUpdate.bonus !== undefined) payStub.setValue(payStubUpdate.bonus, 'bonus');
                            if (payStubUpdate.expenses !== undefined) payStub.setValue(payStubUpdate.expenses, 'expenses');

                            // If details exist, update those too
                            if (payStubUpdate.details) {
                                payStubUpdate.details.forEach((detailUpdate: ModifyPayStubDetailInput) => {
                                    if (detailUpdate.id) {
                                        const detail = store.get(detailUpdate.id);
                                        if (detail) {
                                            if (detailUpdate.name !== undefined) detail.setValue(detailUpdate.name, 'name');
                                            if (detailUpdate.workDate !== undefined) detail.setValue(detailUpdate.workDate, 'workDate');
                                            if (detailUpdate.stHours !== undefined) detail.setValue(detailUpdate.stHours, 'stHours');
                                            if (detailUpdate.otHours !== undefined) detail.setValue(detailUpdate.otHours, 'otHours');
                                            if (detailUpdate.dtHours !== undefined) detail.setValue(detailUpdate.dtHours, 'dtHours');
                                            // totalHours is now computed automatically, no need to set it
                                            if (detailUpdate.jobCode !== undefined) detail.setValue(detailUpdate.jobCode, 'jobCode');
                                            if (detailUpdate.costCenter !== undefined)
                                                detail.setValue(detailUpdate.costCenter, 'costCenter');
                                            if (detailUpdate.hourlyRate !== undefined)
                                                detail.setValue(detailUpdate.hourlyRate, 'hourlyRate');
                                            if (detailUpdate.bonus !== undefined) detail.setValue(detailUpdate.bonus, 'bonus');
                                            if (detailUpdate.expenses !== undefined) detail.setValue(detailUpdate.expenses, 'expenses');
                                            if (detailUpdate.agreementId !== undefined)
                                                detail.setValue(detailUpdate.agreementId, 'agreementId');
                                            if (detailUpdate.classificationId !== undefined)
                                                detail.setValue(detailUpdate.classificationId, 'classificationId');
                                            if (detailUpdate.subClassificationId !== undefined)
                                                detail.setValue(detailUpdate.subClassificationId, 'subClassificationId');
                                        }
                                    }
                                });
                            }
                        }
                    }
                });
            }
            
            // Handle addPayStubs (new PayStubs being added)
            // Note: Optimistic updates for new PayStubs are more complex since they don't have IDs yet
            // For now, we'll let the server response handle this
            
            // Handle deletePayStubIds (PayStubs being deleted)
            if (timeSheet && input.deletePayStubIds) {
                input.deletePayStubIds.forEach((payStubId: string) => {
                    const payStub = store.get(payStubId);
                    if (payStub) {
                        // Mark for deletion or remove from connection
                        payStub.setValue(true, 'delete');
                    }
                });
            }
        },
        onCompleted: (response) => {
            // ModifyTimeSheetPayload doesn't have errors field, mutation completed successfully
        },
        onError: (error) => {
            console.error('ModifyTimeSheet mutation failed:', error);
            // Relay automatically rolls back optimistic updates on error
        }
    });
}
