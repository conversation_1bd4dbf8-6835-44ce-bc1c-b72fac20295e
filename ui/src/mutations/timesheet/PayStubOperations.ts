import type { Environment } from 'relay-runtime';
import { modifyTimeSheet } from './ModifyTimeSheetMutation';
import type { ModifyTimeSheetInput, ModifyPayStubInput, AddPayStubInput } from '@/src/types/graphql-timesheet';
import { RelayIdService } from '@/src/services/RelayIdService';

/**
 * Add a new PayStub to an existing TimeSheet by using ModifyTimeSheet mutation
 */
export function addPayStubToTimeSheet(
  environment: Environment,
  timeSheetId: string,
  numericId: number,
  employerGuid: string,
  newPayStub: AddPayStubInput
) {
  const input: ModifyTimeSheetInput = {
    id: String(numericId),
    timeSheetId,
    employerGuid,
    addPayStubs: [newPayStub]
  };

  return modifyTimeSheet(environment, input);
}

/**
 * Delete a PayStub from a TimeSheet by setting its delete flag
 */
export function deletePayStubFromTimeSheet(
  environment: Environment,
  timeSheetId: string,
  numericId: number,
  employerGuid: string,
  payStubId: string
) {
  const input: ModifyTimeSheetInput = {
    id: String(numericId),
    timeSheetId,
    employerGuid,
    deletePayStubIds: [payStubId]
  };

  return modifyTimeSheet(environment, input);
}

/**
 * Update specific PayStub fields using ModifyTimeSheet mutation
 */
export function updatePayStubInTimeSheet(
  environment: Environment,
  timeSheetId: string,
  numericId: number,
  employerGuid: string,
  payStubUpdates: ModifyPayStubInput[]
) {
  const input: ModifyTimeSheetInput = {
    id: String(numericId),
    timeSheetId,
    employerGuid,
    modifyPayStubs: payStubUpdates
  };

  return modifyTimeSheet(environment, input);
}