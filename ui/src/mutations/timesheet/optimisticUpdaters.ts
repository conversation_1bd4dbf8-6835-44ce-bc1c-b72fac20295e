import type { RecordSourceSelectorProxy } from 'relay-runtime';
import { TimesheetError, parseRelayError, shouldRetryError, incrementRetryCount } from '../../utils/error-handling';

// Counter for unique ID generation
let tempIdCounter = 1;

/**
 * Optimistic timesheet data interface
 */
export interface OptimisticTimesheetData {
  id?: string;
  name?: string;
  payPeriodStartDate?: string;
  payPeriodEndDate?: string;
  employerGuid?: string;
  status?: string;
}

/**
 * Creates an optimistic Timesheet record for testing/mocking
 */
export function createOptimisticTimesheet(data: OptimisticTimesheetData = {}): any {
  const tempId = data.id || `client:temp:timesheet:${Date.now()}_${tempIdCounter++}`;
  
  return {
    id: tempId,
    name: data.name || 'New Timesheet',
    payPeriodStartDate: data.payPeriodStartDate || new Date().toISOString().split('T')[0],
    payPeriodEndDate: data.payPeriodEndDate || new Date().toISOString().split('T')[0],
    employerGuid: data.employerGuid,
    status: data.status || 'Draft',
    payStubs: [],
    __typename: 'TimeSheet'
  };
}

/**
 * Shared types for optimistic updates
 */
export interface OptimisticPayStubData {
  id?: string;
  employeeId?: string | null;
  name?: string;
  totalHours?: number;
  employee?: {
    id: string;
    firstName: string;
    lastName: string;
    active: boolean;
  } | null;
}

export interface OptimisticPayStubDetailData {
  id?: string;
  payStubId?: string;
  reportLineItemId?: number | null;
  workDate?: string;
  name?: string;
  stHours?: number;
  otHours?: number;
  dtHours?: number;
  totalHours?: number;
  jobCode?: string;
  agreementId?: number | null;
  classificationId?: number | null;
  subClassificationId?: number | null;
  costCenter?: string;
  hourlyRate?: number;
  bonus?: number;
  expenses?: number;
  earningsCode?: string;
}

/**
 * Creates an optimistic PayStub record for testing/mocking
 * @deprecated Use createOptimisticPayStubInStore for actual Relay store operations
 */
export function createOptimisticPayStub(data: OptimisticPayStubData = {}): any {
  const tempId = data.id || `client:temp:paystub:${Date.now()}_${tempIdCounter++}`;
  
  return {
    id: tempId,
    employeeId: data.employeeId ?? null,
    name: data.name ?? undefined,
    totalHours: data.totalHours || 0,
    details: [],
    employee: data.employee || null,
    customProperty: (data as any).customProperty,
    __typename: 'PayStub'
  };
}

/**
 * Creates an optimistic PayStub record in the actual Relay store
 */
export function createOptimisticPayStubInStore(
  store: RecordSourceSelectorProxy,
  data: OptimisticPayStubData
): string {
  const tempId = data.id || `client:temp:paystub:${Date.now()}`;
  
  const payStub = store.create(tempId, 'PayStub');
  payStub.setValue(tempId, 'id');
  
  if (data.employeeId !== undefined) {
    payStub.setValue(data.employeeId, 'employeeId');
  }
  
  if (data.name !== undefined) {
    payStub.setValue(data.name, 'name');
  }
  
  if (data.totalHours !== undefined) {
    payStub.setValue(data.totalHours, 'totalHours');
  }
  
  // Link employee if provided
  if (data.employee) {
    const employee = store.get(data.employee.id) || store.create(data.employee.id, 'Employee');
    employee.setValue(data.employee.id, 'id');
    employee.setValue(data.employee.firstName, 'firstName');
    employee.setValue(data.employee.lastName, 'lastName');
    employee.setValue(data.employee.active, 'active');
    
    payStub.setLinkedRecord(employee, 'employee');
  }
  
  // Create empty details connection
  const detailsConnection = store.create(`${tempId}:details`, 'PayStubDetailConnection');
  detailsConnection.setLinkedRecords([], 'edges');
  payStub.setLinkedRecord(detailsConnection, 'details');
  
  return tempId;
}

/**
 * Creates an optimistic PayStubDetail record for testing/mocking
 * @deprecated Use createOptimisticPayStubDetailInStore for actual Relay store operations
 */
export function createOptimisticPayStubDetail(data: OptimisticPayStubDetailData = {}): any {
  const tempId = data.id || `client:temp:detail:${Date.now()}_${tempIdCounter++}`;
  
  // Calculate totalHours from provided hour values
  const stHours = data.stHours || 0;
  const otHours = data.otHours || 0;
  const dtHours = data.dtHours || 0;
  const totalHours = data.totalHours !== undefined ? data.totalHours : (stHours + otHours + dtHours);
  
  return {
    id: tempId,
    payStubId: data.payStubId || null,
    reportLineItemId: data.reportLineItemId || null,
    workDate: data.workDate || null,
    name: data.name || null,
    stHours,
    otHours,
    dtHours,
    totalHours,
    jobCode: data.jobCode || null,
    agreementId: data.agreementId || null,
    classificationId: data.classificationId || null,
    subClassificationId: data.subClassificationId || null,
    costCenter: data.costCenter || null,
    hourlyRate: data.hourlyRate || null,
    bonus: data.bonus || null,
    expenses: data.expenses || null,
    earningsCode: data.earningsCode || null,
    __typename: 'PayStubDetail'
  };
}

/**
 * Creates an optimistic PayStubDetail record in the actual Relay store
 */
export function createOptimisticPayStubDetailInStore(
  store: RecordSourceSelectorProxy,
  data: OptimisticPayStubDetailData
): string {
  const tempId = data.id || `client:temp:detail:${Date.now()}`;
  
  const detail = store.create(tempId, 'PayStubDetail');
  detail.setValue(tempId, 'id');
  
  // Set specific fields individually to avoid type issues
  if (data.payStubId !== undefined) detail.setValue(data.payStubId, 'payStubId');
  if (data.reportLineItemId !== undefined) detail.setValue(data.reportLineItemId, 'reportLineItemId');
  if (data.workDate !== undefined) detail.setValue(data.workDate, 'workDate');
  if (data.name !== undefined) detail.setValue(data.name, 'name');
  if (data.stHours !== undefined) detail.setValue(data.stHours, 'stHours');
  if (data.otHours !== undefined) detail.setValue(data.otHours, 'otHours');
  if (data.dtHours !== undefined) detail.setValue(data.dtHours, 'dtHours');
  if (data.totalHours !== undefined) detail.setValue(data.totalHours, 'totalHours');
  if (data.jobCode !== undefined) detail.setValue(data.jobCode, 'jobCode');
  if (data.agreementId !== undefined) detail.setValue(data.agreementId, 'agreementId');
  if (data.classificationId !== undefined) detail.setValue(data.classificationId, 'classificationId');
  if (data.subClassificationId !== undefined) detail.setValue(data.subClassificationId, 'subClassificationId');
  if (data.costCenter !== undefined) detail.setValue(data.costCenter, 'costCenter');
  if (data.hourlyRate !== undefined) detail.setValue(data.hourlyRate, 'hourlyRate');
  if (data.bonus !== undefined) detail.setValue(data.bonus, 'bonus');
  if (data.expenses !== undefined) detail.setValue(data.expenses, 'expenses');
  if (data.earningsCode !== undefined) detail.setValue(data.earningsCode, 'earningsCode');
  
  return tempId;
}

/**
 * Updates PayStub fields optimistically
 */
export function updateOptimisticPayStub(
  store: RecordSourceSelectorProxy,
  payStubId: string,
  updates: Partial<OptimisticPayStubData>
): void {
  const payStub = store.get(payStubId);
  if (!payStub) {
    console.warn(`PayStub ${payStubId} not found for optimistic update`);
    return;
  }
  
  // Update specific fields individually to avoid type issues
  if (updates.employeeId !== undefined) payStub.setValue(updates.employeeId, 'employeeId');
  if (updates.name !== undefined) payStub.setValue(updates.name, 'name');
  if (updates.totalHours !== undefined) payStub.setValue(updates.totalHours, 'totalHours');
  
  // Handle employee updates separately
  if (updates.employee) {
    const employee = store.get(updates.employee.id) || store.create(updates.employee.id, 'Employee');
    employee.setValue(updates.employee.id, 'id');
    employee.setValue(updates.employee.firstName, 'firstName');
    employee.setValue(updates.employee.lastName, 'lastName');
    employee.setValue(updates.employee.active, 'active');
    
    payStub.setLinkedRecord(employee, 'employee');
  }
}

/**
 * Updates PayStubDetail fields optimistically
 */
export function updateOptimisticPayStubDetail(
  store: RecordSourceSelectorProxy,
  detailId: string,
  updates: Partial<OptimisticPayStubDetailData>
): void {
  const detail = store.get(detailId);
  if (!detail) {
    console.warn(`PayStubDetail ${detailId} not found for optimistic update`);
    return;
  }
  
  // Update specific fields individually to avoid type issues
  if (updates.payStubId !== undefined) detail.setValue(updates.payStubId, 'payStubId');
  if (updates.reportLineItemId !== undefined) detail.setValue(updates.reportLineItemId, 'reportLineItemId');
  if (updates.workDate !== undefined) detail.setValue(updates.workDate, 'workDate');
  if (updates.name !== undefined) detail.setValue(updates.name, 'name');
  if (updates.stHours !== undefined) detail.setValue(updates.stHours, 'stHours');
  if (updates.otHours !== undefined) detail.setValue(updates.otHours, 'otHours');
  if (updates.dtHours !== undefined) detail.setValue(updates.dtHours, 'dtHours');
  if (updates.totalHours !== undefined) detail.setValue(updates.totalHours, 'totalHours');
  if (updates.jobCode !== undefined) detail.setValue(updates.jobCode, 'jobCode');
  if (updates.agreementId !== undefined) detail.setValue(updates.agreementId, 'agreementId');
  if (updates.classificationId !== undefined) detail.setValue(updates.classificationId, 'classificationId');
  if (updates.subClassificationId !== undefined) detail.setValue(updates.subClassificationId, 'subClassificationId');
  if (updates.costCenter !== undefined) detail.setValue(updates.costCenter, 'costCenter');
  if (updates.hourlyRate !== undefined) detail.setValue(updates.hourlyRate, 'hourlyRate');
  if (updates.bonus !== undefined) detail.setValue(updates.bonus, 'bonus');
  if (updates.expenses !== undefined) detail.setValue(updates.expenses, 'expenses');
  if (updates.earningsCode !== undefined) detail.setValue(updates.earningsCode, 'earningsCode');
}

/**
 * Enhanced mutation error handling with retry capabilities and user-friendly messages
 */
export interface MutationErrorHandlerOptions {
  entityId: string;
  rollback?: (error: TimesheetError) => void;
  onError?: (error: TimesheetError) => void;
  onRetrySuccess?: () => void;
  onRetryFailure?: (error: TimesheetError) => void;
  retryOperation?: () => Promise<void>;
}

/**
 * Handles mutation errors with enhanced error handling and automatic retry
 */
export function handleMutationError(
  error: Error,
  options: MutationErrorHandlerOptions
): TimesheetError {
  const enhancedError = parseRelayError(error);
  
  console.error('Mutation error:', {
    error: enhancedError,
    entityId: options.entityId,
    context: enhancedError.enhancedError.context
  });
  
  // Notify error handler
  options.onError?.(enhancedError);
  
  // Attempt rollback if provided
  if (options.rollback) {
    try {
      options.rollback(enhancedError);
    } catch (rollbackError) {
      console.error('Error during rollback:', rollbackError);
    }
  }
  
  // Attempt automatic retry for retryable errors
  if (shouldRetryError(enhancedError) && options.retryOperation) {
    handleAutomaticRetry(enhancedError, options);
  }
  
  return enhancedError;
}

/**
 * Handles automatic retry with exponential backoff
 */
async function handleAutomaticRetry(
  error: TimesheetError,
  options: MutationErrorHandlerOptions
): Promise<void> {
  if (!options.retryOperation || !shouldRetryError(error)) {
    return;
  }
  
  const retryDelay = 1000 * Math.pow(2, error.enhancedError.retryCount);
  
  setTimeout(async () => {
    try {
      await options.retryOperation!();
      options.onRetrySuccess?.();
    } catch (retryError) {
      const incrementedError = incrementRetryCount(error);
      options.onRetryFailure?.(incrementedError);
      
      // Try again if we haven't exceeded max retries
      if (shouldRetryError(incrementedError)) {
        handleAutomaticRetry(incrementedError, options);
      }
    }
  }, retryDelay);
}

/**
 * Legacy error handler for backward compatibility
 * @deprecated Use handleMutationError with MutationErrorHandlerOptions instead
 */
export function handleLegacyMutationError(
  error: Error,
  rollback?: (error: Error) => void
): void {
  console.error('Mutation error:', error);
  
  if (rollback) {
    try {
      rollback(error);
    } catch (rollbackError) {
      console.error('Error during rollback:', rollbackError);
    }
  }
}

/**
 * Removes a PayStub from a TimeSheet's payStubs connection
 */
export function removePayStubFromConnection(
  store: RecordSourceSelectorProxy,
  timeSheetId: string,
  payStubId: string
): void {
  const timeSheet = store.get(timeSheetId);
  if (!timeSheet) {
    console.warn(`TimeSheet ${timeSheetId} not found for connection update`);
    return;
  }
  
  const payStubsConnection = timeSheet.getLinkedRecord('payStubs');
  if (payStubsConnection) {
    const existingEdges = payStubsConnection.getLinkedRecords('edges') || [];
    const filteredEdges = existingEdges.filter(edge => {
      const node = edge?.getLinkedRecord('node');
      return node?.getValue('id') !== payStubId;
    });
    
    payStubsConnection.setLinkedRecords(filteredEdges, 'edges');
  }
}

/**
 * Adds a PayStub to a TimeSheet's payStubs connection
 */
export function addPayStubToConnection(
  store: RecordSourceSelectorProxy,
  timeSheetId: string,
  payStubId: string
): void {
  const timeSheet = store.get(timeSheetId);
  const payStub = store.get(payStubId);
  
  if (!timeSheet || !payStub) {
    console.warn(`TimeSheet ${timeSheetId} or PayStub ${payStubId} not found for connection update`);
    return;
  }
  
  const payStubsConnection = timeSheet.getLinkedRecord('payStubs');
  if (payStubsConnection) {
    const existingEdges = payStubsConnection.getLinkedRecords('edges') || [];
    const newEdge = store.create(`${payStubId}:edge`, 'PayStubEdge');
    newEdge.setLinkedRecord(payStub, 'node');
    
    payStubsConnection.setLinkedRecords(
      [...existingEdges, newEdge],
      'edges'
    );
  }
}