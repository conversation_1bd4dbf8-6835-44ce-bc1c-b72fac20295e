/**
 * Tests for AddEmptyPayStub mutation
 *
 * Comprehensive test suite covering:
 * - Hook functionality and state management
 * - Input validation (Global ID format)
 * - Error handling scenarios
 * - Optimistic updates with Global IDs
 * - Integration with Relay store
 */

import { renderHook } from '@testing-library/react';
import { useAddEmptyPayStub, validateAddEmptyPayStubInput, safeAddEmptyPayStub, AddEmptyPayStubInput, addEmptyPayStubMutation } from '../AddEmptyPayStubMutation';

// Override the global mock for this specific test file
jest.mock('../AddEmptyPayStubMutation', () => {
    const actual = jest.requireActual('../AddEmptyPayStubMutation');
    return actual; // Use the real implementation
});

// Mock both useMutation and commitMutation from react-relay
jest.mock('react-relay', () => ({
    ...jest.requireActual('react-relay'),
    useMutation: jest.fn(),
    commitMutation: jest.fn()
}));

const mockCommitMutation = jest.fn();
const mockUseMutation = jest.mocked(require('react-relay').useMutation);
const mockCommitMutationDirect = jest.mocked(require('react-relay').commitMutation);

describe('AddEmptyPayStubMutation', () => {
    beforeEach(() => {
        jest.clearAllMocks();

        // Mock useMutation to return our mock commitMutation function
        mockUseMutation.mockReturnValue([mockCommitMutation, false]);

        // Reset the mock implementation
        mockCommitMutation.mockImplementation((config) => {
            // Return a disposable object like the real commitMutation does
            return { dispose: jest.fn() };
        });
    });

    describe('useAddEmptyPayStub hook', () => {
        it('should return addEmptyPayStub function and loading state', () => {
            const { result } = renderHook(() => useAddEmptyPayStub());

            expect(result.current).toHaveProperty('addEmptyPayStub');
            expect(result.current).toHaveProperty('isLoading');
            expect(typeof result.current.addEmptyPayStub).toBe('function');
            expect(typeof result.current.isLoading).toBe('boolean');
        });

        it('should call commitMutation when addEmptyPayStub is called', async () => {
            const { result } = renderHook(() => useAddEmptyPayStub());

            const timeSheetId = 'VGltZVNoZWV0OjEyMw=='; // TimeSheet:123 in base64
            const employeeId = 'RW1wbG95ZWU6NDU2'; // Employee:456 in base64

            // Set up the mock to call onCompleted to resolve the promise
            mockCommitMutation.mockImplementation((config) => {
                // Cast to any to access the config properties
                const mutationConfig = config;
                mutationConfig.onCompleted?.(
                    {
                        addEmptyPayStub: {
                            payStubEdge: { 
                                node: { id: 'ps-123' }
                            },
                            errors: []
                        }
                    },
                    null
                );
                return { dispose: jest.fn() };
            });

            // Call the function and wait for it to complete
            await result.current.addEmptyPayStub(timeSheetId, employeeId);

            expect(mockCommitMutation).toHaveBeenCalledWith(
                expect.objectContaining({
                    variables: {
                        input: {
                            timeSheetId: 'VGltZVNoZWV0OjEyMw==', // Should pass Global ID directly
                            employeeId: 'RW1wbG95ZWU6NDU2' // Should pass Global ID directly
                        },
                        connections: expect.arrayContaining([expect.any(String)])
                    },
                    onCompleted: expect.any(Function),
                    onError: expect.any(Function),
                    optimisticResponse: expect.any(Object)
                })
            );
        });
    });

    describe('Input Validation', () => {
        it('should validate valid input', () => {
            const validInput: AddEmptyPayStubInput = {
                timeSheetId: 'VGltZVNoZWV0OjEyMw==', // TimeSheet:123 in base64
                employeeId: 'RW1wbG95ZWU6NDU2' // Employee:456 in base64
            };

            expect(() => validateAddEmptyPayStubInput(validInput)).not.toThrow();
        });

        it('should throw error for invalid timeSheetId', () => {
            const invalidInput: AddEmptyPayStubInput = {
                timeSheetId: '',
                employeeId: 'RW1wbG95ZWU6NDU2'
            };

            expect(() => validateAddEmptyPayStubInput(invalidInput)).toThrow('timeSheetId is required and must be a non-empty string');
        });

        it('should throw error for invalid employeeId', () => {
            const invalidInput: AddEmptyPayStubInput = {
                timeSheetId: 'VGltZVNoZWV0OjEyMw==',
                employeeId: ''
            };

            expect(() => validateAddEmptyPayStubInput(invalidInput)).toThrow('employeeId is required and must be a non-empty string');
        });
    });

    describe('Safe Wrapper', () => {
        it('should call validation before executing function', async () => {
            const mockAddEmptyPayStub = jest.fn().mockResolvedValue('ps-123');
            const timeSheetId = 'VGltZVNoZWV0OjEyMw=='; // TimeSheet:123 in base64
            const employeeId = 'RW1wbG95ZWU6NDU2'; // Employee:456 in base64
            const connectionId = 'connection-123';

            await safeAddEmptyPayStub(mockAddEmptyPayStub, timeSheetId, employeeId, connectionId);

            expect(mockAddEmptyPayStub).toHaveBeenCalledWith(timeSheetId, employeeId, connectionId);
        });

        it('should throw validation error for invalid input', async () => {
            const mockAddEmptyPayStub = jest.fn();

            await expect(safeAddEmptyPayStub(mockAddEmptyPayStub, '', 'RW1wbG95ZWU6NDU2', 'connection-123')).rejects.toThrow(
                'timeSheetId is required and must be a non-empty string'
            );

            expect(mockAddEmptyPayStub).not.toHaveBeenCalled();
        });
    });

    describe('Optimistic Updates', () => {
        it('should include optimisticUpdater in mutation config', async () => {
            const { result } = renderHook(() => useAddEmptyPayStub());

            // Set up the mock to call onCompleted to resolve the promise
            mockCommitMutation.mockImplementation((config) => {
                const mutationConfig = config;
                mutationConfig.onCompleted?.(
                    {
                        addEmptyPayStub: {
                            payStubEdge: { 
                                node: { id: 'ps-123' }
                            },
                            errors: []
                        }
                    },
                    null
                );
                return { dispose: jest.fn() };
            });

            await result.current.addEmptyPayStub('VGltZVNoZWV0OjEyMw==', 'RW1wbG95ZWU6NDU2');

            const mutationConfig = mockCommitMutation.mock.calls[0][0];
            expect(mutationConfig).toHaveProperty('optimisticResponse');
            expect(typeof mutationConfig.optimisticResponse).toBe('object');
        });

        it('should create optimistic PayStub with correct structure', async () => {
            const { result } = renderHook(() => useAddEmptyPayStub());

            // Set up the mock to call onCompleted to resolve the promise
            mockCommitMutation.mockImplementation((config) => {
                const mutationConfig = config;
                mutationConfig.onCompleted?.(
                    {
                        addEmptyPayStub: {
                            payStubEdge: { 
                                node: { id: 'ps-123' }
                            },
                            errors: []
                        }
                    },
                    null
                );
                return { dispose: jest.fn() };
            });

            await result.current.addEmptyPayStub('VGltZVNoZWV0OjEyMw==', 'RW1wbG95ZWU6NDU2');

            const mutationConfig = mockCommitMutation.mock.calls[0][0];
            const optimisticResponse = mutationConfig.optimisticResponse;

            // Verify optimistic response structure
            expect(optimisticResponse).toHaveProperty('addEmptyPayStub');
            expect(optimisticResponse.addEmptyPayStub).toHaveProperty('payStubEdge');
            expect(optimisticResponse.addEmptyPayStub.payStubEdge).toHaveProperty('node');
            expect(optimisticResponse.addEmptyPayStub.payStubEdge.node).toHaveProperty('id');
            expect(optimisticResponse.addEmptyPayStub.payStubEdge.node.id).toMatch(/client:temp:paystub:RW1wbG95ZWU6NDU2:\d+/);
            expect(optimisticResponse.addEmptyPayStub.payStubEdge.node).toHaveProperty('employee');
            expect(optimisticResponse.addEmptyPayStub.payStubEdge.node.employee).toHaveProperty('id');
            expect(optimisticResponse.addEmptyPayStub.payStubEdge.node.employee.id).toBe('RW1wbG95ZWU6NDU2');
            // After Phase 1: optimistic response only includes employee ID link, not full data
            expect(optimisticResponse.addEmptyPayStub.payStubEdge.node.employee.firstName).toBeUndefined();
        });

        it.skip('should handle missing TimeSheet gracefully in optimistic update', async () => {
            const mockStore = {
                get: jest.fn(() => null), // TimeSheet not found
                create: jest.fn()
            } as any;

            const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

            const { result } = renderHook(() => useAddEmptyPayStub());

            // Set up the mock to call onCompleted to resolve the promise
            mockCommitMutation.mockImplementation((config) => {
                const mutationConfig = config;
                mutationConfig.onCompleted?.(
                    {
                        addEmptyPayStub: {
                            payStubEdge: { 
                                node: { id: 'ps-123' }
                            },
                            errors: []
                        }
                    },
                    null
                );
                return { dispose: jest.fn() };
            });

            await result.current.addEmptyPayStub('VGltZVNoZWV0Ojk5OQ==', 'RW1wbG95ZWU6MTIz');

            const mutationConfig = mockCommitMutation.mock.calls[0][0];
            const optimisticUpdater = mutationConfig.optimisticUpdater;

            // Should not throw when TimeSheet is missing
            expect(() => optimisticUpdater(mockStore)).not.toThrow();

            // Should log warning with Global ID
            expect(consoleSpy).toHaveBeenCalledWith('TimeSheet VGltZVNoZWV0Ojk5OQ== not found for optimistic update');

            consoleSpy.mockRestore();
        });

        it.skip('should create minimal employee record when employee not in cache', async () => {
            const mockStore = {
                get: jest.fn((id: string) => {
                    if (id === 'VGltZVNoZWV0OjEyMw==') {
                        // TimeSheet Global ID
                        return {
                            getValue: jest.fn(),
                            setValue: jest.fn(),
                            getLinkedRecords: jest.fn(() => []),
                            setLinkedRecords: jest.fn()
                        };
                    }
                    // Employee not in cache
                    return null;
                }),
                create: jest.fn((id: string, type: string) => ({
                    setValue: jest.fn(),
                    setLinkedRecord: jest.fn(),
                    setLinkedRecords: jest.fn()
                }))
            } as any;

            const { result } = renderHook(() => useAddEmptyPayStub());

            // Set up the mock to call onCompleted to resolve the promise
            mockCommitMutation.mockImplementation((config) => {
                const mutationConfig = config;
                mutationConfig.onCompleted?.(
                    {
                        addEmptyPayStub: {
                            payStubEdge: { 
                                node: { id: 'ps-123' }
                            },
                            errors: []
                        }
                    },
                    null
                );
                return { dispose: jest.fn() };
            });

            await result.current.addEmptyPayStub('VGltZVNoZWV0OjEyMw==', 'RW1wbG95ZWU6Nzg5');

            const mutationConfig = mockCommitMutation.mock.calls[0][0];
            const optimisticUpdater = mutationConfig.optimisticUpdater;

            optimisticUpdater(mockStore);

            // Should create temporary employee record with Global ID
            expect(mockStore.create).toHaveBeenCalledWith('RW1wbG95ZWU6Nzg5', 'Employee');

            // Should create PayStub with Loading... name
            const payStubRecord = mockStore.create.mock.results[0].value;
            expect(payStubRecord.setValue).toHaveBeenCalledWith('Loading...', 'name');
        });
    });

    describe('addEmptyPayStubMutation (non-hook version)', () => {
        const mockEnvironment = {
            execute: jest.fn(),
            retain: jest.fn(),
            check: jest.fn(),
            lookup: jest.fn(),
            subscribe: jest.fn(),
            sendMutation: jest.fn(),
            executeMutation: jest.fn(),
            getStore: jest.fn(),
            UNSTABLE_getDefaultRenderPolicy: jest.fn(),
            configName: 'test'
        } as any;

        beforeEach(() => {
            jest.clearAllMocks();
            
            // Set up the mock to call onCompleted to resolve the promise
            mockCommitMutationDirect.mockImplementation((environment: any, config: any) => {
                // Return a disposable object like the real commitMutation does
                return { dispose: jest.fn() };
            });
        });

        it('should successfully call addEmptyPayStub mutation', async () => {
            // Arrange
            const timeSheetId = 'VGltZVNoZWV0OjEyMw=='; // TimeSheet:123
            const employeeId = 'RW1wbG95ZWU6NDU2'; // Employee:456

            const mockResponse = {
                addEmptyPayStub: {
                    payStubEdge: {
                        cursor: 'cursor123',
                        node: {
                            id: 'UGF5U3R1YjoxMjM=', // PayStub:123
                            employeeId: employeeId,
                            totalHours: 0,
                            employee: {
                                id: employeeId
                            },
                            details: []
                        }
                    },
                    errors: []
                }
            };

            mockCommitMutationDirect.mockImplementation((environment: any, config: any) => {
                // Simulate successful mutation
                setTimeout(() => {
                    config.onCompleted(mockResponse);
                }, 0);
                return { dispose: jest.fn() };
            });

            // Act
            const promise = addEmptyPayStubMutation(mockEnvironment, timeSheetId, employeeId);

            // Wait for async completion
            const result = await promise;

            // Assert
            expect(result).toBe('UGF5U3R1YjoxMjM='); // Should return the PayStub ID
            expect(mockCommitMutationDirect).toHaveBeenCalledWith(
                mockEnvironment,
                expect.objectContaining({
                    variables: {
                        input: { timeSheetId, employeeId },
                        connections: expect.arrayContaining([expect.stringContaining('PayStubTable_connectionFragment__payStubs')])
                    },
                    onCompleted: expect.any(Function),
                    onError: expect.any(Function),
                    optimisticResponse: expect.objectContaining({
                        addEmptyPayStub: expect.objectContaining({
                            payStubEdge: expect.objectContaining({
                                node: expect.objectContaining({
                                    employeeId: employeeId
                                })
                            })
                        })
                    })
                })
            );
        });

        it('should handle mutation errors properly', async () => {
            // Arrange
            const timeSheetId = 'VGltZVNoZWV0OjEyMw==';
            const employeeId = 'RW1wbG95ZWU6NDU2';

            const mockResponse = {
                addEmptyPayStub: {
                    payStubEdge: null,
                    errors: ['TimeSheet not found']
                }
            };

            mockCommitMutationDirect.mockImplementation((environment: any, config: any) => {
                setTimeout(() => {
                    config.onCompleted(mockResponse);
                }, 0);
                return { dispose: jest.fn() };
            });

            // Act & Assert
            await expect(addEmptyPayStubMutation(mockEnvironment, timeSheetId, employeeId))
                .rejects
                .toThrow('Mutation Error: TimeSheet not found');
        });

        it('should handle network errors properly', async () => {
            // Arrange
            const timeSheetId = 'VGltZVNoZWV0OjEyMw==';
            const employeeId = 'RW1wbG95ZWU6NDU2';

            const networkError = new Error('Network failure');

            mockCommitMutationDirect.mockImplementation((environment: any, config: any) => {
                setTimeout(() => {
                    config.onError(networkError);
                }, 0);
                return { dispose: jest.fn() };
            });

            // Act & Assert
            await expect(addEmptyPayStubMutation(mockEnvironment, timeSheetId, employeeId))
                .rejects
                .toThrow('Network Error: Network failure');
        });

        it('should validate input before making the request', async () => {
            // Arrange
            const invalidTimeSheetId = '';
            const employeeId = 'RW1wbG95ZWU6NDU2';

            // Act & Assert
            await expect(addEmptyPayStubMutation(mockEnvironment, invalidTimeSheetId, employeeId))
                .rejects
                .toThrow('timeSheetId is required and must be a non-empty string');

            // Should not call the mutation if validation fails
            expect(mockCommitMutationDirect).not.toHaveBeenCalled();
        });
    });
});
