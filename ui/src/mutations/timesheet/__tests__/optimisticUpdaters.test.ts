import { 
  createOptimisticPayStub, 
  createOptimisticPayStubDetail,
  handleMutationError,
  handleLegacyMutationError,
  createOptimisticTimesheet,
  type MutationErrorHandlerOptions
} from '../optimisticUpdaters';

describe('Optimistic Update Helpers', () => {
  describe('createOptimisticPayStub', () => {
    it('should create optimistic PayStub with temp ID', () => {
      const result = createOptimisticPayStub({
        employeeId: 'RW1wbG95ZWU6MTIz', // Global ID for Employee:123
        name: 'John Doe PayStub'
      });
      
      expect(result).toMatchObject({
        id: expect.stringContaining('client:temp:paystub:'),
        employeeId: 'RW1wbG95ZWU6MTIz',
        name: 'John Doe PayStub',
        totalHours: 0,
        details: [],
        __typename: 'PayStub'
      });
      
      expect(result.id).toMatch(/^client:temp:paystub:\d+_\d+$/);
    });
    
    it('should use provided ID if given', () => {
      const result = createOptimisticPayStub({
        id: 'custom-id',
        employeeId: 'RW1wbG95ZWU6NDU2', // Global ID for Employee:456
        name: 'Custom PayStub'
      });
      
      expect(result.id).toBe('custom-id');
      expect(result.employeeId).toBe('RW1wbG95ZWU6NDU2');
      expect(result.name).toBe('Custom PayStub');
    });
    
    it('should generate unique temp IDs for multiple calls', () => {
      const result1 = createOptimisticPayStub({ employeeId: 'RW1wbG95ZWU6MQ==', name: 'PS1' }); // Global ID for Employee:1
      const result2 = createOptimisticPayStub({ employeeId: 'RW1wbG95ZWU6Mg==', name: 'PS2' }); // Global ID for Employee:2
      
      expect(result1.id).not.toBe(result2.id);
      expect(result1.id).toContain('client:temp:paystub:');
      expect(result2.id).toContain('client:temp:paystub:');
    });
    
    it('should merge provided overrides with defaults', () => {
      const result = createOptimisticPayStub({
        employeeId: 'RW1wbG95ZWU6Nzg5', // Global ID for Employee:789
        name: 'Test PayStub',
        totalHours: 40,
        customProperty: 'custom value'
      } as any);
      
      expect(result.employeeId).toBe('RW1wbG95ZWU6Nzg5');
      expect(result.name).toBe('Test PayStub');
      expect(result.totalHours).toBe(40);
      expect((result).customProperty).toBe('custom value');
      expect(result.__typename).toBe('PayStub');
    });
    
    it('should handle empty overrides', () => {
      const result = createOptimisticPayStub({});
      
      expect(result.id).toContain('client:temp:paystub:');
      expect(result.employeeId).toBeNull();
      expect(result.name).toBeUndefined();
      expect(result.totalHours).toBe(0);
      expect(result.details).toEqual([]);
      expect(result.__typename).toBe('PayStub');
    });
  });
  
  describe('createOptimisticPayStubDetail', () => {
    it('should create optimistic PayStubDetail with temp ID', () => {
      const result = createOptimisticPayStubDetail({
        payStubId: 'ps-1',
        workDate: '2024-01-15',
        name: 'Monday'
      });
      
      expect(result).toMatchObject({
        id: expect.stringContaining('client:temp:detail:'),
        payStubId: 'ps-1',
        workDate: '2024-01-15',
        name: 'Monday',
        stHours: 0,
        otHours: 0,
        dtHours: 0,
        totalHours: 0,
        __typename: 'PayStubDetail'
      });
      
      expect(result.id).toMatch(/^client:temp:detail:\d+_\d+$/);
    });
    
    it('should use provided ID if given', () => {
      const result = createOptimisticPayStubDetail({
        id: 'detail-custom',
        payStubId: 'ps-1',
        workDate: '2024-01-16',
        name: 'Tuesday'
      });
      
      expect(result.id).toBe('detail-custom');
      expect(result.payStubId).toBe('ps-1');
      expect(result.workDate).toBe('2024-01-16');
      expect(result.name).toBe('Tuesday');
    });
    
    it('should calculate totalHours from stHours, otHours, dtHours', () => {
      const result = createOptimisticPayStubDetail({
        payStubId: 'ps-1',
        stHours: 8,
        otHours: 2,
        dtHours: 1
      });
      
      expect(result.stHours).toBe(8);
      expect(result.otHours).toBe(2);
      expect(result.dtHours).toBe(1);
      expect(result.totalHours).toBe(11);
    });
    
    it('should handle partial hour data', () => {
      const result1 = createOptimisticPayStubDetail({
        payStubId: 'ps-1',
        stHours: 6
      });
      
      expect(result1.totalHours).toBe(6);
      
      const result2 = createOptimisticPayStubDetail({
        payStubId: 'ps-1',
        otHours: 3
      });
      
      expect(result2.totalHours).toBe(3);
    });
    
    it('should generate unique temp IDs', () => {
      const result1 = createOptimisticPayStubDetail({ payStubId: 'ps-1' });
      const result2 = createOptimisticPayStubDetail({ payStubId: 'ps-1' });
      
      expect(result1.id).not.toBe(result2.id);
      expect(result1.id).toContain('client:temp:detail:');
      expect(result2.id).toContain('client:temp:detail:');
    });
  });
  
  describe('createOptimisticTimesheet', () => {
    it('should create optimistic Timesheet with temp ID', () => {
      const result = createOptimisticTimesheet({
        employerGuid: 'employer-1',
        payPeriodEndDate: '2024-01-15'
      });
      
      expect(result).toMatchObject({
        id: expect.stringContaining('client:temp:timesheet:'),
        employerGuid: 'employer-1',
        payPeriodEndDate: '2024-01-15',
        name: 'New Timesheet',
        status: 'Draft',
        payStubs: [],
        __typename: 'TimeSheet'
      });
    });
    
    it('should use provided overrides', () => {
      const result = createOptimisticTimesheet({
        id: 'ts-custom',
        name: 'Custom Timesheet',
        status: 'Submitted',
        employerGuid: 'employer-2'
      });
      
      expect(result.id).toBe('ts-custom');
      expect(result.name).toBe('Custom Timesheet');
      expect(result.status).toBe('Submitted');
      expect(result.employerGuid).toBe('employer-2');
    });
  });
  
  describe('handleMutationError (Enhanced)', () => {
    let consoleErrorSpy: jest.SpyInstance;
    
    beforeEach(() => {
      consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    });
    
    afterEach(() => {
      consoleErrorSpy.mockRestore();
    });
    
    it('should return enhanced error and call error handler', () => {
      const testError = new Error('Test error message');
      const onError = jest.fn();
      const rollback = jest.fn();
      
      const options: MutationErrorHandlerOptions = {
        entityId: 'test-entity',
        onError,
        rollback
      };
      
      const result = handleMutationError(testError, options);
      
      expect(result).toHaveProperty('enhancedError');
      expect(result.enhancedError.message).toBe('Test error message');
      expect(onError).toHaveBeenCalledWith(result);
      expect(rollback).toHaveBeenCalledWith(result);
    });
    
    it('should handle error without optional callbacks', () => {
      const testError = new Error('Another test error');
      const options: MutationErrorHandlerOptions = {
        entityId: 'test-entity-2'
      };
      
      const result = handleMutationError(testError, options);
      
      expect(result).toHaveProperty('enhancedError');
      expect(result.enhancedError.message).toBe('Another test error');
    });
    
    it('should not throw if rollback function throws', () => {
      const testError = new Error('Original error');
      const rollback = jest.fn().mockImplementation(() => {
        throw new Error('Rollback error');
      });
      
      const options: MutationErrorHandlerOptions = {
        entityId: 'test-entity-3',
        rollback
      };
      
      expect(() => {
        handleMutationError(testError, options);
      }).not.toThrow();
      
      expect(rollback).toHaveBeenCalled();
    });
  });

  describe('handleLegacyMutationError', () => {
    let consoleErrorSpy: jest.SpyInstance;
    
    beforeEach(() => {
      consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    });
    
    afterEach(() => {
      consoleErrorSpy.mockRestore();
    });
    
    it('should log error and call rollback if provided', () => {
      const testError = new Error('Test error message');
      const rollback = jest.fn();
      
      handleLegacyMutationError(testError, rollback);
      
      expect(consoleErrorSpy).toHaveBeenCalledWith('Mutation error:', testError);
      expect(rollback).toHaveBeenCalledWith(testError);
    });
    
    it('should log error without rollback if not provided', () => {
      const testError = new Error('Another test error');
      
      handleLegacyMutationError(testError);
      
      expect(consoleErrorSpy).toHaveBeenCalledWith('Mutation error:', testError);
    });
  });
  
  describe('ID Generation', () => {
    it('should generate unique IDs across different types', () => {
      const payStub = createOptimisticPayStub({});
      const detail = createOptimisticPayStubDetail({ payStubId: 'ps-1' });
      const timesheet = createOptimisticTimesheet({});
      
      expect(payStub.id).toContain('paystub');
      expect(detail.id).toContain('detail');
      expect(timesheet.id).toContain('timesheet');
      
      // Should not conflict with each other
      expect(payStub.id).not.toBe(detail.id);
      expect(detail.id).not.toBe(timesheet.id);
      expect(payStub.id).not.toBe(timesheet.id);
    });
    
    it('should generate consistent format for temp IDs', () => {
      const payStub1 = createOptimisticPayStub({});
      const payStub2 = createOptimisticPayStub({});
      
      // Both should follow the same pattern
      expect(payStub1.id).toMatch(/^client:temp:paystub:\d+_\d+$/);
      expect(payStub2.id).toMatch(/^client:temp:paystub:\d+_\d+$/);
      
      // Extract the counter part and verify it's incrementing
      const counter1 = parseInt(payStub1.id.split('_')[1]);
      const counter2 = parseInt(payStub2.id.split('_')[1]);
      
      expect(counter2).toBeGreaterThan(counter1);
    });
  });
  
  describe('Type Safety', () => {
    it('should include __typename for GraphQL compatibility', () => {
      const payStub = createOptimisticPayStub({});
      const detail = createOptimisticPayStubDetail({ payStubId: 'ps-1' });
      const timesheet = createOptimisticTimesheet({});
      
      expect(payStub.__typename).toBe('PayStub');
      expect(detail.__typename).toBe('PayStubDetail');
      expect(timesheet.__typename).toBe('TimeSheet');
    });
    
    it('should maintain type structure for Relay compatibility', () => {
      const payStub = createOptimisticPayStub({
        employeeId: 'RW1wbG95ZWU6MTIz', // Global ID for Employee:123
        name: 'Test PayStub'
      });
      
      // Verify all required PayStub fields are present
      expect(payStub).toHaveProperty('id');
      expect(payStub).toHaveProperty('employeeId');
      expect(payStub).toHaveProperty('name');
      expect(payStub).toHaveProperty('totalHours');
      expect(payStub).toHaveProperty('details');
      expect(payStub).toHaveProperty('__typename');
      
      const detail = createOptimisticPayStubDetail({
        payStubId: 'ps-1',
        workDate: '2024-01-15'
      });
      
      // Verify all required PayStubDetail fields are present
      expect(detail).toHaveProperty('id');
      expect(detail).toHaveProperty('payStubId');
      expect(detail).toHaveProperty('workDate');
      expect(detail).toHaveProperty('stHours');
      expect(detail).toHaveProperty('otHours');
      expect(detail).toHaveProperty('dtHours');
      expect(detail).toHaveProperty('totalHours');
      expect(detail).toHaveProperty('__typename');
    });
  });
});