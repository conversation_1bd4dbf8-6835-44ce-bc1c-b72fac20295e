import { commitMutation } from 'react-relay';
import { modifyTimeSheet } from '../ModifyTimeSheetMutation';
import { createMockRelayEnvironment, MockEnvironment } from '../../../test-utils/relay-test-helpers';

// Mock react-relay
jest.mock('react-relay', () => ({
    ...jest.requireActual('react-relay'),
    commitMutation: jest.fn(),
    graphql: jest.fn((strings: TemplateStringsArray) => strings.join(''))
}));

describe('ModifyTimeSheetMutation', () => {
    let mockEnvironment: MockEnvironment;
    let mockCommitMutation: jest.MockedFunction<typeof commitMutation>;

    beforeEach(() => {
        mockEnvironment = createMockRelayEnvironment();
        mockCommitMutation = commitMutation as jest.MockedFunction<typeof commitMutation>;
        jest.clearAllMocks();
    });

    it('should call commitMutation with correct parameters', async () => {
        const input = {
            id: '1',
            employerGuid: 'employer-guid-1',
            timeSheetId: 'ts-1',
            modifyPayStubs: [
                {
                    id: 'ps-1',
                    employeeId: 'RW1wbG95ZWU6MTIz', // Global ID for Employee:123
                    totalHours: 10,
                    name: 'Updated notes'
                }
            ]
        };

        mockCommitMutation.mockImplementation((env, config) => {
            config.onCompleted?.(
                {
                    modifyTimeSheet: {
                        timeSheet: { id: 'ts-1' }
                    }
                },
                null
            );
            return { dispose: jest.fn() };
        });

        modifyTimeSheet(mockEnvironment, input);

        expect(mockCommitMutation).toHaveBeenCalledWith(
            mockEnvironment,
            expect.objectContaining({
                mutation: expect.any(Object), // GraphQL object from mocked graphql
                variables: { input },
                optimisticUpdater: expect.any(Function),
                onCompleted: expect.any(Function),
                onError: expect.any(Function)
            })
        );
    });

    it('should handle optimistic updater correctly', async () => {
        const mockPayStubRecord = {
            setValue: jest.fn(),
            getValue: jest.fn().mockReturnValue('ps-1')
        };

        const mockStore = {
            get: jest.fn().mockReturnValue(mockPayStubRecord),
            getRoot: jest.fn()
        };

        const input = {
            id: '1',
            employerGuid: 'employer-guid-1',
            timeSheetId: 'ts-1',
            modifyPayStubs: [
                {
                    id: 'ps-1',
                    employeeId: 'RW1wbG95ZWU6MTIz', // Global ID for Employee:123
                    totalHours: 10,
                    name: 'Updated'
                } as any
            ]
        };

        mockCommitMutation.mockImplementation((env, config) => {
            // Execute the optimistic updater
            if (config.optimisticUpdater) {
                config.optimisticUpdater(mockStore as any, {});
            }

            config.onCompleted?.(
                {
                    modifyTimeSheet: {
                        timeSheet: { id: 'ts-1' }
                    }
                },
                null
            );
            return { dispose: jest.fn() };
        });

        modifyTimeSheet(mockEnvironment, input);

        expect(mockStore.get).toHaveBeenCalledWith('ps-1');
        expect(mockPayStubRecord.setValue).toHaveBeenCalledWith('Updated', 'name');
        // Note: totalHours is now computed server-side, not set in optimistic updates
    });

    it('should handle errors appropriately', async () => {
        const error = new Error('Network error');

        mockCommitMutation.mockImplementation((env, config) => {
            config.onError?.(error);
            return { dispose: jest.fn() };
        });

        const result = modifyTimeSheet(mockEnvironment, {
            id: '1',
            employerGuid: 'employer-guid-1',
            timeSheetId: 'ts-1',
            modifyPayStubs: []
        });

        expect(result).toEqual({ dispose: expect.any(Function) });
    });

    it('should handle successful completion', async () => {
        const successResponse = {
            modifyTimeSheet: {
                timeSheet: {
                    id: 'ts-1',
                    modifyPayStubs: [{ id: 'ps-1', totalHours: 8 }]
                }
            }
        };

        mockCommitMutation.mockImplementation((env, config) => {
            config.onCompleted?.(successResponse, null);
            return { dispose: jest.fn() };
        });

        const result = modifyTimeSheet(mockEnvironment, {
            id: '1',
            employerGuid: 'employer-guid-1',
            timeSheetId: 'ts-1',
            modifyPayStubs: [
                {
                    id: 'ps-1',
                    employeeId: 'RW1wbG95ZWU6MTIz', // Global ID for Employee:123
                    totalHours: 8
                } as any
            ]
        });

        expect(result).toEqual({ dispose: expect.any(Function) });
    });

    it('should handle server-side validation errors', async () => {
        const successResponse = {
            modifyTimeSheet: {
                timeSheet: {
                    id: 'ts-1'
                }
            }
        };

        mockCommitMutation.mockImplementation((env, config) => {
            config.onCompleted?.(successResponse, null);
            return { dispose: jest.fn() };
        });

        const result = modifyTimeSheet(mockEnvironment, {
            id: '1',
            employerGuid: 'employer-guid-1',
            timeSheetId: 'ts-1',
            modifyPayStubs: [
                {
                    id: 'ps-1',
                    employeeId: 'RW1wbG95ZWU6MTIz', // Global ID for Employee:123
                    totalHours: -5,
                    name: ''
                } as any
            ]
        });

        expect(result).toEqual({ dispose: expect.any(Function) });
    });

    it('should create proper optimistic updater', () => {
        const input = {
            id: '1',
            employerGuid: 'employer-guid-1',
            timeSheetId: 'ts-1',
            modifyPayStubs: [
                { id: 'ps-1', employeeId: 'RW1wbG95ZWU6MTIz', name: 'Monday', totalHours: 8 } as any,
                { id: 'ps-2', employeeId: 'RW1wbG95ZWU6MTI0', name: 'Tuesday', totalHours: 7 } as any
            ]
        };

        mockCommitMutation.mockImplementation((env, config) => {
            // Verify optimistic updater exists
            expect(config.optimisticUpdater).toEqual(expect.any(Function));
            expect(config.onCompleted).toEqual(expect.any(Function));
            expect(config.onError).toEqual(expect.any(Function));

            return { dispose: jest.fn() };
        });

        modifyTimeSheet(mockEnvironment, input);

        expect(mockCommitMutation).toHaveBeenCalled();
    });

    it('should handle empty payStubs array', () => {
        const input = {
            id: '1',
            employerGuid: 'employer-guid-1',
            timeSheetId: 'ts-1',
            modifyPayStubs: []
        };

        mockCommitMutation.mockImplementation((env, config) => {
            expect(config.optimisticUpdater).toEqual(expect.any(Function));
            expect(config.variables).toEqual({ input });

            return { dispose: jest.fn() };
        });

        modifyTimeSheet(mockEnvironment, input);

        expect(mockCommitMutation).toHaveBeenCalled();
    });

    it('should handle partial field updates in optimistic updater', () => {
        const mockStore = {
            get: jest.fn(),
            create: jest.fn(),
            delete: jest.fn(),
            getRootField: jest.fn(),
            getPluralRootField: jest.fn(),
            invalidateStore: jest.fn(),
            readUpdatableQuery: jest.fn(),
            readUpdatableFragment: jest.fn(),
            commitPayload: jest.fn(),
            publishSource: jest.fn(),
            getRoot: jest.fn()
        };

        const mockPayStubRecord = {
            setValue: jest.fn()
        };

        const mockTimeSheetRecord = {
            getValue: jest.fn(),
            setValue: jest.fn()
        };

        mockStore.get.mockImplementation((id) => {
            if (id === 'ps-1') return mockPayStubRecord;
            if (id === 'ts-1') return mockTimeSheetRecord;
            return null;
        });

        const input = {
            id: '1',
            employerGuid: 'employer-guid-1',
            timeSheetId: 'ts-1',
            modifyPayStubs: [
                {
                    id: 'ps-1',
                    employeeId: 'RW1wbG95ZWU6MTIz', // Global ID for Employee:123
                    totalHours: 9
                    // Note: name is intentionally omitted
                } as any
            ]
        };

        // Clear previous mock implementation
        mockCommitMutation.mockClear();
        mockCommitMutation.mockImplementation((env, config) => {
            return { dispose: jest.fn() };
        });

        modifyTimeSheet(mockEnvironment, input);

        const callArgs = mockCommitMutation.mock.calls[0][1];
        const optimisticUpdater = callArgs.optimisticUpdater;
        if (optimisticUpdater) {
            optimisticUpdater(mockStore, {});
        }

        // Note: totalHours is now computed server-side, not set in optimistic updates
        expect(mockPayStubRecord.setValue).not.toHaveBeenCalledWith(expect.anything(), 'name');
    });
});
