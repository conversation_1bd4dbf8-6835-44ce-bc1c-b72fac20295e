/**
 * PayStubRowWrapper - Phase 3: Updated Component Integration
 *
 * This component has been updated to use the Phase 3 pattern:
 * - Uses the new PayStubRow component for fragment-based data loading
 * - Implements domain model event handlers
 * - Maintains backward compatibility while enabling Phase 3 benefits
 */

import React, { useCallback, memo } from 'react';
import { graphql, useFragment } from 'react-relay';
import { PayStubTable_payStub$key } from '@/src/relay/__generated__/PayStubTable_payStub.graphql';
import type { TimeSheetDetailTableView_payStub$key } from '@/src/relay/__generated__/TimeSheetDetailTableView_payStub.graphql';
import { Employee } from 'src/types/timesheet';
import { useTimesheetUIStore } from '@/src/store/timesheetUIStore';
import type { FlatPayStubWithDrafts } from '@/src/hooks/useFlatPayStub';
import PayStubRow from './PayStubRow';

/**
 * Props for PayStubRowWrapper component
 *
 * @param payStub - Relay fragment reference (NOT plain object)
 * @param employees - Employee list for selection (from parent query)
 * @param formatNumber - Formatting utility passed from parent
 * @param show*Column - Column visibility flags from parent configuration
 */
interface PayStubRowWrapperProps {
    // Only accept Relay fragment - no more temporary PayStub support
    payStub: PayStubTable_payStub$key;
    index: number;
    readOnly: boolean;
    employees?: ReadonlyArray<Employee>; // Only accept resolved employee data
    // Visibility flags passed down
    showBonusColumn: boolean;
    showCostCenterColumn: boolean;
    showDTHoursColumn: boolean;
    showEarningsCodesColumn: boolean;
    showExpensesColumn: boolean;
    // Helper functions passed down
    formatNumber: (value: number | null | undefined, format: 'n2' | 'c2') => string;
    payPeriodEndDate: string | null | undefined;
    // Timesheet context for mutations
    timeSheetId: string;
    numericId: number;
    employerGuid: string;
}

// Removed local PayStubDetail interface definition
// RelayPayStub is now imported from context instead of being defined locally

// Type guards removed - using single source of truth with Relay fragments

// Import the existing fragment from PayStubTable
import { PayStubTable_payStubFragment } from './PayStubTable';

/**
 * PayStubRowWrapper - Phase 3: Component Integration with Domain Models
 *
 * This component now uses the Phase 3 pattern:
 * - Uses existing PayStubTable fragment for data loading
 * - Passes fragment data to PayStubRow which handles domain model conversion
 * - Implements domain model event handlers that work with the TimesheetUI context
 * - Maintains all existing functionality while enabling type safety and testability
 */
const PayStubRowWrapper: React.FC<PayStubRowWrapperProps> = (props) => {

    // Resolve the existing PayStubTable fragment
    const payStubData = useFragment(PayStubTable_payStubFragment, props.payStub);

    // Get UI store actions for domain model event handlers with proper scoping
    const toggleExpansion = useTimesheetUIStore(state => state.toggleExpansion);
    const markForDeletion = useTimesheetUIStore(state => state.markForDeletion);
    const unmarkForDeletion = useTimesheetUIStore(state => state.unmarkForDeletion);
    const isMarkedForDeletion = useTimesheetUIStore(state => state.isMarkedForDeletion);

    // Flat types event handlers (Phase 5 migration)
    const handleEdit = useCallback((payStub: FlatPayStubWithDrafts) => {
        // Future: Implement edit functionality with flat types
    }, []);

    const handleDelete = useCallback(
        (payStub: FlatPayStubWithDrafts) => {
            if (!props.readOnly) {
                // Toggle deletion state using Zustand store with proper scoping
                const currentlyMarked = isMarkedForDeletion(props.timeSheetId, payStub.id);
                if (currentlyMarked) {
                    unmarkForDeletion(props.timeSheetId, payStub.id);
                } else {
                    markForDeletion(props.timeSheetId, payStub.id);
                }
            }
        },
        [props.readOnly, props.timeSheetId, markForDeletion, unmarkForDeletion, isMarkedForDeletion]
    );

    const handleToggleExpanded = useCallback(
        (payStub: FlatPayStubWithDrafts) => {
            if (payStub.employeeId) {
                // Use Zustand store action with proper scoping
                toggleExpansion(props.timeSheetId, payStub.id);
            }
        },
        [props.timeSheetId, toggleExpansion]
    );

    // Handle null payStub data
    if (!payStubData) {
        return (
            <tr>
                <td colSpan={10}>No PayStub data available</td>
            </tr>
        );
    }

    // Use the new Phase 3 PayStubRow component, passing both resolved data and fragment reference
    return (
        <PayStubRow
            {...props}
            payStub={payStubData}
            payStubFragmentRef={payStubData as unknown as PayStubTable_payStub$key} // Pass resolved data that contains nested fragment references
            onEdit={handleEdit}
            onDelete={handleDelete}
            onToggleExpanded={handleToggleExpanded}
        />
    );
};

// Define comparison function for memo to prevent unnecessary re-renders
const arePropsEqual = (prevProps: PayStubRowWrapperProps, nextProps: PayStubRowWrapperProps): boolean => {
    // Use reference equality for Relay fragments
    if (prevProps.payStub !== nextProps.payStub) {
        return false;
    }

    // Compare other props that could affect rendering behavior
    // Use deep comparison for employees array since it can affect EmployeeSelector
    if (prevProps.employees !== nextProps.employees) {
        if (!prevProps.employees || !nextProps.employees) {
            return false;
        }
        if (prevProps.employees.length !== nextProps.employees.length) {
            return false;
        }
        // Shallow comparison of employee objects (they should be stable references)
        for (let i = 0; i < prevProps.employees.length; i++) {
            if (prevProps.employees[i] !== nextProps.employees[i]) {
                return false;
            }
        }
    }

    // Compare scalar props
    return (
        prevProps.index === nextProps.index &&
        prevProps.readOnly === nextProps.readOnly &&
        prevProps.showBonusColumn === nextProps.showBonusColumn &&
        prevProps.showCostCenterColumn === nextProps.showCostCenterColumn &&
        prevProps.showDTHoursColumn === nextProps.showDTHoursColumn &&
        prevProps.showEarningsCodesColumn === nextProps.showEarningsCodesColumn &&
        prevProps.showExpensesColumn === nextProps.showExpensesColumn &&
        prevProps.formatNumber === nextProps.formatNumber &&
        prevProps.payPeriodEndDate === nextProps.payPeriodEndDate &&
        prevProps.timeSheetId === nextProps.timeSheetId &&
        prevProps.numericId === nextProps.numericId &&
        prevProps.employerGuid === nextProps.employerGuid
    );
};

// Re-enable memoization with enhanced debugging - the debugging will help identify any future issues
const MemoizedPayStubRowWrapper = memo(PayStubRowWrapper, arePropsEqual);

export default MemoizedPayStubRowWrapper;
