import React from 'react';
import { View, Text, ActionButton } from '@adobe/react-spectrum';
import AlertIcon from '@spectrum-icons/workflow/Alert';

interface ValidationBannerProps {
    count: number;
    onClick: () => void;
}

/**
 * ValidationBanner - Shows blocking validation errors count and allows user to navigate to errors
 *
 * Uses Spectrum design tokens for consistent negative/error styling since Banner component
 * is not available in this version of Adobe Spectrum.
 */
export const ValidationBanner: React.FC<ValidationBannerProps> = ({ count, onClick }) => {
    if (count === 0) {
        return null;
    }

    const message = `❌ ${count} ${count === 1 ? 'row' : 'rows'} contain blocking errors – fix them before saving.`;

    return (
        <View
            backgroundColor="negative"
            padding="size-200"
            borderRadius="medium"
            borderWidth="thin"
            borderColor="negative"
            marginBottom="size-200">
            <div role="alert" aria-live="assertive" style={{ width: '100%' }}>
                <ActionButton
                    onPress={onClick}
                    isQuiet
                    UNSAFE_style={{
                        color: 'var(--spectrum-semantic-negative-color-text-emphasis)',
                        width: '100%',
                        justifyContent: 'flex-start',
                        textAlign: 'left'
                    }}
                    aria-label={`${message} Click to navigate to first error.`}>
                    <AlertIcon />
                    <Text
                        UNSAFE_style={{
                            color: 'var(--spectrum-semantic-negative-color-text-emphasis)'
                        }}>
                        {message}
                    </Text>
                </ActionButton>
            </div>
        </View>
    );
};

export default ValidationBanner;
