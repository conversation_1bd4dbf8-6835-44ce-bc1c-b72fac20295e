import React, { Suspense, useState, useCallback } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import {
    Flex,
    ProgressCircle,
    Text,
    Dialog,
    Heading,
    Content,
    ButtonGroup,
    Button,
    Divider,
    DialogContainer,
    DialogTrigger,
    ActionButton
} from '@adobe/react-spectrum';
import { instance } from '@/src/core/http/axios-config';

import { useFragment, graphql } from 'react-relay';
import { useSWRQuery } from '@/src/relay/useSWRQuery';
import { TimesheetValidationError } from '@/src/hooks/useTimesheetSaver';
import { useSaveTimesheet } from '@/src/hooks/useSaveTimesheet';
import { useTimesheetUIStore } from '@/src/store/timesheetUIStore';
import { useTimesheetDirtyFlag } from '@/src/hooks/useTimesheetDirtyFlag';
import { ErrorHandlingService } from '@/src/services/error-handling';

import TimesheetDetailView from './TimesheetDetailView';
import type { TimesheetDetailQuery as TimesheetDetailQueryType } from '@/lib/relay/__generated__/TimesheetDetailQuery.graphql';
import type { TimesheetDetail_timeSheet$key } from '@/lib/relay/__generated__/TimesheetDetail_timeSheet.graphql';
import type { TimeSheetSettingsState } from './TimeSheetSettings';

import { TimesheetDetailErrorBoundary } from './TimesheetDetailErrorBoundary';
import { TimesheetContextProvider } from './TimesheetContext';
import '@/src/fragments/TimeSheetPayStubsConnectionFragment';
import { PayStubTable_connectionFragment } from './PayStubTable';
import { EmployeeDataPaginationFragment } from '@/src/fragments/EmployeeDataFragment';
import { usePaginationFragment } from 'react-relay';
import { TimeSheetPayStubsConnectionFragment } from '@/src/fragments/TimeSheetPayStubsConnectionFragment';
import type { TimeSheetPayStubsConnectionFragment_timeSheet$key } from '@/lib/relay/__generated__/TimeSheetPayStubsConnectionFragment_timeSheet.graphql';
import type { TimeSheetPayStubsConnectionRefetchQuery } from '@/lib/relay/__generated__/TimeSheetPayStubsConnectionRefetchQuery.graphql';

// Fragment for timesheet detail page data
// eslint-disable-next-line custom/relay-fragment-dependencies -- Fragments are spread for child components
const TimesheetDetail_timeSheetFragment = graphql`
    fragment TimesheetDetail_timeSheet on TimeSheet {
        # Spread fragments needed by child components
        ...TimesheetDetailView_timeSheet
        ...TimeSheetGrid_timeSheet
        ...TimeSheetSettings_timeSheet
        ...PayStubTable_connectionFragment
        ...TimeSheetPayStubsConnectionFragment_timeSheet
        # Include fields needed by TimesheetDetailView and others
        id
        numericId
        name
        status
        payPeriodEndDate
        showBonusColumn
        showCostCenterColumn
        showDTHoursColumn
        showEarningsCodesColumn
        showExpensesColumn
        employerGuid
        creationDate
        modificationDate
        modifiedByUserId
        type
        hoursWorked
        oldId
        ...TimesheetToolbar_timeSheet
    }
`;

// Enhanced query to fetch timesheet data with employee data for page-level caching
// Phase 5: Includes employee data for CSV upload functionality
const TIMESHEET_DETAIL_QUERY = graphql`
    query TimesheetDetailQuery($timeSheetId: ID!, $employerGuid: UUID!) {
        timeSheetById(id: $timeSheetId) {
            id
            employerGuid
            ...TimesheetDetail_timeSheet
        }
        # Add employee data for page-level caching used by CSV upload
        ...EmployeeDataFragment_pagination @arguments(employerGuid: $employerGuid)
    }
`;

// Use generated types from Relay
type TimesheetDetailQuery$data = TimesheetDetailQueryType['response'];
type TimesheetDetailQuery$variables = TimesheetDetailQueryType['variables'];

/**
 * Inner component accessing timesheet context after provider is rendered
 * Handles save operations, navigation, and dialog interactions
 */
const TimesheetDetailContent: React.FC<{
    timesheetData: TimesheetDetail_timeSheet$key;
    queryRef: TimesheetDetailQueryType['response'];
}> = ({ timesheetData, queryRef }) => {
    const navigate = useNavigate();
    // Get resolved data and UI store actions
    const clearError = useTimesheetUIStore((state) => state.clearError);
    const clearDraftForPayStub = useTimesheetUIStore((state) => state.clearDraftForPayStub);
    const stopEditingPayStub = useTimesheetUIStore((state) => state.stopEditingPayStub);
    const resolvedMasterData = useFragment(TimesheetDetail_timeSheetFragment, timesheetData);

    // Timesheet-scoped dirty flag calculation
    const isDirty = useTimesheetDirtyFlag(resolvedMasterData?.id || '');

    const [settings, setSettings] = useState<TimeSheetSettingsState>({
        showBonusColumn: false,
        showCostCenterColumn: false,
        showDTHoursColumn: false,
        showEarningsCodesColumn: false,
        showExpensesColumn: false
    });

    const { submitTimesheet, saveForLater, isSaving, saveError } = useSaveTimesheet();

    // State for confirmation dialog (leaving page)
    const [showConfirmDialog, setShowConfirmDialog] = useState(false);
    const [dialogReason, setDialogReason] = useState<'cancel' | null>(null);

    // State for error dialog
    const [isErrorDialogOpen, setIsErrorDialogOpen] = useState(false);
    const [errorDialogContent, setErrorDialogContent] = useState<string | null>(null);

    // ------------------------------
    // Obtain server pay-stubs via the existing @connection fragment
    // ------------------------------
    const { data: payStubConnData } = usePaginationFragment<
        TimeSheetPayStubsConnectionRefetchQuery,
        TimeSheetPayStubsConnectionFragment_timeSheet$key
    >(TimeSheetPayStubsConnectionFragment, resolvedMasterData);

    const serverPayStubs = payStubConnData?.payStubsConnection?.edges?.map((edge) => edge.node) ?? [];

    /**
     * Saves the timesheet and submits it
     * Calls the savereports endpoint to populate payroll data
     * Navigates to payroll roster on success, shows error dialog on failure
     */
    const handleSave = useCallback(async () => {
        if (!resolvedMasterData || isSaving) return;

        setErrorDialogContent(null);
        setIsErrorDialogOpen(false);
        let success = false;
        try {
            success = await submitTimesheet(resolvedMasterData, serverPayStubs);

            if (success) {
                // Successfully saved, redirecting to payroll

                try {
                    // Call the savereports endpoint to populate payroll data
                    // We need to pass the timesheet's Id to the endpoint instead of oldId
                    if (resolvedMasterData.id) {
                        await instance.post('/api/TimeSheets/savereports', {
                            TimeSheetID: resolvedMasterData.numericId
                        });
                    } else {
                        console.warn('Cannot populate payroll reports: Timesheet has no id');
                    }

                    // After successful API call (or if there's no id), redirect to payroll page
                    window.location.href = `${import.meta.env.VITE_OLD_APP_URL}/PayrollReporting/ReportRoster.aspx`;
                } catch (apiError) {
                    // Use centralized error handling for API errors
                    const errorResult = ErrorHandlingService.handleMutationError(apiError, {
                        component: 'TimesheetDetailContent',
                        action: 'handleSave_savereports',
                        metadata: { timesheetId: resolvedMasterData?.id, api: 'savereports' }
                    });

                    setErrorDialogContent(
                        `Timesheet saved successfully, but there was an error populating payroll reports:\n${errorResult.message}`
                    );
                    setIsErrorDialogOpen(true);
                }
            } else {
                // Use centralized error handling for mutation errors
                const errorResult = ErrorHandlingService.handleMutationError(saveError || new Error('Unknown mutation error'), {
                    component: 'TimesheetDetailContent',
                    action: 'handleSave_mutation',
                    metadata: { timesheetId: resolvedMasterData?.id, isSubmit: true }
                });
                setErrorDialogContent(`Failed to save timesheet:\n${errorResult.message}`);
                setIsErrorDialogOpen(true);
            }
        } catch (err) {
            // Check for validation errors first - these should show user-friendly messages
            if (err instanceof TimesheetValidationError) {
                // Log detailed validation failure information for debugging
                console.log('🔴 Timesheet Validation Failed - handleSave (Submit):', {
                    timesheetId: resolvedMasterData?.id,
                    timesheetName: resolvedMasterData?.name,
                    timesheetStatus: resolvedMasterData?.status,
                    errorMessage: err.message,
                    detailedMessage: err.detailedMessage,
                    errorStack: err.stack,
                    timestamp: new Date().toISOString(),
                    action: 'submit',
                    serverPayStubsCount: serverPayStubs?.length || 0
                });

                // Show validation errors without logging as errors
                setErrorDialogContent(err.detailedMessage);
                setIsErrorDialogOpen(true);
            } else {
                // Use centralized error handling for server errors
                const errorResult = ErrorHandlingService.handleMutationError(err, {
                    component: 'TimesheetDetailContent',
                    action: 'handleSave',
                    metadata: { timesheetId: resolvedMasterData?.id, isSubmit: true }
                });

                setErrorDialogContent(`Failed to save timesheet:\n${errorResult.message}`);
                setIsErrorDialogOpen(true);
            }
        }
    }, [resolvedMasterData, isSaving, submitTimesheet, saveError, serverPayStubs]);

    /**
     * Saves the timesheet without submitting it
     * Navigates to roster on success, shows error dialog on failure
     */
    const handleSaveForLater = useCallback(async () => {
        if (!resolvedMasterData || isSaving) return;

        setErrorDialogContent(null);
        setIsErrorDialogOpen(false);
        let success = false;
        try {
            success = await saveForLater(resolvedMasterData, serverPayStubs);

            if (success) {
                // Successfully saved, navigating to roster
                navigate('/timesheet-roster');
            } else {
                // Use centralized error handling for mutation errors
                const errorResult = ErrorHandlingService.handleMutationError(saveError || new Error('Unknown mutation error'), {
                    component: 'TimesheetDetailContent',
                    action: 'handleSaveForLater_mutation',
                    metadata: { timesheetId: resolvedMasterData?.id, isSubmit: false }
                });
                setErrorDialogContent(`Failed to save timesheet for later:\n${errorResult.message}`);
                setIsErrorDialogOpen(true);
            }
        } catch (err) {
            // Check for validation errors first - these should show user-friendly messages
            if (err instanceof TimesheetValidationError) {
                // Log detailed validation failure information for debugging
                console.log('🔴 Timesheet Validation Failed - handleSaveForLater:', {
                    timesheetId: resolvedMasterData?.id,
                    timesheetName: resolvedMasterData?.name,
                    timesheetStatus: resolvedMasterData?.status,
                    errorMessage: err.message,
                    detailedMessage: err.detailedMessage,
                    errorStack: err.stack,
                    timestamp: new Date().toISOString(),
                    action: 'saveForLater',
                    serverPayStubsCount: serverPayStubs?.length || 0
                });

                // Show validation errors without logging as errors
                setErrorDialogContent(err.detailedMessage);
                setIsErrorDialogOpen(true);
            } else {
                // Use centralized error handling for server errors
                const errorResult = ErrorHandlingService.handleMutationError(err, {
                    component: 'TimesheetDetailContent',
                    action: 'handleSaveForLater',
                    metadata: { timesheetId: resolvedMasterData?.id, isSubmit: false }
                });

                setErrorDialogContent(`Failed to save timesheet for later:\n${errorResult.message}`);
                setIsErrorDialogOpen(true);
            }
        }
    }, [resolvedMasterData, isSaving, saveForLater, saveError, navigate, serverPayStubs]);

    /**
     * Handles cancel button click with unsaved changes check
     */
    const handleCancel = useCallback(() => {
        if (isDirty) {
            setDialogReason('cancel');
            setShowConfirmDialog(true);
        } else {
            // Clear any draft state and stop editing
            // Note: For now, we'll clear all drafts for this timesheet
            // TODO: Implement proper draft cleanup based on timesheet scope
            if (resolvedMasterData?.id) {
                stopEditingPayStub(resolvedMasterData.id);
            }

            // Navigate directly without blocking
            navigate('/timesheet-roster');
        }
    }, [isDirty, navigate, clearDraftForPayStub, stopEditingPayStub, resolvedMasterData]);

    // Browser navigation warning for unsaved changes
    React.useEffect(() => {
        // Push initial state when component mounts so popstate can work
        if (!window.history.state || !window.history.state.page) {
            window.history.pushState({ page: 'timesheet-detail' }, '', window.location.href);
        }

        // Handle page refresh, tab close (beforeunload)
        const handleBeforeUnload = (event: BeforeUnloadEvent) => {
            if (isDirty) {
                // This message may not be shown in modern browsers due to security,
                // but the beforeunload event will still trigger the browser's default warning
                const message = 'You have unsaved changes. Are you sure you want to leave?';
                event.preventDefault();
                event.returnValue = message; // Required for Chrome
                return message; // Required for other browsers
            }
        };

        // Handle browser back/forward button (popstate)
        const handlePopState = (event: PopStateEvent) => {
            if (isDirty) {
                // Prevent the navigation by pushing the current state back
                const currentPath = window.location.pathname + window.location.search;
                window.history.pushState({ page: 'timesheet-detail' }, '', currentPath);

                // Show our custom confirmation dialog
                setDialogReason('cancel');
                setShowConfirmDialog(true);
            }
        };

        window.addEventListener('beforeunload', handleBeforeUnload);
        window.addEventListener('popstate', handlePopState);

        return () => {
            window.removeEventListener('beforeunload', handleBeforeUnload);
            window.removeEventListener('popstate', handlePopState);
        };
    }, [isDirty]);

    /**
     * Closes the unsaved changes dialog
     */
    const closeUnsavedChangesDialog = useCallback(() => {
        setShowConfirmDialog(false);
        setDialogReason(null);
    }, []);

    /**
     * Confirms leaving the page after unsaved changes dialog
     */
    const confirmLeavePage = useCallback(() => {
        setShowConfirmDialog(false);

        // Clear any draft state when leaving
        // Note: For now, we'll clear all drafts for this timesheet
        // TODO: Implement proper draft cleanup based on timesheet scope
        if (resolvedMasterData?.id) {
            stopEditingPayStub(resolvedMasterData.id);
        }

        // Navigate to timesheet roster
        // We use navigate instead of history.back() to ensure we go to the correct page
        navigate('/timesheet-roster');

        setDialogReason(null);
    }, [navigate, clearDraftForPayStub, stopEditingPayStub, resolvedMasterData]);

    /**
     * Closes the error dialog
     */
    const closeErrorDialog = useCallback(() => {
        setIsErrorDialogOpen(false);
        setErrorDialogContent(null);
    }, []);

    return (
        <>
            {resolvedMasterData && settings && (
                <TimesheetDetailView
                    timeSheetRef={resolvedMasterData}
                    queryRef={queryRef}
                    isDirty={isDirty}
                    onSave={handleSave}
                    onCancel={handleCancel}
                    onSaveForLater={handleSaveForLater}
                    isSaving={isSaving}
                    settings={settings}
                    onSettingsChange={setSettings}
                />
            )}

            {/* Unsaved Changes Confirmation Dialog */}
            {showConfirmDialog && (
                <DialogContainer onDismiss={closeUnsavedChangesDialog}>
                    <Dialog>
                        <Heading>Unsaved Changes</Heading>
                        <Divider />
                        <Content>
                            <Text>You have unsaved changes. Are you sure you want to leave this page? Your changes will be lost.</Text>
                        </Content>
                        <ButtonGroup>
                            <Button variant="secondary" onPress={closeUnsavedChangesDialog}>
                                Stay
                            </Button>
                            <Button variant="negative" onPress={confirmLeavePage} autoFocus>
                                Leave
                            </Button>
                        </ButtonGroup>
                    </Dialog>
                </DialogContainer>
            )}

            {/* Error Dialog */}
            <DialogTrigger isOpen={isErrorDialogOpen} onOpenChange={setIsErrorDialogOpen}>
                {/* Hidden trigger element for DialogTrigger requirement */}
                <ActionButton isQuiet aria-label="Error dialog trigger" UNSAFE_style={{ display: 'none' }} />
                <Dialog>
                    <Heading>Error</Heading>
                    <Divider />
                    <Content>
                        <Text UNSAFE_style={{ whiteSpace: 'pre-wrap' }}>{errorDialogContent || 'An unknown error occurred.'}</Text>
                    </Content>
                    <ButtonGroup>
                        <Button variant="secondary" onPress={closeErrorDialog} autoFocus>
                            Close
                        </Button>
                    </ButtonGroup>
                </Dialog>
            </DialogTrigger>
        </>
    );
};

/**
 * Container component that fetches data via Relay and provides context
 */
interface TimesheetDetailContainerProps {
    timesheetId: string;
    employerGuid: string;
}

const TimesheetDetailContainer: React.FC<TimesheetDetailContainerProps> = ({ timesheetId, employerGuid }) => {
    // Fetch the full data including employee data for CSV upload
    const data = useSWRQuery<TimesheetDetailQueryType>(TIMESHEET_DETAIL_QUERY, {
        timeSheetId: timesheetId,
        employerGuid: employerGuid
    });

    if (!data.timeSheetById) {
        return (
            <Flex justifyContent="center" alignItems="center" height="100vh" marginTop="size-400">
                <Text>Timesheet not found</Text>
            </Flex>
        );
    }

    return <TimesheetDetailWithData timesheetRef={data.timeSheetById} queryRef={data} />;
};

// Separate component to handle fragment resolution and provide context
const TimesheetDetailWithData: React.FC<{
    timesheetRef: TimesheetDetail_timeSheet$key;
    queryRef: TimesheetDetailQueryType['response'];
}> = ({ timesheetRef, queryRef }) => {
    // Resolve the fragment to get the actual data for the context
    const resolvedTimesheetData = useFragment(TimesheetDetail_timeSheetFragment, timesheetRef);

    // Create context value with proper type safety
    const contextValue = {
        queryData: queryRef,
        employerGuid: resolvedTimesheetData.employerGuid,
        timesheetId: resolvedTimesheetData.id,
        numericId: resolvedTimesheetData.numericId
    };

    return (
        <TimesheetContextProvider value={contextValue}>
            <TimesheetDetailContent timesheetData={timesheetRef} queryRef={queryRef} />
        </TimesheetContextProvider>
    );
};

/**
 * Error boundary and loading wrapper for the timesheet detail page
 */
interface TimesheetDetailPageProps {
    timesheetId: string;
    employerGuid: string;
}

const TimesheetDetailPage: React.FC<TimesheetDetailPageProps> = ({ timesheetId, employerGuid }) => {
    return (
        <TimesheetDetailErrorBoundary>
            <Suspense
                fallback={
                    <Flex justifyContent="center" alignItems="center" height="100vh" marginTop="size-400">
                        <ProgressCircle aria-label="Loading timesheet" isIndeterminate size="L" />
                    </Flex>
                }>
                <TimesheetDetailContainer timesheetId={timesheetId} employerGuid={employerGuid} />
            </Suspense>
        </TimesheetDetailErrorBoundary>
    );
};

/**
 * Main TimesheetDetail component
 * Receives timesheet ID and employer GUID as props from container
 */
interface TimesheetDetailProps {
    timeSheetId: string;
}

const TimesheetDetail: React.FC<TimesheetDetailProps> = ({ timeSheetId }) => {
    const [searchParams] = useSearchParams();
    const employerGuid = searchParams.get('employerGuid');

    if (!employerGuid) {
        return (
            <Flex justifyContent="center" alignItems="center" height="100vh" marginTop="size-400">
                <Text>No employer GUID provided</Text>
            </Flex>
        );
    }

    return <TimesheetDetailPage timesheetId={timeSheetId} employerGuid={employerGuid} />;
};

export default TimesheetDetail;
