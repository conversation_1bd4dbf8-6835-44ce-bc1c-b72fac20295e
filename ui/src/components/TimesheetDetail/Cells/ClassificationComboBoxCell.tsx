import React, { useEffect, useState, useMemo, useRef, useCallback } from 'react';
import { Text, View, ComboBox, Item } from '@adobe/react-spectrum'; // Import ComboBox and Item
import { graphql } from 'react-relay';
import { Classification } from '@/src/types/relay-ui-extensions';
import EmptyValueDivider from '@ui/EmptyValueDivider';
import { RelayIdService } from '../../../services/RelayIdService';
import type { ClassificationComboBoxCellQuery } from '@/src/relay/__generated__/ClassificationComboBoxCellQuery.graphql';
import { useComboBoxQuery, transformNodesResponse, type DropdownOption } from '@/src/hooks/useComboBoxQuery';
import { ClassificationComboBoxErrorBoundary } from './ComboBoxErrorBoundary';

interface ClassificationComboBoxCellProps {
    // Renamed
    value: string | null | undefined;
    onChange: (newValue: string | null) => void;
    agreementId: string | null | undefined;
    isDisabled?: boolean;
    isQuiet?: boolean; // ComboBox doesn't have isQuiet, handle styling if needed
    // Keyboard Nav Props
    rowIndex: number;
    colIndex: number;
    isActive: boolean;
    isEditingActive: boolean;
    onActivate: (rowIndex: number, colIndex: number) => void;
    onDeactivate: () => void;
}

// Remove unused select styles

const ClassificationComboBoxCellComponent: React.FC<ClassificationComboBoxCellProps> = ({
    // Renamed
    value,
    onChange,
    agreementId,
    isDisabled = false,
    isQuiet = false,
    // Keyboard Nav Props
    rowIndex,
    colIndex,
    isActive,
    isEditingActive,
    onActivate,
    onDeactivate
}) => {
    // GraphQL query for fetching classifications by agreement
    const classificationsByAgreementQuery = graphql`
        query ClassificationComboBoxCellQuery($agreementId: ID!) {
            classificationsByAgreementId(agreementId: $agreementId) {
                nodes {
                    id
                    name
                }
            }
        }
    `;

    // Use the custom hook for standardized query handling
    const { options: availableClassifications, isLoading, hasError } = useComboBoxQuery(
        classificationsByAgreementQuery,
        {
            shouldExecute: !!agreementId,
            variables: {
                agreementId: RelayIdService.toGlobalId('Agreement', agreementId || '0')
            },
            fetchPolicy: 'store-or-network'
        },
        (data) => transformNodesResponse(data as Record<string, any>, 'classificationsByAgreementId.nodes')
    );
    // Removed selectRef
    const [inputValue, setInputValue] = useState(''); // State for input value
    const [isActivelyFiltering, setIsActivelyFiltering] = useState<boolean>(false); // State for filtering trigger

    // isLoading is now provided by useComboBoxQuery

    // Track if component has been initialized to prevent clearing on mount
    const [isInitialized, setIsInitialized] = React.useState(false);
    
    // Effect to clear selection if agreement changes and current value is no longer valid
    useEffect(() => {
        // Only clear if we are NOT loading AND the value is invalid for the loaded classifications
        // AND the component has been initialized (prevents clearing on mount)
        if (
            isInitialized &&
            agreementId &&
            !isLoading &&
            value !== null &&
            value !== undefined &&
            availableClassifications.length > 0 && // Ensure classifications have loaded
            !availableClassifications.some((c) => String(c.value) === String(value)) // Check if current value is valid
        ) {
            console.log('🚨 ClassificationComboBoxCell auto-clearing invalid value:', {
                agreementId,
                value,
                availableClassifications: availableClassifications.map(c => c.value)
            });
            onChange(null);
        }
    }, [isInitialized, agreementId, value, availableClassifications, onChange, isLoading]);
    
    // Mark as initialized after first render
    useEffect(() => {
        setIsInitialized(true);
    }, []);

    // Removed useEffect for manual focus

    // Find the display text for the current value
    const selectedItemText = useMemo(() => {
        if (value === null || value === undefined) return '';
        const selectedClassification = availableClassifications.find((c) => String(c.value) === String(value));
        return selectedClassification ? selectedClassification.text : '';
    }, [value, availableClassifications]);

    // --- Explicit Filtering Logic ---
    // Add onFocus handler
    const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
        setIsActivelyFiltering(false); // Show all items initially on focus
        // Select text
        const input = e.target;
        setTimeout(() => {
            input.select();
        }, 0);
    };

    const itemsToDisplay = useMemo(() => {
        // Match EmployeeSelector logic: Show all if not actively filtering
        if (!isActivelyFiltering) {
            return availableClassifications;
        }
        // If actively filtering, but input is empty, show all
        if (!inputValue) {
            return availableClassifications;
        }
        // Otherwise, perform the filter
        const lowerCaseInput = inputValue.toLowerCase();
        return availableClassifications.filter((item) => item.text.toLowerCase().includes(lowerCaseInput));
        // Update dependencies for the filter logic
    }, [inputValue, availableClassifications, isActivelyFiltering]);
    // --- End Explicit Filtering Logic ---

    // Effect to sync input value when the external value changes
    useEffect(() => {
        // Use functional update to avoid circular dependency
        setInputValue((prevInputValue) => {
            // Only update if value has changed - we don't check isActivelyFiltering here
            // to avoid circular dependency since this effect should only run when selectedItemText changes
            if (prevInputValue === selectedItemText) {
                return prevInputValue;
            }
            return selectedItemText;
        });
    }, [selectedItemText]); // Only depend on selectedItemText to avoid circular dependency

    const handleSelectionChange = (key: React.Key | null) => {
        const selectedValue = key === null ? null : String(key);
        const currentValue = value === null || value === undefined ? null : String(value);
        const selectedItem = selectedValue ? availableClassifications.find((c) => String(c.value) === selectedValue) : null;

        // Update input value first to match selection
        setInputValue(selectedItem ? selectedItem.text : '');
        setIsActivelyFiltering(false); // Turn off filtering after selection

        // --- Only trigger onChange if the value is actually different ---
        if (selectedValue !== currentValue) {
            onChange(selectedValue); // Call original onChange
            // Note: Sub-classification fetching is handled by the SubClassificationComboBoxCell component
            onDeactivate(); // Deactivate after selection change
        } else {
            // Value is the same, still deactivate to return focus control
            onDeactivate();
        }
        // Note: Deactivation might happen twice if blur also calls it, but should be harmless.
    };

    const handleInputChange = (text: string) => {
        setInputValue(text);
        if (!isActivelyFiltering) {
            setIsActivelyFiltering(true); // Turn on filtering when user types
        }
    };

    const handleBlur = () => {
        // If the input is cleared, trigger onChange with null
        if (inputValue === '') {
            if (value !== null && value !== undefined) {
                onChange(null);
            }
            // No need to setInputValue here, it's already empty
        } else {
            // If input is not empty, check if it matches a valid item's text
            const isValidInput = availableClassifications.some((item) => item.text === inputValue);
            if (!isValidInput) {
                // If input is invalid (doesn't match any item), reset to the text of the actual selected value
                setInputValue(selectedItemText);
            }
            // If input is valid and matches an item, leave inputValue as is
        }
        setIsActivelyFiltering(false); // Turn off filtering on blur
        onDeactivate(); // Deactivate after handling blur logic
    };

    const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
        if ((event.key === 'Enter' || event.key === 'Tab') && inputValue === '') {
            event.preventDefault(); // Stop default commit/navigation
            if (value !== null && value !== undefined) {
                onChange(null); // Clear the value
            }
            onDeactivate(); // Deactivate the cell
        } else if (event.key === 'Escape') {
            event.preventDefault();
            // Reset input value to original on Escape before deactivating
            setInputValue(selectedItemText);
            onDeactivate();
        }
        // Allow other keys (like arrows for navigation within dropdown) to function normally
    };

    const handleViewClick = () => {
        if (!isDisabled && agreementId) {
            onActivate(rowIndex, colIndex);
        }
    };

    // Removed currentSelectedValue

    // Determine if the fetch is complete and the result is confirmed empty
    const isConfirmedEmpty = !isLoading && availableClassifications.length === 0;

    // Disable if explicitly passed via props, agreementId is missing, OR if fetch is complete and the list is confirmed empty.
    const finalIsDisabled: boolean = !!(isDisabled || !agreementId || isConfirmedEmpty);
    const placeholderText = isLoading
        ? 'Loading...'
        : !agreementId
          ? '-' // Show dash if no agreement selected
          : isConfirmedEmpty // Check if disabled due to empty list *after* loading
            ? 'N/A'
            : 'Select Classification';

    // Memoize styles to prevent re-renders - must be before any conditional returns
    const activeStyle = useMemo<React.CSSProperties>(
        () =>
            isActive
                ? {
                      outline: '2px solid var(--spectrum-alias-focus-ring-color)',
                      outlineOffset: '-1px'
                  }
                : {},
        [isActive]
    );

    const containerStyle = useMemo<React.CSSProperties>(
        () => ({
            width: '100%',
            minHeight: 'var(--spectrum-global-dimension-size-400)',
            display: 'flex',
            alignItems: 'center',
            cursor: finalIsDisabled ? 'default' : 'pointer',
            padding: '0 var(--spectrum-global-dimension-size-50)',
            boxSizing: 'border-box',
            ...activeStyle
        }),
        [finalIsDisabled, activeStyle]
    );

    // Error fallback component
    const ErrorFallback = (
        <div style={containerStyle}>
            <View width="100%">
                <Text>Error loading classifications</Text>
            </View>
        </div>
    );

    if (isEditingActive) {
        return (
            <ComboBox
                // Removed ref
                items={itemsToDisplay} // DropdownOption has compatible properties
                selectedKey={value === null || value === undefined ? null : String(value)}
                inputValue={inputValue}
                onInputChange={handleInputChange}
                onSelectionChange={handleSelectionChange}
                onBlur={handleBlur}
                onFocus={handleFocus} // Add onFocus handler
                onKeyDown={handleKeyDown} // Add keydown handler
                isDisabled={finalIsDisabled}
                width="100%"
                aria-label="Classification Picker"
                allowsCustomValue={false}
                description={placeholderText} // Use description instead of placeholder
                autoFocus // Add autoFocus prop
                menuWidth={200}>
                {(item: DropdownOption) => <Item key={item.value}>{item.text}</Item>}
            </ComboBox>
        );
    }

    return (
        <div onClick={handleViewClick} style={containerStyle}>
            <View width="100%">
                {/* Display EmptyValueDivider if selectedItemText is empty, otherwise display the text */}
                {selectedItemText ? <Text>{selectedItemText}</Text> : <EmptyValueDivider />}
            </View>
        </div>
    );
};

// Wrap the component with error boundary
const ClassificationComboBoxCellWithErrorBoundary = React.memo(ClassificationComboBoxCellComponent);

export const ClassificationComboBoxCell: React.FC<ClassificationComboBoxCellProps> = (props) => (
    <ClassificationComboBoxErrorBoundary>
        <ClassificationComboBoxCellWithErrorBoundary {...props} />
    </ClassificationComboBoxErrorBoundary>
);
