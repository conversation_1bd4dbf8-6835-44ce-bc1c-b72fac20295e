import React, { useMemo, useState, useRef, useEffect, useCallback } from 'react';
import { Text, View, ComboBox, Item } from '@adobe/react-spectrum'; // Import ComboBox and Item
import type { Agreement } from '../../../types/timesheet';
import EmptyValueDivider from '@ui/EmptyValueDivider';
import { useAgreementsByEmployer } from '../../../hooks/useAgreementsByEmployer';
import { AgreementComboBoxErrorBoundary } from './ComboBoxErrorBoundary';

/**
 * Props for AgreementComboBoxCell component
 */
interface AgreementComboBoxCellProps {
    // Renamed
    value: string | null | undefined;
    onChange: (newValue: string | null) => void;
    payStubEmployeeId: string | null | undefined; // Still needed for filtering logic potentially
    employerGuid: string; // Add employerGuid prop for API calls
    isDisabled?: boolean;
    isQuiet?: boolean; // Apply basic styling
    // Keyboard Nav Props
    rowIndex: number;
    colIndex: number;
    isActive: boolean;
    isEditingActive: boolean;
    onActivate: (rowIndex: number, colIndex: number) => void;
    onDeactivate: () => void;
}

// Remove unused select styles

/**
 * Component that renders a combobox for selecting agreements
 * Handles filtering, selection, and keyboard navigation
 */
const AgreementComboBoxCellComponent: React.FC<AgreementComboBoxCellProps> = ({
    // Renamed
    value,
    onChange,
    payStubEmployeeId,
    employerGuid,
    isDisabled = false,
    isQuiet = false, // ComboBox doesn't have a direct 'quiet' prop, handle styling if needed
    // Keyboard Nav Props
    rowIndex,
    colIndex,
    isActive,
    isEditingActive,
    onActivate,
    onDeactivate
}) => {
    // Use the new custom hook for agreements
    const {
        agreements: fetchedAgreements,
        isLoading,
        hasError,
        errorMessage
    } = useAgreementsByEmployer({
        employerGuid,
        includeInactiveAgreements: false
    });
    // Removed comboBoxRef creation
    const [inputValue, setInputValue] = useState(''); // State for input value
    const [isActivelyFiltering, setIsActivelyFiltering] = useState<boolean>(false); // State for filtering trigger

    /**
     * Filters agreements based on employee association
     */
    const filteredAgreements = useMemo(() => {
        if (!fetchedAgreements /* || !payStubEmployeeId */) {
            return [];
        }
        // TODO: Revisit filtering logic if needed
        return fetchedAgreements;
    }, [fetchedAgreements /*, payStubEmployeeId */]);

    /**
     * Filters the agreements list based on input value when actively filtering
     */
    const itemsToDisplay = useMemo(() => {
        // Show all if not actively filtering
        if (!isActivelyFiltering) {
            return filteredAgreements;
        }
        // If actively filtering, but input is empty, show all
        if (!inputValue) {
            return filteredAgreements;
        }
        // Otherwise, perform the filter
        const lowerCaseInput = inputValue.toLowerCase();
        return filteredAgreements.filter((item) => item.text.toLowerCase().includes(lowerCaseInput));
        // Update dependencies for the filter logic
    }, [inputValue, filteredAgreements, isActivelyFiltering]);
    // --- End Explicit Filtering Logic ---

    /**
     * Gets the display text for the current value
     */
    const selectedItemText = useMemo(() => {
        if (value === null || value === undefined) return '';

        const selectedAgreement = filteredAgreements.find((agreement) => String(agreement.value) === String(value));

        return selectedAgreement ? selectedAgreement.text : '';
    }, [value, filteredAgreements]);

    // Sync input value when the external value changes or when not actively filtering
    useEffect(() => {
        // Use functional update to avoid circular dependency
        setInputValue((prevInputValue) => {
            // Only update if not actively filtering and value has changed
            if (isActivelyFiltering || prevInputValue === selectedItemText) {
                return prevInputValue;
            }
            return selectedItemText;
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [selectedItemText]); // Remove isActivelyFiltering from deps since we use functional update

    // Focus the ComboBox when activated for editing
    useEffect(() => {
        // Removed useEffect for manual focus
    }, [isEditingActive]);
    /**
     * Handles selection change in the combobox
     */
    const handleSelectionChange = useCallback(
        (key: React.Key | null) => {
            const selectedValue = key === null ? null : String(key);
            const currentValue = value === null || value === undefined ? null : String(value);
            const selectedItem = selectedValue ? filteredAgreements.find((a) => String(a.value) === selectedValue) : null;

            // Update input value to match selection
            setInputValue(selectedItem ? selectedItem.text : '');
            setIsActivelyFiltering(false);

            // Only trigger onChange if the value is actually different
            if (selectedValue !== currentValue) {
                onChange(selectedValue);
                onDeactivate();
            } else {
                // Value is the same, still deactivate to return focus control
                onDeactivate();
            }
        },
        [filteredAgreements, value, onChange, onDeactivate]
    );

    /**
     * Handles input changes in the combobox
     */
    const handleInputChange = useCallback(
        (text: string) => {
            setInputValue(text);
            if (!isActivelyFiltering) {
                setIsActivelyFiltering(true);
            }
            // Optional: Add logic here if you want to clear selection if input doesn't match
        },
        [isActivelyFiltering]
    );

    /**
     * Handles blur event for the combobox
     */
    const handleBlur = useCallback(() => {
        // If the input is cleared, trigger onChange with null
        if (inputValue === '') {
            if (value !== null && value !== undefined) {
                onChange(null);
            }
        } else {
            // Check if input matches a valid item's text
            const isValidInput = filteredAgreements.some((item) => item.text === inputValue);
            if (!isValidInput) {
                // Reset to the text of the actual selected value if invalid
                setInputValue(selectedItemText);
            }
        }
        setIsActivelyFiltering(false);
        onDeactivate();
    }, [inputValue, value, filteredAgreements, selectedItemText, onChange, onDeactivate]);

    /**
     * Handles keyboard events for the combobox
     */
    const handleKeyDown = useCallback(
        (event: React.KeyboardEvent<HTMLInputElement>) => {
            if ((event.key === 'Enter' || event.key === 'Tab') && inputValue === '') {
                event.preventDefault();
                if (value !== null && value !== undefined) {
                    onChange(null);
                }
                onDeactivate();
            } else if (event.key === 'Escape') {
                event.preventDefault();
                // Reset input value to original on Escape before deactivating
                setInputValue(selectedItemText);
                onDeactivate();
            }
            // Allow other keys (like arrows for navigation within dropdown) to function normally
        },
        [inputValue, value, selectedItemText, onChange, onDeactivate]
    );

    /**
     * Activates the cell for editing when clicked
     */
    const handleViewClick = useCallback(() => {
        if (!isDisabled) {
            onActivate(rowIndex, colIndex);
        }
    }, [isDisabled, onActivate, rowIndex, colIndex]);

    // Removed currentSelectedValue

    /**
     * Handles focus event for the combobox
     */
    const handleFocus = useCallback((e: React.FocusEvent<HTMLInputElement>) => {
        setIsActivelyFiltering(false);
        // Select text
        const input = e.target;
        setTimeout(() => {
            input.select();
        }, 0);
    }, []);

    const finalIsDisabled = isDisabled || !payStubEmployeeId || filteredAgreements.length === 0;

    // Render Text view, make it clickable, add active style
    const activeStyle = useMemo<React.CSSProperties>(
        () =>
            isActive
                ? {
                      outline: '2px solid var(--spectrum-alias-focus-ring-color)',
                      outlineOffset: '-1px'
                  }
                : {},
        [isActive]
    );

    const containerStyle = useMemo<React.CSSProperties>(
        () => ({
            width: '100%',
            minHeight: 'var(--spectrum-global-dimension-size-400)',
            display: 'flex',
            alignItems: 'center',
            cursor: finalIsDisabled ? 'default' : 'pointer',
            padding: '0 var(--spectrum-global-dimension-size-50)', // Adjust padding as needed
            boxSizing: 'border-box',
            ...activeStyle // Apply active style
        }),
        [finalIsDisabled, activeStyle]
    );

    if (isEditingActive) {
        // Use prop for conditional rendering
        return (
            <ComboBox
                // Pass the inferred ref type
                // Removed ref prop
                items={itemsToDisplay} // Pass the filtered list
                selectedKey={value === null || value === undefined ? null : String(value)}
                inputValue={inputValue}
                onInputChange={handleInputChange}
                onSelectionChange={handleSelectionChange}
                onBlur={handleBlur}
                onFocus={handleFocus} // Add onFocus handler
                onKeyDown={handleKeyDown} // Add keydown handler
                isDisabled={finalIsDisabled}
                width="100%"
                aria-label="Agreement Picker"
                allowsCustomValue={false} // Don't allow values not in the list
                description="Select Agreement" // Use description instead of placeholder
                autoFocus // Add autoFocus prop
                menuWidth={200} // Adjust menu width as needed
            >
                {(item: Agreement) => <Item key={item.value}>{item.text}</Item>}
            </ComboBox>
        );
    }

    return (
        <div onClick={handleViewClick} style={containerStyle}>
            <View width="100%">
                {/* Display selectedItemText which is derived from the value prop */}
                {/* Display EmptyValueDivider if selectedItemText is empty, otherwise display the text */}
                {selectedItemText ? <Text>{selectedItemText}</Text> : <EmptyValueDivider />}
            </View>
        </div>
    );
};

// Wrap the component with error boundary
const AgreementComboBoxCellWithErrorBoundary = React.memo(AgreementComboBoxCellComponent);

export const AgreementComboBoxCell: React.FC<AgreementComboBoxCellProps> = (props) => (
    <AgreementComboBoxErrorBoundary>
        <AgreementComboBoxCellWithErrorBoundary {...props} />
    </AgreementComboBoxErrorBoundary>
);
