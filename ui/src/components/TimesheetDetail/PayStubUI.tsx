/**
 * PayStubUI - Phase 3: Pure UI Component
 * 
 * This component demonstrates the Phase 3 pattern:
 * - Accepts domain models only (no GraphQL dependencies)
 * - Focuses purely on presentation logic
 * - Easy to test, reuse, and reason about
 * - No Relay fragments or useFragment calls
 * 
 * Architecture:
 * ┌─────────────────────────────────────────┐
 * │ PayStubRow (Fragment + Conversion)      │ ← Fragment boundary
 * ├─────────────────────────────────────────┤
 * │ PayStubUI (Pure UI Component)           │ ← THIS COMPONENT
 * └─────────────────────────────────────────┘
 */

import React, { useCallback, useMemo } from 'react';
import { View, Text, Flex, ActionButton, TooltipTrigger, Tooltip } from '@adobe/react-spectrum';
import ChevronRight from '@spectrum-icons/workflow/ChevronRight';
import ChevronDown from '@spectrum-icons/workflow/ChevronDown';
import DeleteIcon from '@spectrum-icons/workflow/Delete';
import Undo from '@spectrum-icons/workflow/Undo';

import type { PayStubDomainModel } from '@/src/types/timesheet-domain';
import type { PayStubTable_payStub$key } from '@/src/relay/__generated__/PayStubTable_payStub.graphql';
import type { TimeSheetDetailTableView_payStub$key } from '@/src/relay/__generated__/TimeSheetDetailTableView_payStub.graphql';
import type { EmployeeSelector_employee$key } from '@/src/relay/__generated__/EmployeeSelector_employee.graphql';
import { Employee } from '@/src/types/relay-ui-extensions';
import { useTimesheetUIStore } from '@/src/store/timesheetUIStore';
import NumericCellContent from '../UI/NumericCellContent';
import { PayStubErrorDisplay } from './EnhancedErrorDisplay';
import TimeSheetDetailTableView from './TimeSheetDetailTableView';
import styles from './PayStubRowWrapper.module.scss';
import rowStyles from './PayStubRow.module.scss';

interface PayStubUIProps {
    payStub: PayStubDomainModel;  // Domain model, no Relay dependencies
    payStubFragmentRef: PayStubTable_payStub$key; // Fragment reference for nested Relay components
    index: number;
    readOnly: boolean;
    employees?: ReadonlyArray<Employee> | ReadonlyArray<EmployeeSelector_employee$key>;
    // Column visibility flags
    showBonusColumn: boolean;
    showCostCenterColumn: boolean;
    showDTHoursColumn: boolean;
    showEarningsCodesColumn: boolean;
    showExpensesColumn: boolean;
    // Helper functions
    formatNumber: (value: number | null | undefined, format: 'n2' | 'c2') => string;
    payPeriodEndDate: string | null | undefined;
    // Timesheet context
    timeSheetId: string;
    numericId: number;
    employerGuid: string;
    // Event handlers
    onEdit?: (payStub: PayStubDomainModel) => void;
    onDelete?: (payStub: PayStubDomainModel) => void;
    onToggleExpanded?: (payStub: PayStubDomainModel) => void;
}

/**
 * Pure UI component for PayStub display
 * Works with domain models only - no GraphQL/Relay code
 */
export const PayStubUI: React.FC<PayStubUIProps> = ({
    payStub,
    payStubFragmentRef,
    index,
    readOnly,
    employees,
    showBonusColumn,
    showCostCenterColumn,
    showDTHoursColumn,
    showEarningsCodesColumn,
    showExpensesColumn,
    formatNumber,
    payPeriodEndDate,
    timeSheetId,
    numericId,
    employerGuid,
    onEdit,
    onDelete,
    onToggleExpanded
}) => {

    // Get UI state from Zustand store with selective subscriptions for performance
    const isExpanded = useTimesheetUIStore(state => state.getExpandedState(timeSheetId, payStub.id)) || payStub.ui.expanded;
    const isEditing = useTimesheetUIStore(state => state.getEditingState(timeSheetId, payStub.id)) || payStub.ui.isEditing;
    const currentError = useTimesheetUIStore(state => state.getErrorForPayStub(timeSheetId, payStub.id));
    const isSaving = useTimesheetUIStore(state => state.isSaving);
    
    // Get action functions (these don't change, so no re-render)
    const setError = useTimesheetUIStore(state => state.setError);

    // Compute derived state from domain model (UI state now comes from Zustand)
    const isDeleted = payStub.ui?.isTemporary || false; // Domain model handles deletion state
    const hasErrorMessage = !!currentError;
    const isInError = hasErrorMessage;

    // Check for draft changes using Zustand store
    const hasDraft = useTimesheetUIStore(state => {
        // Check for PayStub-level drafts using timesheet-scoped keys
        return !!state.getDraftForPayStub(timeSheetId, payStub.id);
    });

    // Use pre-computed totals from domain model (calculated with draft overlays in PayStubRow)
    // This is safe because PayStubRow computes these totals with proper draft overlays and deletion handling
    const currentRowTotals = useMemo(() => {
        if (isDeleted) return null;

        // Use pre-computed totals instead of recalculating to ensure consistency
        // These totals are already computed in PayStubRow with draft overlays and deletion states
        return {
            stHours: payStub.hours?.standard || 0,
            otHours: payStub.hours?.overtime || 0,
            dtHours: payStub.hours?.doubletime || 0,
            totalHours: payStub.hours?.total || 0,
            bonus: payStub.amounts?.bonus || 0,
            expenses: payStub.amounts?.expenses || 0
        };
    }, [payStub.hours, payStub.amounts, isDeleted]);

    // Memoized styles
    const actionsCellStyle = useMemo(() => ({
        width: '100px',
        textAlign: 'left' as const
    }), []);

    const expandCellStyle = useMemo(() => ({
        width: '50px',
        textAlign: 'left' as const
    }), []);

    const rowNumberCellStyle = useMemo(() => ({
        width: '40px',
        textAlign: 'center' as const
    }), []);

    const employeeCellStyle = useMemo(() => ({
        width: '300px',
        textAlign: 'left' as const
    }), []);

    const paddingStyle = useMemo(() => ({
        paddingLeft: 'var(--spectrum-global-dimension-size-50)'
    }), []);

    const detailsCellStyle = useMemo(() => ({
        backgroundColor: '#e8f3fa',
        padding: '4px'
    }), []);

    const detailsContentStyle = useMemo(() => ({
        backgroundColor: 'white'
    }), []);

    const savingTextStyle = useMemo(() => ({
        color: 'blue',
        fontSize: '0.8em'
    }), []);

    const draftTextStyle = useMemo(() => ({
        color: 'orange',
        fontSize: '0.8em'
    }), []);

    const rowStyle = useMemo(() => {
        const hasEmployee = payStub.employeeId;
        const isClickable = !isDeleted && hasEmployee;

        return {
            cursor: isClickable ? 'pointer' : 'default',
            backgroundColor: isExpanded ? '#e8f3fa' : index % 2 === 1 ? 'var(--spectrum-gray-75, #fafafa)' : 'white'
        };
    }, [isDeleted, payStub.employeeId, isExpanded, index]);

    // Event handlers with Zustand store updates
    const handleExpandClick = useCallback((e?: any) => {
        if (e && e.stopPropagation) {
            e.stopPropagation();
        }
        // Let wrapper handle store updates to avoid double toggle
        onToggleExpanded?.(payStub);
    }, [onToggleExpanded, payStub]);

    const handleDeleteClick = useCallback((e?: any) => {
        if (e && e.stopPropagation) {
            e.stopPropagation();
        }
        if (e && e.preventDefault) {
            e.preventDefault();
        }
        if (!readOnly) {
            onDelete?.(payStub);
        }
    }, [onDelete, payStub, readOnly]);

    const handleRowClick = useCallback(() => {
        if (!isDeleted && payStub.employeeId) {
            // Let wrapper handle store updates to avoid double toggle
            onToggleExpanded?.(payStub);
        }
    }, [isDeleted, onToggleExpanded, payStub]);

    const handleMouseOver = useCallback((e: React.MouseEvent<HTMLTableRowElement>) => {
        if (!isExpanded) {
            e.currentTarget.style.backgroundColor = '#ebeced';
        }
    }, [isExpanded]);

    const handleMouseOut = useCallback((e: React.MouseEvent<HTMLTableRowElement>) => {
        if (!isExpanded) {
            const backgroundColor = index % 2 === 1 ? 'var(--spectrum-gray-75, #fafafa)' : 'white';
            e.currentTarget.style.backgroundColor = backgroundColor;
        }
    }, [isExpanded, index]);

    const handleRetryError = useCallback(async (payStubId: string) => {
        // Clear current error in Zustand store
        setError(timeSheetId, payStubId, null);
        // TODO: Implement actual retry operation
        console.log('Retry operation for payStub:', payStubId);
    }, [setError, timeSheetId]);

    const handleClearError = useCallback((payStubId: string) => {
        // Clear error in Zustand store
        setError(timeSheetId, payStubId, null);
    }, [setError, timeSheetId]);

    // Calculate colspan based on visible columns
    const calculateColSpan = () => {
        let count = 6; // Base columns: Expand, Row#, Employee, ST, OT, Total
        if (showDTHoursColumn) count++;
        if (showBonusColumn) count++;
        if (showExpensesColumn) count++;
        if (!readOnly) count++; // Actions column
        return count;
    };

    // Error message and enhanced error are already available from Zustand store
    const errorMessage = currentError?.message;
    const enhancedError = currentError;

    return (
        <React.Fragment>
            {/* Summary Row */}
            <tr
                role="row"
                className={`
                    ${rowStyles.payStubRow}
                    ${isDeleted ? rowStyles.deleted : ''}
                    ${isInError ? rowStyles.inError : ''}
                    ${index % 2 === 1 ? rowStyles.alternateRow : ''}
                `.trim()}
                onClick={handleRowClick}
                style={rowStyle}
                onMouseOver={handleMouseOver}
                onMouseOut={handleMouseOut}
                aria-label={`Pay stub row for ${payStub.employee.fullName !== 'Select Employee' && payStub.employee.fullName !== 'Error' ? payStub.employee.fullName : `employee ${index + 1}`}`}>
                
                {/* Actions Cell */}
                {!readOnly && (
                    <td role="cell" className={styles.tableBodyTd} style={actionsCellStyle}>
                        <ActionButton
                            data-testid={`delete-ps-${index}`}
                            isQuiet
                            aria-label={
                                isDeleted
                                    ? `Restore pay stub for ${payStub.employee.fullName}`
                                    : `Delete pay stub for ${payStub.employee.fullName}`
                            }
                            onPress={handleDeleteClick}
                            isDisabled={readOnly}
                            type="button">
                            {isDeleted ? <Undo /> : <DeleteIcon />}
                        </ActionButton>
                    </td>
                )}

                {/* Expand/Collapse Cell */}
                <td role="cell" className={styles.tableBodyTd} style={expandCellStyle}>
                    <ActionButton
                        data-testid={`expand-ps-${index}`}
                        isQuiet
                        aria-label={
                            isExpanded
                                ? `Collapse details for ${payStub.employee.fullName}`
                                : `Expand details for ${payStub.employee.fullName}`
                        }
                        onPress={handleExpandClick}
                        isDisabled={isDeleted || !payStub.employeeId}
                        type="button"
                        aria-expanded={isExpanded}>
                        {isExpanded ? <ChevronDown /> : <ChevronRight />}
                    </ActionButton>
                </td>

                {/* Row Number Cell */}
                <td role="cell" className={styles.tableBodyTd} style={rowNumberCellStyle}>
                    <span style={paddingStyle}>{index + 1}</span>
                </td>

                {/* Employee Cell */}
                <td role="cell" className={styles.tableBodyTd} style={employeeCellStyle}>
                    <Flex direction="column" justifyContent="start">
                        <Flex justifyContent="start">
                            <Text>{payStub.employee.fullName || payStub.employeeName || 'Loading...'}</Text>
                        </Flex>
                        
                        {/* Enhanced error display */}
                        {enhancedError && (
                            <PayStubErrorDisplay
                                payStubId={payStub.id}
                                error={enhancedError.message}
                                enhancedError={undefined} // Using simplified error from Zustand store
                                onRetry={handleRetryError}
                                onClear={handleClearError}
                            />
                        )}
                        
                        {/* State indicators */}
                        {isSaving && <Text UNSAFE_style={savingTextStyle}>Saving...</Text>}
                        {hasDraft && <Text UNSAFE_style={draftTextStyle}>Unsaved draft changes</Text>}
                    </Flex>
                </td>

                {/* ST Hours */}
                <td role="cell" className={styles.tableBodyTd}>
                    <Flex justifyContent="end" alignItems="center" height="100%">
                        <NumericCellContent 
                            value={currentRowTotals?.stHours} 
                            format="n2" 
                            formatNumber={formatNumber} 
                        />
                    </Flex>
                </td>

                {/* OT Hours */}
                <td role="cell" className={styles.tableBodyTd}>
                    <Flex justifyContent="end" alignItems="center" height="100%">
                        <NumericCellContent 
                            value={currentRowTotals?.otHours} 
                            format="n2" 
                            formatNumber={formatNumber} 
                        />
                    </Flex>
                </td>

                {/* DT Hours (Conditional) */}
                {showDTHoursColumn && (
                    <td role="cell" className={styles.tableBodyTd}>
                        <Flex justifyContent="end" alignItems="center" height="100%">
                            <NumericCellContent 
                                value={currentRowTotals?.dtHours} 
                                format="n2" 
                                formatNumber={formatNumber} 
                            />
                        </Flex>
                    </td>
                )}

                {/* Total Hours */}
                <td role="cell" className={styles.tableBodyTd}>
                    <View paddingEnd="size-200" height="100%">
                        <Flex justifyContent="end" alignItems="center" height="100%">
                            <NumericCellContent 
                                value={currentRowTotals?.totalHours} 
                                format="n2" 
                                formatNumber={formatNumber} 
                            />
                        </Flex>
                    </View>
                </td>

                {/* Bonus (Conditional) */}
                {showBonusColumn && (
                    <td role="cell" className={styles.tableBodyTd}>
                        <Flex justifyContent="end" alignItems="center" height="100%">
                            <NumericCellContent 
                                value={currentRowTotals?.bonus} 
                                format="c2" 
                                formatNumber={formatNumber} 
                            />
                        </Flex>
                    </td>
                )}

                {/* Expenses (Conditional) */}
                {showExpensesColumn && (
                    <td role="cell" className={styles.tableBodyTd}>
                        <Flex justifyContent="end" alignItems="center" height="100%">
                            <NumericCellContent 
                                value={currentRowTotals?.expenses} 
                                format="c2" 
                                formatNumber={formatNumber} 
                            />
                        </Flex>
                    </td>
                )}
            </tr>

            {/* Detail Row (Conditionally Rendered) */}
            {isExpanded && !isDeleted && (
                <tr className={styles.detailsRow} data-testid={`details-ps-${index}`}>
                    <td colSpan={calculateColSpan()} style={detailsCellStyle}>
                        <div className={styles.detailsContent} style={detailsContentStyle}>
                            {/* Phase 4 Implementation: Fragment composition through PayStubTable_payStub */}
                            <TimeSheetDetailTableView
                                payStub={payStubFragmentRef as unknown as TimeSheetDetailTableView_payStub$key} // ✅ PayStubTable_payStub fragment spreads ...TimeSheetDetailTableView_payStub
                                readOnly={readOnly || isDeleted}
                                employees={employees}
                                showBonusColumn={showBonusColumn}
                                showCostCenterColumn={showCostCenterColumn}
                                showDTHoursColumn={showDTHoursColumn}
                                showEarningsCodesColumn={showEarningsCodesColumn}
                                showExpensesColumn={showExpensesColumn}
                                formatNumber={formatNumber}
                                payPeriodEndDate={payPeriodEndDate}
                                timeSheetId={timeSheetId}
                                numericId={numericId}
                                employerGuid={employerGuid}
                                payStubIndex={index}
                            />
                        </div>
                    </td>
                </tr>
            )}
        </React.Fragment>
    );
};

export default PayStubUI;