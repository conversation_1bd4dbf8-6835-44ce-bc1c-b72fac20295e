import React, { useReducer, useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { useRelayEnvironment, useFragment, graphql } from 'react-relay';
import { But<PERSON>, Text, DialogContainer } from '@adobe/react-spectrum';
import Upload from '@spectrum-icons/workflow/DataUpload';
import UploadDialog from './UploadDialog';
import ProcessingDialog from './ProcessingDialog';
import {
    ProcessingState,
    UploadAction,
    UploadActionType,
    UploadState,
    csvColumnMappings,
    NormalizedCsvHeader,
    PayStubUpload
} from './types';
import { parseCsvFile } from './utils/csvProcessing';
import { validateCsvData, safeParseDate } from './utils/dataValidation';
import { transformEnrichedData, convertEnrichedToRelayPayStubInput, transformValidatedDataWithContext } from './utils/dataTransformation';
import { enrichPayStubData, type DefaultApplication } from './utils/dataEnrichment';
import { useTimesheetUIStore } from '@/src/store/timesheetUIStore';
import { bulkAddPayStubs } from '@/src/mutations/timesheet/BulkAddPayStubsMutation';
import { useErrorRecovery } from '@/src/hooks/useErrorRecovery';
import { TimesheetError, ErrorType } from '@/errorHandling';
import type { Employee } from '@/src/types/relay-ui-extensions';
import type { UploadTimeSheetFragments_timeSheetConsolidated$key } from '@/src/relay/__generated__/UploadTimeSheetFragments_timeSheetConsolidated.graphql';
import { UploadTimeSheetConsolidatedFragment } from './fragments/UploadTimeSheetFragments';

/**
 * Processed employee data interface for CSV upload
 */
interface ProcessedEmployeeData {
    id: string;
    firstName: string;
    lastName: string;
    externalEmployeeId: string;
    ssn: string;
}

/**
 * Props for UploadTimeSheetContent component (Phase 5 update)
 */
interface UploadTimeSheetContentProps {
    /** TimeSheet fragment reference for upload operations */
    timeSheetRef: UploadTimeSheetFragments_timeSheetConsolidated$key;
    /** Callback when upload dialog is closed */
    onClose: () => void;
    /** Whether the component is in read-only mode */
    readOnly?: boolean;
    /** Phase 5: Processed employee data from parent query */
    employeeData?: ProcessedEmployeeData[];
    /** Phase 5: Whether there are more employees to load */
    hasMoreEmployees?: boolean;
    /** Phase 5: Function to load more employees */
    loadMoreEmployees?: () => void;
}

/**
 * Initial state for the upload reducer
 */
const initialState: UploadState = {
    status: ProcessingState.IDLE,
    file: null,
    data: [],
    processedData: [],
    payStubs: [],
    errors: [],
    progress: 0,
    headers: [],
    rowCount: 0,
    appliedDefaults: []
};

/**
 * Reducer for managing upload state
 */
function uploadReducer(state: UploadState, action: UploadAction): UploadState {
    switch (action.type) {
        case UploadActionType.SET_FILE:
            return {
                ...state,
                file: action.payload.file,
                status: ProcessingState.IDLE,
                errors: [],
                data: [],
                processedData: [],
                payStubs: [],
                progress: 0,
                headers: [],
                rowCount: 0
            };

        case UploadActionType.CLEAR_FILE:
            return {
                ...state,
                file: null,
                status: ProcessingState.IDLE,
                errors: [],
                data: [],
                processedData: [],
                payStubs: [],
                progress: 0,
                headers: [],
                rowCount: 0
            };

        case UploadActionType.PARSE_START:
            return {
                ...state,
                status: ProcessingState.PARSING_FILE
            };

        case UploadActionType.PARSE_SUCCESS:
            return {
                ...state,
                data: action.payload.data,
                headers: action.payload.headers,
                rowCount: action.payload.rowCount,
                status: ProcessingState.IDLE
            };

        case UploadActionType.PARSE_ERROR:
            return {
                ...state,
                errors: action.payload.errors,
                status: ProcessingState.ERROR
            };

        case UploadActionType.VALIDATE_DATA_START:
            return {
                ...state,
                status: ProcessingState.VALIDATING_DATA
            };

        case UploadActionType.VALIDATE_DATA_SUCCESS:
            return {
                ...state,
                processedData: action.payload.processedData,
                status: ProcessingState.IDLE
            };

        case UploadActionType.VALIDATE_DATA_ERROR:
            return {
                ...state,
                errors: action.payload.errors,
                status: ProcessingState.ERROR
            };

        case UploadActionType.TRANSFORM_DATA_START:
            return {
                ...state,
                status: ProcessingState.TRANSFORMING_DATA
            };

        case UploadActionType.TRANSFORM_DATA_SUCCESS:
            return {
                ...state,
                payStubs: action.payload.payStubs,
                status: ProcessingState.IDLE
            };

        case UploadActionType.TRANSFORM_DATA_ERROR:
            return {
                ...state,
                errors: action.payload.errors,
                status: ProcessingState.ERROR
            };

        case UploadActionType.ENRICH_DATA_SUCCESS:
            return {
                ...state,
                appliedDefaults: action.payload.appliedDefaults
            };

        case UploadActionType.PARSE_PROGRESS:
            return {
                ...state,
                progress: action.payload.progress
            };

        case UploadActionType.UPLOAD_SUCCESS:
            return {
                ...state,
                successMessage: action.payload.message,
                status: ProcessingState.SUCCESS
            };

        case UploadActionType.UPLOAD_FAILURE:
            return {
                ...state,
                status: ProcessingState.ERROR,
                errors: action.payload.errors || []
            };

        case UploadActionType.RESET:
            return initialState;

        default:
            return state;
    }
}

/**
 * CSV Upload content component that uses employee data from parent query (Phase 5)
 *
 * This component implements Phase 5 changes:
 * - Receives processed employee data as props from parent component
 * - No longer depends on context anti-patterns  
 * - Employee data comes from page-level timesheet query
 * - Maintains all existing upload functionality
 * - Follows Relay best practices for data flow
 */
const UploadTimeSheetContent: React.FC<UploadTimeSheetContentProps> = ({ 
    timeSheetRef, 
    onClose, 
    readOnly = false,
    employeeData = [],
    hasMoreEmployees = false,
    loadMoreEmployees
}) => {
    // Check if we have employee data available (Phase 5 approach)
    const isLoadingEmployees = !employeeData || employeeData.length === 0;

    // State management
    // Resolve the fragment to access timesheet data
    const timeSheetData = useFragment(UploadTimeSheetConsolidatedFragment, timeSheetRef);
    
    const [state, dispatch] = useReducer(uploadReducer, initialState);
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const environment = useRelayEnvironment();
    const errorRecovery = useErrorRecovery();
    // Get global error handler from Zustand store
    const setGlobalError = useTimesheetUIStore(state => state.setGlobalError);

    // Refs for cleanup and component mounted state
    const abortControllerRef = useRef<AbortController | null>(null);
    const isMountedRef = useRef<boolean>(true);

    // Safe dispatch function that only dispatches if component is still mounted
    const safeDispatch = useCallback((action: UploadAction) => {
        if (isMountedRef.current) {
            dispatch(action);
        }
    }, []);

    // Convert employee data to format expected by CSV processing (Phase 5)
    const processedEmployeeDataForCSV = useMemo(() => {
        if (!employeeData || employeeData.length === 0) {
            return [];
        }

        return employeeData.map((emp) => ({
            id: emp.id,
            firstName: emp.firstName,
            lastName: emp.lastName,
            externalEmployeeId: emp.externalEmployeeId,
            // Handle SSN field - already processed by parent component
            ssn: emp.ssn,
            // Add required EmployeeUI properties for CSV processing
            value: emp.id,
            text: `${emp.lastName || ''}, ${emp.firstName || ''}`.trim()
        }));
    }, [employeeData]);

    // Helper function to convert RawCsvRow to ProcessedTimesheetEntry
    const convertRawToProcessed = useCallback((validRows: any[]): any[] => {
        return validRows.map((row, index) => {
            // Convert the date to a standard format
            const dateStr = row[NormalizedCsvHeader.DATE] || '';

            return {
                // Employee identification fields
                employeeId: null, // Will be populated in transformation phase
                employeeName: undefined,
                externalEmployeeId: row[NormalizedCsvHeader.EXTERNALEMPLOYEEID],
                ssn: row[NormalizedCsvHeader.SSN],

                // Classification data
                agreementId: null, // Will be populated in transformation phase
                agreementName: row[NormalizedCsvHeader.AGREEMENT],
                classificationId: null,
                classificationName: row[NormalizedCsvHeader.CLASSIFICATION],
                subClassificationId: null,
                subClassificationName: row[NormalizedCsvHeader.SUBCLASSIFICATION],

                // Work date information
                workDate: safeParseDate(dateStr) || new Date(), // Fallback to current date if parsing fails
                day: row[NormalizedCsvHeader.DAY],

                // Financial data (convert strings to numbers)
                hourlyRate: row[NormalizedCsvHeader.HOURLYRATE] ? parseFloat(row[NormalizedCsvHeader.HOURLYRATE]) : null,
                jobCode: row[NormalizedCsvHeader.JOBCODE],
                earningsCode: row[NormalizedCsvHeader.EARNINGSCODE],
                costCenter: row[NormalizedCsvHeader.COSTCENTER],
                stHours: row[NormalizedCsvHeader.STHOURS] ? parseFloat(row[NormalizedCsvHeader.STHOURS]) : null,
                otHours: row[NormalizedCsvHeader.OTHOURS] ? parseFloat(row[NormalizedCsvHeader.OTHOURS]) : null,
                dtHours: row[NormalizedCsvHeader.DTHOURS] ? parseFloat(row[NormalizedCsvHeader.DTHOURS]) : null,
                bonus: row[NormalizedCsvHeader.DIRECTPAY] ? parseFloat(row[NormalizedCsvHeader.DIRECTPAY]) : null,
                expenses: row[NormalizedCsvHeader.EXPENSES] ? parseFloat(row[NormalizedCsvHeader.EXPENSES]) : null,

                // Metadata
                rowIndex: index + 1,
                isValid: true,
                errors: []
            };
        });
    }, []);

    // Dialog state management
    const openUploadDialog = useCallback(() => {
        if (!readOnly) {
            setIsDialogOpen(true);
        }
    }, [readOnly]);

    const closeUploadDialog = useCallback(() => {
        setIsDialogOpen(false);
        dispatch({ type: UploadActionType.RESET });
    }, []);

    // File handling
    const handleFileSelected = useCallback((file: File) => {
        dispatch({ type: UploadActionType.SET_FILE, payload: { file } });
    }, []);

    const handleFileCleared = useCallback(() => {
        dispatch({ type: UploadActionType.CLEAR_FILE });
    }, []);

    // Main file upload handler
    const handleFileUpload = useCallback(
        async (file: File) => {
            if (isLoadingEmployees) {
                setGlobalError('Employee data is still loading. Please wait.');
                return;
            }

            if (!processedEmployeeDataForCSV || processedEmployeeDataForCSV.length === 0) {
                setGlobalError('No employee data available for CSV matching.');
                return;
            }

            // Create abort controller for this upload
            abortControllerRef.current = new AbortController();

            try {
                // Check if component is still mounted before starting
                if (!isMountedRef.current) {
                    return;
                }

                safeDispatch({ type: UploadActionType.PARSE_PROGRESS, payload: { progress: 10 } });

                // Parse CSV file - use correct signature with dispatch and column mappings
                const parseResult = await parseCsvFile(file, safeDispatch, csvColumnMappings, abortControllerRef.current?.signal);
                
                if (!isMountedRef.current) return; // Check if component is still mounted
                safeDispatch({ type: UploadActionType.PARSE_PROGRESS, payload: { progress: 30 } });

                // Validate CSV data - use the returned data instead of state
                safeDispatch({ type: UploadActionType.VALIDATE_DATA_START });
                const { validRows, rowErrors, headerErrors } = validateCsvData(parseResult.data);

                if (!isMountedRef.current) return; // Check if component is still mounted

                if (rowErrors.length > 0 || headerErrors.length > 0) {
                    const allErrors = [...rowErrors, ...headerErrors];
                    safeDispatch({ type: UploadActionType.VALIDATE_DATA_ERROR, payload: { errors: allErrors } });
                    return;
                }

                // Convert RawCsvRow to ProcessedTimesheetEntry
                const processedData = convertRawToProcessed(validRows);
                safeDispatch({ type: UploadActionType.VALIDATE_DATA_SUCCESS, payload: { processedData } });
                safeDispatch({ type: UploadActionType.PARSE_PROGRESS, payload: { progress: 50 } });

                // Transform validated data using props employee data (Phase 5)
                safeDispatch({ type: UploadActionType.TRANSFORM_DATA_START });
                const { payStubs, rowErrors: transformErrors } = transformValidatedDataWithContext(
                    processedData,
                    processedEmployeeDataForCSV // Use props data from parent query
                );

                if (!isMountedRef.current) return; // Check if component is still mounted

                if (transformErrors.length > 0) {
                    safeDispatch({ type: UploadActionType.TRANSFORM_DATA_ERROR, payload: { errors: transformErrors } });
                    return;
                }

                safeDispatch({ type: UploadActionType.TRANSFORM_DATA_SUCCESS, payload: { payStubs } });
                safeDispatch({ type: UploadActionType.PARSE_PROGRESS, payload: { progress: 70 } });

                // Convert to Relay input format and upload
                // For now, we'll use the enriched data approach for Relay input
                // TODO: Create a new function that works directly with context data
                const { enrichedEntries, rowErrors: enrichErrors, appliedDefaults } = await enrichPayStubData(processedData);

                if (!isMountedRef.current) return; // Check if component is still mounted

                if (enrichErrors.length > 0) {
                    safeDispatch({ type: UploadActionType.TRANSFORM_DATA_ERROR, payload: { errors: enrichErrors } });
                    return;
                }

                safeDispatch({ type: UploadActionType.ENRICH_DATA_SUCCESS, payload: { processedData, appliedDefaults } });
                safeDispatch({ type: UploadActionType.PARSE_PROGRESS, payload: { progress: 90 } });
                const relayInputs = convertEnrichedToRelayPayStubInput(enrichedEntries);

                // Extract timesheet data from resolved fragment
                await bulkAddPayStubs(environment, {
                    timeSheetId: timeSheetData.id,
                    numericId: timeSheetData.numericId,
                    employerGuid: timeSheetData.employerGuid,
                    payStubs: relayInputs
                });

                if (!isMountedRef.current) return; // Check if component is still mounted

                safeDispatch({ type: UploadActionType.PARSE_PROGRESS, payload: { progress: 100 } });
                safeDispatch({
                    type: UploadActionType.UPLOAD_SUCCESS,
                    payload: { message: `Successfully uploaded ${payStubs.length} pay stubs from ${file.name}` }
                });

                // Clear any previous errors
                setGlobalError(null);
            } catch (error) {
                console.error('Upload failed:', error);
                const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
                
                // Only update state and error if component is still mounted
                if (isMountedRef.current) {
                    setGlobalError(`Upload failed: ${errorMessage}`);
                    safeDispatch({
                        type: UploadActionType.UPLOAD_FAILURE,
                        payload: {
                            message: errorMessage,
                            errors: [{ type: 'general', message: errorMessage }]
                        }
                    });
                }
            } finally {
                // Clean up abort controller
                if (abortControllerRef.current) {
                    abortControllerRef.current.abort();
                    abortControllerRef.current = null;
                }
            }
        },
        [processedEmployeeDataForCSV, isLoadingEmployees, environment, setGlobalError, state.data, convertRawToProcessed]
    );

    // Cleanup on unmount and track component state
    useEffect(() => {
        isMountedRef.current = true;
        
        return () => {
            isMountedRef.current = false;
            
            // Cleanup abort controller if it exists
            if (abortControllerRef.current) {
                abortControllerRef.current.abort();
                abortControllerRef.current = null;
            }
        };
    }, []);

    // Processing dialog state
    const isProcessingDialogOpen = state.status !== ProcessingState.IDLE && state.status !== ProcessingState.ERROR;

    const handleCloseProcessingDialog = useCallback(() => {
        if (state.status === ProcessingState.SUCCESS) {
            closeUploadDialog();
            onClose();
        } else {
            dispatch({ type: UploadActionType.RESET });
        }
    }, [state.status, closeUploadDialog, onClose]);

    return (
        <>
            <Button variant="secondary" isDisabled={readOnly || isLoadingEmployees} onPress={openUploadDialog}>
                <Upload />
                <Text>{isLoadingEmployees ? 'Loading...' : 'Upload'}</Text>
            </Button>

            {isDialogOpen && (
                <DialogContainer onDismiss={closeUploadDialog}>
                    <UploadDialog
                        onClose={closeUploadDialog}
                        onUpload={handleFileUpload}
                        onFileSelected={handleFileSelected}
                        onFileCleared={handleFileCleared}
                        selectedFile={state.file}
                    />
                </DialogContainer>
            )}

            {/* Processing Dialog */}
            <ProcessingDialog
                isOpen={isProcessingDialogOpen}
                onClose={handleCloseProcessingDialog}
                status={state.status}
                errors={state.errors}
                progress={state.progress}
                rowCount={state.rowCount}
                fileName={state.file?.name}
                dispatch={dispatch}
                successMessage={state.successMessage}
            />
        </>
    );
};

export default UploadTimeSheetContent;
