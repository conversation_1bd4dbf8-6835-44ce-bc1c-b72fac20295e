import React, { use<PERSON>emo } from 'react';
import {
    <PERSON><PERSON>,
    DialogContainer,
    <PERSON><PERSON>,
    Divider,
    Content,
    ButtonGroup,
    Button,
    Text,
    View,
    Flex,
    ProgressCircle,
    ProgressBar,
    Well,
    Link as SpectrumLink,
    Divider as SpectrumDivider
} from '@adobe/react-spectrum';
import DownloadIcon from '@spectrum-icons/workflow/Download';
import SuccessIcon from '@spectrum-icons/workflow/CheckmarkCircle';
import ErrorIcon from '@spectrum-icons/workflow/AlertCircle';
import InfoIcon from '@spectrum-icons/workflow/InfoOutline';
import { ProcessingState, ProcessingError, RowError, HeaderError, FileError, UploadActionType, UploadAction } from './types';

interface ProcessingDialogProps {
    isOpen: boolean;
    onClose: () => void;
    status: ProcessingState;
    errors: ProcessingError[];
    progress: number;
    rowCount: number;
    fileName?: string;
    dispatch: React.Dispatch<UploadAction>;
    successMessage?: string;
}

// Helper function to sanitize error messages for display
const sanitizeErrorMessage = (message: string): string => {
    if (typeof message !== 'string') return String(message);
    
    // Remove or escape HTML tags and dangerous characters
    return message
        .replace(/[<>]/g, '') // Remove < and > characters
        .replace(/javascript:/gi, '') // Remove javascript: protocol
        .replace(/on\w+=/gi, '') // Remove event handlers like onclick=
        .trim();
};

// Helper function to get a user-friendly message for error codes
const getErrorMessageForCode = (code: string): string => {
    switch (code) {
        case 'MISSING_REQUIRED':
            return 'Missing required field';
        case 'INVALID_FORMAT':
            return 'Invalid format';
        case 'INVALID_VALUE':
            return 'Invalid value';
        case 'EMPLOYEE_NOT_FOUND':
            return 'Employee not found';
        case 'AGREEMENT_NOT_FOUND':
            return 'Agreement not found';
        default:
            return 'Unknown error';
    }
};

/**
 * ProcessingDialog component - Displays processing status, progress, and errors
 * This component uses Adobe React Spectrum components for the UI
 */
const ProcessingDialog: React.FC<ProcessingDialogProps> = ({
    isOpen,
    onClose,
    status,
    errors,
    progress,
    rowCount,
    fileName,
    dispatch,
    successMessage
}) => {
    // Group errors by type for better display
    const { fileErrors, headerErrors, rowErrors } = useMemo(() => {
        return {
            fileErrors: errors.filter((error): error is FileError => error.type === 'file'),
            headerErrors: errors.filter((error): error is HeaderError => error.type === 'header'),
            rowErrors: errors.filter((error): error is RowError => error.type === 'row')
        };
    }, [errors]);

    // Generate a summary of row errors by type
    const rowErrorSummary = useMemo(() => {
        if (rowErrors.length === 0) return [];

        const errorsByCode: Record<string, number> = {};
        rowErrors.forEach((error) => {
            const code = error.code || 'UNKNOWN';
            errorsByCode[code] = (errorsByCode[code] || 0) + 1;
        });

        return Object.entries(errorsByCode).map(([code, count]) => ({
            code,
            count,
            message: getErrorMessageForCode(code)
        }));
    }, [rowErrors]);

    // Sanitize CSV values to prevent formula injection
    const sanitizeCsvValue = (value: string): string => {
        if (typeof value !== 'string') return String(value);
        
        // Check if the value starts with formula indicators
        if (/^[=+\-@]/.test(value)) {
            return `'${value}`; // Prefix with single quote to prevent formula execution
        }
        return value;
    };

    // Generate CSV content for error report download
    const generateErrorReportCsv = (): string => {
        // Create CSV header row
        const headers = ['Row', 'Column', 'Value', 'Error Message'];
        const csvRows = [headers.join(',')];

        // Add each row error to the CSV
        rowErrors.forEach((error) => {
            const row = [
                String(error.rowIndex),
                sanitizeCsvValue(error.columnName || ''),
                '', // We don't have the value in the error object
                `"${sanitizeCsvValue(error.message).replace(/"/g, '""')}"` // Sanitize and escape quotes for CSV
            ];
            csvRows.push(row.join(','));
        });

        return csvRows.join('\n');
    };

    // Handle download of error report
    const handleDownloadErrorReport = () => {
        try {
            const csvContent = generateErrorReportCsv();
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', `error-report-${new Date().toISOString().slice(0, 10)}.csv`);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url); // Clean up the object URL
        } catch (error) {
            console.error('Failed to generate or download error report:', error);
            // Optionally, inform the user via a toast message or alert
        }
    };

    // Handle close button click
    const handleClose = () => {
        dispatch({ type: UploadActionType.RESET });
        onClose();
    };

    // Render dialog content based on status
    const renderContent = () => {
        switch (status) {
            case ProcessingState.VALIDATING_FILE:
                return (
                    <Flex direction="column" gap="size-200" alignItems="center">
                        <ProgressCircle isIndeterminate aria-label="Validating file" size="L" />
                        <Text>Validating file...</Text>
                    </Flex>
                );
            case ProcessingState.PARSING_FILE:
                return (
                    <Flex direction="column" gap="size-200" alignItems="center">
                        {progress > 0 ? (
                            <>
                                <ProgressBar label="Parsing CSV file" value={progress} minValue={0} maxValue={100} width="size-3000" />
                                <Text data-testid="parsing-progress">Parsing file: {progress.toFixed(0)}% complete</Text>
                            </>
                        ) : (
                            <>
                                <ProgressCircle isIndeterminate aria-label="Parsing file" size="L" />
                                <Text>Parsing CSV file...</Text>
                            </>
                        )}
                    </Flex>
                );
            case ProcessingState.VALIDATING_DATA:
                return (
                    <Flex direction="column" gap="size-200" alignItems="center">
                        <ProgressCircle isIndeterminate aria-label="Validating data" size="L" />
                        <Text>Validating data...</Text>
                    </Flex>
                );
            case ProcessingState.ENRICHING_DATA:
                return (
                    <Flex direction="column" gap="size-200" alignItems="center">
                        <ProgressCircle isIndeterminate aria-label="Enriching data" size="L" />
                        <Text>Enriching data with employee and agreement information...</Text>
                    </Flex>
                );
            case ProcessingState.TRANSFORMING_DATA:
                return (
                    <Flex direction="column" gap="size-200" alignItems="center">
                        <ProgressCircle isIndeterminate aria-label="Transforming data" size="L" />
                        <Text>Transforming data into timesheet format...</Text>
                    </Flex>
                );
            case ProcessingState.FINALIZING_DATA:
                return (
                    <Flex direction="column" gap="size-200" alignItems="center">
                        <ProgressCircle isIndeterminate aria-label="Finalizing data" size="L" />
                        <Text>Finalizing and updating timesheet data...</Text>
                    </Flex>
                );
            case ProcessingState.SUCCESS:
                // Special case for empty CSV files
                if (rowCount === 0) {
                    return (
                        <Flex direction="column" gap="size-200" alignItems="center">
                            <InfoIcon size="XL" color="notice" />
                            <Heading level={3}>No Data Found</Heading>
                            <Text data-testid="no-data-message">No valid data rows found</Text>
                        </Flex>
                    );
                }
                return (
                    <Flex direction="column" gap="size-200" alignItems="center">
                        <SuccessIcon size="XL" color="positive" />
                        <Heading level={3}>Success!</Heading>
                        <Text>{successMessage || `Successfully processed ${rowCount} rows from ${fileName}.`}</Text>
                    </Flex>
                );
            case ProcessingState.ERROR:
                return (
                    <Flex direction="column" gap="size-200">
                        <Flex alignItems="center" gap="size-100">
                            <ErrorIcon size="M" color="negative" />
                            <Heading level={3}>Error Processing CSV</Heading>
                        </Flex>

                        {fileErrors.length > 0 && (
                            <Well>
                                <Heading level={4}>File Errors</Heading>
                                {fileErrors.map((error, index) => (
                                    <Text key={`file-error-${index}`} data-testid="file-error-message">
                                        {sanitizeErrorMessage(error.message)}
                                    </Text>
                                ))}
                            </Well>
                        )}

                        {headerErrors.length > 0 && (
                            <Well>
                                <Heading level={4}>Header Errors</Heading>
                                {headerErrors.map((error, index) => (
                                    <Text key={`header-error-${index}`} data-testid="header-error-message">
                                        {sanitizeErrorMessage(error.message)}
                                    </Text>
                                ))}
                            </Well>
                        )}

                        {rowErrors.length > 0 && (
                            <Well>
                                <Heading level={4}>Data Errors</Heading>
                                <Text>Found {rowErrors.length} errors in the data:</Text>
                                <View marginTop="size-100">
                                    {rowErrorSummary.map((summary, index) => (
                                        <Text key={`summary-${index}`}>
                                            • {summary.count} {summary.message} errors
                                        </Text>
                                    ))}
                                </View>
                                {/* Display individual row errors for testing purposes */}
                                <View marginTop="size-100">
                                    {rowErrors.map((error, index) => (
                                        <Text key={`row-error-${index}`} data-testid="row-error-message">
                                            {sanitizeErrorMessage(error.message)}
                                        </Text>
                                    ))}
                                </View>
                                <View marginTop="size-200">
                                    <Button variant="primary" onPress={handleDownloadErrorReport} marginTop="size-100">
                                        <DownloadIcon />
                                        <Text>Download Error Report</Text>
                                    </Button>
                                </View>
                            </Well>
                        )}

                        {/* Display a general error message for API errors */}
                        {rowErrors.some((error) => error.message.includes('Enrichment error:')) && (
                            <Well>
                                <Heading level={4}>API Error</Heading>
                                <Text data-testid="api-error-message">Error processing data</Text>
                            </Well>
                        )}

                        <Flex alignItems="center" gap="size-100" marginTop="size-100">
                            <InfoIcon size="S" />
                            <Text>Please fix the errors and try uploading again.</Text>
                        </Flex>
                    </Flex>
                );
            default:
                return null;
        }
    };

    // For testing purposes, we need to ensure the dialog has the correct aria-label
    const getDialogTitle = () => {
        if (status === ProcessingState.ERROR) {
            return 'Error Processing CSV';
        } else if (status === ProcessingState.SUCCESS) {
            return 'Upload Complete';
        } else {
            return 'Processing CSV File';
        }
    };

    return (
        <DialogContainer onDismiss={handleClose} isDismissable>
            {isOpen && (
                <Dialog size="L" aria-label={getDialogTitle()}>
                    <Heading>{getDialogTitle()}</Heading>
                    <Divider />
                    <Content>
                        <View padding="size-200">{renderContent()}</View>
                    </Content>
                    <ButtonGroup>
                        <Button variant="secondary" onPress={handleClose}>
                            Close
                        </Button>
                    </ButtonGroup>
                </Dialog>
            )}
        </DialogContainer>
    );
};

export default ProcessingDialog;
