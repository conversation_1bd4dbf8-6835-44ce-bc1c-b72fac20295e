/**
 * UploadTimeSheet Component Types
 *
 * This file contains TypeScript interfaces and types for the UploadTimeSheet component,
 * including CSV column configuration, processing states, and error types.
 */

import type { DefaultApplication } from './utils/dataEnrichment';

// -----------------------------------------------------------------------------
// CSV Column Configuration
// -----------------------------------------------------------------------------

/**
 * Expected CSV column headers
 * These are the original headers as they appear in the CSV file
 */
export enum CsvHeader {
    EMPLOYEE_ID = 'Employee ID',
    SSN = 'SSN',
    AGREEMENT = 'Agreement',
    CLASSIFICATION = 'Classification',
    SUB_CLASSIFICATION = 'Sub Classification',
    DAY = 'Day',
    DATE = 'Date',
    HOURLY_RATE = 'Hourly Rate',
    JOB_CODE = 'Job Code',
    EARNINGS_CODE = 'Earnings Code',
    COST_CENTER = 'Cost Center',
    ST_HOURS = 'ST Hours',
    OT_HOURS = 'OT Hours',
    DT_HOURS = 'DT Hours',
    DIRECT_PAY = 'Direct Pay', // Maps to 'bonus' internally
    EXPENSES = 'Expenses'
}

/**
 * Normalized CSV column headers
 * These are the headers after normalization (uppercase, no spaces)
 */
export enum NormalizedCsvHeader {
    EXTERNALEMPLOYEEID = 'EXTERNALEMPLOYEEID', // Transformed from 'EMPLOYEE ID'
    SSN = 'SSN',
    AGREEMENT = 'AGREEMENT',
    CLASSIFICATION = 'CLASSIFICATION',
    SUBCLASSIFICATION = 'SUBCLASSIFICATION',
    DAY = 'DAY',
    DATE = 'DATE',
    HOURLYRATE = 'HOURLYRATE',
    JOBCODE = 'JOBCODE',
    EARNINGSCODE = 'EARNINGSCODE',
    COSTCENTER = 'COSTCENTER',
    STHOURS = 'STHOURS',
    OTHOURS = 'OTHOURS',
    DTHOURS = 'DTHOURS',
    DIRECTPAY = 'DIRECTPAY', // Maps to 'bonus' internally
    EXPENSES = 'EXPENSES'
}

/**
 * Maps original CSV headers to normalized headers
 */
export const csvColumnMappings: Record<string, string> = {
    [CsvHeader.EMPLOYEE_ID]: NormalizedCsvHeader.EXTERNALEMPLOYEEID,
    [CsvHeader.SSN]: NormalizedCsvHeader.SSN,
    [CsvHeader.AGREEMENT]: NormalizedCsvHeader.AGREEMENT,
    [CsvHeader.CLASSIFICATION]: NormalizedCsvHeader.CLASSIFICATION,
    [CsvHeader.SUB_CLASSIFICATION]: NormalizedCsvHeader.SUBCLASSIFICATION,
    [CsvHeader.DAY]: NormalizedCsvHeader.DAY,
    [CsvHeader.DATE]: NormalizedCsvHeader.DATE,
    [CsvHeader.HOURLY_RATE]: NormalizedCsvHeader.HOURLYRATE,
    [CsvHeader.JOB_CODE]: NormalizedCsvHeader.JOBCODE,
    [CsvHeader.EARNINGS_CODE]: NormalizedCsvHeader.EARNINGSCODE,
    [CsvHeader.COST_CENTER]: NormalizedCsvHeader.COSTCENTER,
    [CsvHeader.ST_HOURS]: NormalizedCsvHeader.STHOURS,
    [CsvHeader.OT_HOURS]: NormalizedCsvHeader.OTHOURS,
    [CsvHeader.DT_HOURS]: NormalizedCsvHeader.DTHOURS,
    [CsvHeader.DIRECT_PAY]: NormalizedCsvHeader.DIRECTPAY,
    [CsvHeader.EXPENSES]: NormalizedCsvHeader.EXPENSES
};

/**
 * Mandatory CSV columns configuration
 * At least one of EXTERNALEMPLOYEEID or SSN must be present
 */
export const mandatoryCsvColumns: {
    required: NormalizedCsvHeader[];
    atLeastOne: NormalizedCsvHeader[][];
} = {
    required: [NormalizedCsvHeader.DATE],
    atLeastOne: [[NormalizedCsvHeader.EXTERNALEMPLOYEEID, NormalizedCsvHeader.SSN]]
};

// -----------------------------------------------------------------------------
// Raw CSV Data Types
// -----------------------------------------------------------------------------

/**
 * Raw CSV row type after parsing but before validation and transformation
 * All fields are strings as they come directly from the CSV
 */
export interface RawCsvRow {
    // Employee identification (at least one required)
    [NormalizedCsvHeader.EXTERNALEMPLOYEEID]?: string;
    [NormalizedCsvHeader.SSN]?: string;

    // Classification data
    [NormalizedCsvHeader.AGREEMENT]?: string;
    [NormalizedCsvHeader.CLASSIFICATION]?: string;
    [NormalizedCsvHeader.SUBCLASSIFICATION]?: string;

    // Work date information
    [NormalizedCsvHeader.DAY]?: string;
    [NormalizedCsvHeader.DATE]: string; // Required

    // Financial data
    [NormalizedCsvHeader.HOURLYRATE]?: string;
    [NormalizedCsvHeader.JOBCODE]?: string;
    [NormalizedCsvHeader.EARNINGSCODE]?: string;
    [NormalizedCsvHeader.COSTCENTER]?: string;
    [NormalizedCsvHeader.STHOURS]?: string;
    [NormalizedCsvHeader.OTHOURS]?: string;
    [NormalizedCsvHeader.DTHOURS]?: string;
    [NormalizedCsvHeader.DIRECTPAY]?: string; // Maps to 'bonus' internally
    [NormalizedCsvHeader.EXPENSES]?: string;

    // Allow for additional properties
    [key: string]: string | undefined;
}

// -----------------------------------------------------------------------------
// Processed Timesheet Entry Types
// -----------------------------------------------------------------------------

/**
 * Processed timesheet entry after initial validation but before transformation
 * This is an intermediate type with validated and potentially converted values
 */
export interface ProcessedTimesheetEntry {
    // Employee identification (matched from context)
    employeeId?: string | number | null;
    employeeName?: string;
    externalEmployeeId?: string;
    ssn?: string;

    // Classification data (matched from context)
    agreementId?: string | number | null; // Support both string (GraphQL) and number (legacy)
    agreementName?: string;
    classificationId?: string | number | null; // Support both string (GraphQL) and number (legacy)
    classificationName?: string;
    subClassificationId?: string | number | null; // Support both string (GraphQL) and number (legacy)
    subClassificationName?: string;

    // Work date information (validated)
    day?: string;
    workDate: string; // ISO date string format

    // Financial data (converted to numbers)
    hourlyRate?: number | null;
    jobCode?: string;
    earningsCode?: string;
    costCenter?: string;
    stHours?: number | null;
    otHours?: number | null;
    dtHours?: number | null;
    bonus?: number | null; // Mapped from 'Direct Pay'
    expenses?: number | null;

    // Validation state
    rowIndex: number;
    isValid: boolean;
    errors: RowError[];
}

// -----------------------------------------------------------------------------
// PayStub Upload Types
// -----------------------------------------------------------------------------

/**
 * PayStubDetailUpload type - similar to PayStubDetail but for upload process
 */
export interface PayStubDetailUpload {
    id: string; // Client-generated ID
    payStubId: string; // Reference to parent PayStubUpload
    workDate: string; // ISO date string
    stHours: number | null;
    otHours: number | null;
    dtHours: number | null;
    totalHours: number | null; // Calculated from stHours + otHours + dtHours
    jobCode?: string | null;
    earningsCode?: string | null;
    agreementId?: string | number | null; // Support both string (GraphQL) and number (legacy)
    classificationId?: string | number | null; // Support both string (GraphQL) and number (legacy)
    subClassificationId?: string | number | null; // Support both string (GraphQL) and number (legacy)
    costCenter?: string | null;
    hourlyRate?: number | null;
    bonus?: number | null; // Mapped from 'Direct Pay'
    expenses?: number | null;
    reportLineItemId?: number | null;

    // Upload-specific fields
    rowIndex: number; // Original CSV row index for error reporting
    inError: boolean;
    errors: RowError[];
}

/**
 * PayStubUpload type - similar to PayStub but for upload process
 */
export interface PayStubUpload {
    id: string; // Client-generated ID
    employeeId: string | number | null;
    name: string; // Usually employee name

    // Aggregated values from details
    stHours: number | null;
    otHours: number | null;
    dtHours: number | null;
    totalHours: number | null;
    bonus: number | null;
    expenses: number | null;

    // Collection of detail items
    details: PayStubDetailUpload[];

    // UI state
    expanded: boolean;
    delete: boolean;
    inError: boolean;

    // Upload-specific fields
    errors: RowError[];
}

// -----------------------------------------------------------------------------
// Error Types
// -----------------------------------------------------------------------------

/**
 * File error type for issues with the CSV file itself
 */
export interface FileError {
    type: 'file';
    message: string;
    code?: 'INVALID_TYPE' | 'READ_ERROR' | 'PARSE_ERROR' | 'FILE_TOO_LARGE' | 'TOO_MANY_ROWS';
}

/**
 * Header error type for issues with CSV headers
 */
export interface HeaderError {
    type: 'header';
    message: string;
    code?: 'MISSING_REQUIRED' | 'INVALID_FORMAT';
    missingHeaders?: string[];
}

/**
 * Row error type for issues with individual CSV rows
 */
export interface RowError {
    type: 'row';
    rowIndex: number;
    columnName?: string;
    message: string;
    code?:
        | 'MISSING_REQUIRED'
        | 'INVALID_FORMAT'
        | 'INVALID_VALUE'
        | 'EMPLOYEE_NOT_FOUND'
        | 'AGREEMENT_NOT_FOUND'
        | 'CLASSIFICATION_NOT_FOUND'
        | 'SUBCLASSIFICATION_NOT_FOUND'
        | 'MISSING_EMPLOYEE_ID';
}

/**
 * Union type for all processing errors
 */
export interface GeneralError {
    type: 'general';
    message: string;
    code?: string;
}

export type ProcessingError = FileError | HeaderError | RowError | GeneralError;

// -----------------------------------------------------------------------------
// Processing State Types
// -----------------------------------------------------------------------------

/**
 * Processing state enum for the upload reducer
 */
export enum ProcessingState {
    IDLE = 'IDLE',
    VALIDATING_FILE = 'VALIDATING_FILE',
    PARSING_FILE = 'PARSING_FILE',
    VALIDATING_DATA = 'VALIDATING_DATA',
    ENRICHING_DATA = 'ENRICHING_DATA',
    TRANSFORMING_DATA = 'TRANSFORMING_DATA',
    FINALIZING_DATA = 'FINALIZING_DATA',
    SUCCESS = 'SUCCESS',
    ERROR = 'ERROR'
}

/**
 * Upload state for the useReducer hook
 * Note: payStubs is kept for compatibility but data flows through Relay store
 */
export interface UploadState {
    status: ProcessingState;
    file: File | null;
    data: RawCsvRow[];
    processedData: ProcessedTimesheetEntry[];
    payStubs: PayStubUpload[]; // Legacy field, kept for compatibility
    errors: ProcessingError[];
    progress: number;
    headers: string[];
    rowCount: number;
    successMessage?: string;
    payStubsToCreate?: PayStubUpload[]; // Legacy field, kept for compatibility  
    appliedDefaults?: DefaultApplication[];
}

/**
 * Upload action types for the useReducer hook
 */
export enum UploadActionType {
    SET_FILE = 'SET_FILE',
    CLEAR_FILE = 'CLEAR_FILE',
    VALIDATE_FILE_START = 'VALIDATE_FILE_START',
    VALIDATE_FILE_SUCCESS = 'VALIDATE_FILE_SUCCESS',
    VALIDATE_FILE_ERROR = 'VALIDATE_FILE_ERROR',
    PARSE_START = 'PARSE_START',
    PARSE_PROGRESS = 'PARSE_PROGRESS',
    PARSE_SUCCESS = 'PARSE_SUCCESS',
    PARSE_ERROR = 'PARSE_ERROR',
    VALIDATE_DATA_START = 'VALIDATE_DATA_START',
    VALIDATE_DATA_SUCCESS = 'VALIDATE_DATA_SUCCESS',
    VALIDATE_DATA_ERROR = 'VALIDATE_DATA_ERROR',
    ENRICH_DATA_START = 'ENRICH_DATA_START',
    ENRICH_DATA_PROGRESS = 'ENRICH_DATA_PROGRESS',
    ENRICH_DATA_SUCCESS = 'ENRICH_DATA_SUCCESS',
    ENRICH_DATA_ERROR = 'ENRICH_DATA_ERROR',
    TRANSFORM_DATA_START = 'TRANSFORM_DATA_START',
    TRANSFORM_DATA_SUCCESS = 'TRANSFORM_DATA_SUCCESS',
    TRANSFORM_DATA_ERROR = 'TRANSFORM_DATA_ERROR',
    FINALIZE_DATA_START = 'FINALIZE_DATA_START',
    FINALIZE_DATA_SUCCESS = 'FINALIZE_DATA_SUCCESS',
    FINALIZE_DATA_ERROR = 'FINALIZE_DATA_ERROR',
    UPLOAD_SUCCESS = 'UPLOAD_SUCCESS',
    UPLOAD_FAILURE = 'UPLOAD_FAILURE',
    RESET = 'RESET'
}

/**
 * Upload action for the useReducer hook
 */
export type UploadAction =
    | { type: UploadActionType.SET_FILE; payload: { file: File } }
    | { type: UploadActionType.CLEAR_FILE }
    | { type: UploadActionType.VALIDATE_FILE_START }
    | { type: UploadActionType.VALIDATE_FILE_SUCCESS }
    | { type: UploadActionType.VALIDATE_FILE_ERROR; payload: { errors: FileError[] } }
    | { type: UploadActionType.PARSE_START }
    | { type: UploadActionType.PARSE_PROGRESS; payload: { progress: number } }
    | { type: UploadActionType.PARSE_SUCCESS; payload: { data: RawCsvRow[]; headers: string[]; rowCount: number } }
    | { type: UploadActionType.PARSE_ERROR; payload: { errors: ProcessingError[] } }
    | { type: UploadActionType.VALIDATE_DATA_START }
    | { type: UploadActionType.VALIDATE_DATA_SUCCESS; payload: { processedData: ProcessedTimesheetEntry[] } }
    | { type: UploadActionType.VALIDATE_DATA_ERROR; payload: { errors: ProcessingError[] } }
    | { type: UploadActionType.ENRICH_DATA_START }
    | { type: UploadActionType.ENRICH_DATA_PROGRESS; payload: { progress: number } }
    | {
          type: UploadActionType.ENRICH_DATA_SUCCESS;
          payload: { processedData: ProcessedTimesheetEntry[]; appliedDefaults: DefaultApplication[] };
      }
    | { type: UploadActionType.ENRICH_DATA_ERROR; payload: { errors: ProcessingError[] } }
    | { type: UploadActionType.TRANSFORM_DATA_START }
    | { type: UploadActionType.TRANSFORM_DATA_SUCCESS; payload: { payStubs: PayStubUpload[] } }
    | { type: UploadActionType.TRANSFORM_DATA_ERROR; payload: { errors: ProcessingError[] } }
    | { type: UploadActionType.FINALIZE_DATA_START }
    | { type: UploadActionType.FINALIZE_DATA_SUCCESS }
    | { type: UploadActionType.FINALIZE_DATA_ERROR; payload: { errors: ProcessingError[] } }
    | { type: UploadActionType.UPLOAD_SUCCESS; payload: { message: string } }
    | { type: UploadActionType.UPLOAD_FAILURE; payload: { message: string; errors?: ProcessingError[] } }
    | { type: UploadActionType.RESET };
