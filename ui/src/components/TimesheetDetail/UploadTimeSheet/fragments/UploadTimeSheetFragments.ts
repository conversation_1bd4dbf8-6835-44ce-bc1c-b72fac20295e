/**
 * Upload TimeSheet Relay Fragments
 *
 * Simplified single-fragment design for upload functionality to reduce fragment
 * dependencies and improve testability while maintaining consistency with
 * the rest of the timesheet fragment-based architecture.
 *
 * Architecture Improvement:
 * - Single fragment instead of three separate fragments
 * - Reduces useFragment calls from 3 to 1
 * - Simplifies testing by reducing mock complexity
 * - Maintains existing API-based employee/agreement lookup for data enrichment
 * - Follows React/Relay best practices for component responsibility
 */

import { graphql } from 'react-relay';

/**
 * Simplified consolidated fragment for upload operations
 * Contains core TimeSheet data needed for upload functionality.
 *
 * Employee and agreement data is retrieved during the data enrichment phase
 * via API calls (maintaining existing functionality) rather than through
 * additional fragments, which simplifies the component architecture.
 *
 * This design:
 * - Eliminates complex multi-fragment dependencies
 * - Makes the component much easier to test (single useFragment call)
 * - Maintains data consistency with existing upload logic
 * - Follows single responsibility principle for fragments
 */
export const UploadTimeSheetConsolidatedFragment = graphql`
    fragment UploadTimeSheetFragments_timeSheetConsolidated on TimeSheet
    @argumentDefinitions(
        first: { type: "Int", defaultValue: 500 }
        after: { type: "String" }
    ) {
        # Core timesheet data needed for upload operations
        id
        numericId
        employerGuid

        # Existing payStubs for context and validation
        payStubs(first: $first, after: $after) 
            @connection(key: "UploadTimeSheetFragments_timeSheetConsolidated_payStubs") {
            edges {
                node {
                    id
                    employeeId
                    name
                    # totalHours is computed from individual hour fields
                }
            }
        }
    }
`;

/**
 * Legacy individual fragments - kept for backward compatibility during transition
 * These will be removed once all code is updated to use the consolidated fragment
 */

/**
 * @deprecated Use UploadTimeSheetConsolidatedFragment instead
 * Main timesheet fragment for upload operations
 * Contains the essential timesheet data needed for uploads
 */
export const UploadTimeSheetFragment = graphql`
    fragment UploadTimeSheetFragments_timeSheet on TimeSheet
    @argumentDefinitions(
        first: { type: "Int", defaultValue: 500 }
        after: { type: "String" }
    ) {
        id
        numericId
        employerGuid
        payStubs(first: $first, after: $after) 
            @connection(key: "UploadTimeSheetFragments_timeSheet_payStubs") {
            edges {
                node {
                    id
                    employeeId
                    name
                    # totalHours is computed from individual hour fields
                }
            }
        }
    }
`;

/**
 * @deprecated Use UploadTimeSheetConsolidatedFragment instead
 * Employee data fragment for upload operations
 * Contains employee fields needed for matching during upload
 */
export const UploadEmployeeDataFragment = graphql`
    fragment UploadTimeSheetFragments_employees on Employee @relay(plural: true) {
        id
        firstName
        lastName
        ssn
        externalEmployeeId
    }
`;

/**
 * @deprecated Use UploadTimeSheetConsolidatedFragment instead
 * Agreement data fragment for upload operations
 * Contains agreement fields needed for matching during upload
 */
export const UploadAgreementDataFragment = graphql`
    fragment UploadTimeSheetFragments_agreements on Agreement @relay(plural: true) {
        id
        name
    }
`;
