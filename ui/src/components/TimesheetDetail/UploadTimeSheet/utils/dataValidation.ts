/**
 * Data Validation Utilities
 *
 * This file contains functions for validating CSV data after parsing
 * to ensure it meets the required format and data types.
 */

import { RawCsvRow, <PERSON><PERSON>rro<PERSON>, HeaderError, NormalizedCsvHeader, mandatoryCsvColumns, ProcessedTimesheetEntry } from '../types';

/**
 * Validates a string as a valid SSN format
 * Valid formats: ###-##-#### or ######### (9 digits)
 *
 * @param ssn The SSN string to validate
 * @returns True if the SSN is valid, false otherwise
 */
export const isValidSSN = (ssn: string): boolean => {
    // Remove any hyphens or spaces
    const cleanedSSN = ssn.replace(/[-\s]/g, '');

    // Check if it's exactly 9 digits
    return /^\d{9}$/.test(cleanedSSN);
};

/**
 * Validates a string as a valid date format with strict validation
 * Valid formats: MM/DD/YYYY or YYYY-MM-DD only
 * Rejects ambiguous formats and invalid dates
 *
 * @param dateStr The date string to validate
 * @returns True if the date is valid, false otherwise
 */
export const isValidDate = (dateStr: string): boolean => {
    if (!dateStr || typeof dateStr !== 'string') return false;

    // Remove any extra whitespace
    const trimmedDateStr = dateStr.trim();
    
    // Strictly validate YYYY-MM-DD format
    const isoDatePattern = /^(\d{4})-(\d{2})-(\d{2})$/;
    const isoMatch = trimmedDateStr.match(isoDatePattern);
    
    if (isoMatch) {
        const year = parseInt(isoMatch[1], 10);
        const month = parseInt(isoMatch[2], 10);
        const day = parseInt(isoMatch[3], 10);
        
        // Validate ranges
        if (year < 1900 || year > 2100) return false; // Reasonable year range
        if (month < 1 || month > 12) return false;
        if (day < 1 || day > 31) return false;
        
        // Create date and verify it wasn't normalized (e.g., Feb 30 -> Mar 1)
        const testDate = new Date(year, month - 1, day);
        return testDate.getFullYear() === year && 
               testDate.getMonth() === month - 1 && 
               testDate.getDate() === day;
    }

    // Strictly validate MM/DD/YYYY format
    const usDatePattern = /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/;
    const usMatch = trimmedDateStr.match(usDatePattern);
    
    if (usMatch) {
        const month = parseInt(usMatch[1], 10);
        const day = parseInt(usMatch[2], 10);
        const year = parseInt(usMatch[3], 10);
        
        // Validate ranges
        if (year < 1900 || year > 2100) return false; // Reasonable year range
        if (month < 1 || month > 12) return false;
        if (day < 1 || day > 31) return false;
        
        // Create date and verify it wasn't normalized
        const testDate = new Date(year, month - 1, day);
        return testDate.getFullYear() === year && 
               testDate.getMonth() === month - 1 && 
               testDate.getDate() === day;
    }

    // Reject all other formats as ambiguous or invalid
    return false;
};

/**
 * Safely parses a date string with strict validation
 * Returns null for invalid dates to prevent date constructor ambiguity
 *
 * @param dateStr The date string to parse
 * @returns Date object or null if invalid
 */
export const safeParseDate = (dateStr: string): Date | null => {
    if (!isValidDate(dateStr)) {
        return null;
    }

    const trimmedDateStr = dateStr.trim();
    
    // Parse YYYY-MM-DD format
    const isoMatch = trimmedDateStr.match(/^(\d{4})-(\d{2})-(\d{2})$/);
    if (isoMatch) {
        const year = parseInt(isoMatch[1], 10);
        const month = parseInt(isoMatch[2], 10);
        const day = parseInt(isoMatch[3], 10);
        return new Date(year, month - 1, day);
    }
    
    // Parse MM/DD/YYYY format
    const usMatch = trimmedDateStr.match(/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/);
    if (usMatch) {
        const month = parseInt(usMatch[1], 10);
        const day = parseInt(usMatch[2], 10);
        const year = parseInt(usMatch[3], 10);
        return new Date(year, month - 1, day);
    }
    
    return null;
};

/**
 * Validates a string as a valid number
 *
 * @param numStr The number string to validate
 * @param allowNegative Whether to allow negative numbers
 * @param maxDecimals Maximum number of decimal places allowed
 * @returns True if the number is valid, false otherwise
 */
export const isValidNumber = (numStr: string, allowNegative = false, maxDecimals = 2): boolean => {
    if (!numStr || numStr.trim() === '') return true; // Empty is valid (will be treated as 0)

    // Remove any commas and spaces
    const cleanedNumStr = numStr.replace(/[,\s]/g, '');

    // Check if it's a valid number
    if (!/^-?\d*\.?\d*$/.test(cleanedNumStr)) {
        return false;
    }

    // Parse the number
    const num = parseFloat(cleanedNumStr);

    // Check if it's negative and not allowed
    if (!allowNegative && num < 0) {
        return false;
    }

    // Check decimal places
    if (maxDecimals >= 0) {
        const decimalPart = cleanedNumStr.includes('.') ? cleanedNumStr.split('.')[1] : '';

        if (decimalPart.length > maxDecimals) {
            return false;
        }
    }

    return true;
};

/**
 * Helper function to check if a text field has meaningful data
 * @param value The text value to check
 * @returns True if the value has meaningful data
 */
const hasTextData = (value: string | undefined): boolean => {
    return value !== undefined && value !== null && value.trim() !== '';
};

/**
 * Helper function to check if a numeric field has meaningful data
 * @param value The numeric value to check
 * @returns True if the value has meaningful data (not null, undefined, empty, or 0)
 */
const hasNumericData = (value: string | undefined): boolean => {
    if (!value || value.trim() === '') return false;
    const num = parseFloat(value.replace(/[,\s]/g, ''));
    return !isNaN(num) && num !== 0;
};

/**
 * Checks if a CSV row has trigger fields that would require agreement and classification
 * According to PRD 3.4.3, trigger fields are: Hours (ST, OT, DT), Job code, Cost center, Bonus, or Expenses
 * @param row The CSV row to check
 * @returns True if any trigger fields have meaningful data
 */
const hasTriggerFieldsWithData = (row: RawCsvRow): boolean => {
    return (
        hasNumericData(row[NormalizedCsvHeader.STHOURS]) ||
        hasNumericData(row[NormalizedCsvHeader.OTHOURS]) ||
        hasNumericData(row[NormalizedCsvHeader.DTHOURS]) ||
        hasTextData(row[NormalizedCsvHeader.JOBCODE]) ||
        hasTextData(row[NormalizedCsvHeader.COSTCENTER]) ||
        hasNumericData(row[NormalizedCsvHeader.DIRECTPAY]) || // Maps to bonus
        hasNumericData(row[NormalizedCsvHeader.EXPENSES])
    );
};

/**
 * Validates required fields for a CSV row according to PRD 3.4.3
 * Agreement and classification are required if trigger fields have data
 * @param row The CSV row to validate
 * @param rowIndex The 1-based row index for error reporting
 * @returns Array of validation errors
 */
const validateRequiredFieldsForCsvRow = (row: RawCsvRow, rowIndex: number): RowError[] => {
    const errors: RowError[] = [];

    // Check if this row has trigger fields that require agreement and classification
    const hasTriggerData = hasTriggerFieldsWithData(row);

    if (hasTriggerData) {
        // Agreement is required when trigger fields have data
        if (!hasTextData(row[NormalizedCsvHeader.AGREEMENT])) {
            errors.push({
                type: 'row',
                rowIndex,
                columnName: NormalizedCsvHeader.AGREEMENT,
                message: 'Agreement is required when hours, job code, cost center, bonus, or expenses are entered.',
                code: 'MISSING_REQUIRED'
            });
        }

        // Classification is required when trigger fields have data (unless agreement is "No Agreement")
        // Note: We can't check for "No Agreement" ID here since we're working with raw CSV text
        // This validation will be refined during the enrichment phase when we have IDs
        if (!hasTextData(row[NormalizedCsvHeader.CLASSIFICATION])) {
            errors.push({
                type: 'row',
                rowIndex,
                columnName: NormalizedCsvHeader.CLASSIFICATION,
                message: 'Classification is required when hours, job code, cost center, bonus, or expenses are entered.',
                code: 'MISSING_REQUIRED'
            });
        }
    }

    return errors;
};

/**
 * Validates processed timesheet entries for required fields according to PRD 3.4.3
 * This is used after enrichment when we have actual IDs and can check for "No Agreement"
 * @param entries Array of processed timesheet entries
 * @returns Array of validation errors
 */
export const validateRequiredFieldsForProcessedEntries = (entries: ProcessedTimesheetEntry[]): RowError[] => {
    const errors: RowError[] = [];

    entries.forEach((entry) => {
        // Check if this entry has trigger fields that require agreement and classification
        const hasTriggerData =
            (entry.stHours !== null && entry.stHours !== undefined && entry.stHours !== 0) ||
            (entry.otHours !== null && entry.otHours !== undefined && entry.otHours !== 0) ||
            (entry.dtHours !== null && entry.dtHours !== undefined && entry.dtHours !== 0) ||
            (entry.jobCode !== null && entry.jobCode !== undefined && entry.jobCode.trim() !== '') ||
            (entry.costCenter !== null && entry.costCenter !== undefined && entry.costCenter.trim() !== '') ||
            (entry.bonus !== null && entry.bonus !== undefined && entry.bonus !== 0) ||
            (entry.expenses !== null && entry.expenses !== undefined && entry.expenses !== 0);

        if (hasTriggerData) {
            // Agreement is required
            if (!entry.agreementId) {
                errors.push({
                    type: 'row',
                    rowIndex: entry.rowIndex,
                    columnName: 'agreement',
                    message: 'Agreement is required when hours, job code, cost center, bonus, or expenses are entered.',
                    code: 'MISSING_REQUIRED'
                });
            }

            // Classification is required unless agreement is "No Agreement" (ID 0)
            const isNoAgreement = entry.agreementId === 0;
            if (!isNoAgreement && !entry.classificationId) {
                errors.push({
                    type: 'row',
                    rowIndex: entry.rowIndex,
                    columnName: 'classification',
                    message: 'Classification is required when hours, job code, cost center, bonus, or expenses are entered.',
                    code: 'MISSING_REQUIRED'
                });
            }
        }
    });

    return errors;
};

/**
 * Validates CSV data for structural integrity, basic data types, and required fields
 *
 * @param parsedData Array of parsed CSV rows
 * @returns Object containing valid rows, row errors, and header errors
 */
export const validateCsvData = (
    parsedData: RawCsvRow[]
): {
    validRows: RawCsvRow[];
    rowErrors: RowError[];
    headerErrors: HeaderError[];
} => {
    const validRows: RawCsvRow[] = [];
    const rowErrors: RowError[] = [];
    const headerErrors: HeaderError[] = [];

    // Validate each row
    parsedData.forEach((row, index) => {
        const rowIndex = index + 1; // 1-based index for user-friendly error messages
        const rowErrorsForThisRow: RowError[] = [];

        // Check for required fields
        // 1. Date is required
        if (!row[NormalizedCsvHeader.DATE]) {
            rowErrorsForThisRow.push({
                type: 'row',
                rowIndex,
                columnName: NormalizedCsvHeader.DATE,
                message: 'Date is required',
                code: 'MISSING_REQUIRED'
            });
        } else if (!isValidDate(row[NormalizedCsvHeader.DATE])) {
            rowErrorsForThisRow.push({
                type: 'row',
                rowIndex,
                columnName: NormalizedCsvHeader.DATE,
                message: 'Invalid date format. Expected MM/DD/YYYY or YYYY-MM-DD',
                code: 'INVALID_FORMAT'
            });
        }

        // 2. At least one of Employee ID or SSN is required
        const hasEmployeeId = !!row[NormalizedCsvHeader.EXTERNALEMPLOYEEID];
        const hasSSN = !!row[NormalizedCsvHeader.SSN];

        if (!hasEmployeeId && !hasSSN) {
            rowErrorsForThisRow.push({
                type: 'row',
                rowIndex,
                message: 'At least one of Employee ID or SSN is required',
                code: 'MISSING_REQUIRED'
            });
        }

        // Validate SSN format if provided
        if (hasSSN && !isValidSSN(row[NormalizedCsvHeader.SSN] as string)) {
            rowErrorsForThisRow.push({
                type: 'row',
                rowIndex,
                columnName: NormalizedCsvHeader.SSN,
                message: 'Invalid SSN format. Expected ###-##-#### or #########',
                code: 'INVALID_FORMAT'
            });
        }

        // 3. Validate required fields according to PRD 3.4.3
        const requiredFieldErrors = validateRequiredFieldsForCsvRow(row, rowIndex);
        rowErrorsForThisRow.push(...requiredFieldErrors);

        // Validate numeric fields
        // Hourly Rate
        if (row[NormalizedCsvHeader.HOURLYRATE] && !isValidNumber(row[NormalizedCsvHeader.HOURLYRATE], false, 2)) {
            rowErrorsForThisRow.push({
                type: 'row',
                rowIndex,
                columnName: NormalizedCsvHeader.HOURLYRATE,
                message: 'Hourly Rate must be a positive number with at most 2 decimal places',
                code: 'INVALID_VALUE'
            });
        }

        // ST Hours
        if (row[NormalizedCsvHeader.STHOURS] && !isValidNumber(row[NormalizedCsvHeader.STHOURS], false, 2)) {
            rowErrorsForThisRow.push({
                type: 'row',
                rowIndex,
                columnName: NormalizedCsvHeader.STHOURS,
                message: 'ST Hours must be a positive number with at most 2 decimal places',
                code: 'INVALID_VALUE'
            });
        }

        // OT Hours
        if (row[NormalizedCsvHeader.OTHOURS] && !isValidNumber(row[NormalizedCsvHeader.OTHOURS], false, 2)) {
            rowErrorsForThisRow.push({
                type: 'row',
                rowIndex,
                columnName: NormalizedCsvHeader.OTHOURS,
                message: 'OT Hours must be a positive number with at most 2 decimal places',
                code: 'INVALID_VALUE'
            });
        }

        // DT Hours
        if (row[NormalizedCsvHeader.DTHOURS] && !isValidNumber(row[NormalizedCsvHeader.DTHOURS], false, 2)) {
            rowErrorsForThisRow.push({
                type: 'row',
                rowIndex,
                columnName: NormalizedCsvHeader.DTHOURS,
                message: 'DT Hours must be a positive number with at most 2 decimal places',
                code: 'INVALID_VALUE'
            });
        }

        // Direct Pay (Bonus)
        if (row[NormalizedCsvHeader.DIRECTPAY] && !isValidNumber(row[NormalizedCsvHeader.DIRECTPAY], false, 2)) {
            rowErrorsForThisRow.push({
                type: 'row',
                rowIndex,
                columnName: NormalizedCsvHeader.DIRECTPAY,
                message: 'Direct Pay must be a positive number with at most 2 decimal places',
                code: 'INVALID_VALUE'
            });
        }

        // Expenses
        if (row[NormalizedCsvHeader.EXPENSES] && !isValidNumber(row[NormalizedCsvHeader.EXPENSES], false, 2)) {
            rowErrorsForThisRow.push({
                type: 'row',
                rowIndex,
                columnName: NormalizedCsvHeader.EXPENSES,
                message: 'Expenses must be a positive number with at most 2 decimal places',
                code: 'INVALID_VALUE'
            });
        }

        // Add all errors for this row to the main errors array
        rowErrors.push(...rowErrorsForThisRow);

        // If no errors for this row, add it to valid rows
        if (rowErrorsForThisRow.length === 0) {
            validRows.push(row);
        }
    });

    return {
        validRows,
        rowErrors,
        headerErrors
    };
};
