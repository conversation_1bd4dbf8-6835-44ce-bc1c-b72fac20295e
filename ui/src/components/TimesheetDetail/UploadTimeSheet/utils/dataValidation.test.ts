/**
 * Unit tests for Data Validation Utilities
 */

import { isValidSSN, isValidDate, isValidNumber, validateCsvData } from './dataValidation';
import { NormalizedCsvHeader, RawCsvRow } from '../types';

describe('Data Validation Utilities', () => {
    describe('isValidSSN', () => {
        it('should return true for valid SSN with hyphens', () => {
            expect(isValidSSN('***********')).toBe(true);
        });

        it('should return true for valid SSN without hyphens', () => {
            expect(isValidSSN('123456789')).toBe(true);
        });

        it('should return true for valid SSN with spaces', () => {
            expect(isValidSSN('123 45 6789')).toBe(true);
        });

        it('should return false for SSN with less than 9 digits', () => {
            expect(isValidSSN('12345678')).toBe(false);
        });

        it('should return false for SSN with more than 9 digits', () => {
            expect(isValidSSN('1234567890')).toBe(false);
        });

        it('should return false for SSN with non-numeric characters', () => {
            expect(isValidSSN('123-45-678A')).toBe(false);
        });
    });

    describe('isValidDate', () => {
        it('should return true for valid date in MM/DD/YYYY format', () => {
            expect(isValidDate('01/15/2023')).toBe(true);
        });

        it('should return true for valid date in YYYY-MM-DD format', () => {
            expect(isValidDate('2023-01-15')).toBe(true);
        });

        it('should return false for invalid date format', () => {
            expect(isValidDate('15-01-2023')).toBe(false); // DD-MM-YYYY not supported
        });

        it('should return false for invalid date values', () => {
            expect(isValidDate('02/30/2023')).toBe(false); // February 30th doesn't exist
        });

        it('should return false for empty date string', () => {
            expect(isValidDate('')).toBe(false);
        });

        it('should return false for ambiguous date format (security)', () => {
            expect(isValidDate('January 15, 2023')).toBe(false);
        });
    });

    describe('isValidNumber', () => {
        it('should return true for valid integer', () => {
            expect(isValidNumber('123')).toBe(true);
        });

        it('should return true for valid decimal with 2 places', () => {
            expect(isValidNumber('123.45')).toBe(true);
        });

        it('should return true for valid decimal with 1 place', () => {
            expect(isValidNumber('123.5')).toBe(true);
        });

        it('should return false for decimal with more than 2 places by default', () => {
            expect(isValidNumber('123.456')).toBe(false);
        });

        it('should return true for decimal with 3 places when maxDecimals=3', () => {
            expect(isValidNumber('123.456', false, 3)).toBe(true);
        });

        it('should return false for negative number by default', () => {
            expect(isValidNumber('-123')).toBe(false);
        });

        it('should return true for negative number when allowNegative=true', () => {
            expect(isValidNumber('-123', true)).toBe(true);
        });

        it('should return true for empty string (treated as 0)', () => {
            expect(isValidNumber('')).toBe(true);
        });

        it('should return true for string with only spaces (treated as 0)', () => {
            expect(isValidNumber('   ')).toBe(true);
        });

        it('should return false for non-numeric string', () => {
            expect(isValidNumber('abc')).toBe(false);
        });

        it('should return false for string with both numbers and letters', () => {
            expect(isValidNumber('123abc')).toBe(false);
        });

        it('should return true for number with commas', () => {
            expect(isValidNumber('1,234.56')).toBe(true);
        });
    });

    describe('validateCsvData', () => {
        // Helper function to create a valid row
        const createValidRow = (): RawCsvRow => ({
            [NormalizedCsvHeader.EXTERNALEMPLOYEEID]: '123',
            [NormalizedCsvHeader.DATE]: '2023-01-15',
            [NormalizedCsvHeader.STHOURS]: '8',
            [NormalizedCsvHeader.OTHOURS]: '2',
            [NormalizedCsvHeader.DTHOURS]: '0',
            [NormalizedCsvHeader.HOURLYRATE]: '25.50',
            [NormalizedCsvHeader.DIRECTPAY]: '100',
            [NormalizedCsvHeader.EXPENSES]: '50',
            [NormalizedCsvHeader.AGREEMENT]: 'Test Agreement', // Required when trigger fields have data
            [NormalizedCsvHeader.CLASSIFICATION]: 'Test Classification' // Required when trigger fields have data
        });

        // Helper function to create a row without trigger fields (no agreement/classification required)
        const createRowWithoutTriggerFields = (): RawCsvRow => ({
            [NormalizedCsvHeader.EXTERNALEMPLOYEEID]: '123',
            [NormalizedCsvHeader.DATE]: '2023-01-15',
            [NormalizedCsvHeader.HOURLYRATE]: '25.50'
            // No hours, job code, cost center, bonus, or expenses - so no agreement/classification required
        });

        it('should return valid rows when all data is valid', () => {
            const rows: RawCsvRow[] = [createValidRow(), createValidRow()];
            const result = validateCsvData(rows);

            expect(result.validRows.length).toBe(2);
            expect(result.rowErrors.length).toBe(0);
            expect(result.headerErrors.length).toBe(0);
        });

        it('should detect missing required DATE field', () => {
            // Create a row with empty DATE field instead of deleting it
            const invalidRow = {
                ...createValidRow(),
                [NormalizedCsvHeader.DATE]: ''
            };

            const rows: RawCsvRow[] = [invalidRow];
            const result = validateCsvData(rows);

            expect(result.validRows.length).toBe(0);
            expect(result.rowErrors.length).toBe(1); // Only the date error since agreement/classification are present

            // Should have the date error
            const dateError = result.rowErrors.find((error) => error.columnName === NormalizedCsvHeader.DATE);
            expect(dateError?.code).toBe('MISSING_REQUIRED');
        });

        it('should detect when both EXTERNALEMPLOYEEID and SSN are missing', () => {
            const invalidRow = createValidRow();
            delete invalidRow[NormalizedCsvHeader.EXTERNALEMPLOYEEID];
            // SSN is not set in the test row

            const rows: RawCsvRow[] = [invalidRow];
            const result = validateCsvData(rows);

            expect(result.validRows.length).toBe(0);
            expect(result.rowErrors.length).toBe(1); // Only the employee ID/SSN error since agreement/classification are present

            // Should have the employee ID/SSN error
            const employeeError = result.rowErrors.find((error) =>
                error.message?.includes('At least one of Employee ID or SSN is required')
            );
            expect(employeeError?.code).toBe('MISSING_REQUIRED');
        });

        it('should validate SSN format when provided', () => {
            const invalidRow = {
                ...createValidRow(),
                [NormalizedCsvHeader.SSN]: '123-45-678' // Missing one digit
            };

            const rows: RawCsvRow[] = [invalidRow];
            const result = validateCsvData(rows);

            expect(result.validRows.length).toBe(0);
            expect(result.rowErrors.length).toBe(1); // Only the SSN format error since agreement/classification are present

            // Should have the SSN format error
            const ssnError = result.rowErrors.find((error) => error.columnName === NormalizedCsvHeader.SSN);
            expect(ssnError?.code).toBe('INVALID_FORMAT');
        });

        it('should validate numeric fields', () => {
            const invalidRow = {
                ...createValidRow(),
                [NormalizedCsvHeader.HOURLYRATE]: '25.505', // Too many decimal places
                [NormalizedCsvHeader.STHOURS]: '-1' // Negative hours not allowed
            };

            const rows: RawCsvRow[] = [invalidRow];
            const result = validateCsvData(rows);

            expect(result.validRows.length).toBe(0);
            expect(result.rowErrors.length).toBe(2); // 2 numeric errors since agreement/classification are present

            // Find the hourly rate error
            const hourlyRateError = result.rowErrors.find((error) => error.columnName === NormalizedCsvHeader.HOURLYRATE);
            expect(hourlyRateError).toBeDefined();
            expect(hourlyRateError?.code).toBe('INVALID_VALUE');

            // Find the ST hours error
            const stHoursError = result.rowErrors.find((error) => error.columnName === NormalizedCsvHeader.STHOURS);
            expect(stHoursError).toBeDefined();
            expect(stHoursError?.code).toBe('INVALID_VALUE');
        });

        it('should validate date format', () => {
            const invalidRow = {
                ...createValidRow(),
                [NormalizedCsvHeader.DATE]: '15/01/2023' // DD/MM/YYYY not supported
            };

            const rows: RawCsvRow[] = [invalidRow];
            const result = validateCsvData(rows);

            expect(result.validRows.length).toBe(0);
            expect(result.rowErrors.length).toBe(1); // Only the date format error since agreement/classification are present

            // Should have the date format error
            const dateError = result.rowErrors.find((error) => error.columnName === NormalizedCsvHeader.DATE);
            expect(dateError?.code).toBe('INVALID_FORMAT');
        });

        it('should handle multiple errors in the same row', () => {
            const invalidRow = {
                ...createValidRow(),
                [NormalizedCsvHeader.DATE]: 'invalid-date',
                [NormalizedCsvHeader.HOURLYRATE]: 'not-a-number',
                [NormalizedCsvHeader.STHOURS]: '8.555' // Too many decimal places
            };

            const rows: RawCsvRow[] = [invalidRow];
            const result = validateCsvData(rows);

            expect(result.validRows.length).toBe(0);
            expect(result.rowErrors.length).toBe(3); // 3 original errors since agreement/classification are present
        });

        it('should handle multiple rows with some valid and some invalid', () => {
            const validRow = createValidRow();
            const invalidRow1 = {
                ...createValidRow(),
                [NormalizedCsvHeader.DATE]: 'invalid-date'
            };
            const invalidRow2 = {
                ...createValidRow(),
                [NormalizedCsvHeader.HOURLYRATE]: 'not-a-number'
            };

            const rows: RawCsvRow[] = [validRow, invalidRow1, invalidRow2];
            const result = validateCsvData(rows);

            expect(result.validRows.length).toBe(1); // validRow should be valid since it has agreement and classification
            expect(result.rowErrors.length).toBe(2); // One error from each invalid row (date error and hourly rate error)
        });

        it('should not require agreement/classification when no trigger fields have data', () => {
            const rowWithoutTriggerFields = createRowWithoutTriggerFields();
            const result = validateCsvData([rowWithoutTriggerFields]);

            expect(result.validRows.length).toBe(1);
            expect(result.rowErrors.length).toBe(0);
        });

        it('should require agreement and classification when trigger fields have data', () => {
            const rowWithTriggerFields = {
                [NormalizedCsvHeader.EXTERNALEMPLOYEEID]: '123',
                [NormalizedCsvHeader.DATE]: '2023-01-15',
                [NormalizedCsvHeader.STHOURS]: '8' // Trigger field present
                // Missing agreement and classification
            };

            const result = validateCsvData([rowWithTriggerFields]);

            expect(result.validRows.length).toBe(0);
            expect(result.rowErrors.length).toBe(2); // Agreement and classification required

            const agreementError = result.rowErrors.find((error) => error.columnName === NormalizedCsvHeader.AGREEMENT);
            expect(agreementError?.code).toBe('MISSING_REQUIRED');
            expect(agreementError?.message).toContain('Agreement is required');

            const classificationError = result.rowErrors.find((error) => error.columnName === NormalizedCsvHeader.CLASSIFICATION);
            expect(classificationError?.code).toBe('MISSING_REQUIRED');
            expect(classificationError?.message).toContain('Classification is required');
        });
    });
});
