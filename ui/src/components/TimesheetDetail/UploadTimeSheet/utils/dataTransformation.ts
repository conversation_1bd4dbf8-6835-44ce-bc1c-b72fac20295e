/**
 * Data Transformation Utilities
 *
 * This file contains functions for transforming validated CSV data into PayStub objects
 * and matching employees and agreements using Relay fragment data.
 */

/**
 * Helper function to safely convert string IDs to numbers for backend mutations
 * GraphQL queries return IDs as strings, but backend mutations expect numbers
 */
function convertIdToNumber(id: string | number | null | undefined): number | undefined {
    if (id === null || id === undefined) return undefined;
    if (typeof id === 'number') return id;

    const parsed = parseInt(String(id), 10);
    return isNaN(parsed) ? undefined : parsed;
}

/**
 * Safely converts a value to string, handling null/undefined cases
 * @param value The value to convert
 * @returns String representation or undefined for null/undefined values
 */
function safeConvertToString(value: unknown): string | undefined {
    if (value === null || value === undefined) return undefined;
    if (typeof value === 'string') return value;
    if (typeof value === 'number' || typeof value === 'boolean') return String(value);
    
    // For objects, arrays, etc., convert to string but handle carefully
    try {
        return String(value);
    } catch (error) {
        console.warn('Failed to convert value to string:', value, error);
        return undefined;
    }
}

/**
 * Safely converts a value to number, handling null/undefined and invalid cases
 * @param value The value to convert
 * @returns Number representation or null for null/undefined/invalid values
 */
function safeConvertToNumber(value: unknown): number | null {
    if (value === null || value === undefined) return null;
    if (typeof value === 'number') return isNaN(value) ? null : value;
    if (typeof value === 'string') {
        if (value.trim() === '') return null;
        const parsed = parseFloat(value.replace(/[,\s]/g, '')); // Remove commas and spaces
        return isNaN(parsed) ? null : parsed;
    }
    if (typeof value === 'boolean') return value ? 1 : 0;
    
    // For other types, try to convert but return null if invalid
    try {
        const parsed = parseFloat(String(value));
        return isNaN(parsed) ? null : parsed;
    } catch (error) {
        console.warn('Failed to convert value to number:', value, error);
        return null;
    }
}

// ✅ PHASE 2.3: Removed UUID dependency - using client temp ID pattern instead
import { Constants } from '@/src/constants/global';

/**
 * Generate a client temporary ID for PayStub or PayStubDetail during CSV upload
 * Follows the consistent pattern used throughout the application
 */
function generateClientTempId(entityType: 'paystub' | 'detail'): string {
    return `client:temp:${entityType}:${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}
import { RawCsvRow, ProcessedTimesheetEntry, PayStubUpload, PayStubDetailUpload, RowError, NormalizedCsvHeader } from '../types';
import { safeParseDate } from './dataValidation';
import type { UploadTimeSheetFragments_employees$data } from '@/lib/relay/__generated__/UploadTimeSheetFragments_employees.graphql';
import type { UploadTimeSheetFragments_agreements$data } from '@/lib/relay/__generated__/UploadTimeSheetFragments_agreements.graphql';
import type { EmployeeUI } from '@/src/types/relay-ui-extensions';

// Use proper Relay types instead of context types
type EmployeeData = UploadTimeSheetFragments_employees$data[0];
type AgreementData = UploadTimeSheetFragments_agreements$data[0];

// New employee data type from context (for the new architecture)
type ContextEmployeeData = EmployeeUI;

/**
 * Relay-compatible PayStub input format for bulk uploads
 * This matches the structure expected by the bulk upload mutation
 */
export interface RelayPayStubInput {
    employeeId: string; // Global ID string
    name?: string;
    details: Array<{
        workDate: string;
        stHours?: number;
        otHours?: number;
        dtHours?: number;
        agreementId?: number;
        classificationId?: number;
        subClassificationId?: number;
        earningsCode?: string;
    }>;
}

/**
 * Normalizes an SSN by removing hyphens and spaces
 * @param ssn The SSN to normalize
 * @returns Normalized SSN
 */
const normalizeSSN = (ssn: string | undefined | null): string | null => {
    if (!ssn) return null;
    return ssn.replace(/[-\s]/g, '');
};

/**
 * Matches an employee from Relay employee data by SSN or Employee ID
 * @param entry The processed timesheet entry
 * @param employees Employees from Relay fragments
 * @returns Matched employee or null if no match
 */
const matchEmployee = (
    entry: ProcessedTimesheetEntry | RawCsvRow,
    employees: UploadTimeSheetFragments_employees$data
): EmployeeData | null => {
    if (!employees || employees.length === 0) {
        return null;
    }

    // Get the SSN and externalEmployeeId from the entry
    const entrySSN = typeof entry === 'object' && 'ssn' in entry ? entry.ssn : (entry as RawCsvRow)[NormalizedCsvHeader.SSN];

    const entryEmployeeId =
        typeof entry === 'object' && 'externalEmployeeId' in entry
            ? entry.externalEmployeeId
            : (entry as RawCsvRow)[NormalizedCsvHeader.EXTERNALEMPLOYEEID];

    // Normalize the SSN for comparison
    const normalizedEntrySSN = normalizeSSN(entrySSN as string);

    // Try to match by SSN first (if provided)
    if (normalizedEntrySSN) {
        const matchedBySSN = employees.find((employee) => {
            // Handle SSN as array (from GraphQL) - use first element or convert to string
            const employeeSSN = Array.isArray(employee.ssn) ? employee.ssn[0] : employee.ssn;
            return normalizeSSN(employeeSSN as string) === normalizedEntrySSN;
        });
        if (matchedBySSN) return matchedBySSN;
    }

    // Then try to match by Employee ID (if provided)
    if (entryEmployeeId) {
        const entryIdString = safeConvertToString(entryEmployeeId);
        if (entryIdString) {
            const matchedById = employees.find((employee) => {
                const employeeIdString = safeConvertToString(employee.externalEmployeeId);
                return employeeIdString && employeeIdString.trim() === entryIdString.trim();
            });
            if (matchedById) return matchedById;
        }
    }

    // No match found
    return null;
};

/**
 * NEW: Matches an employee from context employee data by SSN or Employee ID
 * This function works with the new architecture using EmployeeUI from context
 * @param entry The processed timesheet entry
 * @param employees Employees from context (EmployeeUI[])
 * @returns Matched employee or null if no match
 */
const matchEmployeeFromContext = (
    entry: ProcessedTimesheetEntry | RawCsvRow,
    employees: ContextEmployeeData[]
): ContextEmployeeData | null => {
    if (!employees || employees.length === 0) {
        return null;
    }

    // Get the SSN and externalEmployeeId from the entry
    const entrySSN = typeof entry === 'object' && 'ssn' in entry ? entry.ssn : (entry as RawCsvRow)[NormalizedCsvHeader.SSN];

    const entryEmployeeId =
        typeof entry === 'object' && 'externalEmployeeId' in entry
            ? entry.externalEmployeeId
            : (entry as RawCsvRow)[NormalizedCsvHeader.EXTERNALEMPLOYEEID];

    // Normalize the SSN for comparison
    const normalizedEntrySSN = normalizeSSN(entrySSN as string);

    // Try to match by SSN first (if provided)
    if (normalizedEntrySSN) {
        const matchedBySSN = employees.find((employee) => {
            // Handle SSN as string or array - support both formats
            let employeeSSN: string | undefined;
            if (employee.ssn) {
                employeeSSN = Array.isArray(employee.ssn) ? employee.ssn[0] : employee.ssn;
            } else if (employee.SSN) {
                employeeSSN = employee.SSN;
            }

            return employeeSSN && normalizeSSN(employeeSSN) === normalizedEntrySSN;
        });
        if (matchedBySSN) return matchedBySSN;
    }

    // Then try to match by Employee ID (if provided)
    if (entryEmployeeId) {
        const entryIdString = safeConvertToString(entryEmployeeId);
        if (entryIdString) {
            const matchedById = employees.find((employee) => {
                const employeeIdString = safeConvertToString(employee.externalEmployeeId);
                return employeeIdString && employeeIdString.trim() === entryIdString.trim();
            });
            if (matchedById) return matchedById;
        }
    }

    // No match found
    return null;
};

/**
 * Matches an agreement from Relay agreement data by name
 * @param agreementName The agreement name to match
 * @param agreements Agreements from Relay fragments
 * @returns Matched agreement or null if no match
 */
const matchAgreement = (
    agreementName: string | undefined | null,
    agreements: UploadTimeSheetFragments_agreements$data
): AgreementData | null => {
    if (!agreementName || !agreements || agreements.length === 0) {
        return null;
    }

    // Case-insensitive match
    const normalizedName = agreementName.trim().toLowerCase();

    const matchedAgreement = agreements.find((agreement) => agreement.name.toLowerCase() === normalizedName);

    return matchedAgreement || null;
};

/**
 * Matches an earnings code from the TimesheetEarningCodesList by name
 * @param earningsCodeName The earnings code name to match
 * @returns Matched earnings code value or null if no match
 */
const matchEarningsCode = (earningsCodeName: string | undefined | null): string | null => {
    if (!earningsCodeName || earningsCodeName.trim() === '') {
        return null;
    }

    // Case-insensitive match
    const normalizedName = earningsCodeName.trim().toLowerCase();

    const matchedEarningsCode = Constants.TimesheetEarningCodesList.find(
        (earningsCode) => earningsCode.text.toLowerCase() === normalizedName
    );

    return matchedEarningsCode ? matchedEarningsCode.value : null;
};

/**
 * Creates a PayStubDetailUpload object from a processed timesheet entry
 * @param entry The processed timesheet entry
 * @param payStubId The ID of the parent PayStubUpload
 * @returns PayStubDetailUpload object
 */
const createPayStubDetail = (entry: ProcessedTimesheetEntry, payStubId: string): PayStubDetailUpload => {
    // Calculate total hours
    const stHours = entry.stHours || 0;
    const otHours = entry.otHours || 0;
    const dtHours = entry.dtHours || 0;
    const totalHours = stHours + otHours + dtHours;

    return {
        id: generateClientTempId('detail'), // Generate a unique client temp ID
        payStubId,
        workDate: entry.workDate,
        stHours: entry.stHours || null,
        otHours: entry.otHours || null,
        dtHours: entry.dtHours || null,
        totalHours: totalHours > 0 ? totalHours : null,
        jobCode: entry.jobCode || null,
        earningsCode: entry.earningsCode || null,
        agreementId: convertIdToNumber(entry.agreementId),
        classificationId: convertIdToNumber(entry.classificationId),
        subClassificationId: convertIdToNumber(entry.subClassificationId),
        costCenter: entry.costCenter || null,
        hourlyRate: entry.hourlyRate || null,
        bonus: entry.bonus || null,
        expenses: entry.expenses || null,
        rowIndex: entry.rowIndex,
        inError: false,
        errors: []
    };
};

/**
 * Transforms validated CSV data into PayStubUpload objects and performs initial matching
 * @param validatedData Array of processed timesheet entries
 * @param employees Employees from Relay fragments
 * @param agreements Agreements from Relay fragments
 * @returns Object containing payStubs and rowErrors
 */
export const transformValidatedData = (
    validatedData: ProcessedTimesheetEntry[],
    employees: UploadTimeSheetFragments_employees$data,
    agreements: UploadTimeSheetFragments_agreements$data
): {
    payStubs: PayStubUpload[];
    rowErrors: RowError[];
} => {
    const rowErrors: RowError[] = [];
    const employeeMap = new Map<string | number | null, ProcessedTimesheetEntry[]>();

    // Group entries by employee ID
    validatedData.forEach((entry) => {
        // Match employee
        const matchedEmployee = matchEmployee(entry, employees);

        if (matchedEmployee) {
            // Update entry with matched employee data
            // Use the ID from Relay data - convert to number if possible, otherwise keep as string
            const numericId = Number(matchedEmployee.id);
            entry.employeeId = isNaN(numericId) ? matchedEmployee.id : numericId;

            entry.employeeName = `${matchedEmployee.firstName} ${matchedEmployee.lastName}`;

            // Match agreement if provided
            if (entry.agreementName) {
                const matchedAgreement = matchAgreement(entry.agreementName, agreements);

                if (matchedAgreement) {
                    // Update entry with matched agreement data
                    entry.agreementId = matchedAgreement.id; // ID is already a number from the fragment
                } else if (entry.agreementName.trim() !== '') {
                    // Add error for unmatched agreement
                    rowErrors.push({
                        type: 'row',
                        rowIndex: entry.rowIndex,
                        columnName: 'agreement',
                        message: `Agreement "${entry.agreementName}" not found`,
                        code: 'AGREEMENT_NOT_FOUND'
                    });
                    entry.isValid = false;
                }
            }

            // Match earnings code if provided
            if (entry.earningsCode) {
                // If it's already a valid value from the list, keep it
                const isValidValue = Constants.TimesheetEarningCodesList.some((code) => code.value === entry.earningsCode);

                if (!isValidValue) {
                    // Try to match by name
                    const matchedEarningsCode = matchEarningsCode(entry.earningsCode);

                    if (matchedEarningsCode) {
                        // Update entry with matched earnings code
                        entry.earningsCode = matchedEarningsCode;
                    } else {
                        // Add error for unmatched earnings code
                        rowErrors.push({
                            type: 'row',
                            rowIndex: entry.rowIndex,
                            columnName: 'earningsCode',
                            message: `Earnings code "${entry.earningsCode}" not found`,
                            code: 'INVALID_VALUE'
                        });
                        // Don't mark the entire entry as invalid, just clear the earnings code
                        entry.earningsCode = undefined;
                    }
                }
            }

            // Group by employee ID
            // Ensure employeeId is not undefined
            const employeeId = entry.employeeId || null;
            const employeeEntries = employeeMap.get(employeeId) || [];
            employeeEntries.push(entry);
            employeeMap.set(employeeId, employeeEntries);
        } else {
            // Add error for unmatched employee
            rowErrors.push({
                type: 'row',
                rowIndex: entry.rowIndex,
                message: `Employee not found with ${entry.ssn ? 'SSN' : 'Employee ID'} "${entry.ssn || entry.externalEmployeeId}"`,
                code: 'EMPLOYEE_NOT_FOUND'
            });
            entry.isValid = false;
        }
    });

    // Create PayStubUpload objects from grouped entries
    const payStubs: PayStubUpload[] = [];

    employeeMap.forEach((entries, employeeId) => {
        // Filter out invalid entries
        const validEntries = entries.filter((entry) => entry.isValid);

        if (validEntries.length > 0) {
            const firstEntry = validEntries[0];
            const payStubId = generateClientTempId('paystub');

            // Create details for each entry
            const details = validEntries.map((entry) => createPayStubDetail(entry, payStubId));

            // Calculate aggregated values
            const stHours = details.reduce((sum, detail) => sum + (detail.stHours || 0), 0);
            const otHours = details.reduce((sum, detail) => sum + (detail.otHours || 0), 0);
            const dtHours = details.reduce((sum, detail) => sum + (detail.dtHours || 0), 0);
            const totalHours = stHours + otHours + dtHours;
            const bonus = details.reduce((sum, detail) => sum + (detail.bonus || 0), 0);
            const expenses = details.reduce((sum, detail) => sum + (detail.expenses || 0), 0);

            // Create PayStubUpload
            const payStub: PayStubUpload = {
                id: payStubId,
                employeeId,
                name: firstEntry.employeeName || `Employee ${employeeId}`,
                stHours: stHours > 0 ? stHours : null,
                otHours: otHours > 0 ? otHours : null,
                dtHours: dtHours > 0 ? dtHours : null,
                totalHours: totalHours > 0 ? totalHours : null,
                bonus: bonus > 0 ? bonus : null,
                expenses: expenses > 0 ? expenses : null,
                details,
                expanded: false,
                delete: false,
                inError: false,
                errors: []
            };

            payStubs.push(payStub);
        }
    });

    return { payStubs, rowErrors };
};

/**
 * Simplified version of transformValidatedData for enriched data (NEW ARCHITECTURE)
 *
 * This function works with data that has already been enriched with employee and agreement IDs
 * during the enrichment phase, eliminating the need for fragment-based matching.
 *
 * @param validatedData Array of processed timesheet entries (already enriched with IDs)
 * @returns Object containing payStubs and rowErrors
 */
export const transformEnrichedData = (
    validatedData: ProcessedTimesheetEntry[]
): {
    payStubs: PayStubUpload[];
    rowErrors: RowError[];
} => {
    const rowErrors: RowError[] = [];
    const employeeMap = new Map<string | number | null, ProcessedTimesheetEntry[]>();

    // Group entries by employee ID (should already be populated from enrichment)
    validatedData.forEach((entry) => {
        // Validate that required data has been enriched
        if (!entry.employeeId) {
            rowErrors.push({
                type: 'row',
                rowIndex: entry.rowIndex,
                message: 'Employee ID not found after enrichment',
                code: 'MISSING_EMPLOYEE_ID'
            });
            return;
        }

        // Group by employee ID
        const employeeId = entry.employeeId;
        const employeeEntries = employeeMap.get(employeeId) || [];
        employeeEntries.push(entry);
        employeeMap.set(employeeId, employeeEntries);
    });

    // Create PayStubUpload objects from grouped entries
    const payStubs: PayStubUpload[] = [];

    employeeMap.forEach((entries, employeeId) => {
        // Filter out invalid entries
        const validEntries = entries.filter((entry) => entry.isValid);

        if (validEntries.length > 0) {
            const firstEntry = validEntries[0];
            const payStubId = generateClientTempId('paystub');

            // Create details for each entry
            const details = validEntries.map((entry) => createPayStubDetail(entry, payStubId));

            // Calculate aggregated values
            const stHours = details.reduce((sum, detail) => sum + (detail.stHours || 0), 0);
            const otHours = details.reduce((sum, detail) => sum + (detail.otHours || 0), 0);
            const dtHours = details.reduce((sum, detail) => sum + (detail.dtHours || 0), 0);
            const totalHours = stHours + otHours + dtHours;
            const bonus = details.reduce((sum, detail) => sum + (detail.bonus || 0), 0);
            const expenses = details.reduce((sum, detail) => sum + (detail.expenses || 0), 0);

            // Create PayStubUpload
            const payStub: PayStubUpload = {
                id: payStubId,
                employeeId,
                name: firstEntry.employeeName || `Employee ${employeeId}`,
                stHours: stHours > 0 ? stHours : null,
                otHours: otHours > 0 ? otHours : null,
                dtHours: dtHours > 0 ? dtHours : null,
                totalHours: totalHours > 0 ? totalHours : null,
                bonus: bonus > 0 ? bonus : null,
                expenses: expenses > 0 ? expenses : null,
                details,
                expanded: false,
                delete: false,
                inError: false,
                errors: []
            };

            payStubs.push(payStub);
        }
    });

    return { payStubs, rowErrors };
};

/**
 * Converts processed timesheet entries to Relay mutation input format
 * @param processedEntries Array of processed timesheet entries
 * @param employees Employee data from Relay fragments
 * @param agreements Agreement data from Relay fragments
 * @returns Array of RelayPayStubInput objects ready for bulk upload
 */
export const convertToRelayPayStubInput = (
    processedEntries: ProcessedTimesheetEntry[],
    employees: UploadTimeSheetFragments_employees$data,
    agreements: UploadTimeSheetFragments_agreements$data
): RelayPayStubInput[] => {
    const payStubMap = new Map<string, RelayPayStubInput>();

    processedEntries.forEach((entry) => {
        const employee = matchEmployee(entry, employees);
        if (!employee) {
            throw new Error(`Employee not found for entry: ${JSON.stringify(entry)}`);
        }

        // Use Global ID directly - employee.id is already the Global ID string from Relay
        const employeeId = employee.id;

        if (!payStubMap.has(employeeId)) {
            payStubMap.set(employeeId, {
                employeeId,
                name: `${employee.firstName} ${employee.lastName}`,
                details: []
            });
        }

        const payStub = payStubMap.get(employeeId)!;
        const agreement = matchAgreement(entry.agreementName, agreements);

        payStub.details.push({
            workDate: entry.workDate,
            stHours: safeConvertToNumber(entry.stHours) || 0,
            otHours: safeConvertToNumber(entry.otHours) || 0,
            dtHours: safeConvertToNumber(entry.dtHours) || 0,
            agreementId: agreement ? convertIdToNumber(agreement.id) : undefined,
            classificationId: convertIdToNumber(entry.classificationId),
            subClassificationId: convertIdToNumber(entry.subClassificationId),
            earningsCode: safeConvertToString(entry.earningsCode)
        });
    });

    return Array.from(payStubMap.values());
};

/**
 * Simplified version of convertToRelayPayStubInput for enriched data (NEW ARCHITECTURE)
 *
 * This function works with data that has already been enriched with employee and agreement IDs
 * during the enrichment phase, eliminating the need for fragment-based matching.
 *
 * @param processedEntries Array of processed timesheet entries (already enriched with IDs)
 * @returns Array of RelayPayStubInput objects ready for bulk upload
 */
export const convertEnrichedToRelayPayStubInput = (processedEntries: ProcessedTimesheetEntry[]): RelayPayStubInput[] => {
    const payStubMap = new Map<string, RelayPayStubInput>();

    processedEntries.forEach((entry) => {
        // Validate that employee ID has been enriched
        if (!entry.employeeId) {
            throw new Error(`Employee ID not found for entry: ${JSON.stringify(entry)}`);
        }

        // Use Global ID directly - employeeId should be a Global ID string
        const employeeId = safeConvertToString(entry.employeeId);
        if (!employeeId) {
            throw new Error(`Invalid employee ID found for entry: ${JSON.stringify(entry)}`);
        }

        if (!payStubMap.has(employeeId)) {
            payStubMap.set(employeeId, {
                employeeId,
                name: entry.employeeName || `Employee ${employeeId}`,
                details: []
            });
        }

        const payStub = payStubMap.get(employeeId)!;

        payStub.details.push({
            workDate: entry.workDate,
            stHours: safeConvertToNumber(entry.stHours) || 0,
            otHours: safeConvertToNumber(entry.otHours) || 0,
            dtHours: safeConvertToNumber(entry.dtHours) || 0,
            agreementId: convertIdToNumber(entry.agreementId),
            classificationId: convertIdToNumber(entry.classificationId),
            subClassificationId: convertIdToNumber(entry.subClassificationId),
            earningsCode: safeConvertToString(entry.earningsCode)
        });
    });

    return Array.from(payStubMap.values());
};

/**
 * NEW: Transforms validated CSV data using context employee data (new architecture)
 * This function works with employee data from context instead of deprecated fragments
 * @param validatedData Array of processed timesheet entries
 * @param employees Employees from context (EmployeeUI[])
 * @param agreements Agreements from context (if available)
 * @returns Object containing payStubs and rowErrors
 */
export const transformValidatedDataWithContext = (
    validatedData: ProcessedTimesheetEntry[],
    employees: ContextEmployeeData[],
    agreements: any[] = [] // TODO: Define proper agreement type from context
): {
    payStubs: PayStubUpload[];
    rowErrors: RowError[];
} => {
    const rowErrors: RowError[] = [];
    const employeeMap = new Map<string | number | null, ProcessedTimesheetEntry[]>();

    // Group entries by employee ID
    validatedData.forEach((entry) => {
        // Match employee using context data
        const matchedEmployee = matchEmployeeFromContext(entry, employees);

        if (matchedEmployee) {
            // Update entry with matched employee data
            // Use the ID from context data
            entry.employeeId = matchedEmployee.id;
            entry.employeeName = `${matchedEmployee.firstName} ${matchedEmployee.lastName}`;

            // TODO: Match agreement if provided and agreements are available from context
            if (entry.agreementName && agreements.length > 0) {
                // Agreement matching logic would go here
                // For now, we'll skip this since agreements aren't implemented in context yet
            }

            // Match earnings code if provided (same logic as before)
            if (entry.earningsCode) {
                const isValidValue = Constants.TimesheetEarningCodesList.some((code) => code.value === entry.earningsCode);

                if (!isValidValue) {
                    const matchedEarningsCode = matchEarningsCode(entry.earningsCode);

                    if (matchedEarningsCode) {
                        entry.earningsCode = matchedEarningsCode;
                    } else {
                        rowErrors.push({
                            type: 'row',
                            rowIndex: entry.rowIndex,
                            columnName: 'earningsCode',
                            message: `Earnings code "${entry.earningsCode}" not found`,
                            code: 'INVALID_VALUE'
                        });
                        entry.earningsCode = undefined;
                    }
                }
            }

            // Group by employee ID
            const employeeId = entry.employeeId || null;
            const employeeEntries = employeeMap.get(employeeId) || [];
            employeeEntries.push(entry);
            employeeMap.set(employeeId, employeeEntries);
        } else {
            // Add error for unmatched employee
            rowErrors.push({
                type: 'row',
                rowIndex: entry.rowIndex,
                message: `Employee not found with ${entry.ssn ? 'SSN' : 'Employee ID'} "${entry.ssn || entry.externalEmployeeId}"`,
                code: 'EMPLOYEE_NOT_FOUND'
            });
            entry.isValid = false;
        }
    });

    // Create PayStubUpload objects from grouped entries (same logic as existing function)
    const payStubs: PayStubUpload[] = [];

    employeeMap.forEach((entries, employeeId) => {
        const validEntries = entries.filter((entry) => entry.isValid);

        if (validEntries.length > 0) {
            const firstEntry = validEntries[0];
            const payStubId = generateClientTempId('paystub');

            const details = validEntries.map((entry) => createPayStubDetail(entry, payStubId));

            const stHours = details.reduce((sum, detail) => sum + (detail.stHours || 0), 0);
            const otHours = details.reduce((sum, detail) => sum + (detail.otHours || 0), 0);
            const dtHours = details.reduce((sum, detail) => sum + (detail.dtHours || 0), 0);
            const totalHours = stHours + otHours + dtHours;
            const bonus = details.reduce((sum, detail) => sum + (detail.bonus || 0), 0);
            const expenses = details.reduce((sum, detail) => sum + (detail.expenses || 0), 0);

            const payStub: PayStubUpload = {
                id: payStubId,
                employeeId,
                name: firstEntry.employeeName || `Employee ${employeeId}`,
                stHours: stHours > 0 ? stHours : null,
                otHours: otHours > 0 ? otHours : null,
                dtHours: dtHours > 0 ? dtHours : null,
                totalHours: totalHours > 0 ? totalHours : null,
                bonus: bonus > 0 ? bonus : null,
                expenses: expenses > 0 ? expenses : null,
                details,
                expanded: false,
                delete: false,
                inError: false,
                errors: []
            };

            payStubs.push(payStub);
        }
    });

    return { payStubs, rowErrors };
};
