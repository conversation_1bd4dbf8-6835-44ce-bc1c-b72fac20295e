/**
 * CSV Processing Utilities
 * 
 * This file contains functions for parsing and processing CSV files
 * using the papaparse library.
 */

import <PERSON> from 'papaparse';
import { 
    RawCsvRow, 
    UploadAction, 
    UploadActionType, 
    ProcessingError, 
    HeaderError,
    NormalizedCsvHeader,
    mandatoryCsvColumns
} from '../types';

// Security limits to prevent memory exhaustion attacks
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB maximum file size
const MAX_ROWS = 10000; // Maximum number of rows allowed

/**
 * Transforms CSV headers to normalized format
 * 
 * @param header Original header from CSV
 * @param columnMappings Mapping of original headers to normalized headers
 * @returns Normalized header or original if no mapping exists
 */
const transformHeader = (header: string, columnMappings: Record<string, string>): string => {
    // Normalize the header by trimming and converting to uppercase
    const normalizedHeader = header.trim().toUpperCase().replace(/\s+/g, '');
    
    // Find the matching original header in the mappings
    const originalHeader = Object.keys(columnMappings).find(
        key => key.trim().toUpperCase().replace(/\s+/g, '') === normalizedHeader
    );
    
    // Return the mapped header or the original if no mapping exists
    return originalHeader ? columnMappings[originalHeader] : header;
};

/**
 * Validates that all mandatory headers are present in the parsed CSV
 * 
 * @param headers Array of headers from the parsed CSV
 * @returns Array of header errors or empty array if all mandatory headers are present
 */
const validateHeaders = (headers: string[]): HeaderError[] => {
    const errors: HeaderError[] = [];
    const normalizedHeaders = headers.map(h => h.toUpperCase());
    
    // Check required headers
    const missingRequired = mandatoryCsvColumns.required.filter(
        required => !normalizedHeaders.includes(required)
    );
    
    if (missingRequired.length > 0) {
        errors.push({
            type: 'header',
            message: `Missing required headers: ${missingRequired.join(', ')}`,
            code: 'MISSING_REQUIRED',
            missingHeaders: missingRequired
        });
    }
    
    // Check "at least one" header groups
    for (const group of mandatoryCsvColumns.atLeastOne) {
        const hasAtLeastOne = group.some(header => normalizedHeaders.includes(header));
        if (!hasAtLeastOne) {
            errors.push({
                type: 'header',
                message: `At least one of these headers is required: ${group.join(', ')}`,
                code: 'MISSING_REQUIRED',
                missingHeaders: group
            });
        }
    }
    
    return errors;
};

/**
 * Parses a CSV file using papaparse
 * 
 * @param file The CSV file to parse
 * @param dispatch React dispatch function for the upload reducer
 * @param columnMappings Mapping of original headers to normalized headers
 * @returns Promise that resolves when parsing is complete
 */
export const parseCsvFile = (
    file: File, 
    dispatch: React.Dispatch<UploadAction>,
    columnMappings: Record<string, string>,
    signal?: AbortSignal
): Promise<{ data: RawCsvRow[], headers: string[], rowCount: number }> => {
    return new Promise((resolve, reject) => {
        // Validate file size before processing
        if (file.size > MAX_FILE_SIZE) {
            const error = {
                type: 'file' as const,
                message: `File size exceeds maximum allowed size of ${MAX_FILE_SIZE / 1024 / 1024}MB. Current size: ${(file.size / 1024 / 1024).toFixed(2)}MB`,
                code: 'FILE_TOO_LARGE' as const
            };
            
            dispatch({ 
                type: UploadActionType.PARSE_ERROR, 
                payload: { errors: [error] } 
            });
            
            reject(new Error(error.message));
            return;
        }

        // Start parsing
        dispatch({ type: UploadActionType.PARSE_START });
        
        // Initialize data array and errors
        const results: RawCsvRow[] = [];
        const errors: ProcessingError[] = [];
        let headers: string[] = [];
        let rowCount = 0;
        let hasExceededRowLimit = false;
        
        // Check for cancellation before starting
        if (signal?.aborted) {
            reject(new Error('Operation was aborted'));
            return;
        }

        // Configure papaparse
        const parser = Papa.parse(file, {
            header: true,
            skipEmptyLines: true,
            transformHeader: (header) => transformHeader(header, columnMappings),
            worker: true, // Use web worker to prevent UI blocking
            step: (results, parser) => {
                // Check for cancellation during parsing
                if (signal?.aborted) {
                    parser.abort();
                    reject(new Error('Operation was aborted'));
                    return;
                }
                
                // Check row count limit to prevent memory exhaustion
                rowCount++;
                if (rowCount > MAX_ROWS && !hasExceededRowLimit) {
                    hasExceededRowLimit = true;
                    parser.abort();
                    
                    const error = {
                        type: 'file' as const,
                        message: `File contains too many rows. Maximum allowed: ${MAX_ROWS.toLocaleString()}. Found: ${rowCount.toLocaleString()}+`,
                        code: 'TOO_MANY_ROWS' as const
                    };
                    
                    dispatch({ 
                        type: UploadActionType.PARSE_ERROR, 
                        payload: { errors: [error] } 
                    });
                    
                    reject(new Error(error.message));
                    return;
                }
                
                // Update progress (approximate since we don't know total rows yet)
                if (rowCount % 100 === 0) {
                    // For parsing, we can only estimate progress. Use a reasonable increment.
                    const estimatedProgress = Math.min(90, 10 + (rowCount / 1000) * 60);
                    dispatch({ 
                        type: UploadActionType.PARSE_PROGRESS, 
                        payload: { progress: estimatedProgress } 
                    });
                }
            },
            complete: (results) => {
                // Get the headers and data
                headers = results.meta.fields || [];
                const data = results.data as RawCsvRow[];
                rowCount = data.length;
                
                // Validate headers
                const headerErrors = validateHeaders(headers);
                
                if (headerErrors.length > 0) {
                    // If we have header errors, dispatch error action
                    dispatch({ 
                        type: UploadActionType.PARSE_ERROR, 
                        payload: { errors: headerErrors } 
                    });
                    reject(new Error('Header validation failed'));
                    return;
                }
                
                // Dispatch success action with parsed data
                dispatch({ 
                    type: UploadActionType.PARSE_SUCCESS, 
                    payload: { 
                        data, 
                        headers, 
                        rowCount 
                    } 
                });
                
                resolve({ data, headers, rowCount });
            },
            error: (error) => {
                // Handle parsing errors
                const fileError = {
                    type: 'file' as const,
                    message: `Error parsing CSV: ${error.message}`,
                    code: 'PARSE_ERROR' as const
                };
                
                dispatch({ 
                    type: UploadActionType.PARSE_ERROR, 
                    payload: { errors: [fileError] } 
                });
                
                reject(error);
            }
        });
    });
};
