/**
 * Data Enrichment Utilities
 *
 * This file contains functions for enriching PayStub objects with data from API calls
 * such as employee settings, classifications, and sub-classifications.
 * It optimizes API calls by batching or running them in parallel.
 *
 * IMPORTANT NOTES:
 * - The backend API (/api/Settings/employeeDefaults) expects employeeId as an integer.
 *   String GUIDs are not supported by this endpoint.
 * - When a non-numeric employeeId is encountered, we skip fetching settings for that employee
 *   and log a warning.
 * - All classification and sub-classification endpoints also expect numeric IDs.
 */

/**
 * Helper function to safely convert string IDs to numbers for Set operations
 * GraphQL queries return IDs as strings, but we may need numbers for compatibility
 */
function convertIdToNumber(id: string | number | null | undefined): number | null {
    if (id === null || id === undefined) return null;
    if (typeof id === 'number') return id;
    
    const parsed = parseInt(String(id), 10);
    return isNaN(parsed) ? null : parsed;
}

import { instance } from '@/src/core/http/axios-config';
import { ProcessedTimesheetEntry, RowError } from '../types';
import { validateRequiredFieldsForProcessedEntries } from './dataValidation';

// Security limits for API calls
const API_TIMEOUT = 30000; // 30 seconds timeout
const MAX_RESPONSE_SIZE = 5 * 1024 * 1024; // 5MB maximum response size

// Helper function for conditional logging based on environment
const logSafely = (level: 'log' | 'warn' | 'error', message: string, sensitiveData?: unknown) => {
    const isDevelopment = process.env.NODE_ENV === 'development';
    
    if (isDevelopment && sensitiveData !== undefined) {
        console[level](message, sensitiveData);
    } else {
        // In production, log message without sensitive data
        console[level](message.replace(/\[REDACTED\]/g, '[REDACTED]'));
    }
};

// Helper function to create safe API calls with timeout and response size limits
const createSafeApiCall = async <T>(apiCall: () => Promise<T>): Promise<T> => {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), API_TIMEOUT);
    
    try {
        const result = await apiCall();
        
        // Check response size if it's a response object
        if (result && typeof result === 'object' && 'data' in result) {
            const responseSize = JSON.stringify(result).length;
            if (responseSize > MAX_RESPONSE_SIZE) {
                throw new Error(`Response size exceeds maximum allowed size of ${MAX_RESPONSE_SIZE / 1024 / 1024}MB`);
            }
        }
        
        return result;
    } catch (error) {
        if (error instanceof Error && error.name === 'AbortError') {
            throw new Error(`API call timed out after ${API_TIMEOUT / 1000} seconds`);
        }
        throw error;
    } finally {
        clearTimeout(timeoutId);
    }
};

// Types for tracking default applications
export interface DefaultApplication {
    employeeId: string | number;
    field: 'Agreement' | 'Classification' | 'Hourly Rate';
    value: number | string;
    rowIndex: number;
}

// Type definitions for API responses
interface EmployeeSetting {
    id: number;
    settingId: number;
    value: string;
    ownerId: string;
    ownerType: string;
}

interface Classification {
    id: number;
    name: string;
    chapterId: number;
    dclassificationCodeId: number;
    dstatusId: number;
    description?: string;
}

interface SubClassification {
    id: number;
    chapterId?: number;
    name: string;
    dstatusId: number;
    description?: string;
}

// Type definitions for enriched data
interface EmployeeSettings {
    employeeId: number; // Changed to number only to match backend expectations
    defaultAgreementId?: number;
    defaultClassificationId?: number;
    defaultHourlyRate?: number;
}

interface ClassificationData {
    agreementId: number;
    classifications: Classification[];
}

interface SubClassificationData {
    agreementId: number;
    classificationId: number;
    subClassifications: SubClassification[];
}

/**
 * Fetches employee settings for multiple employees using the batch endpoint
 * @param employeeIds Array of unique employee IDs
 * @returns Promise resolving to a map of original employee ID to settings
 */
const fetchEmployeeSettings = async (employeeIds: (string | number)[]): Promise<Map<string | number, EmployeeSettings | null>> => {
    const settingsMap = new Map<string | number, EmployeeSettings | null>();

    // Initialize the map with null entries for all employee IDs
    // This ensures we have entries even if some API calls fail
    employeeIds.forEach((employeeId) => {
        settingsMap.set(employeeId, null);
    });

    // If no employee IDs, return empty map
    if (employeeIds.length === 0) {
        return settingsMap;
    }

    // Convert all employeeIds to numbers where possible
    const numericEmployeeIds: number[] = [];
    const idMapping: Map<number, string | number> = new Map(); // Maps numeric IDs back to original IDs

    for (const originalEmployeeId of employeeIds) {
        let numericEmployeeId: number | null = null;

        if (typeof originalEmployeeId === 'number') {
            numericEmployeeId = originalEmployeeId;
        } else if (typeof originalEmployeeId === 'string') {
            // Try to parse as integer
            const parsedId = parseInt(originalEmployeeId, 10);
            if (!isNaN(parsedId)) {
                numericEmployeeId = parsedId;
            } else {
                // If it's not a valid number (e.g., it's a GUID), we can't use it with this API
                logSafely('warn', `Cannot fetch settings for non-numeric employeeId: [REDACTED]`, originalEmployeeId);
                continue; // Skip this employee
            }
        }

        if (numericEmployeeId === null) {
            logSafely('warn', `Skipping employee settings fetch for invalid employeeId: [REDACTED]`, originalEmployeeId);
            continue; // Skip this employee
        }

        numericEmployeeIds.push(numericEmployeeId);
        idMapping.set(numericEmployeeId, originalEmployeeId);
    }

    // If no valid numeric IDs, return the map with null values
    if (numericEmployeeIds.length === 0) {
        return settingsMap;
    }

    try {
        // Use the new batch endpoint to fetch settings for all employees in a single request
        const response = await createSafeApiCall(() => 
            instance.post('/api/Settings/employeeDefaultsBatch', numericEmployeeIds, {
                timeout: API_TIMEOUT
            })
        );
        const batchSettings: Record<string, EmployeeSetting[]> = response.data;

        // Process the batch response
        for (const [numericIdStr, settings] of Object.entries(batchSettings)) {
            const numericId = parseInt(numericIdStr, 10);
            const originalEmployeeId = idMapping.get(numericId);

            if (!originalEmployeeId) {
                console.warn(`Could not find original employee ID for numeric ID: [REDACTED]`);
                continue;
            }

            if (settings && settings.length > 0) {
                const employeeDefaultAgreementIdIndex = settings.findIndex((d) => d.settingId === 3);
                const employeeDefaultClassificationIdIndex = settings.findIndex((d) => d.settingId === 4);
                const employeeDefaultHourlyRateIndex = settings.findIndex((d) => d.settingId === 5);

                const employeeSettings: EmployeeSettings = {
                    employeeId: numericId // Use the numeric ID for the settings
                };

                if (employeeDefaultAgreementIdIndex !== -1 && settings[employeeDefaultAgreementIdIndex].value) {
                    try {
                        employeeSettings.defaultAgreementId = parseInt(settings[employeeDefaultAgreementIdIndex].value, 10);
                    } catch (parseError) {
                        console.error(`Error parsing agreement ID for employee [REDACTED]:`, parseError);
                    }
                }

                if (employeeDefaultClassificationIdIndex !== -1 && settings[employeeDefaultClassificationIdIndex].value) {
                    try {
                        employeeSettings.defaultClassificationId = parseInt(settings[employeeDefaultClassificationIdIndex].value, 10);
                    } catch (parseError) {
                        console.error(`Error parsing classification ID for employee [REDACTED]:`, parseError);
                    }
                }

                if (employeeDefaultHourlyRateIndex !== -1 && settings[employeeDefaultHourlyRateIndex].value) {
                    try {
                        employeeSettings.defaultHourlyRate = parseFloat(settings[employeeDefaultHourlyRateIndex].value);
                    } catch (parseError) {
                        console.error(`Error parsing hourly rate for employee [REDACTED]:`, parseError);
                    }
                }

                // Store the settings with the original employee ID as the key
                // This allows us to look up settings by the original ID later
                settingsMap.set(originalEmployeeId, employeeSettings);
            }
        }
    } catch (error) {
        console.error('Error fetching employee settings in batch:', error);
        // We already initialized the map with null entries, so no need to set again
    }

    return settingsMap;
};

/**
 * Fetches classifications for multiple agreements in parallel
 * @param agreementIds Array of unique agreement IDs
 * @returns Promise resolving to a map of agreement ID to classifications
 */
const fetchClassifications = async (agreementIds: number[]): Promise<Map<number, Classification[]>> => {
    const classificationsMap = new Map<number, Classification[]>();

    // Initialize the map with empty arrays for all agreement IDs
    // This ensures we have entries even if some API calls fail
    agreementIds.forEach((agreementId) => {
        classificationsMap.set(agreementId, []);
    });

    // If no agreement IDs, return empty map
    if (agreementIds.length === 0) {
        return classificationsMap;
    }

    // Create an array of promises for each agreement
    const promises = agreementIds.map(async (agreementId) => {
        try {
            const response = await createSafeApiCall(() => 
                instance.get(`/api/Classifications/agreement/${agreementId}`, {
                    timeout: API_TIMEOUT
                })
            );
            const classifications: Classification[] = response.data;

            if (classifications && Array.isArray(classifications)) {
                classificationsMap.set(agreementId, classifications);
            }
        } catch (error) {
            console.error(`Error fetching classifications for agreement ${agreementId}:`, error);
            // We already initialized the map with empty arrays, so no need to set again
        }
    });

    // Wait for all promises to resolve, but don't fail if some fail
    await Promise.allSettled(promises);

    return classificationsMap;
};

/**
 * Fetches sub-classifications for multiple agreement/classification pairs in parallel
 * @param agreementClassificationPairs Array of agreement/classification pairs
 * @returns Promise resolving to a map of agreement/classification pair to sub-classifications
 */
const fetchSubClassifications = async (
    agreementClassificationPairs: { agreementId: number; classificationId: number }[]
): Promise<Map<string, SubClassification[]>> => {
    const subClassificationsMap = new Map<string, SubClassification[]>();

    // Initialize the map with empty arrays for all pairs
    // This ensures we have entries even if some API calls fail
    agreementClassificationPairs.forEach(({ agreementId, classificationId }) => {
        const key = `${agreementId}-${classificationId}`;
        subClassificationsMap.set(key, []);
    });

    // If no pairs, return empty map
    if (agreementClassificationPairs.length === 0) {
        return subClassificationsMap;
    }

    // Create an array of promises for each agreement/classification pair
    const promises = agreementClassificationPairs.map(async ({ agreementId, classificationId }) => {
        const key = `${agreementId}-${classificationId}`;

        try {
            const response = await createSafeApiCall(() => 
                instance.get(`/api/Classifications/agreement/${agreementId}/subclassifications/${classificationId}`, {
                    timeout: API_TIMEOUT
                })
            );
            const subClassifications: SubClassification[] = response.data;

            if (subClassifications && Array.isArray(subClassifications)) {
                subClassificationsMap.set(key, subClassifications);
            }
        } catch (error) {
            console.error(`Error fetching sub-classifications for agreement ${agreementId}, classification ${classificationId}:`, error);
            // We already initialized the map with empty arrays, so no need to set again
        }
    });

    // Wait for all promises to resolve, but don't fail if some fail
    await Promise.allSettled(promises);

    return subClassificationsMap;
};

/**
 * Validates if a classification is valid for a given agreement
 * @param classificationId The classification ID to validate
 * @param agreementId The agreement ID to validate against
 * @param classificationsMap The fetched classifications map
 * @returns True if the classification is valid for the agreement, false otherwise
 */
const isClassificationValidForAgreement = (
    classificationId: number,
    agreementId: number,
    classificationsMap: Map<number, Classification[]>
): boolean => {
    const availableClassifications = classificationsMap.get(agreementId) || [];
    return availableClassifications.some((classification) => classification.id === classificationId);
};

/**
 * Enriches processed timesheet entries with data from API calls
 * @param entries Array of processed timesheet entries
 * @param updateProgress Optional callback to update progress
 * @returns Promise resolving to an object containing enriched entries, row errors, and applied defaults feedback
 */
export const enrichPayStubData = async (
    entries: ProcessedTimesheetEntry[],
    updateProgress?: (progress: number) => void
): Promise<{
    enrichedEntries: ProcessedTimesheetEntry[];
    rowErrors: RowError[];
    appliedDefaults: DefaultApplication[];
}> => {
    const rowErrors: RowError[] = [];
    const appliedDefaults: DefaultApplication[] = [];

    // Update progress to indicate we're starting
    if (updateProgress) {
        updateProgress(10);
    }

    // 1. Identify unique employee IDs and agreement IDs
    const uniqueEmployeeIds = new Set<string | number>();
    const uniqueAgreementIds = new Set<number>();
    const agreementClassificationPairs: { agreementId: number; classificationId: number }[] = [];

    entries.forEach((entry) => {
        if (entry.employeeId) {
            uniqueEmployeeIds.add(entry.employeeId);
        }

        if (entry.agreementId) {
            const numericId = convertIdToNumber(entry.agreementId);
            if (numericId !== null) {
                uniqueAgreementIds.add(numericId);
            }
        }
    });

    // Update progress
    if (updateProgress) {
        updateProgress(20);
    }

    // 2. Fetch employee settings first to identify default agreement IDs
    const employeeSettingsMap = await fetchEmployeeSettings(Array.from(uniqueEmployeeIds));

    // Add default agreement IDs from employee settings to the agreement IDs to fetch
    employeeSettingsMap.forEach((settings) => {
        if (settings?.defaultAgreementId) {
            uniqueAgreementIds.add(settings.defaultAgreementId);
        }
    });

    // 3. Fetch classifications for all agreement IDs (including defaults)
    const classificationsMap = await fetchClassifications(Array.from(uniqueAgreementIds));

    // Update progress
    if (updateProgress) {
        updateProgress(50);
    }

    // 4. Identify classification pairs for sub-classification fetching
    classificationsMap.forEach((classifications, agreementId) => {
        classifications.forEach((classification) => {
            agreementClassificationPairs.push({
                agreementId,
                classificationId: classification.id
            });
        });
    });

    // 5. Fetch sub-classifications
    const subClassificationsMap = await fetchSubClassifications(agreementClassificationPairs);

    // Update progress
    if (updateProgress) {
        updateProgress(80);
    }

    // 6. Enrich entries with fetched data
    const enrichedEntries = entries.map((entry) => {
        const enrichedEntry = { ...entry };

        // Apply employee settings if available
        if (entry.employeeId && employeeSettingsMap.has(entry.employeeId)) {
            const settings = employeeSettingsMap.get(entry.employeeId);

            if (settings) {
                // Apply default agreement if not already set
                if (!entry.agreementId && settings.defaultAgreementId) {
                    enrichedEntry.agreementId = settings.defaultAgreementId;
                    if (entry.employeeId) {
                        appliedDefaults.push({
                            employeeId: entry.employeeId,
                            field: 'Agreement',
                            value: settings.defaultAgreementId,
                            rowIndex: entry.rowIndex
                        });
                    }
                }

                // Apply default classification if not already set and valid for the agreement
                if (!entry.classificationId && settings.defaultClassificationId) {
                    // Determine the agreement ID to validate against (either from CSV or default)
                    const currentAgreementId = enrichedEntry.agreementId;

                    if (currentAgreementId) {
                        const numericClassificationId = convertIdToNumber(settings.defaultClassificationId);
                        const numericAgreementId = convertIdToNumber(currentAgreementId);
                        
                        const isValid = numericClassificationId && numericAgreementId && 
                            isClassificationValidForAgreement(
                                numericClassificationId,
                                numericAgreementId,
                                classificationsMap
                            );

                        if (isValid) {
                            enrichedEntry.classificationId = settings.defaultClassificationId;
                            if (entry.employeeId) {
                                appliedDefaults.push({
                                    employeeId: entry.employeeId,
                                    field: 'Classification',
                                    value: settings.defaultClassificationId,
                                    rowIndex: entry.rowIndex
                                });
                            }
                        }
                        // If invalid, we don't apply the default and don't add an error
                        // as this is expected behavior per the PRD
                    }
                }

                // Apply default hourly rate if not already set
                if (!entry.hourlyRate && settings.defaultHourlyRate) {
                    enrichedEntry.hourlyRate = settings.defaultHourlyRate;
                    if (entry.employeeId) {
                        appliedDefaults.push({
                            employeeId: entry.employeeId,
                            field: 'Hourly Rate',
                            value: settings.defaultHourlyRate,
                            rowIndex: entry.rowIndex
                        });
                    }
                }
            } else {
                // If we have an employeeId but couldn't fetch settings (e.g., non-numeric ID)
                console.warn(`No settings found for employee [REDACTED] (possibly non-numeric ID)`);
            }
        }

        // Match classification name to ID if we have a name but no ID
        if (entry.agreementId && entry.classificationName && !entry.classificationId) {
            const numericAgreementId = convertIdToNumber(entry.agreementId);
            const classifications = numericAgreementId ? (classificationsMap.get(numericAgreementId) || []) : [];
            const matchedClassification = classifications.find((c) => c.name.toLowerCase() === entry.classificationName?.toLowerCase());

            if (matchedClassification) {
                enrichedEntry.classificationId = matchedClassification.id;
            } else if (entry.classificationName.trim() !== '') {
                // Add error for unmatched classification
                rowErrors.push({
                    type: 'row',
                    rowIndex: entry.rowIndex,
                    columnName: 'classification',
                    message: `Classification "${entry.classificationName}" not found for agreement`,
                    code: 'CLASSIFICATION_NOT_FOUND'
                });
            }
        }

        // Match sub-classification name to ID if we have a name but no ID
        if (entry.agreementId && entry.classificationId && entry.subClassificationName && !entry.subClassificationId) {
            const key = `${entry.agreementId}-${entry.classificationId}`;
            const subClassifications = subClassificationsMap.get(key) || [];
            const matchedSubClassification = subClassifications.find(
                (sc) => sc.name.toLowerCase() === entry.subClassificationName?.toLowerCase()
            );

            if (matchedSubClassification) {
                enrichedEntry.subClassificationId = matchedSubClassification.id;
            } else if (entry.subClassificationName.trim() !== '') {
                // Add error for unmatched sub-classification
                rowErrors.push({
                    type: 'row',
                    rowIndex: entry.rowIndex,
                    columnName: 'subClassification',
                    message: `Sub-classification "${entry.subClassificationName}" not found for classification`,
                    code: 'SUBCLASSIFICATION_NOT_FOUND'
                });
            }
        }

        return enrichedEntry;
    });

    // 7. Validate required fields according to PRD 3.4.3
    // This validation runs after enrichment so defaults can satisfy the requirements
    const requiredFieldErrors = validateRequiredFieldsForProcessedEntries(enrichedEntries);
    rowErrors.push(...requiredFieldErrors);

    // Update progress to indicate we're done
    if (updateProgress) {
        updateProgress(100);
    }

    return { enrichedEntries, rowErrors, appliedDefaults };
};
