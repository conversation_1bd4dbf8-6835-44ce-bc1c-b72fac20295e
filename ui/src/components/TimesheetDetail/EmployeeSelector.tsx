import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { ComboBox, Item } from '@adobe/react-spectrum';
import { graphql, useFragment } from 'react-relay';
import { useTimesheetUIStore } from '@/src/store/timesheetUIStore';
import type {
    EmployeeSelector_employee$key,
    EmployeeSelector_employee$data
} from '@/lib/relay/__generated__/EmployeeSelector_employee.graphql';

// GraphQL fragment for employee data
// eslint-disable-next-line react-refresh/only-export-components
export const EmployeeSelector_employeeFragment = graphql`
    fragment EmployeeSelector_employee on Employee {
        id
        firstName
        lastName
        middleName
        active
    }
`;

/**
 * Processed employee data for UI consumption
 * Created from Relay fragment data
 */
interface EmployeeUIData {
    id: string;
    value: string;
    text: string;
    firstName: string | null | undefined;
    lastName: string | null | undefined;
    middleName: string | null | undefined;
    active: boolean | null | undefined;
}

interface EmployeeSelectorProps {
    payStubId: string;
    timesheetId: string; // Required for Zustand store scoping
    selectedKey: string | null;
    isDisabled?: boolean;
    'aria-label': string;
    autoFocus?: boolean;
    onBlur?: () => void;
    // Accept processed employee data ready for UI consumption
    // This should come from a parent component that has resolved Relay fragments
    employees?: ReadonlyArray<EmployeeUIData>;
    // Optional callback for temporary payStub employee selection
    onEmployeeSelect?: (id: string) => Promise<void>;
}

const EmployeeSelector: React.FC<EmployeeSelectorProps> = ({
    payStubId,
    timesheetId,
    selectedKey,
    isDisabled,
    'aria-label': ariaLabel,
    autoFocus,
    onBlur,
    employees = [],
    onEmployeeSelect
}) => {
    // Use the employee data passed via props - no more context dependency
    const employeeList: EmployeeUIData[] = useMemo(() => {
        return [...employees];
    }, [employees]);

    const isInvalid = false;

    // Use Zustand store actions with proper scoping
    const selectEmployeeForNewPayStub = useTimesheetUIStore(state => state.selectEmployeeForNewPayStub);
    const clearError = useTimesheetUIStore(state => state.clearError);

    const selectEmployeeForPayStub = async (payStubId: string, id: string | null) => {
        if (!id) return;
        
        // Use the store action with proper scoping
        selectEmployeeForNewPayStub(timesheetId, id);
    };

    const clearTimesheetError = () => {
        // Clear any existing errors for this payStub using store action
        clearError(timesheetId, payStubId);
    };

    const getNameFromKey = useCallback(
        (key: string | null): string => {
            if (!key || !employeeList) {
                return '';
            }
            // Convert emp.value to string for comparison
            const selectedEmployee = employeeList.find((emp) => String(emp.value) === key);
            const name = selectedEmployee ? selectedEmployee.text : '';
            return name;
        },
        [employeeList]
    );
    const [localInputValue, setLocalInputValue] = useState<string>(() => {
        const initialName = getNameFromKey(selectedKey);
        return initialName;
    });
    const [isActivelyFiltering, setIsActivelyFiltering] = useState<boolean>(false);

    // Effect to sync input value if selectedKey prop changes externally
    useEffect(() => {
        // Use functional update to avoid circular dependency
        setLocalInputValue((prevValue) => {
            // Only update if value has changed - we don't check isActivelyFiltering here
            // to avoid circular dependency since this effect should only run when selectedKey changes
            const nameToSet = getNameFromKey(selectedKey);
            return prevValue !== nameToSet ? nameToSet : prevValue;
        });
    }, [selectedKey, employeeList, getNameFromKey]); // Only depend on data that affects the name calculation

    const handleInputChange = (value: string) => {
        setIsActivelyFiltering(true);
        setLocalInputValue(value);
        clearTimesheetError();
    };

    const handleSelectionChange = (key: React.Key | null) => {
        const newEmployeeId = key !== null ? String(key) : null;
        const currentEmployeeId = selectedKey !== null ? String(selectedKey) : null;

        if (process.env.NODE_ENV === 'development') {
            console.log('EmployeeSelector: handleSelectionChange called', {
                newEmployeeId,
                currentEmployeeId,
                payStubId,
                hasOnEmployeeSelect: !!onEmployeeSelect
            });
        }

        // Update local state first to ensure UI reflects selection immediately
        const nameToSet = getNameFromKey(newEmployeeId);
        setLocalInputValue(nameToSet);
        setIsActivelyFiltering(false);

        // Force blur immediately to close dropdown
        const forceBlur = () => {
            onBlur?.();
            // Force blur on the current active element to ensure dropdown closes
            const activeElement = document.activeElement as HTMLElement;
            if (activeElement && (activeElement.tagName === 'INPUT' || activeElement.getAttribute('role') === 'combobox')) {
                activeElement.blur();
            }
        };

        // Only call the update function if the employee actually changed
        if (newEmployeeId !== currentEmployeeId) {
            // Use onEmployeeSelect if provided (for temporary payStubs)
            if (onEmployeeSelect && newEmployeeId) {
                // For temporary payStubs, force immediate blur then handle async operation
                forceBlur();

                // Immediately update filtering state to ensure dropdown closes
                setIsActivelyFiltering(false);

                onEmployeeSelect(newEmployeeId)
                    .then(() => {
                        if (process.env.NODE_ENV === 'development') {
                            console.log('EmployeeSelector: onEmployeeSelect completed successfully');
                        }
                    })
                    .catch((error) => {
                        console.error('EmployeeSelector: onEmployeeSelect failed:', error);
                        // On error, might need to reset state
                        setLocalInputValue(getNameFromKey(currentEmployeeId));
                        setIsActivelyFiltering(false);
                    });
            } else {
                selectEmployeeForPayStub(payStubId, newEmployeeId);
                forceBlur();
            }
        } else {
            // Even if no change, still blur to close dropdown
            forceBlur();
        }
    };

    const filteredItems = useMemo(() => {
        const currentEmployees = employeeList || [];
        if (!isActivelyFiltering) {
            // If not actively filtering, return all employees
            return currentEmployees;
        }
        if (!localInputValue) {
            // If filtering but input is empty, show all
            return currentEmployees;
        }
        const lowerCaseInput = localInputValue.toLowerCase();
        return currentEmployees.filter((employee) => employee.text && employee.text.toLowerCase().includes(lowerCaseInput));
    }, [employeeList, localInputValue, isActivelyFiltering]);

    const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
        setIsActivelyFiltering(false); // Show all items when focused initially
        // Select text using a timeout
        const input = e.target;
        setTimeout(() => {
            input.select();
        }, 0);
    };

    return (
        <ComboBox
            id={`employee-selector-${payStubId}`}
            aria-label={ariaLabel}
            items={filteredItems}
            selectedKey={selectedKey}
            inputValue={localInputValue}
            onInputChange={handleInputChange}
            onSelectionChange={handleSelectionChange}
            onFocus={handleFocus}
            isDisabled={isDisabled}
            validationState={isInvalid ? 'invalid' : undefined}
            autoFocus={autoFocus} // Pass autoFocus
            onBlur={onBlur} // Pass onBlur
            width="100%"
            allowsCustomValue>
            {(item: EmployeeUIData) => <Item key={item.value}>{item.text}</Item>}
        </ComboBox>
    );
};

/**
 * Helper function to convert resolved employee fragment data to UI format
 * This should be called by parent components that have resolved employee fragments
 */
export const convertEmployeeFragmentToUIData = (employeeData: EmployeeSelector_employee$data): EmployeeUIData => {
    const displayName = [employeeData.lastName, employeeData.firstName]
        .filter(Boolean)
        .join(', ') || 'Unknown Employee';
        
    return {
        id: employeeData.id,
        value: employeeData.id,
        text: displayName,
        firstName: employeeData.firstName,
        lastName: employeeData.lastName,
        middleName: employeeData.middleName,
        active: employeeData.active
    };
};

export default EmployeeSelector;
