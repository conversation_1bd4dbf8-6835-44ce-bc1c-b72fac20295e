import { Button, Text } from '@adobe/react-spectrum';
import Refresh from '@spectrum-icons/workflow/Refresh';
import { useMutation, graphql } from 'react-relay';
import { useState } from 'react';

// Mutation to reset timesheet pay stub details
const RESET_TIMESHEET_MUTATION = graphql`
    mutation TimeSheetResetMutation($input: ModifyTimeSheetInput!, $first: Int = 500) {
        modifyTimeSheet(input: $input) {
            timeSheet {
                id
                payStubs(first: $first) @connection(key: "TimeSheetReset_payStubs") {
                    edges {
                        node {
                            id
                            details {
                                id
                                name
                                workDate
                                stHours
                                otHours
                                dtHours
                                totalHours
                                jobCode
                                earningsCode
                                agreementId
                                classificationId
                                subClassificationId
                                costCenter
                                hourlyRate
                                bonus
                                expenses
                            }
                        }
                    }
                }
            }
        }
    }
`;

interface TimeSheetResetProps {
    readOnly: boolean;
    timeSheetId: string;
    payStubs: Array<{
        id: string;
        details: Array<{ id: string }>;
    }>;
}

const TimeSheetReset = ({ readOnly, timeSheetId, payStubs }: TimeSheetResetProps) => {
    const [isResetting, setIsResetting] = useState(false);
    const [commitMutation] = useMutation(RESET_TIMESHEET_MUTATION);

    const resetAllPayStubDetails = () => {
        setIsResetting(true);
        
        // Create mutation input to clear all details from all pay stubs
        const modifiedPayStubs = payStubs.map(payStub => ({
            id: payStub.id,
            details: payStub.details.map(detail => ({
                id: detail.id,
                delete: true
            }))
        }));

        const input = {
            id: timeSheetId,
            payStubs: modifiedPayStubs
        };

        commitMutation({
            variables: { input },
            onCompleted: () => {
                setIsResetting(false);
                console.log('Timesheet reset successfully');
            },
            onError: (error) => {
                setIsResetting(false);
                console.error('Failed to reset timesheet:', error);
            }
        });
    };

    const reset = () => {
        if (window.confirm('Are you sure you want to reset this timesheet? This will clear all hours and data from all pay stubs.')) {
            resetAllPayStubDetails();
        }
    };

    return (
        <>
            <Button 
                variant="secondary" 
                isDisabled={readOnly || isResetting} 
                onPress={reset}
            >
                <Refresh />
                <Text>{isResetting ? 'Resetting...' : 'Reset'}</Text>
            </Button>
        </>
    );
};

export default TimeSheetReset;
