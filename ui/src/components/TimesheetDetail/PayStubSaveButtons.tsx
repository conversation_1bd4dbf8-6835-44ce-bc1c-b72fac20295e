import React from 'react';
import { Button, Flex, ProgressCircle } from '@adobe/react-spectrum';
import { useTimesheetUIStore } from '@/src/store/timesheetUIStore';

interface PayStubSaveButtonsProps {
  payStubId: string;
  timesheetId: string;
  onSave: () => Promise<void>;
  index?: number; // For test IDs
}

export function PayStubSaveButtons({ 
  payStubId,
  timesheetId,
  onSave,
  index
}: PayStubSaveButtonsProps) {
  // Use Zustand store instead of Context
  const stopEditingPayStub = useTimesheetUIStore(state => state.stopEditingPayStub);
  const clearDraftForPayStub = useTimesheetUIStore(state => state.clearDraftForPayStub);
  const isSavingPayStub = useTimesheetUIStore(state => state.isSavingPayStub);
  const clearError = useTimesheetUIStore(state => state.clearError);
  
  const isSaving = isSavingPayStub(timesheetId, payStubId);
  
  const handleCancel = () => {
    clearDraftForPayStub(timesheetId, payStubId);
    clearError(timesheetId, payStubId);
    stopEditingPayStub(timesheetId);
  };
  
  const handleSave = async () => {
    try {
      await onSave();
      // stopEditingPayStub is called by context after successful save
    } catch (error) {
      // Error handling is done by the context
      console.error('Save failed:', error);
    }
  };
  
  return (
    <Flex gap="size-100" justifyContent="end" marginTop="size-200">
      <Button
        variant="secondary"
        onPress={handleCancel}
        isDisabled={isSaving}
      >
        Cancel
      </Button>
      
      <Button
        variant="cta"
        data-testid={index !== undefined ? `save-ps-${index}` : `save-${payStubId}`}
        onPress={handleSave}
        isDisabled={isSaving}
      >
        {isSaving ? (
          <Flex gap="size-100" alignItems="center">
            <ProgressCircle aria-label="Saving" isIndeterminate size="S" />
            Saving...
          </Flex>
        ) : (
          'Save Changes'
        )}
      </Button>
    </Flex>
  );
}