/**
 * Enhanced Error Display Component
 * Provides user-friendly error messages with retry capabilities and contextual actions
 */

import React, { useMemo, useCallback } from 'react';
import { View, Text, Button, Flex, StatusLight, Heading } from '@adobe/react-spectrum';
import AlertIcon from '@spectrum-icons/workflow/Alert';
import { TimesheetError, ErrorHandlingResult } from '@/errorHandling';

interface EnhancedErrorDisplayProps {
    error?: TimesheetError | null;
    errorResult?: ErrorHandlingResult | null;
    entityId?: string;
    onRetry?: () => Promise<void>;
    onDismiss?: () => void;
    isRetrying?: boolean;
    showTechnicalDetails?: boolean;
}

export const EnhancedErrorDisplay: React.FC<EnhancedErrorDisplayProps> = ({
    error,
    errorResult,
    entityId,
    onRetry,
    onDismiss,
    isRetrying = false,
    showTechnicalDetails = false
}) => {
    // Memoize style objects to prevent re-renders - BEFORE any early returns
    const boldTextStyle = useMemo(() => ({ fontWeight: 'bold' }), []);
    const suggestionHeaderStyle = useMemo(() => ({ fontWeight: 'bold', fontSize: '14px' }), []);
    const suggestionTextStyle = useMemo(() => ({ fontSize: '14px' }), []);
    const suggestionListStyle = useMemo(() => ({ margin: '8px 0', paddingLeft: '20px' }), []);
    const technicalHeaderStyle = useMemo(() => ({ fontWeight: 'bold', fontSize: '12px' }), []);
    const technicalTextStyle = useMemo(() => ({ fontFamily: 'monospace', fontSize: '12px' }), []);

    // Early return AFTER all hooks
    if (!error && !errorResult) return null;

    // Use errorResult if available, otherwise extract from TimesheetError
    const message = errorResult?.message || error?.enhancedError?.userMessage || error?.message || 'An error occurred';
    const canRetry = errorResult?.canRetry ?? error?.enhancedError?.canRetry ?? false;
    const severity = errorResult?.severity || 'error';
    const suggestions = error?.enhancedError?.suggestions || [];

    const getSeverityProps = (severity: 'error' | 'warning' | 'info' | string) => {
        switch (severity) {
            case 'warning':
                return { variant: 'notice' as const, icon: AlertIcon };
            case 'error':
                return { variant: 'negative' as const, icon: AlertIcon };
            case 'info':
                return { variant: 'info' as const, icon: AlertIcon };
            default:
                return { variant: 'negative' as const, icon: AlertIcon };
        }
    };

    const severityProps = getSeverityProps(severity);

    return (
        <View
            padding="size-200"
            backgroundColor="gray-100"
            borderRadius="medium"
            borderWidth="thin"
            borderColor={severity === 'error' ? 'negative' : severity === 'warning' ? 'yellow-400' : 'gray-400'}>
            <Flex direction="column" gap="size-150">
                {/* Error Header */}
                <Flex alignItems="center" gap="size-100">
                    <StatusLight variant={severityProps.variant}>
                        {severity === 'error' ? 'Error' : severity === 'warning' ? 'Warning' : 'Info'}
                    </StatusLight>
                    <Text UNSAFE_style={boldTextStyle}>{severity === 'error' ? 'Operation Failed' : 'Issue Detected'}</Text>
                </Flex>

                {/* Error Message */}
                <Text>{message}</Text>

                {/* Suggestions */}
                {suggestions.length > 0 && (
                    <View>
                        <Text UNSAFE_style={suggestionHeaderStyle}>Suggestions:</Text>
                        <ul style={suggestionListStyle}>
                            {suggestions.map((suggestion, index) => (
                                <li key={index}>
                                    <Text UNSAFE_style={suggestionTextStyle}>{suggestion}</Text>
                                </li>
                            ))}
                        </ul>
                    </View>
                )}

                {/* Technical Details (if enabled) */}
                {showTechnicalDetails && error?.enhancedError?.technical && (
                    <View padding="size-100" backgroundColor="gray-200" borderRadius="small">
                        <Text UNSAFE_style={technicalHeaderStyle}>Technical Details:</Text>
                        <Text UNSAFE_style={technicalTextStyle}>{error.enhancedError.technical}</Text>
                    </View>
                )}

                {/* Action Buttons */}
                <Flex gap="size-100" justifyContent="end">
                    {onDismiss && (
                        <Button variant="secondary" onPress={onDismiss}>
                            Dismiss
                        </Button>
                    )}

                    {canRetry && onRetry && (
                        <Button variant="primary" onPress={onRetry} isDisabled={isRetrying}>
                            {isRetrying ? 'Retrying...' : 'Try Again'}
                        </Button>
                    )}
                </Flex>
            </Flex>
        </View>
    );
};

/**
 * Simplified Error Display for inline usage
 */
interface SimpleErrorDisplayProps {
    message: string;
    canRetry?: boolean;
    onRetry?: () => void;
    isRetrying?: boolean;
}

export const SimpleErrorDisplay: React.FC<SimpleErrorDisplayProps> = ({ message, canRetry = false, onRetry, isRetrying = false }) => {
    // Memoize style objects to prevent re-renders
    const messageTextStyle = useMemo(() => ({ fontSize: '14px' }), []);

    return (
        <View padding="size-100" backgroundColor="red-400" borderRadius="small">
            <Flex direction="row" alignItems="center" gap="size-100">
                <StatusLight variant="negative">Error</StatusLight>
                <Text flex={1} UNSAFE_style={messageTextStyle}>
                    {message}
                </Text>

                {canRetry && onRetry && (
                    <Button variant="secondary" onPress={onRetry} isDisabled={isRetrying}>
                        {isRetrying ? 'Retrying...' : 'Retry'}
                    </Button>
                )}
            </Flex>
        </View>
    );
};

/**
 * Error Display for PayStub rows
 */
interface PayStubErrorDisplayProps {
    payStubId: string;
    error?: string;
    enhancedError?: TimesheetError;
    onRetry?: (payStubId: string) => Promise<void>;
    onClear?: (payStubId: string) => void;
}

export const PayStubErrorDisplay: React.FC<PayStubErrorDisplayProps> = ({ payStubId, error, enhancedError, onRetry, onClear }) => {
    // Memoize style objects and callbacks to prevent re-renders - BEFORE any early returns
    const errorTextStyle = useMemo(() => ({ fontSize: '13px' }), []);
    const handleClear = useCallback(() => onClear?.(payStubId), [onClear, payStubId]);
    const handleRetry = useCallback(() => onRetry?.(payStubId), [onRetry, payStubId]);

    // Early return AFTER all hooks
    if (!error && !enhancedError) return null;

    const canRetry = enhancedError?.enhancedError?.canRetry ?? false;
    const displayMessage = error || enhancedError?.enhancedError?.userMessage || 'An error occurred';

    return (
        <View padding="size-75" backgroundColor="red-400" borderRadius="small" marginTop="size-50">
            <Flex direction="row" alignItems="center" gap="size-75">
                <StatusLight variant="negative">Error</StatusLight>
                <Text flex={1} UNSAFE_style={errorTextStyle}>
                    {displayMessage}
                </Text>

                <Flex gap="size-50">
                    {onClear && (
                        <Button variant="secondary" onPress={handleClear}>
                            Dismiss
                        </Button>
                    )}

                    {canRetry && onRetry && (
                        <Button variant="primary" onPress={handleRetry}>
                            Retry
                        </Button>
                    )}
                </Flex>
            </Flex>
        </View>
    );
};
