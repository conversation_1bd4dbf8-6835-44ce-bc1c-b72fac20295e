import React, { createContext, useContext, ReactNode } from 'react';
import type { TimesheetDetailQuery$data } from '@/lib/relay/__generated__/TimesheetDetailQuery.graphql';

/**
 * Type-safe interface for Timesheet Context
 * Replaces the dangerous 'any' types with proper TypeScript interfaces
 */
export interface TimesheetContextValue {
  /** The query data containing employee information for CSV upload */
  queryData: TimesheetDetailQuery$data;
  /** Employer GUID for the current timesheet */
  employerGuid: string;
  /** Timesheet ID for context scoping */
  timesheetId: string;
  /** Numeric ID for API operations */
  numericId: number;
}

/**
 * React Context for sharing timesheet and employee data across components
 * 
 * This context eliminates the prop drilling anti-pattern by providing
 * a centralized way to access query data needed for CSV upload functionality.
 * 
 * Benefits:
 * - Eliminates prop drilling through 5+ component levels
 * - Provides type-safe access to query data
 * - Centralizes error handling for missing data
 * - Follows React best practices for cross-component data sharing
 */
const TimesheetContext = createContext<TimesheetContextValue | null>(null);

/**
 * Provider component for TimesheetContext
 * 
 * Should be placed at the root of the timesheet detail component tree
 * to provide access to query data for all child components.
 */
export interface TimesheetContextProviderProps {
  children: ReactNode;
  value: TimesheetContextValue;
}

export const TimesheetContextProvider: React.FC<TimesheetContextProviderProps> = ({
  children,
  value
}) => {
  return (
    <TimesheetContext.Provider value={value}>
      {children}
    </TimesheetContext.Provider>
  );
};

/**
 * Custom hook to access TimesheetContext with proper error handling
 * 
 * This hook provides defensive programming by:
 * - Validating that the context is available
 * - Providing clear error messages for missing context
 * - Type-safe access to context data
 * 
 * @throws Error if used outside of TimesheetContextProvider
 * @returns TimesheetContextValue with type safety guaranteed
 */
export const useTimesheetContext = (): TimesheetContextValue => {
  const context = useContext(TimesheetContext);
  
  if (!context) {
    throw new Error(
      'useTimesheetContext must be used within a TimesheetContextProvider. ' +
      'Make sure your component is wrapped in <TimesheetContextProvider>.'
    );
  }

  // Additional defensive programming - validate required data
  if (!context.queryData) {
    throw new Error(
      'TimesheetContext: queryData is missing. ' +
      'This indicates a data loading issue in the parent component.'
    );
  }

  if (!context.employerGuid) {
    throw new Error(
      'TimesheetContext: employerGuid is missing. ' +
      'This is required for employee data operations.'
    );
  }

  if (!context.timesheetId) {
    throw new Error(
      'TimesheetContext: timesheetId is missing. ' +
      'This is required for timesheet operations.'
    );
  }

  return context;
};

/**
 * Optional hook for components that might not need the context
 * 
 * Returns null if context is not available, allowing components
 * to gracefully handle missing context without throwing errors.
 * 
 * @returns TimesheetContextValue | null
 */
export const useOptionalTimesheetContext = (): TimesheetContextValue | null => {
  const context = useContext(TimesheetContext);
  
  // Validate required data if context exists
  if (context) {
    if (!context.queryData || !context.employerGuid || !context.timesheetId) {
      console.warn('TimesheetContext: Missing required data in context', {
        hasQueryData: !!context.queryData,
        hasEmployerGuid: !!context.employerGuid,
        hasTimesheetId: !!context.timesheetId
      });
      return null;
    }
  }
  
  return context;
};