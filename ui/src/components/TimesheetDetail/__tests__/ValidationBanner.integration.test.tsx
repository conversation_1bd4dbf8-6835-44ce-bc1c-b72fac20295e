import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { Provider as SpectrumProvider, defaultTheme } from '@adobe/react-spectrum';
import { useTimesheetUIStore } from '@/src/store/timesheetUIStore';
import ValidationBanner from '../ValidationBanner';
import type { ValidationError } from '@/src/utils/validationUtils';

// Mock the store
jest.mock('@/src/store/timesheetUIStore');

const mockUseTimesheetUIStore = useTimesheetUIStore as jest.MockedFunction<typeof useTimesheetUIStore>;

// Mock DOM methods
const mockScrollIntoView = jest.fn();
const mockFocus = jest.fn();

beforeEach(() => {
    // Reset mocks
    mockScrollIntoView.mockClear();
    mockFocus.mockClear();

    // Mock querySelector to return an element with our mocked methods
    jest.spyOn(document, 'querySelector').mockReturnValue({
        scrollIntoView: mockScrollIntoView,
        focus: mockFocus
    } as unknown as Element);
});

afterEach(() => {
    jest.restoreAllMocks();
});

const renderWithSpectrum = (component: React.ReactElement) => {
    return render(<SpectrumProvider theme={defaultTheme}>{component}</SpectrumProvider>);
};

describe('ValidationBanner Integration', () => {
    const mockOnClick = jest.fn();

    beforeEach(() => {
        mockOnClick.mockClear();
    });

    it('should render banner when there are blocking validation errors', () => {
        const mockValidationErrors: ValidationError[] = [
            {
                field: 'stHours',
                message: 'Standard hours is required',
                payStubId: 'ps-1',
                detailId: 'detail-1',
                columnUid: 'stHours',
                severity: 'error'
            },
            {
                field: 'agreementId',
                message: 'Agreement is required',
                payStubId: 'ps-1',
                detailId: 'detail-1',
                columnUid: 'agreementId',
                severity: 'error'
            }
        ];

        // Mock store to return blocking error count
        mockUseTimesheetUIStore.mockImplementation((selector: any) => {
            if (typeof selector === 'function') {
                const mockState = {
                    getBlockingValidationErrorCount: jest.fn(() => 2),
                    validationErrorsByPayStubId: new Map([['ts-1:ps-1', mockValidationErrors]])
                };
                return selector(mockState);
            }
            return undefined;
        });

        renderWithSpectrum(<ValidationBanner count={2} onClick={mockOnClick} />);

        expect(screen.getByRole('alert')).toBeInTheDocument();
        expect(screen.getByText(/2 rows contain blocking errors/)).toBeInTheDocument();
    });

    it('should not render banner when there are only warning validation errors', () => {
        const mockValidationErrors: ValidationError[] = [
            {
                field: 'stHours',
                message: 'High hours warning',
                payStubId: 'ps-1',
                detailId: 'detail-1',
                columnUid: 'stHours',
                severity: 'warning'
            }
        ];

        // Mock store to return 0 blocking errors (only warnings)
        mockUseTimesheetUIStore.mockImplementation((selector: any) => {
            if (typeof selector === 'function') {
                const mockState = {
                    getBlockingValidationErrorCount: jest.fn(() => 0),
                    validationErrorsByPayStubId: new Map([['ts-1:ps-1', mockValidationErrors]])
                };
                return selector(mockState);
            }
            return undefined;
        });

        renderWithSpectrum(<ValidationBanner count={0} onClick={mockOnClick} />);

        expect(screen.queryByRole('alert')).not.toBeInTheDocument();
    });

    it('should scroll to first error element when banner is clicked', () => {
        renderWithSpectrum(<ValidationBanner count={1} onClick={mockOnClick} />);

        const button = screen.getByRole('button');
        fireEvent.click(button);

        expect(mockOnClick).toHaveBeenCalledTimes(1);
    });

    it('should count only blocking errors correctly', () => {
        const mixedValidationErrors: ValidationError[] = [
            {
                field: 'stHours',
                message: 'Standard hours is required',
                payStubId: 'ps-1',
                detailId: 'detail-1',
                columnUid: 'stHours',
                severity: 'error' // Blocking
            },
            {
                field: 'otHours',
                message: 'High overtime warning',
                payStubId: 'ps-1',
                detailId: 'detail-1',
                columnUid: 'otHours',
                severity: 'warning' // Non-blocking
            },
            {
                field: 'agreementId',
                message: 'Agreement is required',
                payStubId: 'ps-2',
                detailId: 'detail-2',
                columnUid: 'agreementId',
                severity: 'error' // Blocking
            }
        ];

        // Mock store with mixed errors - should count only 2 blocking errors
        mockUseTimesheetUIStore.mockImplementation((selector: any) => {
            if (typeof selector === 'function') {
                const mockState = {
                    getBlockingValidationErrorCount: jest.fn(() => 2),
                    validationErrorsByPayStubId: new Map([
                        ['ts-1:ps-1', [mixedValidationErrors[0], mixedValidationErrors[1]]],
                        ['ts-1:ps-2', [mixedValidationErrors[2]]]
                    ])
                };
                return selector(mockState);
            }
            return undefined;
        });

        renderWithSpectrum(<ValidationBanner count={2} onClick={mockOnClick} />);

        expect(screen.getByRole('alert')).toBeInTheDocument();
        expect(screen.getByText(/2 rows contain blocking errors/)).toBeInTheDocument();
    });
});
