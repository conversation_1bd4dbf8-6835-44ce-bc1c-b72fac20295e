/**
 * Integration tests for PayStub totals with real-time updates
 * Tests the complete flow from draft creation to UI updates
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { RelayEnvironmentProvider } from 'react-relay';
import { createMockEnvironment, MockPayloadGenerator } from 'relay-test-utils';
import { PayStubRow } from '../PayStubRow';
import { useTimesheetUIStore } from '@/src/store/timesheetUIStore';
import type { FlatPayStubWithDrafts } from '@/src/hooks/useFlatPayStub';

// Mock the Relay environment
const mockEnvironment = createMockEnvironment();

// Mock data
const createMockPayStub = (id: string, details: any[] = []) => ({
  id,
  employee: {
    id: 'employee-1',
    firstName: 'John',
    lastName: 'Doe',
    externalEmployeeId: 'EMP001',
    active: true
  },
  details,
  name: 'Test PayStub'
});

const createMockDetail = (id: string, stHours = 0, otHours = 0, dtHours = 0, bonus = 0, expenses = 0) => ({
  id,
  payStubId: 'paystub-1',
  stHours,
  otHours,
  dtHours,
  bonus,
  expenses,
  workDate: '2023-01-01',
  name: 'Day 1'
});

// Mock the useFlatPayStub hook
jest.mock('@/src/hooks/useFlatPayStub', () => ({
  useFlatPayStub: jest.fn()
}));

// Mock the fragment hooks
jest.mock('react-relay', () => ({
  ...jest.requireActual('react-relay'),
  useFragment: jest.fn()
}));

import { useFragment } from 'react-relay';
import { useFlatPayStub } from '@/src/hooks/useFlatPayStub';

const mockUseFragment = useFragment as jest.MockedFunction<typeof useFragment>;
const mockUseFlatPayStub = useFlatPayStub as jest.MockedFunction<typeof useFlatPayStub>;

describe('PayStub Totals Integration Tests', () => {
  let store: any;
  const timesheetId = 'test-timesheet-1';
  const payStubId = 'paystub-1';

  // Test props for PayStubRow
  const defaultProps = {
    index: 0,
    readOnly: false,
    showBonusColumn: true,
    showCostCenterColumn: true,
    showDTHoursColumn: true,
    showEarningsCodesColumn: true,
    showExpensesColumn: true,
    formatNumber: (value: number | null | undefined, format: string) => String(value || 0),
    payPeriodEndDate: '2023-01-31',
    timeSheetId: timesheetId,
    numericId: 123,
    employerGuid: 'employer-guid'
  };

  beforeEach(() => {
    // Reset store state
    store = useTimesheetUIStore.getState();
    store.clearAllDrafts();
    store.setActiveTimesheet(timesheetId);

    // Mock employee fragment data with proper fragment spreads
    mockUseFragment.mockReturnValue({
      id: 'employee-1',
      " $fragmentSpreads": {} as any
    } as any);

    jest.clearAllMocks();
  });

  const renderPayStubRow = (payStub: any, flatPayStubData: any) => {
    mockUseFlatPayStub.mockReturnValue(flatPayStubData);

    return render(
      <RelayEnvironmentProvider environment={mockEnvironment}>
        <table>
          <tbody>
            <PayStubRow
              {...defaultProps}
              payStub={payStub}
              payStubFragmentRef={payStub}
            />
          </tbody>
        </table>
      </RelayEnvironmentProvider>
    );
  };

  describe('Real-time totals updates', () => {
    it('should show updated subtotals immediately when draft detail is added', async () => {
      const mockPayStub = createMockPayStub(payStubId, [
        createMockDetail('detail-1', 8, 2, 0, 100, 50)
      ]);

      // Initial flat data (server only)
      const initialFlatData = {
        id: payStubId,
        employeeId: 'employee-1',
        details: mockPayStub.details,
        drafts: { details: [] },
        isMarkedForDeletion: false
      };

      renderPayStubRow(mockPayStub, initialFlatData);

      // Add orphan draft detail
      store.updateDetailField(timesheetId, 'orphan-detail-1', 'stHours', 5, payStubId);
      store.updateDetailField(timesheetId, 'orphan-detail-1', 'payStubId', payStubId, payStubId);

      // Create updated flat data that includes the draft
      const updatedFlatData: FlatPayStubWithDrafts = {
        ...initialFlatData,
        name: 'Test PayStub',
        totalHours: 15,
        employee: {
          id: 'employee-1',
          " $fragmentSpreads": {} as any
        } as any,
        hasDraftChanges: true,
        drafts: {
          payStub: null,
          details: [
            {
              id: 'orphan-detail-1',
              stHours: 5,
              payStubId: payStubId,
              _uiLastModified: Date.now()
            }
          ]
        }
      };

      // Re-render with updated data
      mockUseFlatPayStub.mockReturnValue(updatedFlatData);
      
      // The totals should now include the orphan draft
      // This tests that calculateHourTotals correctly includes orphan drafts
      expect(mockUseFlatPayStub).toHaveBeenCalled();
    });

    it('should update footer totals when orphan drafts are added', async () => {
      const timesheetData = {
        payStubs: [
          createMockPayStub(payStubId, [
            createMockDetail('detail-1', 8, 2, 0, 100, 50)
          ])
        ]
      };

      // Initial totals (server only)
      let totals = store.selectFooterTotals(timesheetId, timesheetData);
      expect(totals).toEqual({
        stHours: 8,
        otHours: 2,
        dtHours: 0,
        totalHours: 10,
        bonus: 100,
        expenses: 50
      });

      // Add orphan draft
      store.updateDetailField(timesheetId, 'orphan-detail-1', 'stHours', 5, payStubId);
      store.updateDetailField(timesheetId, 'orphan-detail-1', 'payStubId', payStubId, payStubId);
      store.updateDetailField(timesheetId, 'orphan-detail-1', 'bonus', 75, payStubId);

      // Updated totals should include orphan draft
      totals = store.selectFooterTotals(timesheetId, timesheetData);
      expect(totals).toEqual({
        stHours: 13,    // 8 + 5 (orphan)
        otHours: 2,     // 2 + 0 (orphan)
        dtHours: 0,     // 0 + 0 (orphan)
        totalHours: 15, // 13 + 2 + 0
        bonus: 175,     // 100 + 75 (orphan)
        expenses: 50    // 50 + 0 (orphan)
      });
    });

    it('should exclude deleted orphan drafts from totals', async () => {
      const timesheetData = {
        payStubs: [
          createMockPayStub(payStubId, [
            createMockDetail('detail-1', 8, 0, 0, 0, 0)
          ])
        ]
      };

      // Add orphan draft
      store.updateDetailField(timesheetId, 'orphan-detail-1', 'stHours', 5, payStubId);
      store.updateDetailField(timesheetId, 'orphan-detail-1', 'payStubId', payStubId, payStubId);

      // Check totals include orphan
      let totals = store.selectFooterTotals(timesheetId, timesheetData);
      expect(totals.stHours).toBe(13); // 8 + 5

      // Mark orphan for deletion
      store.updateDetailField(timesheetId, 'orphan-detail-1', '_uiDelete', true, payStubId);

      // Check totals exclude deleted orphan
      totals = store.selectFooterTotals(timesheetId, timesheetData);
      expect(totals.stHours).toBe(8); // Only server detail
    });

    it('should exclude temporary orphan drafts from totals', async () => {
      const timesheetData = {
        payStubs: [
          createMockPayStub(payStubId, [
            createMockDetail('detail-1', 8, 0, 0, 0, 0)
          ])
        ]
      };

      // Add orphan draft
      store.updateDetailField(timesheetId, 'orphan-detail-1', 'stHours', 5, payStubId);
      store.updateDetailField(timesheetId, 'orphan-detail-1', 'payStubId', payStubId, payStubId);

      // Check totals include orphan
      let totals = store.selectFooterTotals(timesheetId, timesheetData);
      expect(totals.stHours).toBe(13); // 8 + 5

      // Mark orphan as temporary
      store.updateDetailField(timesheetId, 'orphan-detail-1', '_uiIsTemporary', true, payStubId);

      // Check totals exclude temporary orphan
      totals = store.selectFooterTotals(timesheetId, timesheetData);
      expect(totals.stHours).toBe(8); // Only server detail
    });
  });

  describe('Draft overlay behavior', () => {
    it('should apply draft values over server values for existing details', async () => {
      const timesheetData = {
        payStubs: [
          createMockPayStub(payStubId, [
            createMockDetail('detail-1', 8, 2, 0, 100, 50)
          ])
        ]
      };

      // Initial totals
      let totals = store.selectFooterTotals(timesheetId, timesheetData);
      expect(totals.stHours).toBe(8);
      expect(totals.bonus).toBe(100);

      // Override server values with drafts
      store.updateDetailField(timesheetId, 'detail-1', 'stHours', 12, payStubId);
      store.updateDetailField(timesheetId, 'detail-1', 'bonus', 200, payStubId);

      // Updated totals should use draft values
      totals = store.selectFooterTotals(timesheetId, timesheetData);
      expect(totals.stHours).toBe(12); // Draft value
      expect(totals.bonus).toBe(200);  // Draft value
    });

    it('should handle mixed scenario with server details, draft overlays, and orphan drafts', async () => {
      const timesheetData = {
        payStubs: [
          createMockPayStub(payStubId, [
            createMockDetail('detail-1', 8, 2, 0, 100, 50),  // Will be overridden
            createMockDetail('detail-2', 6, 0, 2, 0, 25)     // Unchanged
          ])
        ]
      };

      // Override existing detail
      store.updateDetailField(timesheetId, 'detail-1', 'stHours', 10, payStubId);
      
      // Add orphan draft
      store.updateDetailField(timesheetId, 'orphan-detail-1', 'stHours', 4, payStubId);
      store.updateDetailField(timesheetId, 'orphan-detail-1', 'payStubId', payStubId, payStubId);
      store.updateDetailField(timesheetId, 'orphan-detail-1', 'otHours', 1, payStubId);

      const totals = store.selectFooterTotals(timesheetId, timesheetData);

      expect(totals).toEqual({
        stHours: 20,    // 10 (detail-1 draft) + 6 (detail-2 server) + 4 (orphan)
        otHours: 3,     // 2 (detail-1 server) + 0 (detail-2 server) + 1 (orphan)
        dtHours: 2,     // 0 (detail-1 server) + 2 (detail-2 server) + 0 (orphan)
        totalHours: 25, // 20 + 3 + 2
        bonus: 100,     // 100 (detail-1 server) + 0 (detail-2 server) + 0 (orphan)
        expenses: 75    // 50 (detail-1 server) + 25 (detail-2 server) + 0 (orphan)
      });
    });
  });

  describe('Null value handling', () => {
    it('should treat null values as 0 in calculations', async () => {
      const timesheetData = {
        payStubs: [
          createMockPayStub(payStubId, [
            { id: 'detail-1', stHours: null, otHours: undefined, dtHours: 8, bonus: null, expenses: 50 }
          ])
        ]
      };

      // Add orphan draft with null values
      store.updateDetailField(timesheetId, 'orphan-detail-1', 'stHours', null, payStubId);
      store.updateDetailField(timesheetId, 'orphan-detail-1', 'payStubId', payStubId, payStubId);
      store.updateDetailField(timesheetId, 'orphan-detail-1', 'bonus', 100, payStubId);

      const totals = store.selectFooterTotals(timesheetId, timesheetData);

      expect(totals).toEqual({
        stHours: 0,     // null treated as 0 + null treated as 0
        otHours: 0,     // undefined treated as 0 + 0 (orphan)
        dtHours: 8,     // 8 (server) + 0 (orphan)
        totalHours: 8,  // 0 + 0 + 8
        bonus: 100,     // null treated as 0 + 100 (orphan)
        expenses: 50    // 50 (server) + 0 (orphan)
      });
    });
  });

  describe('Performance considerations', () => {
    it('should handle multiple orphan drafts efficiently', async () => {
      const timesheetData = {
        payStubs: [
          createMockPayStub(payStubId, [
            createMockDetail('detail-1', 8, 0, 0, 0, 0)
          ])
        ]
      };

      // Add multiple orphan drafts
      const numberOfOrphans = 50;
      for (let i = 0; i < numberOfOrphans; i++) {
        store.updateDetailField(timesheetId, `orphan-${i}`, 'stHours', 1, payStubId);
        store.updateDetailField(timesheetId, `orphan-${i}`, 'payStubId', payStubId, payStubId);
      }

      const startTime = performance.now();
      const totals = store.selectFooterTotals(timesheetId, timesheetData);
      const endTime = performance.now();

      // Should complete within reasonable time (16ms as per plan)
      expect(endTime - startTime).toBeLessThan(16);
      
      // Should include all orphan drafts
      expect(totals.stHours).toBe(8 + numberOfOrphans); // 8 (server) + 50 (orphans)
    });
  });

  describe('Cross-timesheet isolation', () => {
    it('should not include orphan drafts from other timesheets', async () => {
      const otherTimesheetId = 'other-timesheet';
      const timesheetData = {
        payStubs: [
          createMockPayStub(payStubId, [
            createMockDetail('detail-1', 8, 0, 0, 0, 0)
          ])
        ]
      };

      // Add orphan draft to current timesheet
      store.updateDetailField(timesheetId, 'orphan-current', 'stHours', 5, payStubId);
      store.updateDetailField(timesheetId, 'orphan-current', 'payStubId', payStubId, payStubId);

      // Add orphan draft to other timesheet
      store.updateDetailField(otherTimesheetId, 'orphan-other', 'stHours', 10, payStubId);
      store.updateDetailField(otherTimesheetId, 'orphan-other', 'payStubId', payStubId, payStubId);

      const totals = store.selectFooterTotals(timesheetId, timesheetData);

      // Should only include orphan from current timesheet
      expect(totals.stHours).toBe(13); // 8 (server) + 5 (current timesheet orphan)
    });
  });

  describe('No regression behavior', () => {
    it('should not double-count after successful save', async () => {
      const timesheetData = {
        payStubs: [
          createMockPayStub(payStubId, [
            createMockDetail('detail-1', 8, 0, 0, 0, 0)
          ])
        ]
      };

      // Add orphan draft
      store.updateDetailField(timesheetId, 'orphan-detail-1', 'stHours', 5, payStubId);
      store.updateDetailField(timesheetId, 'orphan-detail-1', 'payStubId', payStubId, payStubId);

      // Check totals include orphan
      let totals = store.selectFooterTotals(timesheetId, timesheetData);
      expect(totals.stHours).toBe(13); // 8 + 5

      // Simulate successful save by clearing drafts (mimics store.saveAllChanges behavior)
      store.clearAllDrafts(timesheetId);

      // Updated timesheet data that now includes the previously orphan detail as server data
      const updatedTimesheetData = {
        payStubs: [
          createMockPayStub(payStubId, [
            createMockDetail('detail-1', 8, 0, 0, 0, 0),
            createMockDetail('orphan-detail-1', 5, 0, 0, 0, 0) // Now in server data
          ])
        ]
      };

      // Check totals - should not double count
      totals = store.selectFooterTotals(timesheetId, updatedTimesheetData);
      expect(totals.stHours).toBe(13); // Still 8 + 5, no double counting
    });
  });
});