import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { Provider as SpectrumProvider, defaultTheme } from '@adobe/react-spectrum';
import ValidationBanner from '../ValidationBanner';

// Mock the scroll behavior
const mockScrollIntoView = jest.fn();
const mockFocus = jest.fn();

// Setup DOM element mocks
beforeEach(() => {
    // Reset mocks
    mockScrollIntoView.mockClear();
    mockFocus.mockClear();

    // Mock querySelector to return an element with our mocked methods
    jest.spyOn(document, 'querySelector').mockReturnValue({
        scrollIntoView: mockScrollIntoView,
        focus: mockFocus
    } as unknown as Element);
});

afterEach(() => {
    jest.restoreAllMocks();
});

const renderWithSpectrum = (component: React.ReactElement) => {
    return render(<SpectrumProvider theme={defaultTheme}>{component}</SpectrumProvider>);
};

describe('ValidationBanner', () => {
    const mockOnClick = jest.fn();

    beforeEach(() => {
        mockOnClick.mockClear();
    });

    it('should not render when count is 0', () => {
        renderWithSpectrum(<ValidationBanner count={0} onClick={mockOnClick} />);

        expect(screen.queryByRole('alert')).not.toBeInTheDocument();
    });

    it('should render banner with singular message when count is 1', () => {
        renderWithSpectrum(<ValidationBanner count={1} onClick={mockOnClick} />);

        expect(screen.getByRole('alert')).toBeInTheDocument();
        expect(screen.getByText(/1 row contain blocking errors/)).toBeInTheDocument();
    });

    it('should render banner with plural message when count is greater than 1', () => {
        renderWithSpectrum(<ValidationBanner count={3} onClick={mockOnClick} />);

        expect(screen.getByRole('alert')).toBeInTheDocument();
        expect(screen.getByText(/3 rows contain blocking errors/)).toBeInTheDocument();
    });

    it('should call onClick when banner is clicked', () => {
        renderWithSpectrum(<ValidationBanner count={2} onClick={mockOnClick} />);

        const button = screen.getByRole('button');
        fireEvent.click(button);

        expect(mockOnClick).toHaveBeenCalledTimes(1);
    });

    it('should have proper accessibility attributes', () => {
        renderWithSpectrum(<ValidationBanner count={1} onClick={mockOnClick} />);

        const alert = screen.getByRole('alert');
        expect(alert).toHaveAttribute('aria-live', 'assertive');

        const button = screen.getByRole('button');
        expect(button).toHaveAttribute('aria-label');
        expect(button.getAttribute('aria-label')).toContain('Click to navigate to first error');
    });

    it('should display error icon and proper styling', () => {
        renderWithSpectrum(<ValidationBanner count={1} onClick={mockOnClick} />);

        // Check that the button contains the alert icon (we can't easily test the exact icon,
        // but we can verify the button structure)
        const button = screen.getByRole('button');
        expect(button).toBeInTheDocument();

        // Check that the alert region exists
        const alert = screen.getByRole('alert');
        expect(alert).toBeInTheDocument();
    });
});
