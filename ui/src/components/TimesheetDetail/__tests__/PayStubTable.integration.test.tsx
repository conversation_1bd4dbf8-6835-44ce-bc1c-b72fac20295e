import React from 'react';
import { screen, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { renderWithRealDependencies, withRenderMonitoring, createSafeTestEnvironment } from '../../../test-utils/integration-test-helpers';
import PayStubTable from '../PayStubTable';
import type { PayStubTable_payStub$key } from '@/src/relay/__generated__/PayStubTable_payStub.graphql';
import type { PayStubTable_connectionFragment$key } from '@/src/relay/__generated__/PayStubTable_connectionFragment.graphql';
import { Employee } from '@/src/types/timesheet';

// DO NOT MOCK @tanstack/react-table - this is critical for catching integration bugs
// DO NOT MOCK react-relay hooks - use real implementations
// The purpose of this test is to catch integration issues that mocking would hide

describe('PayStubTable Integration Tests - Infinite Loop Prevention', () => {
  let testEnvironment: ReturnType<typeof createSafeTestEnvironment>;
  
  const mockEmployees: Employee[] = [
    {
      id: 'emp-1',
      value: 'emp-1',
      text: '<PERSON><PERSON>, <PERSON>',
      SSN: '***********'
    },
    {
      id: 'emp-2',
      value: 'emp-2',
      text: 'Smith, Jane',
      SSN: '***********'
    }
  ];

  // Create mock timeSheetRef with connection structure
  const createMockTimeSheetRef = (payStubs: PayStubTable_payStub$key[] = []) => ({
    $fragmentRefs: 'PayStubTable_connectionFragment',
    id: 'timesheet-1'
  } as unknown as PayStubTable_connectionFragment$key);

  // Mock data for testing
  const createMockPayStubRef = (id: string) => ({
    $fragmentRefs: `paystub-fragment-${id}`,
    id: `ps-${id}`
  } as unknown as PayStubTable_payStub$key);

  const defaultProps = {
    timeSheetRef: createMockTimeSheetRef([]),
    readOnly: false,
    showBonusColumn: false,
    showCostCenterColumn: false,
    showDTHoursColumn: false,
    showEarningsCodesColumn: false,
    showExpensesColumn: false,
    employees: mockEmployees,
    payPeriodEndDate: '2024-01-15',
    timeSheetId: 'test-timesheet-id',
    numericId: 123,
    employerGuid: 'test-employer-guid'
  };

  beforeEach(() => {
    testEnvironment = createSafeTestEnvironment();
  });

  afterEach(() => {
    testEnvironment.cleanup();
  });

  describe('Infinite Loop Prevention', () => {
    it('should not cause infinite re-renders during initial mount', async () => {
      // This test specifically targets the bug that was fixed in PayStubTable
      const MonitoredPayStubTable = withRenderMonitoring(PayStubTable, 20);
      
      expect(() => {
        renderWithRealDependencies(
          <MonitoredPayStubTable {...defaultProps} />,
          {
            skipMocks: ['@tanstack/react-table', 'react-relay']
          }
        );
      }).not.toThrow(/INFINITE LOOP DETECTED/);
      
      // Component should render successfully
      expect(screen.getByRole('table')).toBeInTheDocument();
      
      // Should not have any React-related errors
      expect(testEnvironment.hasReactErrors()).toBe(false);
    });

    it('should not cause infinite re-renders when data changes', async () => {
      const mockPayStubRef = {
        $fragmentRefs: 'test-fragment-ref',
        id: 'ps-1'
      } as unknown as PayStubTable_payStub$key;

      const MonitoredPayStubTable = withRenderMonitoring(PayStubTable, 25);
      
      const { rerender } = renderWithRealDependencies(
        <MonitoredPayStubTable {...defaultProps} />,
        {
          skipMocks: ['@tanstack/react-table', 'react-relay']
        }
      );

      // Simulate multiple data changes that previously caused infinite loops
      for (let i = 0; i < 5; i++) {
        await act(async () => {
          rerender(
            <MonitoredPayStubTable 
              {...defaultProps} 
              timeSheetRef={createMockTimeSheetRef([{ ...mockPayStubRef, id: `ps-${i}` } as unknown as PayStubTable_payStub$key])}
            />
          );
        });
        
        // Brief wait to allow any async effects
        await waitFor(() => {}, { timeout: 50 });
      }

      expect(screen.getByRole('table')).toBeInTheDocument();
      expect(testEnvironment.hasReactErrors()).toBe(false);
    });

    it('should handle React Table state changes without loops', async () => {
      const user = userEvent.setup();
      const mockPayStubRef = {
        $fragmentRefs: 'test-fragment-ref',
        id: 'ps-1'
      } as unknown as PayStubTable_payStub$key;

      const MonitoredPayStubTable = withRenderMonitoring(PayStubTable, 30);

      renderWithRealDependencies(
        <MonitoredPayStubTable 
          {...defaultProps} 
          timeSheetRef={createMockTimeSheetRef([mockPayStubRef])}
        />,
        {
          skipMocks: ['@tanstack/react-table', 'react-relay']
        }
      );

      // Wait for initial render
      await waitFor(() => {
        expect(screen.getByRole('table')).toBeInTheDocument();
      });

      // Try to interact with sortable columns to trigger React Table state changes
      const columnHeaders = screen.getAllByRole('columnheader');
      const sortableHeaders = columnHeaders.filter(header => {
        const element = header;
        return element.style.cursor === 'pointer' || 
               element.getAttribute('data-sortable') === 'true' ||
               element.textContent?.includes('Employee') ||
               element.textContent?.includes('Hours');
      });

      if (sortableHeaders.length > 0) {
        // Click on the first sortable header
        await act(async () => {
          await user.click(sortableHeaders[0]);
        });
        await waitFor(() => {}, { timeout: 100 });
        
        // Click again to reverse sort
        await act(async () => {
          await user.click(sortableHeaders[0]);
        });
        await waitFor(() => {}, { timeout: 100 });
      }

      expect(screen.getByRole('table')).toBeInTheDocument();
      expect(testEnvironment.hasReactErrors()).toBe(false);
    });

    it('should handle column visibility changes without infinite loops', async () => {
      const MonitoredPayStubTable = withRenderMonitoring(PayStubTable, 25);
      
      const { rerender } = renderWithRealDependencies(
        <MonitoredPayStubTable {...defaultProps} />,
        {
          skipMocks: ['@tanstack/react-table', 'react-relay']
        }
      );

      // Test different column visibility combinations that could trigger re-renders
      const columnConfigs = [
        { showBonusColumn: true },
        { showDTHoursColumn: true },
        { showExpensesColumn: true },
        { showBonusColumn: true, showDTHoursColumn: true },
        { showBonusColumn: false, showDTHoursColumn: false, showExpensesColumn: false }
      ];

      for (const config of columnConfigs) {
        await act(async () => {
          rerender(
            <MonitoredPayStubTable 
              {...defaultProps} 
              {...config}
            />
          );
        });
        
        await waitFor(() => {
          expect(screen.getByRole('table')).toBeInTheDocument();
        }, { timeout: 100 });
      }

      expect(testEnvironment.hasReactErrors()).toBe(false);
    });
  });

  describe('Data Flow Integration', () => {
    it('should handle empty timeSheetRef without errors', () => {
      const MonitoredPayStubTable = withRenderMonitoring(PayStubTable);
      
      renderWithRealDependencies(
        <MonitoredPayStubTable {...defaultProps} timeSheetRef={createMockTimeSheetRef([])} />,
        {
          skipMocks: ['@tanstack/react-table', 'react-relay']
        }
      );

      expect(screen.getByRole('table')).toBeInTheDocument();
      // Should show table headers even with no data
      expect(screen.getByText('Employee')).toBeInTheDocument();
      expect(testEnvironment.hasReactErrors()).toBe(false);
    });

    it('should handle rapid data updates without state conflicts', async () => {
      const MonitoredPayStubTable = withRenderMonitoring(PayStubTable, 40);
      
      const { rerender } = renderWithRealDependencies(
        <MonitoredPayStubTable {...defaultProps} />,
        {
          skipMocks: ['@tanstack/react-table', 'react-relay']
        }
      );

      // Simulate rapid state updates that could cause conflicts
      const updates = Array.from({ length: 10 }, (_, i) => ({
        $fragmentRefs: `fragment-${i}`,
        id: `ps-${i}`
      })) as unknown as PayStubTable_payStub$key[];

      for (const update of updates) {
        await act(async () => {
          rerender(
            <MonitoredPayStubTable 
              {...defaultProps} 
              timeSheetRef={createMockTimeSheetRef([update])}
            />
          );
        });
        
        // Allow React to process the update
        await waitFor(() => {}, { timeout: 10 });
      }

      expect(screen.getByRole('table')).toBeInTheDocument();
      expect(testEnvironment.hasReactErrors()).toBe(false);
    });

    it('should handle readOnly mode changes gracefully', async () => {
      const MonitoredPayStubTable = withRenderMonitoring(PayStubTable, 20);
      
      const { rerender } = renderWithRealDependencies(
        <MonitoredPayStubTable {...defaultProps} readOnly={false} />,
        {
          skipMocks: ['@tanstack/react-table', 'react-relay']
        }
      );

      // Switch to read-only mode
      await act(async () => {
        rerender(
          <MonitoredPayStubTable {...defaultProps} readOnly={true} />
        );
      });

      // Switch back to editable mode
      await act(async () => {
        rerender(
          <MonitoredPayStubTable {...defaultProps} readOnly={false} />
        );
      });

      expect(screen.getByRole('table')).toBeInTheDocument();
      expect(testEnvironment.hasReactErrors()).toBe(false);
    });
  });

  describe('React Table Integration', () => {
    it('should initialize React Table without causing render loops', async () => {
      const MonitoredPayStubTable = withRenderMonitoring(PayStubTable, 15);
      
      renderWithRealDependencies(
        <MonitoredPayStubTable {...defaultProps} />,
        {
          skipMocks: ['@tanstack/react-table']
        }
      );

      // Wait for React Table to fully initialize
      await waitFor(() => {
        expect(screen.getByRole('table')).toBeInTheDocument();
      });

      // Check that table structure is present
      expect(screen.getByRole('columnheader', { name: /employee/i })).toBeInTheDocument();
      expect(screen.getByRole('columnheader', { name: /st hours/i })).toBeInTheDocument();
      expect(screen.getByRole('columnheader', { name: /ot hours/i })).toBeInTheDocument();
      
      expect(testEnvironment.hasReactErrors()).toBe(false);
    });

    it('should handle table configuration changes without loops', async () => {
      const MonitoredPayStubTable = withRenderMonitoring(PayStubTable, 25);
      
      const { rerender } = renderWithRealDependencies(
        <MonitoredPayStubTable {...defaultProps} />,
        {
          skipMocks: ['@tanstack/react-table']
        }
      );

      // Add bonus column
      await act(async () => {
        rerender(
          <MonitoredPayStubTable 
            {...defaultProps} 
            showBonusColumn={true}
          />
        );
      });

      await waitFor(() => {
        expect(screen.getByRole('columnheader', { name: /direct pay/i })).toBeInTheDocument();
      });

      // Add DT Hours column
      await act(async () => {
        rerender(
          <MonitoredPayStubTable 
            {...defaultProps} 
            showBonusColumn={true}
            showDTHoursColumn={true}
          />
        );
      });

      await waitFor(() => {
        expect(screen.getByRole('columnheader', { name: /dt hours/i })).toBeInTheDocument();
      });

      expect(testEnvironment.hasReactErrors()).toBe(false);
    });
  });

  describe('Component Lifecycle', () => {
    it('should mount and unmount cleanly without memory leaks', async () => {
      const MonitoredPayStubTable = withRenderMonitoring(PayStubTable, 15);
      
      const { unmount } = renderWithRealDependencies(
        <MonitoredPayStubTable {...defaultProps} />,
        {
          skipMocks: ['@tanstack/react-table', 'react-relay']
        }
      );

      expect(screen.getByRole('table')).toBeInTheDocument();
      
      // Unmount the component
      await act(async () => {
        unmount();
      });

      // Should not have errors during unmount
      expect(testEnvironment.hasReactErrors()).toBe(false);
    });
  });
});