/* src/components/TimesheetDetail/TimeSheetDetailTableView.module.scss */

.detailTable {
    width: 100%;
    border-collapse: collapse;
    /* Add a subtle border or background to distinguish from parent */
    /* border removed as per request */
    background-color: var(--spectrum-alias-background-color-primary);
}

.tableHead th {
    padding: var(--spectrum-global-dimension-size-125) var(--spectrum-global-dimension-size-100); /* Increased vertical padding */
    font-weight: var(--spectrum-global-font-weight-bold);
    background-color: var(--spectrum-alias-background-color-secondary);
    text-align: left;
    border-bottom: 1px solid var(--spectrum-alias-border-color-dark);
    white-space: nowrap; /* Prevent header text wrapping */
}

.tableBody tr:not(:last-child) {
    border-bottom: 1px solid var(--spectrum-alias-border-color-light);
}

/* Alternating row colors for better readability */
.alternateRow {
    background-color: var(--spectrum-gray-75, #fafafa);
}

.tableBody td {
    /* Minimal padding, let the inner View handle spacing */
    padding: var(--spectrum-global-dimension-size-25) var(--spectrum-global-dimension-size-50);
    vertical-align: middle; /* Align cell content middle */
    border-right: 1px solid var(--spectrum-alias-border-color-light); /* Add vertical borders */
}

.tableBody td:last-child {
    border-right: none; /* Remove border from last cell */
}

.deletedRow {
    /* Styles for deleted rows with fallback colors */
    background-color: var(--spectrum-semantic-negative-color-background-subtle, #feebeb) !important;
    color: var(--spectrum-semantic-negative-color-text-small, #ae2a19) !important;
    text-decoration: line-through !important;
    opacity: 0.7 !important;
    /* Additional fallback for better visibility */
    border-left: 3px solid #d13212 !important;
    /* Simple fallback for testing CSS module functionality */
    background: #ffe6e6 !important;
}

.deletedRow:hover {
    background-color: var(--spectrum-semantic-negative-color-background-subtle-hover, #fdd8d4) !important;
}

.deletedRow td {
    /* Ensure deleted row cells also get the line-through */
    text-decoration: line-through !important;
    background-color: inherit !important;
    color: inherit !important;
}

.emptyRow td {
    height: var(--spectrum-global-dimension-size-600); /* Smaller height for empty detail */
    text-align: center;
    padding: var(--spectrum-global-dimension-size-100);
    color: var(--spectrum-alias-text-color-secondary);
}

/* Ensure inner View takes up cell space for alignment */
.cellContentWrapper {
    display: flex;
    align-items: center; /* Vertically center content within the wrapper */
    width: 100%;
    height: 100%;
    min-height: var(--spectrum-global-dimension-size-400); /* Ensure minimum cell height */
}

/* Phase 4.2: Cell-level highlights for validation errors (FR-06) */
.hasErrors {
    outline: 2px solid var(--spectrum-semantic-negative-color-border) !important;
    position: relative !important;
}

/* Validation error cell highlighting - uses Spectrum semantic negative colors */
.validationErrorCell {
    outline: 2px solid var(--spectrum-semantic-negative-color-border) !important;
    position: relative !important;
    background-color: var(--spectrum-semantic-negative-color-background-subtle, #feebeb) !important;
}

/* Visual indicator for fields with non-default values (values that differ from employee defaults) */
.nonDefaultValueCell {
    background-color: var(--spectrum-blue-100, #e6f3ff) !important;
    border-left: 3px solid var(--spectrum-blue-500, #1473e6) !important;
}

.nonDefaultValueCell:hover {
    background-color: var(--spectrum-blue-200, #cce7ff) !important;
}

/* Visual indicator for fields where employee has no default value but a value is entered */
.noDefaultValueCell {
    border-left: 3px solid var(--spectrum-blue-500, #1473e6) !important;
    /* No background color - only the blue left bar */
}

.noDefaultValueCell:hover {
    background-color: var(--spectrum-gray-100, #f5f5f5) !important;
}

/* Visual indicator for cells with unsaved edits */
.editedCell {
    background-color: var(--spectrum-green-100, #e8f5e8) !important;
    /* No left border - only background color for regular unsaved edits */
}

.editedCell:hover {
    background-color: var(--spectrum-green-200, #d3f3d3) !important;
}

/* Visual indicator for unsaved edits that differ from employee default */
.editedNonDefaultCell {
    background-color: var(--spectrum-green-100, #e8f5e8) !important;
    border-left: 3px solid var(--spectrum-blue-500, #1473e6) !important;
}

.editedNonDefaultCell:hover {
    background-color: var(--spectrum-green-200, #d3f3d3) !important;
}

/* Visual indicator for unsaved edits where employee has no default value */
.editedNoDefaultCell {
    background-color: var(--spectrum-green-100, #e8f5e8) !important;
    border-left: 3px solid var(--spectrum-blue-500, #1473e6) !important;
}

.editedNoDefaultCell:hover {
    background-color: var(--spectrum-green-200, #d3f3d3) !important;
}
