# Timesheet Detail Feature (`src/components/TimesheetDetail`)

## 1. Overview & Business Purpose

The Timesheet Detail page provides a comprehensive interface for managing detailed employee work hours and related payroll information for a specific employer and pay period. Its primary purpose is to allow authorized users (such as payroll administrators, project managers, or supervisors) to accurately record, review, modify, and finalize the hours worked by employees, ensuring compliance with agreements and proper allocation for payroll processing.

This feature facilitates:

- **Accurate Time Tracking:** Capturing Standard Time (ST), Overtime (OT), and Double Time (DT) hours for each employee on a daily basis within the selected pay period.
- **Detailed Allocation:** Assigning hours worked to specific Job Codes, Agreements, Classifications, Sub-Classifications, and Cost Centers as required by contracts or internal tracking needs.
- **Earnings & Expenses:** Recording additional earnings (like Bonuses/Direct Pay) and reimbursable Expenses associated with the work performed.
- **Data Validation & Consistency:** Ensuring data integrity through lookups (e.g., valid agreements/classifications for an employee) and calculations (e.g., total hours).
- **Workflow Management:** Supporting the process of creating, editing, saving, and potentially submitting timesheet data for approval or payroll processing.

## 2. Key Features

- **Pay Period Information:** Displays the selected Pay Period End Date and the overall Status of the timesheet (e.g., Open, Submitted, Approved).
- **Employee Pay Stubs:** Lists employees associated with the timesheet, allowing for adding or removing employees (represented as "Pay Stubs" in the data model).
- **Detailed Daily Entry:** Each employee row can be expanded to reveal a daily breakdown (typically 7 days ending on the pay period end date) where specific hours, codes, and rates can be entered or modified.
- **Configurable Columns:** Allows users to show/hide optional columns (like DT Hours, Cost Center, Bonus, Expenses, Earnings Codes, Sub-Classifications) via a Settings panel to tailor the view to their needs.
- **Data Input Controls:** Utilizes various input fields:
    - **Pickers/Dropdowns:** For selecting Employees, Agreements, Classifications, Sub-Classifications, and Earnings Codes from predefined lists.
    - **Number Fields:** For entering hours (ST, OT, DT), hourly rates, bonus amounts, and expenses.
    - **Text Fields:** For entering Job Codes and Cost Centers.
- **Row Actions:** Provides actions for each detail row, such as deleting or duplicating a row to speed up data entry.
- **Toolbar Actions:** Includes main actions like Saving the timesheet, accessing Settings, Uploading data (potentially via CSV), Resetting changes, and collapsing/expanding all detail views.
- **Calculated Totals:** Automatically calculates and displays total hours for each detail row and potentially summary totals for the pay stub.

## 3. How it Works (User Perspective)

1.  **Access:** A user navigates to a specific timesheet, typically by selecting it from a roster or list.
2.  **Initial View:** The page loads, displaying the header information (Pay Period End, Status) and a grid/table listing the employees (Pay Stubs) currently on the timesheet. Each row shows summary information for the employee.
3.  **Adding/Removing Employees:** Users can add new employee rows (Pay Stubs) using an "Add Employee" button/selector and assign an employee from a list. Existing rows can be marked for deletion.
4.  **Viewing/Editing Details:** Users can expand an employee's row to see the `TimeSheetDetailTableView`. This nested table shows the 7 days of the pay period.
5.  **Data Entry:** Within the detail table, users click into cells to:
    - Enter ST, OT, DT hours.
    - Enter Job Codes, Cost Centers, Bonuses, Expenses.
    - Select Agreements, Classifications, Sub-Classifications, and Earnings Codes using dropdown pickers. The available options in dependent pickers (like Classification) are often filtered based on preceding selections (like Agreement).
6.  **Row Manipulation:** Users can use the action icons on each detail row to duplicate it (copying relevant data like codes and rates) or mark it for deletion/undeletion.
7.  **Using Toolbar:**
    - **Settings:** Open a dialog to toggle the visibility of optional columns.
    - **Upload:** (If applicable) Initiate a workflow to upload timesheet data from a file.
    - **Save:** Persist all changes made to the timesheet data.
    - **Reset:** Discard changes made since the last save.
    - **Collapse/Expand All:** Toggle the detail view for all employee rows simultaneously.
8.  **Review:** Users review the entered data and calculated totals for accuracy.

## 4. Technical Architecture

The Timesheet Detail feature now follows the same **Relay + Zustand** pattern adopted across the app.

- **Framework** React 19 + TypeScript.
- **Data** Relay Modern for all GraphQL queries, fragments and mutations.
- **Local/UI state** `timesheetUIStore` (Zustand). It stores:
    - Pay-stub & detail **drafts** (flat types)
    - UI flags (expanded rows, editing state, column visibility…)
    - Error & saving status
      Drafts persist only in-memory and are merged with server data via hooks such as `useMergedPayStub`.
- **Data flow**
    1. `TimesheetDetail.tsx` executes the route-level query with `useLazyLoadQuery` and sets the **active timesheet ID** in the store.
    2. Components subscribe to just the slices they need; e.g. `PayStubTable` listens to expansion state, `EditableNumberCell` to the specific draft row.
    3. Cell edits call `updatePayStubDraft` (Zustand) → UI re-renders instantly.
    4. **Save** triggers `useTimesheetSaver` which converts drafts → GraphQL input and commits `ModifyTimeSheetMutation` with optimistic updater.
- **Dual-prop pattern** `PayStubRowWrapper` (and `PayStubRow`) pass both the Relay **fragment ref** and the **flat pay-stub data** to children, so nested components can continue to use `useFragment` if needed.
- **Type safety** Relay compiler generates typed fragments. UI-specific “flat” types live in `src/types` and are derived via script to stay schema-aware.
- **ID handling** All IDs are Relay Global IDs (`ID` scalar); no manual base64 decoding in the client.

The Timesheet Detail feature leverages a modern React architecture focused on component composition, clear state management, and type safety.

- **Framework:** React with TypeScript.
- **UI Libraries:** Primarily uses `@adobe/react-spectrum` components for UI elements (Pickers, Buttons, TextFields, etc.) and potentially `@/components/ui` (Shadcn UI) for layout components like `Table`.
- **Data Management:** Uses Relay for GraphQL data fetching and caching, with React Context for local state management.
- **Component Structure:** The feature is broken down into smaller, reusable components following the Single Responsibility Principle.
    - **Orchestrator (`TimesheetDetail.tsx`):** Manages overall feature logic, initializes data fetching hooks, and provides the central state context. Renders `TimesheetDetailView`.
    - **View (`TimesheetDetailView.tsx`):** Responsible for the main layout structure (header, buttons, grid placement). Consumes context.
    - **Grid Coordinator (`TimeSheetGrid.tsx`):** Manages the main data grid area, rendering the toolbar and the pay stub table. Consumes context and defines handlers.
    - **Context (`TimesheetContext.tsx`):** A React Context provider (`TimesheetProvider`) and consumer hook (`useTimesheetContext`) that centralizes the management of shared state. This includes the core timesheet data (`timeSheetHeader`), the modifiable list of pay stubs (`payStubs`), lookup data (`employees`, `fetchedAgreements`), **employee default settings via Relay queries**, UI settings (`settings`), and loading/error status. It exposes state values and update functions (e.g., `updatePayStubDetails`, `selectEmployeeForPayStub`, `togglePayStubDelete`).
    - **Data Hooks:**
        - `useTimesheetData`: Handles fetching the main timesheet data (header and initial pay stubs) via Relay/API.
        - `useEmployeeAndAgreementData`: Handles fetching related lookup data like employees and agreements via API.
        - `useTimesheetSaver`: Encapsulates the logic and mutations required to save the timesheet data.
        - **Employee Defaults via Relay**: Uses `useQueryLoader`/`usePreloadedQuery` pattern for efficient employee default settings fetching, eliminating the previous custom caching mechanism.
    - **Presentational Components:** Numerous smaller components handle specific UI parts:
        - `TimesheetToolbar.tsx`: Renders the top action bar.
        - `PayStubTable.tsx`: Renders the main table structure for employee pay stubs using `@tanstack/react-table`. It handles column definitions, sorting, expansion (showing `TimeSheetDetailTableView`), and rendering rows based on `payStubs` from context.
        - ~~`PayStubRow.tsx`~~: (Removed) Logic incorporated into `PayStubTable.tsx`'s cell rendering and row mapping.
        - `EmployeeSelector.tsx`: Provides the combobox for selecting an employee for a pay stub.
        - `TimeSheetDetailTableView.tsx`: Renders the nested table for daily details of a single pay stub. Orchestrates updates for its rows via context.
        - `Cells/*`: A collection of highly reusable, memoized components for rendering specific cell types within the detail table (e.g., `EditableNumberCell`, `AgreementPickerCell`, `ReadOnlyCell`). These consume context where necessary (e.g., for picker options) and accept `value`, `onChange`, and `isDisabled` props.
        - `DetailRowActions.tsx`: Renders the action buttons (Delete, Duplicate) for a detail row.
- **Data Flow:**
    1.  The orchestrator (`TimesheetDetail`) initializes data fetching via hooks.
    2.  The hooks update the state within the `TimesheetProvider`.
    3.  **Employee defaults are fetched via Relay using the `useQueryLoader`/`usePreloadedQuery` pattern when employees are selected for pay stubs.**
    4.  Components throughout the feature subscribe to the `TimesheetContext` using `useTimesheetContext` to get the data they need (e.g., `PayStubTable` gets `payStubs`, `AgreementPickerCell` gets `fetchedAgreements`, components get employee defaults via `getRelayEmployeeDefaults`).
    5.  User interactions (e.g., changing a value in an `EditableNumberCell`) trigger `onChange` handlers.
    6.  These handlers typically call update functions obtained from the context (e.g., `handleCellChange` in `TimeSheetDetailTableView` calls `updatePayStubDetails` from the context).
    7.  The context updates its state, causing subscribed components to re-render with the new data.
- **Type Safety:** Utilizes TypeScript extensively. Data structures originating from the backend GraphQL schema primarily use types generated by the Relay Compiler. Client-side state and UI-specific types are defined manually (e.g., in `src/types/timesheet.ts` or within components).

## 5. Key Components in this Folder (at a glance)

| File / Dir                                                          | Responsibility                                                                                               |
| ------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------ |
| `TimesheetDetail.tsx`                                               | Route container – runs query, sets active timesheet, renders `TimesheetDetailView`.                          |
| `TimesheetDetailView.tsx`                                           | Page chrome & high-level layout.                                                                             |
| `TimeSheetGrid.tsx`                                                 | Wraps `TimesheetToolbar` + `PayStubTable`.                                                                   |
| `TimesheetToolbar.tsx`                                              | Save / Submit / Settings / Upload actions.                                                                   |
| `PayStubTable.tsx`                                                  | Main table of pay stubs (`@tanstack/react-table`) – supports expansion, sorting, column resize.              |
| `PayStubRowWrapper.tsx` / `PayStubRow.tsx`                          | Handles a single pay-stub row; pulls drafts from store; renders `EmployeeSelector` and expanded detail view. |
| `TimeSheetDetailTableView.tsx`                                      | Nested table showing the 7-day breakdown for one pay stub.                                                   |
| `TimeSheetDetailRow.tsx`                                            | Row component inside the detail view (cell renderers live in `Cells/`).                                      |
| `Cells/`                                                            | Reusable cell components (`EditableNumberCell`, `AgreementPickerCell`, etc.).                                |
| `DetailRowActions.tsx`                                              | Delete / Undelete / Duplicate buttons for a detail row.                                                      |
| `EmployeeSelector*.tsx`                                             | Combo-box for picking employees; `EmployeeSelectorWithData` wires in Relay query.                            |
| `TimeSheetSettings.tsx`                                             | Modal for toggling optional columns (persists to store).                                                     |
| `UploadTimeSheet/`                                                  | CSV upload wizard (parsing, preview, commit).                                                                |
| `PayStubTableErrorBoundary.tsx`, `TimesheetDetailErrorBoundary.tsx` | Component-scoped error boundaries.                                                                           |

> Outside this folder: **`src/store/timesheetUIStore.ts`** (Zustand) & state-selector hooks drive all local state, while GraphQL mutations live in `src/mutations/timesheet/*`. |

- **`Cells/`**: Contains reusable, memoized components for rendering individual cells within the detail grid (e.g., `EditableNumberCell.tsx`, `AgreementPickerCell.tsx`, `ReadOnlyCell.tsx`). These are the building blocks for the detail view.
- **`DetailRowActions.tsx`**: Renders the action buttons (Delete/Undelete, Duplicate) for a single row within the `TimeSheetDetailTableView`.
- **`EmployeeSelector.tsx`**: A specialized combobox component used within `PayStubTable`'s cell rendering to select an employee for a specific pay stub. It fetches/filters employee lists and updates the context when an employee is selected.
- ~~**`PayStubRow.tsx`**~~: (Removed) This component's responsibilities (rendering a single pay stub summary row, handling expansion, rendering `EmployeeSelector` and `TimeSheetDetailTableView`) have been integrated into `PayStubTable.tsx`.
- **`PayStubTable.tsx`**: Renders the main table for pay stubs using `@tanstack/react-table`. It defines columns, handles sorting and expansion, renders rows (including `EmployeeSelector` and the expanded `TimeSheetDetailTableView`), and consumes `payStubs` and expansion state from the context.
- **`TimeSheetDetailTableView.tsx`**: Renders the _nested_ detail grid that appears when a `PayStubRow` is expanded. It displays the daily breakdown for a single pay stub and uses the `Cells/*` components for data entry. It orchestrates updates for its rows by calling the `updatePayStubDetails` function from the context.
- **`TimeSheetGrid.tsx`**: Acts as a coordinator for the main grid area. It renders the `TimesheetToolbar` and the `PayStubTable`. It consumes context and defines handlers that often trigger context update functions.
- **`TimeSheetSettings.tsx`**: (Likely) Implements the dialog/modal used to configure which optional columns are visible in the `TimeSheetDetailTableView`. Interacts with the `settings` state in the context.
- **`TimesheetToolbar.tsx`**: Renders the main action bar at the top of the grid, including buttons for Save, Settings, Upload, Reset, etc. It consumes context for data (like status, loading state) and triggers actions (often via context or hooks).
- **`UploadTimeSheet.tsx`**: (Likely) Handles the UI and logic for the timesheet upload feature, potentially using a dedicated hook (`useCsvUploadProcessor`) and interacting with the context.
