import React, { useMemo, useState, useCallback, useEffect } from 'react';
import { Text, ActionButton, View, Flex } from '@adobe/react-spectrum';
import {
    useReactTable,
    getCoreRowModel,
    getSortedRowModel,
    getExpandedRowModel,
    ColumnDef,
    flexRender,
    RowData,
    CellContext,
    SortingState,
    SortingFn
} from '@tanstack/react-table';
import ChevronRight from '@spectrum-icons/workflow/ChevronRight';
import ChevronDown from '@spectrum-icons/workflow/ChevronDown';
import DeleteIcon from '@spectrum-icons/workflow/Delete';
import Undo from '@spectrum-icons/workflow/Undo';
import { ArrowUp, ArrowDown, ChevronsUpDown } from 'lucide-react';
import { graphql, useFragment, usePaginationFragment } from 'react-relay'; // Combined import
import TimeSheetDetailTableView from './TimeSheetDetailTableView';
import EmployeeSelector from './EmployeeSelector';

import { useTimesheetUIStore } from '@/src/store/timesheetUIStore';
import { PayStubTable_payStub$key, PayStubTable_payStub$data } from '@/src/relay/__generated__/PayStubTable_payStub.graphql'; // Corrected import path
import { Employee } from '@/src/types/relay-ui-extensions';
import type { EmployeeSelector_employee$key } from '@/src/relay/__generated__/EmployeeSelector_employee.graphql';
import PayStubRowWrapper from './PayStubRowWrapper';
import { PayStubTableErrorBoundary } from './PayStubTableErrorBoundary';
import styles from './PayStubTable.module.scss';
import rowStyles from './PayStubRow.module.scss';
import EmptyValueDivider from '@ui/EmptyValueDivider';
import { EmployeeDisplayFragment } from '@/src/fragments/EmployeeDisplayFragment';

/**
 * Represents an individual timesheet detail row item
 * Contains hours, bonus, and expense data
 */
interface DetailItem {
    id: string;
    stHours: number | null;
    otHours: number | null;
    dtHours: number | null;
    bonus: number | null;
    expenses: number | null;
    delete?: boolean;
    [key: string]: unknown;
}

/**
 * Represents a pay stub entity with employee association
 * and its collection of detail items
 */
// Note: ResolvedPayStub might not be strictly needed if we rely on Relay types and RelayPayStub
interface ResolvedPayStub {
    id: string;
    employeeId: string | number | null;
    details: DetailItem[];
    [key: string]: unknown;
}

/**
 * Detailed information for a single pay stub entry
 * Contains work date and financial data
 */
// This local definition might conflict if PayStubDetail is imported elsewhere.
// Consider using the shared type from types/timesheet if applicable.
interface PayStubDetail {
    id: string;
    workDate?: string | null; // Keep as string for local definition if needed, but ensure consistency
    stHours?: number | null;
    otHours?: number | null;
    dtHours?: number | null;
    bonus?: number | null;
    expenses?: number | null;
    delete?: boolean;
    [key: string]: unknown;
}

/**
 * GraphQL fragment for individual PayStub data
 * Updated for Phase 4: Using link-only employee references
 * Used to define the data requirements for individual PayStub items
 */
export const PayStubTable_payStubFragment = graphql`
    fragment PayStubTable_payStub on PayStub {
        id
        employeeId
        employee {
            id
            # Link-only reference - employee data resolved separately
            ...EmployeeDisplayFragment_employee
            # Include TimeSheetDetailRow employee fragment
            ...TimeSheetDetailRow_employee
        }
        name
        totalHours
        details {
            id
            payStubId
            reportLineItemId
            workDate
            name
            stHours
            otHours
            dtHours
            totalHours
            bonus
            expenses
            # These fields are needed by TimeSheetDetailTableView
            jobCode
            agreementId
            classificationId
            subClassificationId
            costCenter
            hourlyRate
            earningsCode
            
            # Include TimeSheetDetailRow fragment for detail editing
            ...TimeSheetDetailRow_payStubDetail
        }

        # Include TimeSheetDetailTableView fragment for expanded row details
        ...TimeSheetDetailTableView_payStub
    }
`;

/**
 * GraphQL plural fragment for PayStubs data
 * Hook violation fix: Allows single useFragment call for multiple PayStubs
 * Contains essential fields needed for footer totals calculation
 */
export const PayStubTable_payStubsFragment = graphql`
    fragment PayStubTable_payStubs on PayStub @relay(plural: true) {
        id
        employeeId
        name
        totalHours
        details {
            id
            payStubId
            stHours
            otHours
            dtHours
            totalHours
            bonus
            expenses
        }
    }
`;

/**
 * GraphQL fragment for PayStub connection pagination
 * Used with usePaginationFragment for connection-based data fetching
 */
export const PayStubTable_connectionFragment = graphql`
    fragment PayStubTable_connectionFragment on TimeSheet
    @argumentDefinitions(first: { type: "Int", defaultValue: 500 }, after: { type: "String" })
    @refetchable(queryName: "PayStubTablePaginationQuery") {
        id
        payStubs(first: $first, after: $after) @connection(key: "PayStubTable_connectionFragment__payStubs") {
            edges {
                cursor
                node {
                    ...PayStubTable_payStub
                    ...PayStubTable_payStubs
                }
            }
            pageInfo {
                hasNextPage
                hasPreviousPage
                startCursor
                endCursor
            }
            totalCount
        }
    }
`;

// Define the type for the data passed to React Table
// Only Relay fragment references since optimistic updates handle temporary state
type TableRowData = PayStubTable_payStub$key;

// Extend ColumnDef meta type (Keep original TData extends RowData)
declare module '@tanstack/react-table' {
    interface ColumnMeta<TData extends RowData, TValue> {
        isNumeric?: boolean;
        isCentered?: boolean;
    }

    interface TableMeta<TData extends RowData> {
        readOnly: boolean;
    }
}

/**
 * Props for PayStubTable component
 * Updated to use connection-based data fetching
 */
interface PayStubTableProps {
    timeSheetRef: any; // Will be updated with proper type after Relay compiler runs
    readOnly: boolean;
    showBonusColumn: boolean;
    showCostCenterColumn: boolean;
    showDTHoursColumn: boolean;
    showEarningsCodesColumn: boolean;
    showExpensesColumn: boolean;
    employees?: ReadonlyArray<Employee | EmployeeSelector_employee$key>; // Accept both for backwards compatibility
    payPeriodEndDate: string | null | undefined;
    // Timesheet context for mutations
    timeSheetId: string;
    numericId: number;
    employerGuid: string;
}

/**
 * FooterTotals type must be updated to match new field names
 */
interface FooterTotals {
    stHours: number;
    otHours: number;
    dtHours: number;
    totalHours: number;
    bonus: number;
    expenses: number;
}

/**
 * PayStubTable component displays an interactive table of employee pay stubs
 * Handles expandable rows, sorting, and actions for timesheet data
 */
const PayStubTable: React.FC<PayStubTableProps> = ({
    timeSheetRef,
    readOnly,
    showBonusColumn,
    showCostCenterColumn,
    showDTHoursColumn,
    showEarningsCodesColumn,
    showExpensesColumn,
    employees,
    payPeriodEndDate,
    timeSheetId,
    numericId,
    employerGuid
}) => {
    // Subscribe to draftsVersion to ensure React re-renders when drafts change
    const draftsVersion = useTimesheetUIStore(state => state.draftsVersion);
    
    // Sorting state for table header clicks
    const [sorting, setSorting] = useState<SortingState>([]);

    // Use pagination fragment for connection-based data fetching
    const { data: paginationData, hasNext, loadNext, isLoadingNext } = usePaginationFragment(PayStubTable_connectionFragment, timeSheetRef);

    /**
     * Formats numeric values for display with appropriate precision
     * Memoized to prevent function recreation on every render
     */
    const formatNumber = useCallback((value: number | null | undefined, format: 'n2' | 'c2'): string => {
        if (value === null || value === undefined || value === 0) return '-';
        try {
            if (format === 'n2') {
                return value.toFixed(2);
            } else if (format === 'c2') {
                return `$${value.toFixed(2)}`;
            }
            return value.toString();
        } catch (e) {
            console.error('Error formatting number:', value, e);
            return '-';
        }
    }, []);

    // Custom sorting change handler (simplified since no temporary PayStub handling needed)
    const handleSortingChange = useCallback(
        (updater: React.SetStateAction<SortingState>) => {
            setSorting(updater);
        },
        [setSorting]
    );

    // Prepare data for the table: Extract nodes from connection edges
    const tableData = useMemo(() => {
        // Type-safe edge traversal
        const payStubEdges = paginationData?.payStubs?.edges ?? [];
        const payStubNodes = payStubEdges
            .map((edge: any) => edge?.node)
            .filter((node: any): node is NonNullable<typeof node> => Boolean(node));
        return payStubNodes;
    }, [paginationData?.payStubs?.edges]);

    // Hook violation fix: Use plural fragment for materialized PayStub data
    // Single useFragment call eliminates hook-inside-hook violations
    const materializedPayStubs = useFragment(PayStubTable_payStubsFragment, tableData);

    // Calculate footer totals with proper store subscriptions
    const detailDrafts = useTimesheetUIStore(state => state.detailDrafts);
    const markedForDeletion = useTimesheetUIStore(state => state.markedForDeletion);
    
    const footerTotals = useMemo(() => {
        // Call selectFooterTotals with materialized PayStub data from plural fragment
        const state = useTimesheetUIStore.getState();
        const result = state.selectFooterTotals(timeSheetId, { payStubs: materializedPayStubs });
        
        
        return result;
    }, [timeSheetId, materializedPayStubs, detailDrafts, markedForDeletion, draftsVersion]);

    /**
     * Define table columns with appropriate configuration
     */
    const tableColumns = useMemo<ColumnDef<TableRowData>[]>(() => {
        const cols: ColumnDef<TableRowData>[] = [
            // Actions column is now first
            ...(!readOnly
                ? [
                      {
                          id: 'actions',
                          header: '',
                          size: 50,
                          meta: { isNumeric: true }
                      }
                  ]
                : []),
            {
                id: 'expand',
                header: '',
                size: 50,
                enableSorting: false
            },
            {
                id: 'rowNumber',
                header: '#',
                meta: {
                    isCentered: true
                },
                size: 50,
                enableSorting: false
            },
            {
                id: 'employee',
                header: 'Employee',
                size: 300,
                enableSorting: true
            },
            {
                id: 'stHours',
                header: 'ST Hours',
                enableSorting: true,
                meta: {
                    isNumeric: true
                },
                size: 100 // Adjusted size
            },
            {
                id: 'otHours',
                header: 'OT Hours',
                enableSorting: true,
                meta: {
                    isNumeric: true
                },
                size: 100 // Adjusted size
            }
        ];

        // Conditionally add dtHours column
        if (showDTHoursColumn) {
            cols.push({
                id: 'dtHours',
                header: 'DT Hours',
                size: 100,
                enableSorting: true,
                meta: {
                    isNumeric: true
                }
            });
        }

        // Add total hours column
        cols.push({
            id: 'totalHours',
            header: 'Total Hours',
            enableSorting: true,
            meta: {
                isNumeric: true
            },
            size: 100 // Adjusted size
        });

        // Conditionally add bonus column
        if (showBonusColumn) {
            cols.push({
                id: 'bonus',
                header: 'Direct Pay',
                enableSorting: true,
                meta: {
                    isNumeric: true
                },
                size: 130 // Adjusted size
            });
        }

        // Conditionally add expenses column
        if (showExpensesColumn) {
            cols.push({
                id: 'expenses',
                header: 'Expenses', // Changed header back from Direct Pay
                size: 130,
                enableSorting: true,
                meta: {
                    isNumeric: true
                }
            });
        }

        return cols;
    }, [showBonusColumn, showDTHoursColumn, showExpensesColumn, readOnly]);

    // Memoize React Table configuration to prevent re-creation on every render
    const tableConfig = useMemo(
        () => ({
            data: tableData,
            columns: tableColumns,
            state: {
                sorting
            },
            onSortingChange: handleSortingChange,
            getCoreRowModel: getCoreRowModel(),
            getSortedRowModel: getSortedRowModel(),
            getExpandedRowModel: getExpandedRowModel(),
            getRowId: (row: TableRowData, index: number) => {
                // All rows are now Relay fragments (including optimistic ones)
                return (row as unknown as { readonly id: string }).id ?? `relay-${index}`;
            },
            meta: {
                readOnly: readOnly
            }
        }),
        [tableData, tableColumns, sorting, handleSortingChange, readOnly]
    );

    // Setup React Table instance
    const table = useReactTable(tableConfig);

    // Memoize style objects to prevent re-renders
    const tableHeadStyle = useMemo(() => ({ backgroundColor: '#f2f2f2' }), []);

    // Memoize header cell style function to prevent inline object creation
    const getHeaderCellStyle = useCallback(
        (header: any, textAlign: 'left' | 'right' | 'center') => ({
            width: header.getSize() !== 150 ? header.getSize() : undefined,
            cursor: header.column.getCanSort() ? 'pointer' : 'default',
            textAlign: textAlign
        }),
        []
    );

    // Memoize footer cell style function to prevent inline object creation
    const getFooterCellStyle = useCallback(
        (key: string, textAlign: 'left' | 'right' | 'center') => ({
            textAlign: textAlign,
            paddingLeft: key === 'rowNumber' ? 'var(--spectrum-global-dimension-size-50)' : undefined
        }),
        []
    );

    return (
        <div className={styles.tableContainer}>
            <table className={styles.table} role="table" aria-label="Timesheet pay stub table">
                <thead className={styles.tableHead} style={tableHeadStyle}>
                    {table.getHeaderGroups().map((headerGroup) => (
                        <tr key={headerGroup.id} role="row">
                            {headerGroup.headers.map((header) => {
                                let textAlign: 'left' | 'right' | 'center' = 'left';
                                if (header.column.columnDef.meta?.isNumeric) {
                                    textAlign = 'right';
                                } else if (header.column.columnDef.meta?.isCentered) {
                                    textAlign = 'center';
                                }
                                return (
                                    <th
                                        key={header.id}
                                        colSpan={header.colSpan}
                                        role="columnheader"
                                        aria-sort={
                                            header.column.getIsSorted() === 'asc'
                                                ? 'ascending'
                                                : header.column.getIsSorted() === 'desc'
                                                  ? 'descending'
                                                  : header.column.getCanSort()
                                                    ? 'none'
                                                    : undefined
                                        }
                                        style={getHeaderCellStyle(header, textAlign)}
                                        onClick={header.column.getToggleSortingHandler()}>
                                        <Flex
                                            direction="row"
                                            gap="size-50"
                                            alignItems="center"
                                            justifyContent={textAlign === 'right' ? 'end' : textAlign === 'center' ? 'center' : 'start'}>
                                            {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                                            {header.column.getCanSort() &&
                                                (header.column.getIsSorted() === 'asc' ? (
                                                    <ArrowUp size={16} />
                                                ) : header.column.getIsSorted() === 'desc' ? (
                                                    <ArrowDown size={16} />
                                                ) : (
                                                    <ChevronsUpDown size={16} opacity={0.5} />
                                                ))}
                                        </Flex>
                                    </th>
                                );
                            })}
                        </tr>
                    ))}
                </thead>
                <tbody className={styles.tableBody}>
                    {/* Render Relay payStub rows - optimistic updates appear here automatically */}
                    {table.getRowModel().rows.length === 0 ? (
                        <tr>
                            <td colSpan={table.getVisibleLeafColumns().length} style={{ textAlign: 'center', padding: 'var(--spectrum-global-dimension-size-400)' }}>
                                <Text>No records.</Text>
                            </td>
                        </tr>
                    ) : (
                        table.getRowModel().rows.map((row, rowIndex) => {
                            const originalItem = row.original;
                            const itemKey = (originalItem as unknown as { readonly id: string }).id ?? `row-${rowIndex}`;

                            return (
                                <ResolvedPayStubRow
                                    key={itemKey}
                                    payStubRef={originalItem}
                                    index={rowIndex}
                                    readOnly={readOnly}
                                    employees={employees}
                                    showBonusColumn={showBonusColumn}
                                    showCostCenterColumn={showCostCenterColumn}
                                    showDTHoursColumn={showDTHoursColumn}
                                    showEarningsCodesColumn={showEarningsCodesColumn}
                                    showExpensesColumn={showExpensesColumn}
                                    formatNumber={formatNumber}
                                    payPeriodEndDate={payPeriodEndDate}
                                    timeSheetId={timeSheetId}
                                    numericId={numericId}
                                    employerGuid={employerGuid}
                                />
                            );
                        })
                    )}
                </tbody>
                <tfoot className={styles.tableFoot} style={tableHeadStyle}>
                    <tr>
                        {table.getVisibleLeafColumns().map((column) => {
                            const key = column.id;
                            let totalValue: string = '-'; // Default

                            // Use footerTotals obtained from the top-level context call
                            // const { footerTotals } = useTimesheetContext(); // Already available via destructuring

                            // Set values based on column ID
                            if (key === 'employee') {
                                totalValue = 'Totals:';
                            } else if (key === 'stHours') {
                                totalValue = footerTotals.stHours === 0 ? '0.00' : formatNumber(footerTotals.stHours, 'n2');
                            } else if (key === 'otHours') {
                                totalValue = footerTotals.otHours === 0 ? '0.00' : formatNumber(footerTotals.otHours, 'n2');
                            } else if (key === 'dtHours' && showDTHoursColumn) {
                                totalValue = footerTotals.dtHours === 0 ? '0.00' : formatNumber(footerTotals.dtHours, 'n2');
                            } else if (key === 'totalHours') {
                                totalValue = footerTotals.totalHours === 0 ? '0.00' : formatNumber(footerTotals.totalHours, 'n2');
                            } else if (key === 'bonus' && showBonusColumn) {
                                totalValue = footerTotals.bonus === 0 ? '$0.00' : formatNumber(footerTotals.bonus, 'c2');
                            } else if (key === 'expenses' && showExpensesColumn) {
                                totalValue = footerTotals.expenses === 0 ? '$0.00' : formatNumber(footerTotals.expenses, 'c2');
                            }

                            // Determine text alignment
                            let textAlign: 'left' | 'right' | 'center' = 'left';
                            const isNumeric = column.columnDef.meta?.isNumeric;
                            const isCentered = column.columnDef.meta?.isCentered;

                            if (isNumeric) {
                                textAlign = 'right';
                            } else if (isCentered) {
                                textAlign = 'center';
                            } else if (key === 'employee') {
                                textAlign = 'right';
                            }

                            // Define columns where the footer dash should be suppressed
                            const suppressDashColumns = ['expand', 'rowNumber', 'actions'];

                            // Only show empty string for suppressed columns
                            if (suppressDashColumns.includes(key)) {
                                totalValue = '';
                            }

                            return (
                                <td
                                    key={`total-${key}`}
                                    className={key === 'employee' ? styles.totalsLabelCell : ''}
                                    style={getFooterCellStyle(key, textAlign)}>
                                    {key === 'totalHours' ? (
                                        <View paddingEnd="size-200" height="100%">
                                            <Flex
                                                justifyContent={
                                                    textAlign === 'right' ? 'end' : textAlign === 'center' ? 'center' : 'start'
                                                }>
                                                {totalValue === '-' ? <EmptyValueDivider /> : <Text>{totalValue}</Text>}
                                            </Flex>
                                        </View>
                                    ) : (
                                        <Flex justifyContent={textAlign === 'right' ? 'end' : textAlign === 'center' ? 'center' : 'start'}>
                                            {totalValue === '-' ? <EmptyValueDivider /> : <Text>{totalValue}</Text>}
                                        </Flex>
                                    )}
                                </td>
                            );
                        })}
                    </tr>
                </tfoot>
            </table>
        </div>
    );
};

// Define the wrapper component for resolving the fragment
interface ResolvedPayStubRowProps {
    payStubRef: PayStubTable_payStub$key;
    index: number;
    readOnly: boolean;
    employees?: ReadonlyArray<Employee | EmployeeSelector_employee$key>; // Accept union type for backwards compatibility
    showBonusColumn: boolean;
    showCostCenterColumn: boolean;
    showDTHoursColumn: boolean;
    showEarningsCodesColumn: boolean;
    showExpensesColumn: boolean;
    formatNumber: (value: number | null | undefined, format: 'n2' | 'c2') => string;
    // Timesheet context for mutations
    timeSheetId: string;
    numericId: number;
    employerGuid: string;
    payPeriodEndDate: string | null | undefined;
}

const ResolvedPayStubRow: React.FC<ResolvedPayStubRowProps> = (props) => {
    const resolvedStub = useFragment(PayStubTable_payStubFragment, props.payStubRef);

    // Don't render if the resolved stub is null
    if (!resolvedStub) {
        return null;
    }

    return (
        <PayStubTableErrorBoundary payStubId={resolvedStub.id}>
            <PayStubRowWrapper
                payStub={props.payStubRef} // Pass fragment reference
                index={props.index}
                readOnly={props.readOnly}
                employees={props.employees?.filter((emp): emp is Employee => 'value' in emp && 'text' in emp && 'id' in emp)}
                showBonusColumn={props.showBonusColumn}
                showCostCenterColumn={props.showCostCenterColumn}
                showDTHoursColumn={props.showDTHoursColumn}
                showEarningsCodesColumn={props.showEarningsCodesColumn}
                showExpensesColumn={props.showExpensesColumn}
                formatNumber={props.formatNumber}
                payPeriodEndDate={props.payPeriodEndDate}
                timeSheetId={props.timeSheetId}
                numericId={props.numericId}
                employerGuid={props.employerGuid}
                // isNewRow prop removed
            />
        </PayStubTableErrorBoundary>
    );
};

// Note: TemporaryPayStubRow component removed as part of Task 5
// Optimistic updates in Relay now handle temporary PayStub rendering

export default PayStubTable;
