import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useFragment, graphql, useRelayEnvironment } from 'react-relay';
import { View, TextField, Button, ActionButton, Text, Flex, Form, NumberField } from '@adobe/react-spectrum';
import { useTimesheetUIStore } from '@/src/store/timesheetUIStore';
import type { PayStubEditForm_payStub$key } from '@/lib/relay/__generated__/PayStubEditForm_payStub.graphql';
import type { PayStubDomainModel } from '@/src/types/timesheet-domain';
import { isEqual } from 'lodash';
import styles from './PayStubEditForm.module.scss';

/**
 * GraphQL fragment for PayStub edit form data
 */
const editFormFragment = graphql`
    fragment PayStubEditForm_payStub on PayStub {
        id
        employeeId
        name
        totalHours
        details {
            id
            payStubId
            reportLineItemId
            workDate
            name
            stHours
            otHours
            dtHours
            totalHours
            jobCode
            earningsCode
            agreementId
            classificationId
            subClassificationId
            costCenter
            hourlyRate
            bonus
            expenses
        }
    }
`;

interface PayStubEditFormProps {
    payStub: PayStubEditForm_payStub$key;
    timeSheetId: string;
    numericId: number;
    employerGuid: string;
    index?: number; // For test IDs
}

export function PayStubEditForm({ payStub, timeSheetId, numericId, employerGuid, index }: PayStubEditFormProps) {
    const data = useFragment(editFormFragment, payStub);
    const environment = useRelayEnvironment();
    
    // Use Zustand store instead of Context
    // Phase 1: saveAllChanges removed - saves now handled by parent component
    const updatePayStubDraft = useTimesheetUIStore(state => state.updatePayStubDraft);
    const isSavingPayStub = useTimesheetUIStore(state => state.isSavingPayStub);
    const getErrorForPayStub = useTimesheetUIStore(state => state.getErrorForPayStub);
    const clearError = useTimesheetUIStore(state => state.clearError);
    const stopEditingPayStub = useTimesheetUIStore(state => state.stopEditingPayStub);
    const clearDraftForPayStub = useTimesheetUIStore(state => state.clearDraftForPayStub);

    // Track initial mount to prevent false draft creation on load
    const isInitialMount = useRef(true);

    // Helper function to check if the computed domain changes are identical to server data
    const isIdenticalToServerData = useCallback((domainChanges: Partial<PayStubDomainModel>) => {
        if (!data) return true; // If no server data, consider it identical to avoid writes
        
        // Normalize values for comparison to handle null/undefined/empty string differences
        const normalizeString = (val: any) => (val === null || val === undefined) ? '' : String(val);
        const normalizeNumber = (val: any) => (val === null || val === undefined) ? 0 : Number(val);
        
        // Compare basic fields with normalization
        if (normalizeString(domainChanges.name) !== normalizeString(data.name)) {
            console.log('🔍 PayStubEditForm: Name difference detected:', {
                draft: domainChanges.name,
                server: data.name
            });
            return false;
        }
        if (normalizeString(domainChanges.employeeId) !== normalizeString(data.employeeId)) {
            console.log('🔍 PayStubEditForm: EmployeeId difference detected:', {
                draft: domainChanges.employeeId,
                server: data.employeeId
            });
            return false;
        }
        
        // Compare details if they exist
        if (domainChanges.details && data.details) {
            if (domainChanges.details.length !== data.details.length) {
                console.log('🔍 PayStubEditForm: Details length difference detected:', {
                    draftLength: domainChanges.details.length,
                    serverLength: data.details.length
                });
                return false;
            }
            
            for (let i = 0; i < domainChanges.details.length; i++) {
                const draftDetail = domainChanges.details[i];
                const serverDetail = data.details[i];
                
                if (!serverDetail) {
                    console.log('🔍 PayStubEditForm: Missing server detail at index:', i);
                    return false;
                }
                
                // Compare key fields that would indicate actual changes
                if (normalizeNumber(draftDetail.hours?.standard) !== normalizeNumber(serverDetail.stHours)) {
                    console.log('🔍 PayStubEditForm: Standard hours difference detected:', {
                        draft: draftDetail.hours?.standard,
                        server: serverDetail.stHours
                    });
                    return false;
                }
                if (normalizeNumber(draftDetail.hours?.overtime) !== normalizeNumber(serverDetail.otHours)) {
                    console.log('🔍 PayStubEditForm: Overtime hours difference detected:', {
                        draft: draftDetail.hours?.overtime,
                        server: serverDetail.otHours
                    });
                    return false;
                }
                if (normalizeNumber(draftDetail.hours?.doubletime) !== normalizeNumber(serverDetail.dtHours)) {
                    console.log('🔍 PayStubEditForm: Doubletime hours difference detected:', {
                        draft: draftDetail.hours?.doubletime,
                        server: serverDetail.dtHours
                    });
                    return false;
                }
                if (normalizeNumber(draftDetail.amounts?.bonus) !== normalizeNumber(serverDetail.bonus)) {
                    console.log('🔍 PayStubEditForm: Bonus difference detected:', {
                        draft: draftDetail.amounts?.bonus,
                        server: serverDetail.bonus
                    });
                    return false;
                }
                if (normalizeNumber(draftDetail.amounts?.expenses) !== normalizeNumber(serverDetail.expenses)) {
                    console.log('🔍 PayStubEditForm: Expenses difference detected:', {
                        draft: draftDetail.amounts?.expenses,
                        server: serverDetail.expenses
                    });
                    return false;
                }
                if (normalizeString(draftDetail.job?.jobCode) !== normalizeString(serverDetail.jobCode)) {
                    console.log('🔍 PayStubEditForm: Job code difference detected:', {
                        draft: draftDetail.job?.jobCode,
                        server: serverDetail.jobCode
                    });
                    return false;
                }
                if (normalizeString(draftDetail.job?.costCenter) !== normalizeString(serverDetail.costCenter)) {
                    console.log('🔍 PayStubEditForm: Cost center difference detected:', {
                        draft: draftDetail.job?.costCenter,
                        server: serverDetail.costCenter
                    });
                    return false;
                }
                if (normalizeNumber(draftDetail.job?.hourlyRate) !== normalizeNumber(serverDetail.hourlyRate)) {
                    console.log('🔍 PayStubEditForm: Hourly rate difference detected:', {
                        draft: draftDetail.job?.hourlyRate,
                        server: serverDetail.hourlyRate
                    });
                    return false;
                }
                if (normalizeString(draftDetail.earnings?.earningsCode) !== normalizeString(serverDetail.earningsCode)) {
                    console.log('🔍 PayStubEditForm: Earnings code difference detected:', {
                        draft: draftDetail.earnings?.earningsCode,
                        server: serverDetail.earningsCode
                    });
                    return false;
                }
                if (normalizeString(draftDetail.agreements?.agreementId) !== normalizeString(serverDetail.agreementId)) {
                    console.log('🔍 PayStubEditForm: Agreement ID difference detected:', {
                        draft: draftDetail.agreements?.agreementId,
                        server: serverDetail.agreementId
                    });
                    return false;
                }
                if (normalizeString(draftDetail.agreements?.classificationId) !== normalizeString(serverDetail.classificationId)) {
                    console.log('🔍 PayStubEditForm: Classification ID difference detected:', {
                        draft: draftDetail.agreements?.classificationId,
                        server: serverDetail.classificationId
                    });
                    return false;
                }
                if (normalizeString(draftDetail.agreements?.subClassificationId) !== normalizeString(serverDetail.subClassificationId)) {
                    console.log('🔍 PayStubEditForm: Sub-classification ID difference detected:', {
                        draft: draftDetail.agreements?.subClassificationId,
                        server: serverDetail.subClassificationId
                    });
                    return false;
                }
            }
        }
        
        return true; // All comparisons passed, data is identical
    }, [data]);

    // Local form state for immediate updates
    const [formData, setFormData] = useState(() => ({
        name: data?.name || '',
        employeeId: data?.employeeId || 0,
        details:
            data?.details?.map((detail) => ({
                id: detail.id,
                payStubId: detail.payStubId,
                reportLineItemId: detail.reportLineItemId,
                workDate: detail.workDate,
                name: detail.name,
                stHours: detail.stHours,
                otHours: detail.otHours,
                dtHours: detail.dtHours,
                totalHours: detail.totalHours,
                jobCode: detail.jobCode,
                earningsCode: detail.earningsCode,
                agreementId: detail.agreementId,
                classificationId: detail.classificationId,
                subClassificationId: detail.subClassificationId,
                costCenter: detail.costCenter,
                hourlyRate: detail.hourlyRate,
                bonus: detail.bonus,
                expenses: detail.expenses
            })) || []
    }));

    const isSaving = data?.id ? isSavingPayStub(timeSheetId, data.id) : false;
    const error = data?.id ? getErrorForPayStub(timeSheetId, data.id) : null;

    // Memoize data to prevent infinite loops - only update when key properties change
    const memoizedData = useMemo(() => {
        if (!data) return null;
        return {
            id: data.id,
            name: data.name,
            employeeId: data.employeeId,
            details: data.details
        };
    }, [data]);

    // Update form state when fragment data changes
    useEffect(() => {
        if (memoizedData) {
            setFormData({
                name: memoizedData.name || '',
                employeeId: memoizedData.employeeId || 0,
                details:
                    memoizedData.details?.map((detail) => ({
                        id: detail.id,
                        payStubId: detail.payStubId,
                        reportLineItemId: detail.reportLineItemId || undefined,
                        workDate: detail.workDate,
                        name: detail.name || undefined,
                        stHours: detail.stHours,
                        otHours: detail.otHours,
                        dtHours: detail.dtHours,
                        totalHours: detail.totalHours,
                        jobCode: detail.jobCode,
                        earningsCode: detail.earningsCode,
                        agreementId: detail.agreementId,
                        classificationId: detail.classificationId,
                        subClassificationId: detail.subClassificationId,
                        costCenter: detail.costCenter,
                        hourlyRate: detail.hourlyRate,
                        bonus: detail.bonus,
                        expenses: detail.expenses
                    })) || []
            });
        }
    }, [memoizedData]);

    // Memoize form data changes to prevent circular dependencies
    const memoizedFormChanges = useMemo(() => {
        if (!formData) return null;
        return {
            name: formData.name,
            employeeId: formData.employeeId || 0,
            details: formData.details.map((detail) => ({
                id: detail.id,
                payStubId: detail.payStubId,
                reportLineItemId: detail.reportLineItemId,
                name: detail.name,
                workDate: detail.workDate,
                stHours: detail.stHours,
                otHours: detail.otHours,
                dtHours: detail.dtHours,
                totalHours: (detail.stHours || 0) + (detail.otHours || 0) + (detail.dtHours || 0),
                jobCode: detail.jobCode,
                earningsCode: detail.earningsCode,
                agreementId: detail.agreementId,
                classificationId: detail.classificationId,
                subClassificationId: detail.subClassificationId,
                costCenter: detail.costCenter,
                hourlyRate: detail.hourlyRate,
                bonus: detail.bonus,
                expenses: detail.expenses
            }))
        };
    }, [formData]);

    // Update draft in store as user types - but prevent false initial drafts
    useEffect(() => {
        if (memoizedFormChanges && data?.id) {
            // Convert form data to domain model format
            const domainChanges: Partial<PayStubDomainModel> = {
                name: memoizedFormChanges.name,
                employeeId: memoizedFormChanges.employeeId?.toString(),
                hours: {
                    standard: 0,
                    overtime: 0,
                    doubletime: 0,
                    total: 0
                },
                amounts: {
                    bonus: 0,
                    expenses: 0
                },
                details: memoizedFormChanges.details?.map(detail => ({
                    id: detail.id,
                    payStubId: detail.payStubId,
                    reportLineItemId: detail.reportLineItemId || undefined,
                    workDate: detail.workDate,
                    name: detail.name || undefined,
                    hours: {
                        standard: detail.stHours || 0,
                        overtime: detail.otHours || 0,
                        doubletime: detail.dtHours || 0,
                        total: detail.totalHours || 0
                    },
                    job: {
                        jobCode: detail.jobCode || undefined,
                        costCenter: detail.costCenter || undefined,
                        hourlyRate: detail.hourlyRate || undefined
                    },
                    amounts: {
                        bonus: detail.bonus || 0,
                        expenses: detail.expenses || 0
                    },
                    earnings: {
                        earningsCode: detail.earningsCode || undefined
                    },
                    agreements: {
                        agreementId: detail.agreementId || undefined,
                        classificationId: detail.classificationId || undefined,
                        subClassificationId: detail.subClassificationId || undefined
                    },
                    employeeId: memoizedFormChanges.employeeId?.toString() || '',
                    dayName: '',
                    ui: {
                        isEditing: false,
                        hasErrors: false,
                        isSelected: false,
                        isTemporary: false,
                        validationErrors: []
                    }
                })) || []
            };
            
            // 🐛 DEBUG: Enhanced mount guard logging
            const isInitial = isInitialMount.current;
            const isIdentical = isIdenticalToServerData(domainChanges);
            
            console.log(`🔄 [PAYSTUB FORM DEBUG] Mount guard check`, {
                timestamp: new Date().toISOString(),
                payStubId: data.id,
                timeSheetId,
                isInitialMount: isInitial,
                isIdenticalToServerData: isIdentical,
                willSkipDraftCreation: isInitial && isIdentical,
                domainChanges: JSON.stringify(domainChanges, null, 2),
                serverData: data ? {
                    name: data.name || '',
                    employeeId: data.employeeId?.toString() || '',
                    detailsLength: data.details?.length || 0
                } : null
            });
            
            // Enhanced guard against false draft creation
            if (isInitialMount.current) {
                if (isIdenticalToServerData(domainChanges)) {
                    console.log('📋 PayStubEditForm: Skipping initial draft creation - data identical to server');
                    isInitialMount.current = false;
                    return;
                } else {
                    console.log('📋 PayStubEditForm: Creating draft on initial mount due to data differences');
                }
            }
            
            // Mark that we've passed the initial mount
            isInitialMount.current = false;
            
            console.log('📋 PayStubEditForm: Creating/updating draft for PayStub:', data.id);
            // PHASE 4: Convert domain model to flat format for store with complete schema
            const flatChanges = {
                // Core fields
                id: data.id, // Use data.id since domainChanges doesn't include id
                employeeId: domainChanges.employeeId && domainChanges.employeeId.trim() 
                    ? domainChanges.employeeId 
                    : undefined, // Leave undefined when blank so server default prevails
                name: domainChanges.name,
                totalHours: domainChanges.details?.reduce((sum, detail) => 
                    sum + (detail.hours?.total || 0), 0) || 0,
                
                // UI fields - complete minimal schema to prevent undefined reads
                _uiDelete: false,
                _uiIsTemporary: false,
                _uiExpanded: true,
                _uiSelected: false,
                _uiEditingMode: 'edit' as const,
                _uiValidationErrors: [],
                _uiLastModified: Date.now(),
                _uiModifiedBy: undefined, // Optional metadata
                _uiDraftId: undefined      // Optional metadata
            };
            
            updatePayStubDraft(timeSheetId, data.id, flatChanges);
        }
    }, [memoizedFormChanges, data?.id, timeSheetId, updatePayStubDraft, isIdenticalToServerData]);

    const handleDetailChange = useCallback((detailId: string, field: string, value: unknown) => {
        setFormData((prev) => ({
            ...prev,
            details: prev.details.map((detail) => (detail.id === detailId ? { ...detail, [field]: value } : detail))
        }));
    }, []);

    // Create memoized handlers for each field type to prevent inline function creation
    const createDetailChangeHandler = useCallback(
        (detailId: string, field: string) => {
            return (value: unknown) => handleDetailChange(detailId, field, value);
        },
        [handleDetailChange]
    );

    const handleSave = useCallback(() => {
        // Phase 1: Save functionality removed - saves are now handled by parent component
        // This component only manages draft state in the store
        // The parent TimesheetDetail component triggers saves via useSaveTimesheet hook
        if (data?.id) {
            console.log('PayStubEditForm: Save requested - notifying parent via draft state');
            // Draft changes are already tracked in the store via updatePayStubDraft
            // Parent will handle the actual save operation
        }
    }, [data?.id]);

    const handleCancel = useCallback(() => {
        if (data?.id) {
            clearDraftForPayStub(timeSheetId, data.id);
            clearError(timeSheetId, data.id);
        }
        stopEditingPayStub(timeSheetId);
    }, [data?.id, timeSheetId, clearDraftForPayStub, clearError, stopEditingPayStub]);

    // Memoize form submission handler
    const handleFormSubmit = useCallback(
        (e: React.FormEvent) => {
            e.preventDefault();
            void handleSave();
        },
        [handleSave]
    );

    // Memoize name change handler
    const handleNameChange = useCallback((value: string) => {
        setFormData((prev) => ({ ...prev, name: value }));
    }, []);

    // Memoize format options for currency fields
    const currencyFormatOptions = useMemo(
        () => ({
            style: 'currency' as const,
            currency: 'USD',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }),
        []
    );

    // Add keyboard shortcuts and accessibility - MOVED BEFORE early return to fix hooks violation
    useEffect(() => {
        if (!data?.id) return; // Guard clause to prevent running when no data

        const handleKeyDown = (e: KeyboardEvent) => {
            if (e.key === 'Escape' && !isSaving) {
                handleCancel();
            } else if (e.key === 'Enter' && e.ctrlKey && !isSaving) {
                e.preventDefault();
                // Phase 1: Ctrl+Enter now closes the form (changes auto-saved)
                handleCancel();
            }
        };

        document.addEventListener('keydown', handleKeyDown);
        return () => document.removeEventListener('keydown', handleKeyDown);
    }, [data?.id, isSaving, handleCancel]); // Only depend on data.id to avoid infinite loops

    // Early return if no data - AFTER all hooks
    if (!data) {
        return (
            <View padding="size-200">
                <Text>No PayStub data available</Text>
            </View>
        );
    }

    const calculateTotalHours = () => {
        return formData.details.reduce((sum, detail) => sum + (detail.stHours || 0) + (detail.otHours || 0) + (detail.dtHours || 0), 0);
    };

    return (
        <View padding="size-200" data-testid={index !== undefined ? `edit-form-ps-${index}` : undefined}>
            {error && (
                <View backgroundColor="negative" padding="size-100" marginBottom="size-200" borderRadius="medium">
                    <Text>{typeof error === 'string' ? error : error.message}</Text>
                </View>
            )}

            <Form onSubmit={handleFormSubmit}>
                <View marginBottom="size-200">
                    <TextField label="PayStub Name" value={formData.name} onChange={handleNameChange} isDisabled={isSaving} width="100%" />
                </View>

                <View marginBottom="size-200">
                    <Text>Time Details</Text>
                    <View borderWidth="thin" borderColor="dark" padding="size-150" borderRadius="medium">
                        {formData.details.map((detail, index) => (
                            <Flex key={detail.id} gap="size-100" marginBottom="size-100" wrap>
                                <View flex="1" minWidth="120px">
                                    <Text>{detail.name}</Text>
                                    <Text>{detail.workDate}</Text>
                                </View>

                                <NumberField
                                    label="ST Hours"
                                    value={detail.stHours || 0}
                                    onChange={createDetailChangeHandler(detail.id, 'stHours')}
                                    isDisabled={isSaving}
                                    step={0.5}
                                    minValue={0}
                                    maxValue={24}
                                    width="100px"
                                    autoFocus={index === 0 && detail === formData.details[0]}
                                    aria-describedby={`st-hours-help-${detail.id}`}
                                />

                                <NumberField
                                    label="OT Hours"
                                    value={detail.otHours || 0}
                                    onChange={createDetailChangeHandler(detail.id, 'otHours')}
                                    isDisabled={isSaving}
                                    step={0.5}
                                    minValue={0}
                                    maxValue={24}
                                    width="100px"
                                />

                                <NumberField
                                    label="DT Hours"
                                    value={detail.dtHours || 0}
                                    onChange={createDetailChangeHandler(detail.id, 'dtHours')}
                                    isDisabled={isSaving}
                                    step={0.5}
                                    minValue={0}
                                    maxValue={24}
                                    width="100px"
                                />

                                <TextField
                                    label="Job Code"
                                    value={detail.jobCode || ''}
                                    onChange={createDetailChangeHandler(detail.id, 'jobCode')}
                                    isDisabled={isSaving}
                                    width="120px"
                                />

                                <NumberField
                                    label="Hourly Rate"
                                    value={detail.hourlyRate || 0}
                                    onChange={createDetailChangeHandler(detail.id, 'hourlyRate')}
                                    isDisabled={isSaving}
                                    formatOptions={currencyFormatOptions}
                                    width="120px"
                                />
                            </Flex>
                        ))}

                        <View marginTop="size-200">
                            <Text>Total Hours: {calculateTotalHours().toFixed(2)}</Text>
                        </View>
                    </View>
                </View>

                <Flex direction="row" gap="size-150" justifyContent="end" marginTop="size-200">
                    <Button
                        variant="secondary"
                        onPress={handleCancel}
                        isDisabled={isSaving}
                        data-testid={index !== undefined ? `cancel-ps-${index}` : undefined}
                        aria-label="Cancel editing and discard changes">
                        Cancel
                    </Button>
                    <Button
                        variant="accent"
                        onPress={handleCancel}
                        isDisabled={isSaving}
                        data-testid={index !== undefined ? `save-ps-${index}` : undefined}
                        aria-label="Close edit form - changes saved automatically">
                        Done
                    </Button>
                </Flex>
            </Form>
        </View>
    );
}
