// src/components/TimesheetDetail/TimeSheetDetailTableView.tsx
import React, { useCallback, useMemo, useState, useEffect, useRef } from 'react';
import { graphql, useFragment } from 'react-relay';
import { View } from '@adobe/react-spectrum';
import { Employee, PayStubDetail } from '@/src/types/relay-ui-extensions';
import type { EmployeeSelector_employee$key } from '@/lib/relay/__generated__/EmployeeSelector_employee.graphql';
import { useTimesheetUIStore } from '@/src/store/timesheetUIStore';
import type { PayStubDetailDraftKeys } from '@/src/types';
import { useSubClassificationVisibility } from '@/src/hooks/useTimesheetSelectors';

import { TimeSheetDetailTable, TableColumn, editableColumnUids } from './TimeSheetDetailTable';
import { generateWeekDisplay, type DetailDisplayData } from '@/src/utils/timesheetDetailDataTransform';

// Relay Types
import { TimeSheetDetailTableView_payStub$key } from '@/lib/relay/__generated__/TimeSheetDetailTableView_payStub.graphql';
import type { TimeSheetDetailRow_employee$key } from '@/lib/relay/__generated__/TimeSheetDetailRow_employee.graphql';

/**
 * Updated PayStub fragment - removed PayStubEditForm dependency
 * Following RELAY_RULES.md #5: Parent fragments must spread child fragments
 */
export const TimeSheetDetailTableView_payStubFragment = graphql`
    fragment TimeSheetDetailTableView_payStub on PayStub {
        id
        employeeId
        name
        totalHours
        employee {
            ...TimeSheetDetailRow_employee
        }
        details {
            id
            workDate
            jobCode
            agreementId
            classificationId
            subClassificationId
            hourlyRate
            stHours
            otHours
            dtHours
            bonus
            expenses
            earningsCode
            costCenter
            payStubId
            reportLineItemId
            totalHours
            name
            ...TimeSheetDetailRow_payStubDetail
        }
    }
`;

/**
 * Props for the TimeSheetDetailTableView component
 */
interface TimeSheetDetailTableViewProps {
    payStub: TimeSheetDetailTableView_payStub$key;
    readOnly: boolean;
    employees?: ReadonlyArray<EmployeeSelector_employee$key> | ReadonlyArray<Employee>; // TODO: Remove this prop - not used, data comes from Relay fragments
    // Visibility flags
    showBonusColumn: boolean;
    showCostCenterColumn: boolean;
    showDTHoursColumn: boolean;
    showEarningsCodesColumn: boolean;
    showExpensesColumn: boolean;
    formatNumber: (value: number | null | undefined, format: 'n2' | 'c2') => string;
    // Add payPeriodEndDate prop
    payPeriodEndDate: string | null | undefined;
    // Add timesheet context props for edit form
    timeSheetId: string;
    numericId: number;
    employerGuid: string;
    // For test IDs
    payStubIndex?: number;
}

/**
 * Component that displays a detailed table view of timesheet data for a pay stub
 * Supports editing rows, keyboard navigation, and visibility toggles
 */
/**
 * Auto-fill cache interface for performance optimization
 */
interface AutoFillCacheEntry {
    processed: boolean; // Has this row been checked for auto-fill?
    appliedFields: string[]; // Which fields were auto-filled?
    employeeId: string; // Which employee (for cache invalidation)
}

const TimeSheetDetailTableView: React.FC<TimeSheetDetailTableViewProps> = ({
    payStub,
    readOnly,
    employees,
    showBonusColumn,
    showCostCenterColumn,
    showDTHoursColumn,
    showEarningsCodesColumn,
    showExpensesColumn,
    formatNumber,
    payPeriodEndDate,
    timeSheetId,
    numericId,
    employerGuid,
    payStubIndex
}) => {

    // Remove old cache hook - now using Relay data directly

    // Use Zustand store instead of Context - use correct APIs for details vs payStubs
    const getDetailDraft = useTimesheetUIStore(state => state.getDetailDraft);
    const updateDetailField = useTimesheetUIStore(state => state.updateDetailField);
    // CRITICAL FIX: Subscribe to draftsVersion to ensure React re-renders when drafts change
    const draftsVersion = useTimesheetUIStore(state => state.draftsVersion);

    // Auto-fill cache for performance optimization - only process each row once
    const autoFillCache = useRef(new Map<string, AutoFillCacheEntry>());

    // ✅ CORRECT: Call useFragment unconditionally (REACT_RULES.md #2)
    const payStubData = useFragment(TimeSheetDetailTableView_payStubFragment, payStub);

    const payStubEmployeeRef: TimeSheetDetailRow_employee$key | null = payStubData?.employee || null;
    const payStubEmployeeId: string | null = payStubData?.employeeId ? String(payStubData.employeeId) : null;

    // Clear auto-fill cache when payStub changes to reset auto-fill behavior
    useEffect(() => {
        autoFillCache.current.clear();
    }, [payStubData.id]);

    // Clear cache entries if employee changes for any row (rare edge case)
    useEffect(() => {
        const currentEmployeeId = payStubEmployeeId;
        if (currentEmployeeId) {
            autoFillCache.current.forEach((cached, detailId) => {
                if (cached.employeeId !== currentEmployeeId) {
                    autoFillCache.current.delete(detailId); // Allow re-processing with new employee
                }
            });
        }
    }, [payStubEmployeeId]);

    const payStubId = payStubData.id;

    // Use extracted data transformation utility
    const displayDetails: DetailDisplayData[] = useMemo(
        () => {
            const currentDetails = payStubData?.details ?? [];
            return generateWeekDisplay(payPeriodEndDate, currentDetails, payStubId, payStubEmployeeId);
        },
        [payPeriodEndDate, payStubData?.details, payStubId, payStubEmployeeId]
    );

    // Calculate visibility specifically for this table's details using proper hook
    // DetailDisplayData has the required fields (agreementId, classificationId) for sub-classification visibility check
    const showSubClassificationColumnForThisTable = useSubClassificationVisibility(
        displayDetails as unknown as ReadonlyArray<PayStubDetail>,
        new Map() // Empty map since we're using draft system now
    );

    // Keyboard navigation and editing state is now handled in TimeSheetDetailTable component

    /**
     * Define table columns based on configuration settings and calculated visibility
     */
    const columns: ReadonlyArray<TableColumn> = useMemo(() => {
        const cols: (TableColumn | false)[] = [
            !readOnly && { uid: 'actions', name: '', width: 50 },
            { name: 'Day', uid: 'name', width: 110 },
            { name: 'Work Date', uid: 'workDate', width: 110 },
            { name: 'Job Code', uid: 'jobCode', width: 150 },
            showEarningsCodesColumn && { name: 'Earnings Code', uid: 'earningsCode', width: 150 },
            { name: 'Agreement', uid: 'agreementId', width: 200 },
            { name: 'Classification', uid: 'classificationId', width: 200 },
            showSubClassificationColumnForThisTable && { name: 'Sub-Classification', uid: 'subClassificationId', width: 200 },
            showCostCenterColumn && { name: 'Cost Center', uid: 'costCenter', width: 120 },
            { name: 'Hourly Rate', uid: 'hourlyRate', width: 130 },
            { name: 'ST Hours', uid: 'stHours', width: 100 },
            { name: 'OT Hours', uid: 'otHours', width: 100 },
            showDTHoursColumn && { name: 'DT Hours', uid: 'dtHours', width: 100 },
            { name: 'Total Hours', uid: 'totalHours', width: 110 },
            showBonusColumn && { name: 'Direct Pay', uid: 'bonus', width: 130 },
            showExpensesColumn && { name: 'Expenses', uid: 'expenses', width: 130 }
        ];
        return cols.filter((c): c is TableColumn => !!c);
    }, [
        readOnly,
        showBonusColumn,
        showCostCenterColumn,
        showDTHoursColumn,
        showEarningsCodesColumn,
        showExpensesColumn,
        showSubClassificationColumnForThisTable
    ]);

    /**
     * Determines if a cell can be edited based on position and column properties,
     * and the cell's dynamic data dependencies
     */
    const isCellEditable = useCallback(
        (rowIndex: number, colIndex: number): boolean => {
            // Basic validation checks
            if (rowIndex < 0 || rowIndex >= displayDetails.length || colIndex < 0 || colIndex >= columns.length) {
                return false;
            }

            // Get column being checked
            const column = columns[colIndex];

            // Initial check: column must exist, we're not in readOnly mode, and column is in editable set
            if (!column || readOnly || !editableColumnUids.has(column.uid)) {
                return false;
            }

            // Get the detail data for this row and check if it's marked for deletion
            const detailRef = displayDetails[rowIndex];
            if (!detailRef) {
                return false;
            }
            
            // Check for deletion using both _uiDelete (from drafts) and delete (from server data)
            const detailId = detailRef.id;
            if (!detailId) {
                return false;
            }
            
            // Use draft data if available for this detail
            const draft = getDetailDraft(timeSheetId, detailId) || {};
            const isDeleted = draft._uiDelete ?? detailRef.delete ?? false;
            if (isDeleted) {
                return false;
            }
            const mergedData = {
                ...detailRef,
                ...draft
            };

            // Determine effective values for dependent fields like Agreement and Classification
            const effectiveAgreementId = mergedData.agreementId;
            const effectiveClassificationId = mergedData.classificationId;

            // Apply dynamic editability rules based on column type
            switch (column.uid) {
                case 'classificationId':
                    // Classification requires an Agreement to be selected
                    return effectiveAgreementId != null;

                case 'subClassificationId':
                    // Sub-Classification requires both Agreement and Classification
                    // The ComboBox component will handle data fetching and availability
                    return effectiveAgreementId != null && effectiveClassificationId != null;

                // For all other editable columns, apply any specific constraints
                // (Ex: conditionally hidden columns that should be skipped)
                case 'bonus':
                    return showBonusColumn;
                case 'expenses':
                    return showExpensesColumn;
                case 'dtHours':
                    return showDTHoursColumn;
                case 'costCenter':
                    return showCostCenterColumn;
                case 'earningsCode':
                    return showEarningsCodesColumn;

                // All other columns don't have dynamic dependencies
                default:
                    return true;
            }
        },
        [
            columns,
            displayDetails,
            readOnly,
            showBonusColumn,
            showCostCenterColumn,
            showDTHoursColumn,
            showEarningsCodesColumn,
            showExpensesColumn,
            getDetailDraft
        ]
    );

    // Keyboard navigation logic moved to useKeyboardNavigation hook and TimeSheetDetailTable component

    // --- Row Actions (Remain here, passed down) ---
    const handleRowDeleteToggle = useCallback(
        (detailId: string) => {
            if (readOnly) return;
            const currentDraft = getDetailDraft(timeSheetId, detailId) || {};
            // PHASE 3: Toggle deletion state using correct field name
            const newDeleteState = !currentDraft._uiDelete;
            
            // Find the original detail to preserve important fields like workDate
            const originalDetail = displayDetails.find(d => d.id === detailId);
            
            updateDetailField(timeSheetId, detailId, '_uiDelete', newDeleteState, payStubId, originalDetail ? { workDate: originalDetail.workDate } : undefined);
        },
        [readOnly, timeSheetId, updateDetailField, getDetailDraft, payStubId, displayDetails]
    );

    const handleRowDuplicate = useCallback(
        (detailIndex: number) => {
            if (readOnly) {
                return;
            }

            // Get the source detail to duplicate
            const sourceDetail = displayDetails[detailIndex];
            if (!sourceDetail || !sourceDetail.id) {
                return;
            }

            // Create a new detail from the source using proper DetailDisplayData type
            // ✅ PHASE 2.3: Use consistent client temp ID pattern instead of UUID
            const newDetailId = `client:temp:detail:${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            const newDetail = {
                id: newDetailId,
                payStubId: payStubData.id,
                reportLineItemId: undefined,
                workDate: sourceDetail.workDate,
                name: sourceDetail.name || undefined,
                stHours: sourceDetail.stHours,
                otHours: sourceDetail.otHours,
                dtHours: sourceDetail.dtHours,
                totalHours: sourceDetail.totalHours || 0,
                jobCode: sourceDetail.jobCode,
                earningsCode: sourceDetail.earningsCode,
                agreementId: sourceDetail.agreementId,
                classificationId: sourceDetail.classificationId,
                subClassificationId: sourceDetail.subClassificationId,
                costCenter: sourceDetail.costCenter,
                hourlyRate: sourceDetail.hourlyRate,
                bonus: sourceDetail.bonus,
                expenses: sourceDetail.expenses,
                delete: false,
                employeeId: payStubEmployeeId ? Number(payStubEmployeeId) : 0, // Default to 0 instead of undefined
                isTemporary: false
            };

            // Add the detail via draft - convert to domain model format
            const domainDetail = {
                id: newDetailId,
                payStubId: payStubData.id,
                reportLineItemId: undefined,
                workDate: sourceDetail.workDate,
                name: sourceDetail.name || undefined,
                dayName: '',
                hours: {
                    standard: sourceDetail.stHours || 0,
                    overtime: sourceDetail.otHours || 0,
                    doubletime: sourceDetail.dtHours || 0,
                    total: sourceDetail.totalHours || 0
                },
                job: {
                    jobCode: sourceDetail.jobCode || undefined,
                    costCenter: sourceDetail.costCenter || undefined,
                    hourlyRate: sourceDetail.hourlyRate || undefined
                },
                earnings: {
                    earningsCode: sourceDetail.earningsCode || undefined
                },
                agreements: {
                    agreementId: sourceDetail.agreementId || undefined,
                    classificationId: sourceDetail.classificationId || undefined,
                    subClassificationId: sourceDetail.subClassificationId || undefined
                },
                amounts: {
                    bonus: sourceDetail.bonus || 0,
                    expenses: sourceDetail.expenses || 0
                },
                employeeId: payStubEmployeeId || '',
                ui: {
                    isEditing: false,
                    hasErrors: false,
                    isSelected: false,
                    isTemporary: true,
                    validationErrors: []
                }
            };
            // PHASE 3: Details are managed separately - this might need to be updateDetailField instead
            // For now, skip this operation as it seems incorrectly structured
            console.warn('PayStub detail duplication needs to be handled via detail-specific operations');
        },
        [readOnly, displayDetails, payStubData.id, payStubEmployeeId, timeSheetId]
    );
    // --- End Row Actions ---

    // Create temporaryPayStubDetailEdits Map from current draft data
    const temporaryPayStubDetailEdits = useMemo(() => {
        const editsMap = new Map<string, any>(); // PHASE 3: Updated for flat types compatibility
        displayDetails.forEach((detail) => {
            if (detail.id) {
                const draft = getDetailDraft(timeSheetId, detail.id);
                if (draft) {
                    editsMap.set(detail.id, draft);
                }
            }
        });
        return editsMap;
    }, [displayDetails, timeSheetId, getDetailDraft, draftsVersion]); // CRITICAL FIX: Add draftsVersion to trigger re-renders

    // Implement updateTemporaryPayStubDetailEdit using the existing draft system
    const updateTemporaryPayStubDetailEdit = useCallback(
        (detailId: string, field: PayStubDetailDraftKeys, value: unknown) => {
            if (readOnly) return;
            
            // Find the original detail to preserve important fields like workDate
            const originalDetail = displayDetails.find(d => d.id === detailId);
            
            // Pass payStubId to ensure draft contains linking field for proper filtering
            // Also pass originalDetail to preserve workDate and other important fields
            updateDetailField(timeSheetId, detailId, field, value, payStubId, originalDetail ? { workDate: originalDetail.workDate } : undefined);
        },
        [readOnly, timeSheetId, updateDetailField, payStubId, displayDetails]
    );

    /**
     * Auto-fill employee default values for empty cells in a detail row
     * Executes exactly once per row with caching for optimal performance
     */
    const applyEmployeeDefaultsOnce = useCallback(
        (detailId: string, employeeDefaults: any) => {
            // Early exit: already processed this row
            const cached = autoFillCache.current.get(detailId);
            if (cached?.processed) {
                if (process.env.NODE_ENV === 'development') {
                }
                return;
            }

            const currentEmployeeId = payStubEmployeeId;

            // Early exit: no defaults available or in read-only mode
            if (!employeeDefaults || !currentEmployeeId || readOnly) {
                // Cache the "no defaults" result to avoid future checks
                autoFillCache.current.set(detailId, {
                    processed: true,
                    appliedFields: [],
                    employeeId: currentEmployeeId || ''
                });
                return;
            }

            if (process.env.NODE_ENV === 'development') {
            }

            // Get current data for this detail row
            const currentDraft = getDetailDraft(timeSheetId, detailId) || {};
            const detailData = displayDetails.find((d) => d.id === detailId);
            const mergedData = { ...detailData, ...currentDraft };

            const appliedFields: string[] = [];

            // Auto-fill agreement if empty
            if (!mergedData.agreementId && employeeDefaults.defaultAgreementId) {
                updateTemporaryPayStubDetailEdit(detailId, 'agreementId', employeeDefaults.defaultAgreementId);
                appliedFields.push('agreementId');

                if (process.env.NODE_ENV === 'development') {
                }
            }

            // Auto-fill classification if empty and agreement is available
            const effectiveAgreementId = mergedData.agreementId || employeeDefaults.defaultAgreementId;
            if (!mergedData.classificationId && employeeDefaults.defaultClassificationId && effectiveAgreementId) {
                updateTemporaryPayStubDetailEdit(detailId, 'classificationId', employeeDefaults.defaultClassificationId);
                appliedFields.push('classificationId');

                if (process.env.NODE_ENV === 'development') {
                }
            }

            // Auto-fill hourly rate if empty
            if (!mergedData.hourlyRate && employeeDefaults.defaultHourlyRate) {
                const rateAsNumber = parseFloat(employeeDefaults.defaultHourlyRate);
                if (!isNaN(rateAsNumber)) {
                    updateTemporaryPayStubDetailEdit(detailId, 'hourlyRate', rateAsNumber);
                    appliedFields.push('hourlyRate');

                    if (process.env.NODE_ENV === 'development') {
                    }
                }
            }

            // ✅ Cache the result - never process this row again
            autoFillCache.current.set(detailId, {
                processed: true,
                appliedFields,
                employeeId: currentEmployeeId
            });
        },
        [payStubEmployeeId, readOnly, timeSheetId, getDetailDraft, updateTemporaryPayStubDetailEdit, displayDetails]
    );

    return (
        <TimeSheetDetailTable
            displayDetails={displayDetails}
            columns={columns}
            payStubEmployeeRef={payStubEmployeeRef}
            payStubIndex={payStubIndex}
            payStubId={payStubData.id}
            timesheetId={timeSheetId}
            readOnly={readOnly}
            showBonusColumn={showBonusColumn}
            showCostCenterColumn={showCostCenterColumn}
            showDTHoursColumn={showDTHoursColumn}
            showEarningsCodesColumn={showEarningsCodesColumn}
            showExpensesColumn={showExpensesColumn}
            formatNumber={formatNumber}
            handleRowDeleteToggle={handleRowDeleteToggle}
            handleRowDuplicate={handleRowDuplicate}
            isCellEditable={isCellEditable}
            temporaryPayStubDetailEdits={temporaryPayStubDetailEdits}
            updateTemporaryPayStubDetailEdit={updateTemporaryPayStubDetailEdit}
            employerGuid={employerGuid}
            applyEmployeeDefaultsOnce={applyEmployeeDefaultsOnce}
        />
    );
};

export default TimeSheetDetailTableView;
