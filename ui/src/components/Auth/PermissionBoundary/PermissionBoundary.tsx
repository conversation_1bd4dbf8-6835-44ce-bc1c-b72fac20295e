import { PropsWithChildren } from 'react';
import { useLocation } from 'react-router';
import { useStore, StoreState } from '@/lib';
import { AuthUtils } from '@/src/core/auth/auth.utils';
import { AccessControlledRoute } from '@/src/types/routes';

type Props = PropsWithChildren<{
    protectedRoutes: AccessControlledRoute[];
}>;

const PermissionBoundary = ({ children, protectedRoutes }: Props) => {
    const { pathname } = useLocation();
    const user = useStore((state: StoreState) => state.user);
    
    // If user not loaded yet, deny access
    if (!user) {
        return null;
    }
    
    const hasPermission = AuthUtils.hasRequiredRoutePermissions(protectedRoutes, user.roles, pathname);

    if (!hasPermission) {
        return null;
    }

    return children;
};

export default PermissionBoundary;
