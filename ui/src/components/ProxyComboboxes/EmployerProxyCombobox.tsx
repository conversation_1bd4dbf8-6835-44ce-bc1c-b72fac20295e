import React, { useEffect, useState } from 'react';
import { Constants } from '@/src/constants/global';
import { Flex } from '@adobe/react-spectrum';
import { ClientUtils } from "@/lib/core/ClientUtils";
import Combobox from '../UI/ProxyCombobox/ProxyCombobox';
import { ProxyComboboxOption } from './types';
import { graphql, usePreloadedQuery, PreloadedQuery, useFragment } from 'react-relay/hooks';
import { EmployerProxyComboboxFragment$key } from '@/src/relay/__generated__/EmployerProxyComboboxFragment.graphql';
import { EmployerProxyComboboxQuery as EmployerProxyComboboxQueryType } from '@/src/relay/__generated__/EmployerProxyComboboxQuery.graphql';
import { StoreState, useStore } from '@/lib';

interface EmployerProxyComboboxProps {
    currentValue: number | null;
    onChange: (value: ProxyComboboxOption | null) => void;
    queryRef: PreloadedQuery<EmployerProxyComboboxQueryType>;
}

export const EmployerProxyComboboxQuery = graphql`
    query EmployerProxyComboboxQuery($chapterId: ID!) {
        ...EmployerProxyComboboxFragment @arguments(chapterId: $chapterId)
    }
`;

const EmployerProxyComboboxFragment = graphql`
    fragment EmployerProxyComboboxFragment on Query
    @argumentDefinitions(chapterId: { type: "ID!" }, first: { type: "Int", defaultValue: 10000 }) {
        employersByChapterId(chapterId: $chapterId, first: $first) {
            nodes {
                id
                organization {
                    name
                }
                root {
                    guid
                }
            }
        }
        __id
    }
`;

const getProxyComboboxOptionFromEmployer = (employer: any): ProxyComboboxOption => {
    return {
        label: employer.organization.name,
        value: employer.root.guid,
        guid: employer.root.guid,
        id: employer.id
    };
};

const EmployerProxyCombobox: React.FC<EmployerProxyComboboxProps> = (props) => {
    const setProxyEmployersConnectionId = useStore((state: StoreState) => state.setProxyEmployersConnectionId);
    const [sortedData, setSortedData] = useState<ProxyComboboxOption[]>([]);

    const rootData = usePreloadedQuery<EmployerProxyComboboxQueryType>(EmployerProxyComboboxQuery, props.queryRef);
    const data = useFragment<EmployerProxyComboboxFragment$key>(EmployerProxyComboboxFragment, rootData);

    useEffect(() => {
        setProxyEmployersConnectionId(data.__id);
    }, [data.__id, setProxyEmployersConnectionId]);

    useEffect(() => {
        if (data && !props.currentValue) {
            const cachedGuid = ClientUtils.getCookie(Constants.Cookies.selectedEmployerID);

            if (cachedGuid) {
                const cachedOption = data.employersByChapterId?.nodes?.find((e) => e?.root?.guid === cachedGuid);
                if (cachedOption) {
                    props.onChange(getProxyComboboxOptionFromEmployer(cachedOption));
                }
            } else if (data.employersByChapterId?.nodes && data.employersByChapterId.nodes[0]?.root?.guid) {
                props.onChange(getProxyComboboxOptionFromEmployer(data.employersByChapterId.nodes[0]));
            }
        } else {
            const currentValueEntry = data.employersByChapterId?.nodes?.find((e) => e.root?.guid === props.currentValue);
            if (!currentValueEntry && data.employersByChapterId?.nodes && data.employersByChapterId.nodes[0]?.id) {
                props.onChange(getProxyComboboxOptionFromEmployer(data.employersByChapterId.nodes[0]));
            }
        }

        const sortedChaptersData =
            data.employersByChapterId?.nodes
                ?.map((e) => getProxyComboboxOptionFromEmployer(e))
                .sort((a, b) => {
                    const textA = a.label.toUpperCase();
                    const textB = b.label.toUpperCase();
                    return textA < textB ? -1 : textA > textB ? 1 : 0;
                }) ?? [];

        setSortedData(sortedChaptersData);
    }, [data, props, props.currentValue]);

    return (
        <Flex direction={'column'}>
            <Combobox
                title={'Employer'}
                data={sortedData || []}
                placeholder="Select Employer"
                currentValue={props.currentValue}
                onChange={props.onChange}
                styles={{ width: 'size-4600' }}
            />
        </Flex>
    );
};

export default EmployerProxyCombobox;
