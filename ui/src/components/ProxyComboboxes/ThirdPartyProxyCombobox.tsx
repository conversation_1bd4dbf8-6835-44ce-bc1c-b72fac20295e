import React, { useEffect } from 'react';
import { Constants } from '@/src/constants/global';
import { Flex } from '@adobe/react-spectrum';
import { ClientUtils } from "@/lib/core/ClientUtils";
import { useStore, StoreState } from '@/lib';
import Combobox from '../UI/ProxyCombobox/ProxyCombobox';
import { ProxyComboboxOption } from './types';
import { graphql, usePreloadedQuery, PreloadedQuery, useFragment } from 'react-relay/hooks';
import { ThirdPartyProxyComboboxFragment$key } from '@/src/relay/__generated__/ThirdPartyProxyComboboxFragment.graphql';
import { ThirdPartyProxyComboboxQuery as ThirdPartyProxyComboboxQueryType } from '@/src/relay/__generated__/ThirdPartyProxyComboboxQuery.graphql';

interface ThirdPartyProxyComboboxProps {
    currentValue: number | null;
    onChange: (value: ProxyComboboxOption | null) => void;
    queryRef: PreloadedQuery<ThirdPartyProxyComboboxQueryType>;
}

export const ThirdPartyProxyComboboxQuery = graphql`
    query ThirdPartyProxyComboboxQuery($chapterId: Int!) {
        ...ThirdPartyProxyComboboxFragment @arguments(chapterId: $chapterId)
    }
`;

const ThirdPartyProxyComboboxFragment = graphql`
    fragment ThirdPartyProxyComboboxFragment on Query
    @argumentDefinitions(first: { type: "Int", defaultValue: 1000 }, chapterId: { type: "Int!" }) {
        thirdParties(chapterId: $chapterId, first: $first) @connection(key: "ThirdPartyProxyComboboxFragment_thirdParties") {
            edges {
                node {
                    label
                    value
                    guid
                    id
                }
            }
        }
        __id
    }
`;

const ThirdPartyProxyCombobox: React.FC<ThirdPartyProxyComboboxProps> = (props) => {
    // const setProxyThirdPartiesConnectionId = useStore((state: StoreState) => state.setProxyThirdPartiesConnectionId);

    const rootData = usePreloadedQuery<ThirdPartyProxyComboboxQueryType>(ThirdPartyProxyComboboxQuery, props.queryRef);

    const data = useFragment<ThirdPartyProxyComboboxFragment$key>(ThirdPartyProxyComboboxFragment, rootData);

    // useEffect(() => {
    //     setProxyThirdPartiesConnectionId(data.__id);
    // }, [data.__id]);

    useEffect(() => {
        if (data && !props.currentValue) {
            const cachedGuid = ClientUtils.getCookie(Constants.Cookies.selectedThirdPartyID);

            if (cachedGuid) {
                const cachedOption = data.thirdParties?.edges?.find((t) => t.node.guid === cachedGuid);

                if (cachedOption) {
                    props.onChange(cachedOption.node);
                }
            } else if (data.thirdParties?.edges && data.thirdParties.edges[0].node.value) {
                props.onChange(data.thirdParties.edges[0].node);
            }
        } else {
            const currentValueEntry = data.thirdParties?.edges?.find((t) => t.node.value === props.currentValue);

            if (!currentValueEntry && data.thirdParties?.edges && data.thirdParties.edges[0].node.value) {
                props.onChange(data.thirdParties.edges[0].node);
            }
        }
    }, [data, props, props.currentValue, props.onChange]);

    const sortedData = data.thirdParties?.edges
        ?.map((e) => e.node)
        .sort((a, b) => {
            const textA = a.label.toUpperCase();
            const textB = b.label.toUpperCase();
            return textA < textB ? -1 : textA > textB ? 1 : 0;
        });

    return (
        <Flex direction={'column'}>
            <Combobox
                title={'Third Party'}
                data={sortedData || []}
                placeholder="Select Third Party"
                currentValue={props.currentValue}
                onChange={props.onChange}
                styles={{ width: 'size-4600' }}
            />
        </Flex>
    );
};

export default ThirdPartyProxyCombobox;
