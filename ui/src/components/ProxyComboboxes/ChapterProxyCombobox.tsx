import { ReactNode, useEffect, useState } from 'react';
import { Flex } from '@adobe/react-spectrum';
import { ClientUtils } from "@/lib/core/ClientUtils";
import { useStore, StoreState } from '@/lib';
import { ProxyComboboxOption } from './types';
import Combobox from '../UI/ProxyCombobox/ProxyCombobox';
import { graphql, usePreloadedQuery, PreloadedQuery, useFragment } from 'react-relay/hooks';
import { ChapterProxyComboboxFragment$key } from '@/src/relay/__generated__/ChapterProxyComboboxFragment.graphql';
import { ChapterProxyComboboxQuery as ChapterProxyComboboxQueryType } from '@/src/relay/__generated__/ChapterProxyComboboxQuery.graphql';

interface ChapterProxyComboboxProps {
    currentValue: string | null;
    onChange: (value: ProxyComboboxOption | null) => void;
    queryRef: PreloadedQuery<ChapterProxyComboboxQueryType>;
    title?: string; // optional title prop - will be superseded by label
    label?: ReactNode;
    labelPosition?: 'top' | 'side';
    labelAlign?: 'start' | 'end';
    width?: string;
}

export const ProxyComboboxChapterQuery = graphql`
    query ChapterProxyComboboxQuery {
        ...ChapterProxyComboboxFragment
    }
`;

const ProxyComboboxChapterFragment = graphql`
    fragment ChapterProxyComboboxFragment on Query
    @argumentDefinitions(first: { type: "Int", defaultValue: 1000 }) {
        chapters(first: $first) @connection(key: "ChapterProxyComboboxFragment_chapters", filters: []) {
            edges {
                node {
                    label
                    value
                    guid
                    id
                }
            }
        }
        __id
    }
`;

const ChapterProxyCombobox: React.FC<ChapterProxyComboboxProps> = (props) => {
    const setProxyChaptersConnectionId = useStore((state: StoreState) => state.setProxyChaptersConnectionId);
    const [sortedData, setSortedData] = useState<ProxyComboboxOption[]>([]);

    const rootData = usePreloadedQuery<ChapterProxyComboboxQueryType>(ProxyComboboxChapterQuery, props.queryRef);
    const data = useFragment<ChapterProxyComboboxFragment$key>(ProxyComboboxChapterFragment, rootData);

    const { currentValue, onChange, queryRef, label, labelPosition, labelAlign, width } = props;

    useEffect(() => {
        setProxyChaptersConnectionId(data.__id);
    }, [data.__id, setProxyChaptersConnectionId]);

    useEffect(() => {
        if (data && !currentValue) {
            const encodedChapterId = ClientUtils.getEncodedChapterIdFromCookie();
            if (encodedChapterId) {
                const cachedOption = data.chapters?.edges?.find((c) => c.node.id === encodedChapterId);
                if (cachedOption) {
                    onChange(cachedOption.node);
                }
            } else if (data.chapters?.edges && data.chapters?.edges[0].node.value) {
                onChange(data.chapters?.edges[0].node);
            }
        }

        if (!sortedData || sortedData.length === 0) {
            const sortedChaptersData =
                data.chapters?.edges
                    ?.map((e) => e.node)
                    .sort((a, b) => {
                        const textA = a.label.toUpperCase();
                        const textB = b.label.toUpperCase();
                        return textA < textB ? -1 : textA > textB ? 1 : 0;
                    }) ?? [];

            setSortedData(sortedChaptersData);
        }
    }, [data, currentValue, sortedData, onChange]);

    return (
        <Flex direction={'column'}>
            <Combobox
                title={'Site Sponsor'}
                label={label}
                labelPosition={labelPosition}
                labelAlign={labelAlign}
                data={sortedData || []}
                placeholder="Select Site Sponsor"
                currentValue={currentValue}
                onChange={onChange}
                styles={{ width: width ?? '100%', minWidth: width ?? '100%' }}
            />
        </Flex>
    );
};

export default ChapterProxyCombobox;
