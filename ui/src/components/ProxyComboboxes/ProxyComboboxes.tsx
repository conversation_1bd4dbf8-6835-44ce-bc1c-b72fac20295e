import { Constants } from '@/src/constants/global';
import { useEffect, useState, useMemo } from 'react';
import { useQueryLoader } from 'react-relay';
import { Flex } from '@adobe/react-spectrum';
import { useLocation } from 'react-router';
import { ClientUtils } from "@/lib/core/ClientUtils";
import { useStore, StoreState } from '@/lib';
import { AuthUtils } from '@/src/core/auth/auth.utils';
import ChapterProxyCombobox from './ChapterProxyCombobox';
import EmployerProxyCombobox from './EmployerProxyCombobox';
import ThirdPartyProxyCombobox from './ThirdPartyProxyCombobox';
import { ProxyComboboxChapterQuery } from './ChapterProxyCombobox';
import { EmployerProxyComboboxQuery } from './EmployerProxyCombobox';
import { ThirdPartyProxyComboboxQuery } from './ThirdPartyProxyCombobox';
import PermissionBoundary from '../Auth/PermissionBoundary/PermissionBoundary';
import { employerAccessiblePages, sponsorAccessiblePages, thirdPartyAccessiblePages } from './data';
import { ChapterProxyComboboxQuery as ChapterProxyComboboxQueryType } from '@/src/relay/__generated__/ChapterProxyComboboxQuery.graphql';
import { EmployerProxyComboboxQuery as EmployerProxyComboboxQueryType } from '@/src/relay/__generated__/EmployerProxyComboboxQuery.graphql';
import { ThirdPartyProxyComboboxQuery as ThirdPartyProxyComboboxQueryType } from '@/src/relay/__generated__/ThirdPartyProxyComboboxQuery.graphql';
import { ProxyComboboxOption } from './types';

interface ProxyComboboxesProps {
    username: string;
}


const ProxyComboboxes: React.FC<ProxyComboboxesProps> = ({ username }) => {
    const [proxyComboboxChapterQueryRef, loadProxyComboboxChapterQuery] =
        useQueryLoader<ChapterProxyComboboxQueryType>(ProxyComboboxChapterQuery);

    const [proxyComboboxThirdPartyQueryRef, loadProxyComboboxThirdPartyQuery] =
        useQueryLoader<ThirdPartyProxyComboboxQueryType>(ThirdPartyProxyComboboxQuery);

    const [proxyComboboxEmployerQueryRef, loadProxyComboboxEmployerQuery] =
        useQueryLoader<EmployerProxyComboboxQueryType>(EmployerProxyComboboxQuery);

    const [employer, setEmployer] = useState<number | null>(null);

    const { pathname } = useLocation();
    const selectedChapterGuid = useStore((state: StoreState) => state.selectedChapterGuid);
    const selectedChapterNumericId = useStore((state: StoreState) => state.selectedChapterNumericId);
    const selectedChapterEncodedId = useStore((state: StoreState) => state.selectedChapterEncodedId);
    const selectedThirdParty = useStore((state: StoreState) => state.selectedThirdParty);
    const setSelectedThirdPartyGuid = useStore((state: StoreState) => state.setSelectedThirdPartyGuid);
    const setSelectedThirdParty = useStore((state: StoreState) => state.setSelectedThirdParty);
    const setSelectedEmployerGuid = useStore((state: StoreState) => state.setSelectedEmployerGuid);
    const user = useStore((state: StoreState) => state.user);
    const userRoles = useMemo(() => user ? user.roles : [], [user]);
    const setChapterGuid = useStore((state: StoreState) => state.setSelectedChapterGuid);
    const setConnectionIdEmplSelect = useStore((state: StoreState) => state.setProxyEmployersConnectionId);
    const setConnectionIdTP = useStore((state: StoreState) => state.setProxyThirdPartiesConnectionId);
    const setSelectedChapterName = useStore((state: StoreState) => state.setSelectedChapterName);

    const onThirdPartyChange = (thirdParty: ProxyComboboxOption | null) => {
        if (thirdParty) {
            setConnectionIdTP(thirdParty.value);
            setSelectedThirdPartyGuid(thirdParty.guid);
            setSelectedThirdParty(thirdParty.value);
        } else {
            setSelectedThirdPartyGuid(null);
            setSelectedThirdParty(null);
        }
    };

    const onEmployerChange = (employer: ProxyComboboxOption | null) => {
        if (employer) {
            setEmployer(employer.value);
            setConnectionIdEmplSelect(employer.value);
            setSelectedEmployerGuid(employer.guid);
        } else {
            setEmployer(null);
            setSelectedEmployerGuid(null);
        }
    };

    const fetchProxyComboboxData = () => {
        if (userRoles.includes(Constants.Roles.SystemAdministrator) || userRoles.includes(Constants.Roles.TradeAdministrator)) {
            loadProxyComboboxChapterQuery({});
        } else if (userRoles.includes(Constants.Roles.ChapterAdministrator) && selectedChapterNumericId) {
            loadProxyComboboxEmployerQuery({ chapterId: ClientUtils.encodeChapterId(selectedChapterNumericId.toString()) });
        }
    };

    useEffect(() => {
        fetchProxyComboboxData();
    }, []); // eslint-disable-line react-hooks/exhaustive-deps

    useEffect(() => {
        if (username) {
            fetchProxyComboboxData();
        }
    }, [username]); // eslint-disable-line react-hooks/exhaustive-deps

    useEffect(() => {
        const hasThirdPartyPermission = AuthUtils.hasRequiredRoutePermissions(thirdPartyAccessiblePages, userRoles, pathname);
        const hasEmployerPermission = AuthUtils.hasRequiredRoutePermissions(employerAccessiblePages, userRoles, pathname);
        const hasChapterPermission = AuthUtils.hasRequiredRoutePermissions(sponsorAccessiblePages, userRoles, pathname);
        if (hasChapterPermission && selectedChapterGuid && selectedChapterNumericId) {
            if (
                hasThirdPartyPermission &&
                (userRoles.includes(Constants.Roles.SystemAdministrator) || userRoles.includes(Constants.Roles.TradeAdministrator))
            ) {
                // Use the numeric ID format for third party queries - typically these expect a number
                loadProxyComboboxThirdPartyQuery({ chapterId: selectedChapterNumericId });
            }
            if (hasEmployerPermission) {
                // Use the encoded chapter ID for GraphQL queries
                if (selectedChapterEncodedId) {
                    loadProxyComboboxEmployerQuery({ chapterId: selectedChapterEncodedId });
                } else if (selectedChapterNumericId) {
                    // If we have the numeric ID but not the encoded ID, encode it
                    loadProxyComboboxEmployerQuery({
                        chapterId: ClientUtils.encodeChapterId(selectedChapterNumericId.toString())
                    });
                } else if (selectedChapterGuid) {
                    // If we only have the GUID, log a warning - this should be handled better
                    console.warn('Using GUID for GraphQL query which may cause issues. Expected encoded ID.', selectedChapterGuid);
                    loadProxyComboboxEmployerQuery({ chapterId: selectedChapterGuid });
                }
            }
        }
    }, [selectedChapterGuid, selectedChapterNumericId, selectedChapterEncodedId, loadProxyComboboxThirdPartyQuery, loadProxyComboboxEmployerQuery, userRoles, pathname]);

    const setSelectedChapter = (chapter: ProxyComboboxOption | null) => {
        if (chapter) {
            setSelectedChapterName(chapter.label);
            setChapterGuid(chapter.guid, chapter.value, chapter.guid);
        }
    };

    return (
        <Flex direction={'row'} gap={16}>
            {proxyComboboxChapterQueryRef && (
                <PermissionBoundary protectedRoutes={sponsorAccessiblePages}>
                    <ChapterProxyCombobox
                        currentValue={selectedChapterNumericId !== null ? selectedChapterNumericId.toString() : null}
                        onChange={setSelectedChapter}
                        queryRef={proxyComboboxChapterQueryRef}
                    />
                </PermissionBoundary>
            )}
            {proxyComboboxThirdPartyQueryRef && (
                <PermissionBoundary protectedRoutes={thirdPartyAccessiblePages}>
                    <ThirdPartyProxyCombobox
                        currentValue={selectedThirdParty}
                        onChange={onThirdPartyChange}
                        queryRef={proxyComboboxThirdPartyQueryRef}
                    />
                </PermissionBoundary>
            )}
            {proxyComboboxEmployerQueryRef && (
                <PermissionBoundary protectedRoutes={employerAccessiblePages}>
                    <EmployerProxyCombobox currentValue={employer} onChange={onEmployerChange} queryRef={proxyComboboxEmployerQueryRef} />
                </PermissionBoundary>
            )}
        </Flex>
    );
};

export default ProxyComboboxes;
