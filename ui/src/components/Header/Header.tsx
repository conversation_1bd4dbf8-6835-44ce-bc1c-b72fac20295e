import styles from './Header.module.css';
import { useStore, StoreState } from '@/lib';
import { Link, useNavigate } from 'react-router';
import React, { useMemo, lazy, Suspense } from 'react';
import { <PERSON><PERSON>, <PERSON>u<PERSON><PERSON>ger, Item, <PERSON><PERSON>utton, Key, Flex } from '@adobe/react-spectrum';

type Props = {
    firstName: string;
    lastName: string;
    pageTitle: string;
};

const LazyAvatar = lazy(() => import('@adobe/react-spectrum').then((module) => ({ default: module.Avatar })));

const Header: React.FC<Props> = (props) => {
    const user = useStore((state: StoreState) => state.user);
    const orgGuid = user?.orgGUID;
    const navigate = useNavigate();

    const menuItems = useMemo(
        () => [
            { label: 'Profile', href: '/profile' },
            { label: 'Settings', href: '/account-settings' },
            {
                label: 'Employer Detail',
                href: `${import.meta.env.VITE_OLD_APP_URL}/Employer/Employer.aspx?GUID=${orgGuid}&UseChapterID=false`
            },
            { label: 'Logout', href: `${import.meta.env.VITE_OLD_APP_URL}/Logout.aspx` }
        ],
        [orgGuid]
    );

    const onMenuActionHandler = (key: Key) => {
        const index = Number(key);
        const item = menuItems[index];
        if (item) {
            // Check if the URL is external (starts with http or matches VITE_OLD_APP_URL)
            const isExternal = item.href.startsWith('http') || item.href.startsWith(import.meta.env.VITE_OLD_APP_URL);
            if (isExternal) {
                window.location.href = item.href;
            } else {
                void navigate(item.href);
            }
        }
    };

    return (
        <Flex data-testid="header" UNSAFE_className={styles.Container} justifyContent="space-between" alignItems="center">
            <Link to={import.meta.env.VITE_OLD_APP_URL || '/'}>
                <img src="/e24/epr_logo.jpg" alt="EPRLive" width="140" height="40" />
            </Link>
            <MenuTrigger>
                <ActionButton data-testid="my-account-dropdown-trigger" isQuiet marginEnd="size-100">
                    <Flex alignItems="center" gap="size-100">
                        <Suspense fallback={<div>Loading...</div>}>
                            <LazyAvatar
                                src={`https://ui-avatars.com/api/?name=${props.firstName[0]}+${props.lastName[0]}&background=EAF1FA&font-size=0.5`}
                                alt={`${props.firstName} ${props.lastName}`}
                                size="avatar-size-400"
                            />
                        </Suspense>
                    </Flex>
                </ActionButton>
                <Menu onAction={onMenuActionHandler}>
                    {menuItems.map((item, index) => (
                        <Item textValue={item.label} key={index}>
                            {item.label}
                        </Item>
                    ))}
                </Menu>
            </MenuTrigger>
        </Flex>
    );
};

export default Header;
