import { useState } from 'react';
import { graphql, useMutation } from 'react-relay';
import { useStore, StoreState } from '@/lib';
import ChangePassword from './ChangePassword/ChangePassword';
import EditInformation from './EditInformation/EditInformation';
import { But<PERSON>, Flex, Heading, Text } from '@adobe/react-spectrum';
import ContainerLoader from '@/src/components/UI/Loader/ContainerLoader/ContainerLoader';
import { PersonalDetailsChangePasswordMutationQuery$data } from '@/lib/relay/__generated__/PersonalDetailsChangePasswordMutationQuery.graphql';
import { PersonalDetailsChangePasswordMutationQuery as PersonalDetailsChangePasswordMutationQueryType } from '@/lib/relay/__generated__/PersonalDetailsChangePasswordMutationQuery.graphql';
import { ToastQueue } from '@react-spectrum/toast';
import { ToastMessages } from '@/src/constants/user-profile';

type Props = {};

export const PersonalDetailsChangePasswordMutationQuery = graphql`
    mutation PersonalDetailsChangePasswordMutationQuery($input: ChangePasswordInput!) {
        changePassword(input: $input) {
            string
        }
    }
`;

const PersonalDetails = ({ }: Props) => {
    const user = useStore((state: StoreState) => state.user);
    const [isOpenChangePasswordDialog, setIsOpenChangePasswordDialog] = useState(false);
    const [commitChangePassword, isCommitChangePasswordInFlight] = useMutation<PersonalDetailsChangePasswordMutationQueryType>(
        PersonalDetailsChangePasswordMutationQuery
    );

    const onOpenChangePasswordDialog = () => {
        setIsOpenChangePasswordDialog(true);
    };

    const onCloseChangePasswordDialog = () => {
        setIsOpenChangePasswordDialog(false);
    };

    const onChangePasswordHandler = (
        data: { currentPassword: string; newPassword: string; confirmPassword: string },
        close: { (): void; (): void }
    ) => {
        commitChangePassword({
            variables: {
                input: data
            },
            onCompleted: (response: any) => {
                const responseMessage = response?.changePassword?.string;
                if (responseMessage === 'Password changed successfully.') {
                    ToastQueue.positive(`${ToastMessages.SUCCESS_UPDATING_PASSWORD}`, { timeout: 7000 });
                    close();
                } else {
                    ToastQueue.negative(`${ToastMessages.ERROR_UPDATING_PASSWORD}`, {
                        timeout: 7000
                    });
                }
            },
            onError: (error) => {
                if (error.cause && Array.isArray(error.cause) && error.cause.length > 0) {
                    ToastQueue.negative(`${ToastMessages.ERROR_UPDATING_PASSWORD}: ${error.cause[0].message}`, {
                        timeout: 7000
                    });
                } else {
                    ToastQueue.negative(`${ToastMessages.ERROR_UPDATING_PASSWORD}: ${error.message}`, {
                        timeout: 7000
                    });
                }
            }
        });
    };

    if (!user) return <ContainerLoader />;

    return (
        <>
            <Flex direction={'column'} width="100%">
                <Flex gap="size-100" alignItems="center">
                    <Heading level={3}>Personal Details</Heading>
                </Flex>

                <Flex direction={'row'} marginY={'size-400'} gap={'size-400'}>
                    <Flex direction={'column'} justifyContent={'start'} alignItems="stretch">
                        <Text UNSAFE_className="Text14 TextGray700">Name</Text>
                        <Text UNSAFE_className="Text14 TextGray700">Organization</Text>
                        <Text UNSAFE_className="Text14 TextGray700">Username</Text>
                        <Text UNSAFE_className="Text14 TextGray700">Role(s)</Text>
                    </Flex>

                    <Flex direction={'column'} justifyContent={'start'}>
                        <Text UNSAFE_className="Text14 fontWeightSemiBold">
                            {user?.firstName} {user?.lastName}
                        </Text>

                        <Text UNSAFE_className="Text14 fontWeightSemiBold">{user?.orgName}</Text>
                        <Text UNSAFE_className="Text14 fontWeightSemiBold">
                            {user?.username} <Text UNSAFE_className="italicLight"> tied to the email {user?.email}</Text>
                        </Text>
                        <Text UNSAFE_className="Text14 fontWeightSemiBold">
                            {user?.roles?.join(', ') || 'No roles assigned'}
                        </Text>
                    </Flex>
                </Flex>

                <Flex gap={'size-200'}>
                    <Button variant="cta" style="outline" onPress={onOpenChangePasswordDialog}>
                        Change Password
                    </Button>

                    {/* <EditInformation /> */}
                </Flex>
            </Flex>

            {isOpenChangePasswordDialog && (
                <ChangePassword
                    isOpen
                    onCloseDialog={onCloseChangePasswordDialog}
                    onSubmit={onChangePasswordHandler}
                    isLoading={isCommitChangePasswordInFlight}
                />
            )}
        </>
    );
};

export default PersonalDetails;
