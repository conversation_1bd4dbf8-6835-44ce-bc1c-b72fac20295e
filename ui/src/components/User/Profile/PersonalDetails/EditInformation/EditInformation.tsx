import { useState } from 'react';
import { useStore, StoreState } from '@/lib';
import Edit from '@spectrum-icons/workflow/Edit';
import { ToastQueue } from '@react-spectrum/toast';
import { Form, TextField } from '@adobe/react-spectrum';
import { instance } from '@/src/core/http/axios-config';
import { ToastMessages } from '@/src/constants/user-profile';
import { DialogTrigger, Divider, Flex, Heading, Text } from '@adobe/react-spectrum';
import ContainerLoader from '@/src/components/UI/Loader/ContainerLoader/ContainerLoader';
import { ActionButton, Button, ButtonGroup, Content, Dialog } from '@adobe/react-spectrum';

type Props = {};

const EditInformation = ({}: Props) => {
    const user = useStore((state: StoreState) => state.user);
    const setUser = useStore((state: StoreState) => state.setUser);
    const [isSavingUserData, setIsSavingUserData] = useState(false);

    const onCancelHandler = (close: { (): void; (): void }) => {
        close();
    };

    const onSaveHandler = (e: React.FormEvent<HTMLFormElement>, close: { (): void; (): void }) => {
        e.preventDefault();

        const { firstName, lastName, email } = Object.fromEntries(new FormData(e.currentTarget));
        if (email === user?.email && firstName === user?.firstName && lastName === user?.lastName) {
            return;
        }

        setIsSavingUserData(true);
        instance
            .post('/api/Users', {
                username: user?.username,
                email: email,
                firstName: firstName,
                lastName: lastName
            })
            .then((response) => {
                if (response.status === 200) {
                    if (user) {
                        setUser({
                            ...user,
                            firstName: firstName.toString(),
                            lastName: lastName.toString(),
                            email: email.toString()
                        });
                    }
                    ToastQueue.positive(`${ToastMessages.SUCCESS_UPDATING_USER_DATA}`, { timeout: 7000 });
                    close();
                } else {
                    ToastQueue.negative(`${ToastMessages.ERROR_UPDATING_USER_DATA}`, { timeout: 7000 });
                }
                setIsSavingUserData(false);
            })
            .catch((err) => {
                ToastQueue.negative(`${ToastMessages.ERROR_UPDATING_USER_DATA}`, { timeout: 7000 });
                setIsSavingUserData(false);
            });
    };

    return (
        <DialogTrigger>
            <ActionButton isQuiet>
                <Edit size="XS" />
                <Text>Edit Information</Text>
            </ActionButton>
            {(close) => (
                <Dialog size="L">
                    <Heading>
                        <Flex alignItems="center" gap="size-100">
                            Edit Information
                        </Flex>
                    </Heading>
                    <Divider />
                    <Content>
                        <Form validationBehavior="native" onSubmit={(e: React.FormEvent<HTMLFormElement>) => onSaveHandler(e, close)}>
                            <Flex direction="row" gap="size-100">
                                <TextField
                                    isRequired
                                    type="text"
                                    name="firstName"
                                    minLength={2}
                                    maxLength={10}
                                    label="First name"
                                    flex={0.5}
                                    defaultValue={user?.firstName}
                                />
                                <TextField
                                    isRequired
                                    type="text"
                                    name="lastName"
                                    minLength={2}
                                    maxLength={10}
                                    label="Last name"
                                    flex={0.5}
                                    defaultValue={user?.lastName}
                                />
                            </Flex>

                            <Flex direction={'row'} gap="size-100">
                                <Flex direction={'column'} flex={0.5} height={'size-1000'}>
                                    <TextField
                                        autoFocus
                                        isRequired
                                        type="email"
                                        name="email"
                                        maxLength={30}
                                        label="Email address"
                                        width={'100%'}
                                        defaultValue={user?.email}
                                    />
                                </Flex>
                                <Flex flex={0.5} alignItems={'center'} height={'size-1000'}>
                                    <Button variant="accent" style="outline">
                                        Verify
                                    </Button>
                                </Flex>
                            </Flex>

                            <Text marginTop={'size-50'} UNSAFE_className="fontWeightLight">
                                You will need to verify a new email address.
                            </Text>

                            <Text marginTop={'size-300'} UNSAFE_className="fontStyleItalic fontWeightUltraLight textColorDisabled">
                                You cannot edit your username or company name.
                            </Text>

                            <Flex direction={'row'} gap="size-400">
                                <Flex direction={'column'}>
                                    <Text UNSAFE_className="fontWeightUltraLight">Username</Text>
                                    <Text UNSAFE_className="fontWeightUltraLight">Company</Text>
                                </Flex>
                                <Flex direction={'column'}>
                                    <Text UNSAFE_className="fontWeightSemiBold">{user?.username}</Text>
                                    <Text UNSAFE_className="fontWeightSemiBold">{user?.orgName}</Text>
                                </Flex>
                            </Flex>

                            {isSavingUserData ? (
                                <ContainerLoader parentStyles={{ height: 'size-1000' }} />
                            ) : (
                                <ButtonGroup marginTop={'size-500'} alignSelf={'end'} align="end">
                                    <Button variant="secondary" type="reset" onPress={() => onCancelHandler(close)}>
                                        Cancel
                                    </Button>
                                    <Button variant={'accent'} type="submit">
                                        Save
                                    </Button>
                                </ButtonGroup>
                            )}
                        </Form>
                    </Content>
                </Dialog>
            )}
        </DialogTrigger>
    );
};

export default EditInformation;
