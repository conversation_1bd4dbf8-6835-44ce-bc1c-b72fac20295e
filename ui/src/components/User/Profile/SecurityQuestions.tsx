import { ClientUtils } from "@/lib/core/ClientUtils";
import { ToastQueue } from '@react-spectrum/toast';
import { instance } from '@/src/core/http/axios-config';
import { ToastMessages } from '@/src/constants/user-profile';
import { SecurityQuestionsApi } from '@/src/constants/api-urls';
import ContainerLoader from '../../UI/Loader/ContainerLoader/ContainerLoader';
import { Dispatch, FormEvent, SetStateAction, useEffect, useMemo, useState } from 'react';
import { Button, Flex, TextField, Picker, Item, Checkbox, Heading, Text, Key, Form, ButtonGroup } from '@adobe/react-spectrum';
import { Dialog, DialogContainer, Divider, Content } from '@adobe/react-spectrum';

const QuestionAnswer = ({
    label,
    questions,
    showAnswer,
    setShowAnswer,
    selectedQuestion,
    setSelectedQuestion,
    customQuestion,
    onCustomQuestionChange,
    answer,
    onAnswerChange
}: {
    label: string;
    questions: { text: string; value: number }[];
    showAnswer: boolean;
    setShowAnswer: Dispatch<SetStateAction<boolean>>;
    selectedQuestion: Key | null;
    setSelectedQuestion: (key: Key) => void;
    customQuestion: string;
    onCustomQuestionChange: (value: string) => void;
    answer: string;
    onAnswerChange: (value: string) => void;
}) => {
    return (
        <Flex direction={'column'}>
            <Text UNSAFE_className="fontWeightSemiBold" marginBottom={'size-200'}>
                {label}
            </Text>
            <Picker
                isRequired
                width={'auto'}
                aria-label={label}
                selectedKey={selectedQuestion?.toString() ?? ''}
                onSelectionChange={setSelectedQuestion}
            >
                {questions.map((question) => (
                    <Item key={question.value} textValue={question.text}>
                        {question.text}
                    </Item>
                ))}
            </Picker>

            {/* {selectedQuestion === 'custom' && (
                <TextField
                    label="My question"
                    value={customQuestion}
                    onChange={onCustomQuestionChange}
                    placeholder="Enter your custom question"
                    isRequired
                    width={'auto'}
                />
            )} */}

            <Flex direction={'row'} alignItems={'end'} gap={'size-200'}>
                <Flex width={'auto'} flex={'1'} height={'size-1000'}>
                    <TextField
                        type={showAnswer ? 'text' : 'password'}
                        value={answer}
                        onChange={onAnswerChange}
                        label="My answer"
                        isRequired
                        width={'auto'}
                        flex={'1'}
                    />
                </Flex>

                <Flex height={'size-1000'} alignItems={'center'}>
                    <Checkbox isSelected={showAnswer} onChange={setShowAnswer} isEmphasized>
                        Show answer
                    </Checkbox>
                </Flex>
            </Flex>
        </Flex>
    );
};

export default function SecurityQuestions() {
    const [kbaQuestions1, setKbaQuestions1] = useState<{ text: string; value: number }[]>([]);
    const [kbaQuestions2, setKbaQuestions2] = useState<{ text: string; value: number }[]>([]);
    const [kbaQuestions3, setKbaQuestions3] = useState<{ text: string; value: number }[]>([]);
    const [selectedKbaQuestion1, setSelectedKbaQuestion1] = useState<{ text: string; value: number } | null>(null);
    const [selectedKbaQuestion2, setSelectedKbaQuestion2] = useState<{ text: string; value: number } | null>(null);
    const [selectedKbaQuestion3, setSelectedKbaQuestion3] = useState<{ text: string; value: number } | null>(null);
    const [originalSelectedKbaQuestion1, setOriginalSelectedKbaQuestion1] = useState<{ text: string; value: number } | null>(null);
    const [originalSelectedKbaQuestion2, setOriginalSelectedKbaQuestion2] = useState<{ text: string; value: number } | null>(null);
    const [originalSelectedKbaQuestion3, setOriginalSelectedKbaQuestion3] = useState<{ text: string; value: number } | null>(null);
    const [kbaAnswer1, setKbaAnswer1] = useState('');
    const [kbaAnswer2, setKbaAnswer2] = useState('');
    const [kbaAnswer3, setKbaAnswer3] = useState('');
    const [originalKbaAnswer1, setOriginalKbaAnswer1] = useState('');
    const [originalKbaAnswer2, setOriginalKbaAnswer2] = useState('');
    const [originalKbaAnswer3, setOriginalKbaAnswer3] = useState('');

    const [showAnswer1, setShowAnswer1] = useState<boolean>(false);
    const [showAnswer2, setShowAnswer2] = useState<boolean>(false);
    const [showAnswer3, setShowAnswer3] = useState<boolean>(false);

    const [isSaving, setIsSaving] = useState<boolean>(false);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [isRemoving, setIsRemoving] = useState<boolean>(false);
    const [showRemoveConfirmDialog, setShowRemoveConfirmDialog] = useState<boolean>(false);

    const isDirty = useMemo(
        () =>
            selectedKbaQuestion1 !== originalSelectedKbaQuestion1 ||
            selectedKbaQuestion2 !== originalSelectedKbaQuestion2 ||
            selectedKbaQuestion3 !== originalSelectedKbaQuestion3 ||
            kbaAnswer1 !== originalKbaAnswer1 ||
            kbaAnswer2 !== originalKbaAnswer2 ||
            kbaAnswer3 !== originalKbaAnswer3,
        [
            selectedKbaQuestion1,
            selectedKbaQuestion2,
            selectedKbaQuestion3,
            kbaAnswer1,
            kbaAnswer2,
            kbaAnswer3,
            originalSelectedKbaQuestion1,
            originalSelectedKbaQuestion2,
            originalSelectedKbaQuestion3,
            originalKbaAnswer1,
            originalKbaAnswer2,
            originalKbaAnswer3
        ]
    );

    const hasSavedQuestions = useMemo(
        () =>
            originalSelectedKbaQuestion1 !== null ||
            originalSelectedKbaQuestion2 !== null ||
            originalSelectedKbaQuestion3 !== null,
        [originalSelectedKbaQuestion1, originalSelectedKbaQuestion2, originalSelectedKbaQuestion3]
    );

    useEffect(() => {
        setIsLoading(true);
        instance
            .get(SecurityQuestionsApi.GET_KBA_QUESTIONS)
            .then((response) => {
                const kbaQuestions1 = response.data
                    .filter((q: any) => q.category === 1)
                    .map((q: any) => ({
                        text: q.question,
                        value: q.id
                    }));
                setKbaQuestions1(kbaQuestions1);

                const kbaQuestions2 = response.data
                    .filter((q: any) => q.category === 2)
                    .map((q: any) => ({
                        text: q.question,
                        value: q.id
                    }));
                setKbaQuestions2(kbaQuestions2);

                const kbaQuestions3 = response.data
                    .filter((q: any) => q.category === 3)
                    .map((q: any) => ({
                        text: q.question,
                        value: q.id
                    }));
                setKbaQuestions3(kbaQuestions3);

                instance.get(SecurityQuestionsApi.GET_KBA_ANSWERS()).then((response) => {
                    if (response.status === 200) {
                        response.data.forEach((a: any) => {
                            if (a.category === 1) {
                                const selectedQuestion1 = kbaQuestions1.find((q: any) => parseInt(q.value) === parseInt(a.questionId));
                                setSelectedKbaQuestion1(selectedQuestion1);
                                setOriginalSelectedKbaQuestion1(selectedQuestion1);
                                setKbaAnswer1(a.answer);
                                setOriginalKbaAnswer1(a.answer);
                            } else if (a.category === 2) {
                                const selectedQuestion2 = kbaQuestions2.find((q: any) => parseInt(q.value) === parseInt(a.questionId));
                                setSelectedKbaQuestion2(selectedQuestion2);
                                setOriginalSelectedKbaQuestion2(selectedQuestion2);
                                setKbaAnswer2(a.answer);
                                setOriginalKbaAnswer2(a.answer);
                            } else if (a.category === 3) {
                                const selectedQuestion3 = kbaQuestions3.find((q: any) => parseInt(q.value) === parseInt(a.questionId));
                                setSelectedKbaQuestion3(selectedQuestion3);
                                setOriginalSelectedKbaQuestion3(selectedQuestion3);
                                setKbaAnswer3(a.answer);
                                setOriginalKbaAnswer3(a.answer);
                            }
                        });
                    } else {
                        ToastQueue.negative(`${ToastMessages.ERROR_GETTING_SECURITY_QUESTIONS}`, { timeout: 7000 });
                    }
                    setIsLoading(false);
                });
            })
            .catch((error) => {
                ToastQueue.negative(`${ToastMessages.ERROR_GETTING_SECURITY_QUESTIONS}`, { timeout: 7000 });
                setIsLoading(false);
            });
    }, []);

    const onInputChangeHandler = (key: string, field: string, value: string) => {
        switch (key) {
            case 'first_question':
                const question = kbaQuestions1.find((q: any) => q.value === parseInt(value)) || null;
                setSelectedKbaQuestion1(question);
                break;
            case 'second_question':
                const question2 = kbaQuestions2.find((q: any) => q.value === parseInt(value)) || null;
                setSelectedKbaQuestion2(question2);
                break;
            case 'third_question':
                const question3 = kbaQuestions3.find((q: any) => q.value === parseInt(value)) || null;
                setSelectedKbaQuestion3(question3);
                break;
        }
    };

    const onSubmitHandler = (e: FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        setIsSaving(true);

        function pushAnswer(question: { text: string; value: number } | null, answer: string, category: number, kbaAnswers: any[]) {
            if (question && answer) {
                kbaAnswers.push({
                    category: category,
                    questionId: question.value,
                    answer: answer
                });
            }
        }
        const kbaAnswers: any[] = [];

        pushAnswer(selectedKbaQuestion1, kbaAnswer1, 1, kbaAnswers);
        pushAnswer(selectedKbaQuestion2, kbaAnswer2, 2, kbaAnswers);
        pushAnswer(selectedKbaQuestion3, kbaAnswer3, 3, kbaAnswers);

        instance
            .post(SecurityQuestionsApi.GET_KBA_QUESTIONS, {
                answers: kbaAnswers
            })
            .then((response) => {
                if (response.status === 200) {
                    setOriginalSelectedKbaQuestion1(selectedKbaQuestion1);
                    setOriginalSelectedKbaQuestion2(selectedKbaQuestion2);
                    setOriginalSelectedKbaQuestion3(selectedKbaQuestion3);
                    setOriginalKbaAnswer1(kbaAnswer1);
                    setOriginalKbaAnswer2(kbaAnswer2);
                    setOriginalKbaAnswer3(kbaAnswer3);
                    ToastQueue.positive(`${ToastMessages.SUCCESS_UPDATING_SECURITY_QUESTIONS}`, { timeout: 7000 });
                } else {
                    ToastQueue.negative(`${ToastMessages.ERROR_UPDATING_SECURITY_QUESTIONS}`, { timeout: 7000 });
                }
            })
            .catch((error) => {
                ToastQueue.negative(`${ToastMessages.ERROR_UPDATING_SECURITY_QUESTIONS}`, { timeout: 7000 });
            })
            .finally(() => {
                setIsSaving(false);
            });
    };

    const handleRemoveQuestions = () => {
        setShowRemoveConfirmDialog(true);
    };

    const handleCancel = () => {
        setSelectedKbaQuestion1(originalSelectedKbaQuestion1);
        setSelectedKbaQuestion2(originalSelectedKbaQuestion2);
        setSelectedKbaQuestion3(originalSelectedKbaQuestion3);
        setKbaAnswer1(originalKbaAnswer1);
        setKbaAnswer2(originalKbaAnswer2);
        setKbaAnswer3(originalKbaAnswer3);
    };

    const confirmRemoveQuestions = () => {
        setShowRemoveConfirmDialog(false);
        setIsRemoving(true);
        instance
            .delete(SecurityQuestionsApi.REMOVE_KBA_ANSWERS())
            .then((response) => {
                if (response.status === 200) {
                    setSelectedKbaQuestion1(null);
                    setSelectedKbaQuestion2(null);
                    setSelectedKbaQuestion3(null);
                    setKbaAnswer1('');
                    setKbaAnswer2('');
                    setKbaAnswer3('');
                    setOriginalSelectedKbaQuestion1(null);
                    setOriginalSelectedKbaQuestion2(null);
                    setOriginalSelectedKbaQuestion3(null);
                    setOriginalKbaAnswer1('');
                    setOriginalKbaAnswer2('');
                    setOriginalKbaAnswer3('');
                    ToastQueue.positive('Security questions removed successfully.', { timeout: 7000 });
                } else {
                    ToastQueue.negative('Failed to remove security questions.', { timeout: 7000 });
                }
            })
            .catch((error) => {
                ToastQueue.negative('Error removing security questions.', { timeout: 7000 });
            })
            .finally(() => {
                setIsRemoving(false);
            });
    };

    if (isLoading || isRemoving) {
        return <ContainerLoader />;
    }

    return (
        <>
            <Flex direction="column" gap="size-125" width={'100%'} maxWidth={500}>
                <Heading level={3}>Security Questions</Heading>
                <Text UNSAFE_className="Text14 TextGray700">
                 Security questions protect your account by verifying your identity during password resets. One of your three questions will be asked at random before your password can be changed.
                </Text>

                <Form validationBehavior="native" onSubmit={(e: React.FormEvent<HTMLFormElement>) => onSubmitHandler(e)}>
                    <Flex marginTop={'size-450'} direction={'column'} gap={'size-200'}>
                        <QuestionAnswer
                            key={'first_question'}
                            label={'First question'}
                            questions={kbaQuestions1}
                            showAnswer={showAnswer1}
                            setShowAnswer={setShowAnswer1}
                            selectedQuestion={selectedKbaQuestion1?.value ?? -999}
                            setSelectedQuestion={(value: Key) => onInputChangeHandler('first_question', 'question', value as string)}
                            customQuestion={''}
                            onCustomQuestionChange={(value: string) => onInputChangeHandler('first_question', 'customQuestion', value)}
                            answer={kbaAnswer1}
                            onAnswerChange={(value: string) => setKbaAnswer1(value)}
                        />

                        <QuestionAnswer
                            key={'second_question'}
                            label={'Second question'}
                            questions={kbaQuestions2}
                            showAnswer={showAnswer2}
                            setShowAnswer={setShowAnswer2}
                            selectedQuestion={selectedKbaQuestion2?.value ?? -999}
                            setSelectedQuestion={(value: Key) => onInputChangeHandler('second_question', 'question', value as string)}
                            customQuestion={''}
                            onCustomQuestionChange={(value: string) => onInputChangeHandler('second_question', 'customQuestion', value)}
                            answer={kbaAnswer2}
                            onAnswerChange={(value: string) => setKbaAnswer2(value)}
                        />

                        <QuestionAnswer
                            key={'third_question'}
                            label={'Third question'}
                            questions={kbaQuestions3}
                            showAnswer={showAnswer3}
                            setShowAnswer={setShowAnswer3}
                            selectedQuestion={selectedKbaQuestion3?.value ?? -999}
                            setSelectedQuestion={(value: Key) => onInputChangeHandler('third_question', 'question', value as string)}
                            customQuestion={''}
                            onCustomQuestionChange={(value: string) => onInputChangeHandler('third_question', 'customQuestion', value)}
                            answer={kbaAnswer3}
                            onAnswerChange={(value: string) => setKbaAnswer3(value)}
                        />
                    </Flex>

                    <ButtonGroup align="end" marginTop={'size-500'} UNSAFE_style={{ gap: 'var(--spectrum-global-dimension-size-100)' }}>
                        <Button variant="negative" onPress={handleRemoveQuestions} isDisabled={!hasSavedQuestions || isRemoving}>
                            Remove Security Questions
                        </Button>
                        <Button variant="secondary" onPress={handleCancel} isDisabled={!isDirty || isSaving}>
                            Cancel
                        </Button>
                        <Button type="submit" variant="cta" isDisabled={!isDirty || isSaving}>
                            {isSaving ? 'Saving...' : 'Save'}
                        </Button>
                    </ButtonGroup>
                </Form>
            </Flex>

            <DialogContainer onDismiss={() => setShowRemoveConfirmDialog(false)}>
                {showRemoveConfirmDialog && (
                    <Dialog>
                        <Heading>Remove Security Questions?</Heading>
                        <Divider />
                        <Content>
                            <Text>
                            Security questions are used to verify your identity if you forget your password.
                            <br/>
                            Removing them will reduce the security of your account recovery process.
                            <br/>
                            Are you sure you want to remove your security questions?
                            </Text>
                        </Content>
                        <ButtonGroup>
                            <Button variant="secondary" onPress={() => setShowRemoveConfirmDialog(false)} isDisabled={isRemoving}>
                                Cancel
                            </Button>
                            <Button variant="negative" onPress={confirmRemoveQuestions} isDisabled={isRemoving} autoFocus>
                                {isRemoving ? 'Removing...' : 'Remove'}
                            </Button>
                        </ButtonGroup>
                    </Dialog>
                )}
            </DialogContainer>
        </>
    );
}
