import uuid4 from 'uuid4';
import { useState } from 'react';
import Add from '@spectrum-icons/workflow/Add';
import CloseCircle from '@spectrum-icons/workflow/CloseCircle';
import CheckmarkCircle from '@spectrum-icons/workflow/CheckmarkCircle';
import { ComboBox, Item, TextField, View, Text } from '@adobe/react-spectrum';
import { ActionButton, Content, Flex, Heading, Key, NumberField } from '@adobe/react-spectrum';

type Props = {};

type Device = {
    id: string;
    type: string;
    date: string;
    email: string;
    contact: number;
    verified: boolean;
};

const MultiFactorAuthentication = ({}: Props) => {
    const [devices, setDevices] = useState<Device[]>([
        { id: uuid4(), type: 'email', email: '<EMAIL>', contact: 0, verified: true, date: 'March 28, 2004' }
    ]);

    const deviceOptions = [
        { key: 'sms', name: 'Mobile phone (SMS)' },
        { key: 'email', name: 'Email' }
    ];

    const handleAddDevice = () => {
        setDevices([...devices, { id: uuid4(), type: '', email: '', contact: 0, verified: false, date: '' }]);
    };

    const handleDeviceChange = (id: string, key: keyof Device, value: string | number) => {
        const updatedDevices: Device[] = [...devices];
        const updatedDevice: Device | undefined = updatedDevices.find((device) => device.id === id);

        if (!updatedDevice) return;

        if (key in updatedDevice) {
            // Type-safe property assignment
            if (key === 'contact' && typeof value === 'number') {
                updatedDevice[key] = value;
            } else if ((key === 'type' || key === 'email' || key === 'date') && typeof value === 'string') {
                updatedDevice[key] = value;
            } else if (key === 'verified' && typeof value === 'boolean') {
                updatedDevice[key] = value;
            }
        }
        setDevices(updatedDevices);
    };

    return (
        <Flex direction="column" gap="size-125" width={'100%'} maxWidth={750}>
            <Heading level={3}>Multi-factor authentication (MFA)</Heading>
            <Content UNSAFE_className="Text14">
                Your account is more secure when a second device is required for each sign-in.
                <br /> We recommend using it with a device you have regular access to when using EPRLive.
            </Content>

            <View>
                {devices.map((device, index) => (
                    <View key={index}>
                        <Flex gap="size-200" marginBottom="size-200">
                            <Flex height={'size-1000'}>
                                <ComboBox
                                    label="Device"
                                    isRequired
                                    selectedKey={device.type}
                                    width={'size-2400'}
                                    onSelectionChange={(value: Key | null) => handleDeviceChange(device.id, 'type', value as string)}
                                >
                                    {deviceOptions.map((option) => (
                                        <Item key={option.key} textValue={option.name}>
                                            {option.name}
                                        </Item>
                                    ))}
                                </ComboBox>
                            </Flex>

                            <Flex height={'size-1000'}>
                                {device.type === 'email' && (
                                    <TextField
                                        label="Email"
                                        type="email"
                                        value={device.email}
                                        width={'size-3400'}
                                        onChange={(value) => handleDeviceChange(device.id, 'email', value)}
                                    />
                                )}

                                {device.type === 'sms' && (
                                    <NumberField
                                        label="Contact number"
                                        value={device.contact}
                                        width={'size-3400'}
                                        onChange={(value) => handleDeviceChange(device.id, 'contact', value)}
                                        hideStepper
                                    />
                                )}
                            </Flex>

                            <Flex alignItems={'center'} height={'size-1000'}>
                                {device.verified ? (
                                    <Flex gap={'size-100'}>
                                        <CheckmarkCircle color="positive" /> <Text>Verified on {device.date}</Text>
                                    </Flex>
                                ) : (
                                    <Flex gap={'size-100'}>
                                        <CloseCircle color="negative" /> <Text>Not verified</Text>
                                    </Flex>
                                )}
                            </Flex>
                        </Flex>
                    </View>
                ))}

                <ActionButton onPress={handleAddDevice}>
                    <Add />
                    <Text>Add MFA device</Text>
                </ActionButton>
            </View>
        </Flex>
    );
};

export default MultiFactorAuthentication;
