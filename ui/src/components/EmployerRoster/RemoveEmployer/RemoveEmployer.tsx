import { graphql } from 'relay-runtime';
import { useLazyLoadQuery } from 'react-relay';
import { useDialogContainer } from '@adobe/react-spectrum';
import { TableData } from '../EmployerRosterTable/EPRTable.data';
import CanDeleteEmployer from './CanDeleteEmployer/CanDeleteEmployer';
import NoOperationPossible from './NoOperationPossible/NoOperationPossible';
import CanDeleteRelationship from './CanDeleteRelationship/CanDeleteRelationship';
import { RemoveEmployerIsPossibleQuery as RemoveEmployerIsPossibleQueryType } from '@/src/relay/__generated__/RemoveEmployerIsPossibleQuery.graphql';

type Props = {
    employer: TableData;
    onRemoveEmployer: (employerId: string) => void;
};

export const RemoveEmployerQuery = graphql`
    query RemoveEmployerIsPossibleQuery($employerId: ID!, $chapterId: ID!) {
        canDeleteEmployer(employerId: $employerId, chapterId: $chapterId) {
            canDelete
            canDeleteRelationship
            message
        }
    }
`;

const RemoveEmployer = ({ employer, onRemoveEmployer }: Props) => {
    const dialog = useDialogContainer();

    const { canDeleteEmployer } = useLazyLoadQuery<RemoveEmployerIsPossibleQueryType>(RemoveEmployerQuery, {
        employerId: employer.id,
        chapterId: employer.chapterId
    });

    let contentUI = null;

    if (!canDeleteEmployer?.canDelete && !canDeleteEmployer?.canDeleteRelationship) {
        contentUI = <NoOperationPossible employer={employer} dialog={dialog} />;
    } else if (canDeleteEmployer?.canDelete) {
        contentUI = <CanDeleteEmployer employer={employer} dialog={dialog} onRemoveEmployer={onRemoveEmployer} />;
    } else if (canDeleteEmployer?.canDeleteRelationship) {
        contentUI = <CanDeleteRelationship employer={employer} dialog={dialog} />;
    }

    return contentUI;
};

export default RemoveEmployer;
