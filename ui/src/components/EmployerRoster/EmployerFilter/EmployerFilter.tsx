import { useMemo, useRef, useCallback, useEffect } from 'react';
import _, { cloneDeep } from 'lodash';
import { graphql } from 'relay-runtime';
import Checkbox from '../Checkbox/Checkbox';
import { ColumnType } from '@/src/types/rosters';
import { LABEL_TEXT } from '@/src/constants/text';
import { usePaginationFragment } from 'react-relay';
import TableAction from '../../UI/SavedViews/TableAction';
import Properties from '@spectrum-icons/workflow/Properties';
import { DialogTrigger, Dialog } from '@adobe/react-spectrum';
import Accordion from '@/src/components/UI/Accordion/Accordion';
import { Content, Flex, Text, View } from '@adobe/react-spectrum';
import { EmployerFilterData, filterButtonStylesData, filterButtonWrapperStylesData, INITIAL_FILTER_DATA } from './data';
import DateRadioGroup from '@/src/components/UI/DateRadioGroup/DateRadioGroup';
import ComboBoxFilter from '@/src/components/UI/ComboBoxFilter/ComboBoxFilter';
import { getFEINOptionsFromEmployerRoster } from '@/src/services/employer-roster';
import { getEmployerOptionsFromEmployerRoster } from '@/src/services/employer-roster';
import { ViewProps, SpectrumActionButtonProps, ActionButton, Divider } from '@adobe/react-spectrum';
import { EmployerFilterInput } from '@/src/relay/__generated__/EmlpoyerNameSelectRefetchQuery.graphql';
import { EmployerFilterFEINFragment$key } from '@/src/relay/__generated__/EmployerFilterFEINFragment.graphql';
import { EmployerFilterNameFragment$key } from '@/src/relay/__generated__/EmployerFilterNameFragment.graphql';
import DisplayFilterConditionally from '@/src/components/UI/DisplayFilterConditionally/DisplayFilterConditionally';
import {
    ACTIVE_RELATIONSHIP_STATUS_ID,
    getDefaultEmployerRosterHeaderQueryVariables,
    getFilterWhereByColumnData
} from '@/src/services/employer-roster';
import Close from '@spectrum-icons/workflow/Close';

type Props = {
    chapterIdEncoded: string;
    data: any;
    backupData: any;
    localFilter: any;
    backupLocalFilter: any;
    columnData: ColumnType[];
    onFilterChange: (filter: EmployerFilterInput) => void;
    setData: React.Dispatch<React.SetStateAction<EmployerFilterData>>;
    setBackupData: React.Dispatch<React.SetStateAction<EmployerFilterData>>;
    queryRef: EmployerFilterNameFragment$key & EmployerFilterFEINFragment$key;
    setLocalFilter: React.Dispatch<React.SetStateAction<EmployerFilterInput>>;
    setBackupLocalFilter: React.Dispatch<React.SetStateAction<EmployerFilterInput>>;
};

const EmployerFilterNameFragment = graphql`
    fragment EmployerFilterNameFragment on Query
    @argumentDefinitions(
        first: { type: "Int", defaultValue: 20 }
        after: { type: "String" }
        chapterId: { type: "ID!" }
        order: { type: "[EmployerSortInput!]", defaultValue: null }
        where: { type: "EmployerFilterInput", defaultValue: null }
    )
    @refetchable(queryName: "EmlpoyerNameSelectRefetchQuery") {
        employersByChapterId(first: $first, after: $after, chapterId: $chapterId, order: $order, where: $where)
            @connection(key: "EmployerFilterNameFragment_employersByChapterId") {
            edges {
                node {
                    id
                    organization {
                        name
                    }
                }
            }
            pageInfo {
                endCursor
                hasNextPage
            }
        }
    }
`;

const EmployerFilterFEINFragment = graphql`
    fragment EmployerFilterFEINFragment on Query
    @argumentDefinitions(
        first: { type: "Int", defaultValue: 20 }
        after: { type: "String" }
        chapterId: { type: "ID!" }
        order: { type: "[EmployerSortInput!]", defaultValue: null }
        where: { type: "EmployerFilterInput", defaultValue: null }
    )
    @refetchable(queryName: "EmployerFilterFEINRefetchQuery") {
        employersByChapterId(first: $first, after: $after, chapterId: $chapterId, order: $order, where: $where)
            @connection(key: "EmployerFilterFEINFragment_employersByChapterId") {
            edges {
                node {
                    id
                    fein
                }
            }
            pageInfo {
                endCursor
                hasNextPage
            }
        }
    }
`;

const EmployerFilter = ({
    chapterIdEncoded: chapterIdEncoded,
    queryRef,
    columnData,
    onFilterChange,
    data,
    backupData,
    localFilter,
    backupLocalFilter,
    setData,
    setBackupData,
    setLocalFilter,
    setBackupLocalFilter
}: Props) => {
    const DEFAULT_ROOT_HEADER_QUERY_PARAMS = useMemo(
        () => getDefaultEmployerRosterHeaderQueryVariables(chapterIdEncoded),
        [chapterIdEncoded]
    );

    // Define proper type for refetch parameters
    type RefetchParams = typeof DEFAULT_ROOT_HEADER_QUERY_PARAMS & { where: EmployerFilterInput };

    const {
        data: employersNameFragmentData,
        loadNext: loadNextEmployers,
        isLoadingNext: isLoadingNextEmployers,
        hasNext: hasNextEmployers,
        refetch: refetchEmployers
    } = usePaginationFragment(EmployerFilterNameFragment, queryRef);

    const {
        data: employerFEINFragmentData,
        loadNext: loadNextEmployerFEIN,
        isLoadingNext: isLoadingNextEmployerFEIN,
        hasNext: hasNextEmployerFEIN,
        refetch: refetchEmployerFEIN
    } = usePaginationFragment(EmployerFilterFEINFragment, queryRef);

    const filtersCount = useMemo(
        () => getFilterWhereByColumnData(backupLocalFilter, columnData)?.length ?? undefined,
        [backupLocalFilter, columnData]
    );
    const showClearButton: boolean = useMemo(() => (filtersCount ? true : false), [filtersCount]);
    const isDisabled = useMemo(() => {
        const filtersAreEqual = _.isEqual(localFilter, backupLocalFilter);
        const dataAreEqual = _.isEqual(data, backupData);
        // Disable button only if BOTH the filter logic and the input data are unchanged
        return filtersAreEqual && dataAreEqual;
    }, [localFilter, backupLocalFilter, data, backupData]);
    const employersFEIN = useMemo(() => getFEINOptionsFromEmployerRoster(employerFEINFragmentData) ?? [], [employerFEINFragmentData]);
    const employers = useMemo(() => getEmployerOptionsFromEmployerRoster(employersNameFragmentData) ?? [], [employersNameFragmentData]);
    const filterButtonWrapperStyles: ViewProps<5> = showClearButton ? filterButtonWrapperStylesData : {};
    const filterButtonStyles: SpectrumActionButtonProps = showClearButton ? filterButtonStylesData : {};

    // Create debounced refetch functions to prevent excessive API calls
    const debouncedRefetchEmployers = useRef(
        _.debounce((params: RefetchParams) => {
            refetchEmployers(params);
        }, 300)
    ).current;

    const debouncedRefetchEmployerFEIN = useRef(
        _.debounce((params: RefetchParams) => {
            refetchEmployerFEIN(params);
        }, 300)
    ).current;

    // Cleanup debounced functions on unmount to prevent memory leaks
    useEffect(() => {
        return () => {
            debouncedRefetchEmployers.cancel?.();
            debouncedRefetchEmployerFEIN.cancel?.();
        };
    }, [debouncedRefetchEmployers, debouncedRefetchEmployerFEIN]);

    const onLocalFilterChangeHandler = useCallback(
        (fieldName: string, filterData: EmployerFilterInput) => {
            const baseField = fieldName.split('.')[0];
            const params = { ...DEFAULT_ROOT_HEADER_QUERY_PARAMS, where: filterData };

            switch (baseField) {
                case 'name':
                    debouncedRefetchEmployers(params);
                    break;
                case 'fein':
                    debouncedRefetchEmployerFEIN(params);
                    break;
            }
        },
        [DEFAULT_ROOT_HEADER_QUERY_PARAMS, debouncedRefetchEmployers, debouncedRefetchEmployerFEIN]
    );

    const onSetInputChangeHandler = useCallback(
        (fieldName: string, value: Set<{ value: string; label: string }> | string) => {
            setData((prevData) => {
                return {
                    ...prevData,
                    [fieldName]: value
                };
            });
        },
        [setData]
    );

    const onIncludeInactiveChangeHandler = useCallback(
        (isSelected: boolean) => {
            setData((prevData) => ({
                ...prevData,
                includeInactiveEmployers: isSelected
            }));

            setLocalFilter((prevFilter) => {
                const updatedFilter = cloneDeep(prevFilter);
                let mutableAnd = Array.isArray(updatedFilter.and) ? [...updatedFilter.and] : [];

                mutableAnd = mutableAnd.filter((condition) => !(condition).relationshipStatusId);

                if (!isSelected) {
                    mutableAnd.push({ relationshipStatusId: { eq: ACTIVE_RELATIONSHIP_STATUS_ID } });
                }

                if (mutableAnd.length > 0) {
                    updatedFilter.and = mutableAnd;
                } else {
                    updatedFilter.and = [];
                }

                return updatedFilter;
            });
        },
        [setData, setLocalFilter]
    );

    const onApplyChangesHandler = useCallback(
        (close: Function) => {
            setBackupData(cloneDeep(data));
            setBackupLocalFilter(cloneDeep(localFilter));
            onFilterChange(cloneDeep(localFilter));
            close();
        },
        [data, localFilter, setBackupData, setBackupLocalFilter, onFilterChange]
    );

    const onClearFiltersHandler = useCallback(() => {
        const clearedData = cloneDeep(INITIAL_FILTER_DATA);
        setData(clearedData);
        setBackupData(clearedData);

        // Reset filter to the default state (active employers only)
        const clearedFilter: EmployerFilterInput = {
            and: [] // Empty filter for now
        };

        setLocalFilter(clearedFilter);
        setBackupLocalFilter(cloneDeep(clearedFilter));
        onFilterChange(cloneDeep(clearedFilter));
    }, [setData, setBackupData, setLocalFilter, setBackupLocalFilter, onFilterChange]);

    const onCancelChangesHandler = useCallback(
        (close: Function) => {
            setData(cloneDeep(backupData));
            setLocalFilter(backupLocalFilter);
            close();
        },
        [backupData, backupLocalFilter, setData, setLocalFilter]
    );

    // Keep track of the last filter to prevent redundant updates
    const lastFilterRef = useRef<any>(null);

    const onSelectionFilterUpdate = useCallback((fieldName: string, filterData: EmployerFilterInput) => {
        // Create a unique key for this filter operation
        const filterKey = `${fieldName}:${JSON.stringify(filterData)}`;

        // Skip if this exact filter was just applied
        if (lastFilterRef.current === filterKey) {
            return;
        }

        lastFilterRef.current = filterKey;

        setLocalFilter((prevFilter) => {
            const updatedFilter = cloneDeep(prevFilter);
            updatedFilter.and = updatedFilter.and ?? [];

            let updatedAnd = [...updatedFilter.and];
            let found = false;

            // Map organization.name to name if needed
            const actualFieldName = fieldName === 'organization.name' ? 'name' : fieldName;

            for (let i = 0; i < updatedAnd.length; i++) {
                const query = updatedAnd[i];

                if (query.or && query.or.length > 0) {
                    const queryFieldKey = Object.keys(query?.or[0])[0];
                    if (queryFieldKey === actualFieldName) {
                        updatedAnd[i] = filterData;
                        found = true;
                    }
                }
            }

            if (!found) {
                updatedAnd.push(filterData);
            }

            updatedAnd = updatedAnd.filter((query) => query.or && query.or.length > 0);
            updatedFilter.and = updatedAnd;

            return updatedFilter;
        });
    }, [setLocalFilter]);

    return (
        <Flex gap={'size-0'}>
            <DialogTrigger type="popover" placement="left top">
                <View {...filterButtonWrapperStyles}>
                    <ActionButton isQuiet {...filterButtonStyles}>
                        <Properties />
                        <Text>Filters{showClearButton ? ` (${filtersCount})` : ''}</Text>
                    </ActionButton>
                </View>

                {(close) => (
                    <Dialog>
                        <Content>
                            <Flex direction={'column'}>
                                <DisplayFilterConditionally columnData={columnData} fieldName="name">
                                    <Divider size="S" marginTop={8} marginBottom={8} />
                                    <Accordion title={'Business Name'} defaultOpen>
                                        <Flex direction="column" gap="size-150">
                                            <ComboBoxFilter
                                                items={employers}
                                                fieldName="organization.name"
                                                selectionFilterFieldName="name"
                                                hasNext={hasNextEmployers}
                                                selectedOptions={data.employerNameOptions}
                                                setSelectedOptions={(options) => onSetInputChangeHandler('employerNameOptions', options)}
                                                loadNext={loadNextEmployers}
                                                isLoadingNext={isLoadingNextEmployers}
                                                onFilterChange={onLocalFilterChangeHandler}
                                                onSelectionFilterUpdate={onSelectionFilterUpdate}
                                                label={LABEL_TEXT.EMPLOYER_NAME_HINT}
                                            />
                                        </Flex>
                                    </Accordion>
                                    <Divider size="S" marginTop={8} marginBottom={8} />
                                </DisplayFilterConditionally>

                                <DisplayFilterConditionally columnData={columnData} fieldName="fein">
                                    <Accordion title={'FEIN'}>
                                        <Flex direction="column" gap="size-150">
                                            <ComboBoxFilter
                                                items={employersFEIN}
                                                hasNext={hasNextEmployerFEIN}
                                                loadNext={loadNextEmployerFEIN}
                                                selectedOptions={data.feinOptions}
                                                setSelectedOptions={(options) => onSetInputChangeHandler('feinOptions', options)}
                                                fieldName="fein"
                                                selectionFilterFieldName="fein"
                                                isLoadingNext={isLoadingNextEmployerFEIN}
                                                onFilterChange={onLocalFilterChangeHandler}
                                                onSelectionFilterUpdate={onSelectionFilterUpdate}
                                                label={LABEL_TEXT.FEIN_HINT}
                                            />
                                        </Flex>
                                    </Accordion>
                                    <Divider size="S" marginTop={8} marginBottom={8} />
                                </DisplayFilterConditionally>

                                <DisplayFilterConditionally columnData={columnData} fieldName="lastReportedDate">
                                    <Accordion title={'Date'}>
                                        <Flex direction="column" gap="size-150">
                                            <DateRadioGroup
                                                fieldName="lastReportedDate"
                                                selectedDateRange={data.dateRange}
                                                setSelectedDateRange={(value: string) => onSetInputChangeHandler('dateRange', value)}
                                                onSelectionFilterUpdate={onSelectionFilterUpdate}
                                                label={LABEL_TEXT.LATEST_REPORT_HINT}
                                            />
                                        </Flex>
                                    </Accordion>
                                    <Divider size="S" marginTop={8} marginBottom={8} />
                                </DisplayFilterConditionally>

                                <Checkbox
                                    isSelected={data.includeInactiveEmployers}
                                    onChange={onIncludeInactiveChangeHandler}
                                    subTitle={LABEL_TEXT.INACTIVE_EMPLOYERS_HINT}>
                                    {LABEL_TEXT.INCLUDE_INACTIVE_LABEL}
                                </Checkbox>
                                <Divider size="S" marginTop={16} marginBottom={8} />
                            </Flex>

                            <Flex justifyContent="end">
                                <TableAction
                                    close={close}
                                    disabled={isDisabled}
                                    onApplyChanges={onApplyChangesHandler}
                                    onCancelChanges={onCancelChangesHandler}
                                />
                            </Flex>
                        </Content>
                    </Dialog>
                )}
            </DialogTrigger>

            {showClearButton && (
                <ActionButton isQuiet onPress={() => onClearFiltersHandler()}>
                    <Close size="XS" />
                    <Text>Clear All</Text>
                </ActionButton>
            )}
        </Flex>
    );
};

export default EmployerFilter;
