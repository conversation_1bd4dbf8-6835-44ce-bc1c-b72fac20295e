import { cloneDeep } from 'lodash';
import { graphql } from 'relay-runtime';
import SaveView from '../SaveView/SaveView';
import { useLazyLoadQuery } from 'react-relay';
import { ColumnType } from '@/src/types/rosters';
import { LABEL_TEXT } from '@/src/constants/text';
import { SavedCustomView } from '@/src/types/views';
import { Suspense, useEffect, useMemo, useState } from 'react';
import AddEmployer from '../AddEmployer/AddEmployer';
import RenameView from '../../UI/SavedViews/RenameView';
import { Flex, Heading, View } from '@adobe/react-spectrum';
import EmployerFilter from '../EmployerFilter/EmployerFilter';
import ShowViewDetails from '../../UI/SavedViews/ShowViewDetails';
import ExportFile from '@/src/components/UI/ExportFile/ExportFile';
import TableColumns from '../../UI/TableHeader/Columns/TableColumns';
import TableView from '@/src/components/EmployerRoster/TableView/TableView';
import { EmployerFilterData, INITIAL_FILTER_DATA } from '../EmployerFilter/data';
import ChooseDefaultViewToDelete from '../../UI/SavedViews/ChooseDefaultViewToDelete';
import { EmployerRosterViews, EmployerRosterViewsOptions } from '@/src/constants/views';
import { getDefaultEmployerRosterHeaderQueryVariables } from '@/src/services/employer-roster';
import { EmployerFilterInput } from '@/src/relay/__generated__/EmployerFilterFEINRefetchQuery.graphql';
import { EmployerRosterHeaderQuery$variables } from '@/src/relay/__generated__/EmployerRosterHeaderQuery.graphql';
import { EmployerRosterHeaderQuery as EmployerRosterHeaderQueryType } from '@/src/relay/__generated__/EmployerRosterHeaderQuery.graphql';
import ContainerLoader from '../../UI/Loader/ContainerLoader/ContainerLoader';

type Props = {
    chapterIdEncoded: string;
    defaultViewId: string;
    customSavedViews: any[];
    columnData: ColumnType[];
    selectedSystemView: string;
    isSavedViewDisabled: boolean;
    onExport: (format: string) => void;
    onRootDeleteView: (id: string) => void;
    selectedCustomView: SavedCustomView | null;
    onRootSelectCustomView: (id: string | null) => void;
    onRootChangeDefaultView: (id: string) => void;
    onToggleColumnShow: (values: string[]) => void;
    onRootSystemViewChange: (view: string | null) => void;
    onRootUpdateView: (id: string, updatedLocalView: any) => void;
    onRootRenameView: (id: string, name: string, description: string) => void;
    onDeleteAndChangeDefaultView: (id: string, newDefaultViewId: string) => void;
    onFilterChange: (filter: EmployerRosterHeaderQuery$variables['where']) => void;
    onRootSaveView: ({ savedLocalView, name, description }: { savedLocalView: any; name: string; description: string }) => void;
};

export const EmployerRosterHeaderQuery = graphql`
    query EmployerRosterHeaderQuery($chapterId: ID!, $order: [EmployerSortInput!], $where: EmployerFilterInput) {
        ...EmployerFilterNameFragment @arguments(chapterId: $chapterId, order: $order, where: $where)
        ...EmployerFilterFEINFragment @arguments(chapterId: $chapterId, order: $order, where: $where)
    }
`;

const EmployerRosterHeader = ({
    chapterIdEncoded: chapterIdEncoded,
    columnData,
    selectedSystemView,
    defaultViewId,
    onFilterChange,
    onExport,
    onRootSaveView,
    customSavedViews,
    onRootUpdateView,
    onRootDeleteView,
    onRootRenameView,
    selectedCustomView,
    onToggleColumnShow,
    isSavedViewDisabled,
    onRootSystemViewChange,
    onRootSelectCustomView,
    onRootChangeDefaultView,
    onDeleteAndChangeDefaultView
}: Props) => {
    const DEFAULT_ROOT_HEADER_QUERY_PARAMS = useMemo(() => getDefaultEmployerRosterHeaderQueryVariables(chapterIdEncoded), [chapterIdEncoded]);
    const employerFilterQuery = useLazyLoadQuery<EmployerRosterHeaderQueryType>(
        EmployerRosterHeaderQuery,
        DEFAULT_ROOT_HEADER_QUERY_PARAMS
    );

    const [data, setData] = useState<EmployerFilterData>(cloneDeep(INITIAL_FILTER_DATA));
    const [backupData, setBackupData] = useState<EmployerFilterData>(cloneDeep(INITIAL_FILTER_DATA));
    const [localFilter, setLocalFilter] = useState<EmployerFilterInput>({ and: [] });
    const [backupLocalFilter, setBackupLocalFilter] = useState<EmployerFilterInput>({ and: [] });
    const [isOpenDeleteDefaultViewDialog, setOpenDeleteDefaultViewDialog] = useState(false);
    const [selectedViewToShowDetails, setSelectedViewToShowDetails] = useState(null);
    const [hasViewToRename, setHasViewToRename] = useState(null);

    useEffect(() => {
        if (selectedSystemView === EmployerRosterViews.EMPLOYER_ROSTER && !selectedCustomView) {
            setData(cloneDeep(INITIAL_FILTER_DATA));
            setBackupData(cloneDeep(INITIAL_FILTER_DATA));
            setLocalFilter({ and: [] });
            setBackupLocalFilter({ and: [] });
        }
    }, [selectedSystemView, selectedCustomView]);

    useEffect(() => {
        if (selectedCustomView) {
            onLoadSavedViewWithLocalFilterStateHandler(selectedCustomView);
        }
    }, [selectedCustomView]);

    const onSaveViewWithLocalFilterStateHandler = ({ name, description }: { name: string; description: string }) => {
        const savedLocalView = {
            data: {
                ...data,
                feinOptions: Array.from(data.feinOptions),
                employerNameOptions: Array.from(data.employerNameOptions)
            },
            localFilter
        };
        onRootSaveView({ savedLocalView, name, description });
    };

    const onUpdateViewWithLocalFilterStateHandler = (id: string) => {
        const updatedLocalView = {
            data: {
                ...data,
                feinOptions: Array.from(data.feinOptions),
                employerNameOptions: Array.from(data.employerNameOptions)
            },
            localFilter
        };
        onRootUpdateView(id, updatedLocalView);
    };

    const onLoadSavedViewWithLocalFilterStateHandler = (customView: any) => {
        if (customView) {
            if (customView && customView.type === EmployerRosterViews.EMPLOYER_ROSTER) {
                const data: EmployerFilterData = cloneDeep(customView.savedLocalView.data);
                const localFilter: EmployerFilterInput = customView.savedLocalView.localFilter;

                data.feinOptions = new Set(data.feinOptions);
                data.employerNameOptions = new Set(data.employerNameOptions);

                setData(cloneDeep(data));
                setBackupData(cloneDeep(data));
                setLocalFilter(localFilter);
                setBackupLocalFilter(cloneDeep(localFilter));
            } else if (customView && customView.type === EmployerRosterViews.BENEFIT_ELECTIONS) {
            }
        }
    };

    const onDeleteDefaultViewHandler = () => {
        setOpenDeleteDefaultViewDialog(true);
    };

    const onOpenRenameViewDialogHandler = (id: string) => {
        const viewToRename = customSavedViews?.find((view) => view.id === id);
        if (viewToRename) {
            setHasViewToRename(viewToRename);
        }
    };

    const onCloseRenameViewDialogHandler = () => {
        setHasViewToRename(null);
    };

    const onSelectAndOpenViewDetailsDialogHandler = (id: string) => {
        const viewToShowDetails = [...EmployerRosterViewsOptions, ...customSavedViews]?.find((view) => view.id === id);
        
        if (viewToShowDetails) {
            const isSystemDefault = EmployerRosterViewsOptions.some(view => view.id === id);
            
            if (isSystemDefault) {
                const viewWithRequiredProps = {
                    ...viewToShowDetails,
                    columns: columnData || [],
                    sortOrder: [],
                    filter: { where: {} },
                    description: viewToShowDetails.description || ''
                };
                setSelectedViewToShowDetails(viewWithRequiredProps);
            } else {
                setSelectedViewToShowDetails(viewToShowDetails);
            }
        }
    };

    const onCloseViewDetailsDialogHandler = () => {
        setSelectedViewToShowDetails(null);
    };

    return (
        <>
            <Heading level={1} marginBottom={'size-400'}>
                {LABEL_TEXT.EMPLOYERS}
            </Heading>

            <View colorVersion={6} borderWidth="none" borderColor="gray-200" borderRadius="regular" width="100%" paddingBottom={'size-75'} paddingX={'size-75'}>
                <Flex justifyContent="space-between" alignItems="center" width="100%">
                    <Flex marginBottom="size-100" columnGap="size-100" alignItems="center">
                        <View colorVersion={6} borderWidth="thin" borderRadius="regular" borderColor="gray-300">
                            <AddEmployer />
                        </View>

                        <ExportFile onExport={onExport} />

                        <SaveView
                            customSavedViews={customSavedViews}
                            builtInViews={EmployerRosterViewsOptions}
                            isSavedViewDisabled={isSavedViewDisabled}
                            onCreateView={onSaveViewWithLocalFilterStateHandler}
                            onRootUpdateView={onUpdateViewWithLocalFilterStateHandler}
                        />

                        <TableView
                            selectedSystemView={selectedSystemView}
                            defaultViewId={defaultViewId}
                            customSavedViews={customSavedViews}
                            onRootDeleteView={onRootDeleteView}
                            builtInViews={EmployerRosterViewsOptions}
                            selectedCustomView={selectedCustomView}
                            onRootSystemViewChange={onRootSystemViewChange}
                            onRootSelectCustomView={onRootSelectCustomView}
                            onDeleteDefaultView={onDeleteDefaultViewHandler}
                            onRootChangeDefaultView={onRootChangeDefaultView}
                            onOpenRenameViewDialog={onOpenRenameViewDialogHandler}
                            onShowViewDetails={onSelectAndOpenViewDetailsDialogHandler}
                        />
                        <TableColumns columnData={columnData} onToggleColumnShow={onToggleColumnShow} />
                        <EmployerFilter
                            chapterIdEncoded={chapterIdEncoded}
                            data={data}
                            setData={setData}
                            backupData={backupData}
                            columnData={columnData}
                            localFilter={localFilter}
                            setBackupData={setBackupData}
                            queryRef={employerFilterQuery}
                            onFilterChange={onFilterChange}
                            setLocalFilter={setLocalFilter}
                            backupLocalFilter={backupLocalFilter}
                            setBackupLocalFilter={setBackupLocalFilter}
                        />
                    </Flex>
                </Flex>
            </View>

            {isOpenDeleteDefaultViewDialog && (
                <ChooseDefaultViewToDelete
                    defaultViewId={defaultViewId}
                    customSavedViews={customSavedViews}
                    builtInViews={EmployerRosterViewsOptions}
                    isOpen={isOpenDeleteDefaultViewDialog}
                    selectedCustomView={selectedCustomView}
                    onRootSystemViewChange={onRootSystemViewChange}
                    onRootSelectCustomView={onRootSelectCustomView}
                    onDeleteAndChangeDefaultView={onDeleteAndChangeDefaultView}
                    onCloseDialog={() => setOpenDeleteDefaultViewDialog(false)}
                />
            )}

            {hasViewToRename && (
                <RenameView
                    view={hasViewToRename}
                    isOpen={!!hasViewToRename}
                    onUpdateView={onRootRenameView}
                    builtInViews={EmployerRosterViewsOptions}
                    onCloseDialog={onCloseRenameViewDialogHandler}
                />
            )}

            {selectedViewToShowDetails && <ShowViewDetails view={selectedViewToShowDetails} onClose={onCloseViewDetailsDialogHandler} />}
        </>
    );
};

export default EmployerRosterHeader;
