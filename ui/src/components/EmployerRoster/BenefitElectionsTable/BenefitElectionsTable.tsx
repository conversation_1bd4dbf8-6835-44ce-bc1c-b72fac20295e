import { graphql } from 'relay-runtime';
import { SortOrder } from '@/src/types/table';
import { ColumnType } from '@/src/types/rosters';
import { usePaginationFragment } from 'react-relay';
import { DynamicCell } from './BenefitElectionsDynamicCell';
import { Cell, TableHeader } from '@adobe/react-spectrum';
import { keyMapForSortingFields } from './BenefitElections.data';
import { TableView, TableBody, Row } from '@adobe/react-spectrum';
import EmptyTableState from '../EmployerRosterTable/EmptyTableState';
import { LoadingStates, isLoading } from '@/src/services/spectrum/table';
import EPRDynamicColumns from '../EmployerRosterTable/EPRDynamicColumns';
import type { SortDescriptor, LoadingState } from '@react-types/shared';
import { Suspense, useEffect, useMemo, useState, useTransition } from 'react';
import ContainerLoader from '../../UI/Loader/ContainerLoader/ContainerLoader';
import FlexibleTableWidthContainer from '../TableView/FlexibleTableWidthContainer';
import { TableData } from '@/src/components/EmployerRoster/BenefitElectionsTable/BenefitElections.data';
import InfiniteScrollTableFooter from '@/src/components/UI/InfiniteScrollTableFooter/InfiniteScrollTableFooter';
import { BenefitElectionsTableFragment$key } from '@/src/relay/__generated__/BenefitElectionsTableFragment.graphql';
import { getNodesFromBenefitElectionsFragment, getTotalCountFromBenefitElectionsFragment } from '@/src/services/employer-roster';
import { EmployerRosterGridQuery as EmployerRosterGridQueryType } from '@/src/relay/__generated__/EmployerRosterGridQuery.graphql';
import { ToastQueue } from '@react-spectrum/toast';
import { BenefitElectionsRosterDtoFilterInput, BenefitElectionsRosterDtoSortInput } from '@/src/relay/__generated__/BenefitElectionsRefetchQuery.graphql';
import { BenefitElectionsRefetchQuery } from '@/src/relay/__generated__/BenefitElectionsRefetchQuery.graphql';

// Create a custom filter type with only the fields needed by this component
type BenefitElectionsTableFilter = {
    chapterId: string;
    where?: BenefitElectionsRosterDtoFilterInput | null;
    order?: ReadonlyArray<BenefitElectionsRosterDtoSortInput> | null;
};

type Props = {
    columnData: ColumnType[];
    onRootSortChange: (sortData: SortOrder) => void;
    queryRef: BenefitElectionsTableFragment$key;
    filter: BenefitElectionsTableFilter;
};

const DEFAULT_SORT_DESCRIPTOR: SortDescriptor = {
    column: 'employerName' as const,
    direction: 'ascending' as const
};

const BenefitElectionsTableFragment = graphql`
    fragment BenefitElectionsTableFragment on Query
    @argumentDefinitions(
        first: { type: "Int", defaultValue: 20 }
        after: { type: "String" }
        chapterId: { type: "ID!" }
        order: { type: "[BenefitElectionsRosterDtoSortInput!]", defaultValue: null }
        where: { type: "BenefitElectionsRosterDtoFilterInput", defaultValue: null }
    )
    @refetchable(queryName: "BenefitElectionsRefetchQuery") {
        benefitElectionRosterByChapterId(first: $first, after: $after, chapterId: $chapterId, order: $order, where: $where)
            @connection(key: "BenefitElectionsTableFragment_benefitElectionRosterByChapterId") {
            edges {
                node {
                    id
                    employerName
                    fein
                    dba
                    benefitName
                    override
                    effectiveStartDate
                    effectiveEndDate
                    elected
                    employerGUID
                    associationID
                    discount
                    lastReport
                }
                cursor
            }
            pageInfo {
                endCursor
                hasNextPage
            }
            totalCount
        }
    }
`;

const BenefitElectionsTable = ({ queryRef, filter, onRootSortChange, columnData }: Props) => {
    const { data, hasNext, loadNext, isLoadingNext, refetch } = usePaginationFragment<
        BenefitElectionsRefetchQuery,
        BenefitElectionsTableFragment$key
    >(BenefitElectionsTableFragment, queryRef);

    const pageSize = 20;
    const [isSortingPending, startSortingTransition] = useTransition();
    const [loadingState, setLoadingState] = useState<LoadingState>(LoadingStates.IDLE);
    const [sortDescriptor, setSortDescriptor] = useState<SortDescriptor>(DEFAULT_SORT_DESCRIPTOR);
    const [hasInitializedFirstPageData, setHasInitializedFirstPageData] = useState(false);
    const isPendingListLoading = useMemo(() => isLoading(loadingState) || isLoadingNext, [loadingState, isLoadingNext]);

    const rows = useMemo<TableData[]>(() => {
        if (isSortingPending) return [];
        const nodes = getNodesFromBenefitElectionsFragment(data);
        return nodes.map((node) => ({
            ...node,
            override: node.override ?? '', // Use actual override if exists, otherwise empty string
            guid: node.employerGUID ?? '' // Map employerGUID to guid field
        }));
    }, [data, isSortingPending]);

    const filterCount = useMemo(() => filter.where?.and?.length ?? 0, [filter]);
    const totalCount = useMemo(() => getTotalCountFromBenefitElectionsFragment(data), [data]);

    useEffect(() => {
        setHasInitializedFirstPageData(true);
    }, []);

    useEffect(() => {
        setLoadingState(isSortingPending ? LoadingStates.LOADING : LoadingStates.IDLE);
    }, [isSortingPending]);

    const getNextPaginatedQuery = (pageSize: number) => {
        loadNext(pageSize, {
            UNSTABLE_extraVariables: { ...filter },
            onComplete: (args: Error | null) => {
                if (args) {
                    ToastQueue.negative(`Error fetching Employer Roster Data: ${JSON.stringify(args)}`, { timeout: 7000 });
                }
                setLoadingState(LoadingStates.IDLE);
            }
        });
    };

    const onLoadMoreHandler = () => {
        if (!hasInitializedFirstPageData || isPendingListLoading || !hasNext || isSortingPending) {
            return;
        }

        setLoadingState(LoadingStates.LOADING_MORE);
        getNextPaginatedQuery(pageSize);
    };

    const onSortChangeHandler = (sortDescriptor: SortDescriptor) => {
        if (isPendingListLoading) {
            return;
        }

        setLoadingState(LoadingStates.SORTING);
        setSortDescriptor({
            ...sortDescriptor,
            column: sortDescriptor.column || 'employerName'
        });

        startSortingTransition(() => {
            sortListOnServerSide(sortDescriptor);
        });
    };

    const sortListOnServerSide = (sortDescriptor: SortDescriptor) => {
        const field = keyMapForSortingFields[sortDescriptor.column as string] || 'employerName';

        const direction = sortDescriptor.direction === 'ascending' ? 'ASC' : 'DESC';
        const newOrder: SortOrder = [{ [field]: direction }];

        onRootSortChange(newOrder);

        refetch(
            { 
                order: newOrder, 
                where: filter.where ?? null, 
                chapterId: filter.chapterId 
            },
            {
                onComplete: (args: Error | null) => {
                    if (args) {
                        ToastQueue.negative(`Error fetching Employer Roster Data: ${JSON.stringify(args)}`, { timeout: 7000 });
                    }
                }
            }
        );
    };

    const columnsUI = EPRDynamicColumns(columnData, isSortingPending || isPendingListLoading || rows.length === 0);

    return (
        <FlexibleTableWidthContainer>
            <Suspense fallback={<ContainerLoader />}>
                <TableView
                    isQuiet
                    flex
                    flexGrow={1}
                    flexBasis={0}
                    flexShrink={0}
                    onSortChange={onSortChangeHandler}
                    aria-label="Employer Roster Table"
                    sortDescriptor={sortDescriptor}
                    renderEmptyState={() => <EmptyTableState isFilterActive={filterCount > 0} loadingState={loadingState} />}
                >
                    <TableHeader>{columnsUI}</TableHeader>

                    <TableBody items={rows} loadingState={loadingState} onLoadMore={onLoadMoreHandler}>
                        {(item: TableData) => <Row key={item.id}>{(key) => <Cell>{DynamicCell(key as string, item)}</Cell>}</Row>}
                    </TableBody>
                </TableView>

                <InfiniteScrollTableFooter totalCount={totalCount} />
            </Suspense>
        </FlexibleTableWidthContainer>
    );
};

export default BenefitElectionsTable;
