import { SortOrder } from '@/src/types/table';
import { DynamicCell } from './EPRDynamicCell';
import EmptyTableState from './EmptyTableState';
import EPRDynamicColumns from './EPRDynamicColumns';
import { keyMapForSortingFields } from './EPRTable.data';
import { graphql, usePaginationFragment } from 'react-relay';
import type { SortDescriptor, LoadingState } from '@react-types/shared';
import { LoadingStates, isLoading } from '@/src/services/spectrum/table';
import { useMemo, useState, Suspense, useEffect, useTransition } from 'react';
import ContainerLoader from '../../UI/Loader/ContainerLoader/ContainerLoader';
import { Cell, Flex, TableHeader, TableView, TableBody, Row } from '@adobe/react-spectrum';
import { TableData } from '@/src/components/EmployerRoster/EmployerRosterTable/EPRTable.data';
import { ColumnType } from '@/src/types/rosters';
import { EmployerRosterGridQuery$variables } from '@/lib/relay/__generated__/EmployerRosterGridQuery.graphql';
import { EmployerRosterTableFragment$key } from '@/lib/relay/__generated__/EmployerRosterTableFragment.graphql';
import InfiniteScrollTableFooter from '@/src/components/UI/InfiniteScrollTableFooter/InfiniteScrollTableFooter';
import { getNodesFromEmployerRosterFragment, getTotalCountFromEmployerRosterFragment } from '@/src/services/employer-roster';
import { EmployerRosterGridQuery as EmployerRosterQueryType } from '@/lib/relay/__generated__/EmployerRosterGridQuery.graphql';
import FlexibleTableWidthContainer from '../TableView/FlexibleTableWidthContainer';
import { ToastQueue } from '@react-spectrum/toast';

type Props = {
    columnData: ColumnType[];
    deletedEmployerIds: string[];
    filter: EmployerRosterGridQuery$variables;
    queryRef: EmployerRosterTableFragment$key;
    onRootSortChange: (sortData: SortOrder) => void;
    onRemoveEmployer: (employerId: string) => void;
};

const EmployerRosterTableFragment = graphql`
    fragment EmployerRosterTableFragment on Query
    @argumentDefinitions(
        first: { type: "Int", defaultValue: 20 }
        after: { type: "String" }
        chapterId: { type: "ID!" }
        order: { type: "[EmployerRosterViewSortInput!]", defaultValue: null }
        where: { type: "EmployerRosterViewFilterInput", defaultValue: null }
    )
    @refetchable(queryName: "EmployerRosterGridRefetchQuery") {
        employerRosterByChapterId(first: $first, after: $after, chapterId: $chapterId, order: $order, where: $where)
            @connection(key: "EmployerRosterTableFragment_employerRosterByChapterId") {
            edges {
                node {
                    id
                    guid
                    name
                    fein
                    dba
                    associationId
                    payrollContactFirstName
                    payrollContactLastName
                    payrollContactPhoneNumber
                    payrollContactEmailAddress
                    lastReportedDate
                    chapterId
                    relationshipStatusId
                }
            }
            pageInfo {
                endCursor
                hasNextPage
            }
            totalCount
        }
    }
`;

const DEFAULT_SORT_DESCRIPTOR: SortDescriptor = {
    column: 'name',
    direction: 'ascending'
};

const EmployerRosterTable = ({ columnData, queryRef, filter, deletedEmployerIds, onRootSortChange, onRemoveEmployer }: Props) => {
    const { data, hasNext, loadNext, isLoadingNext, refetch } = usePaginationFragment<
        EmployerRosterQueryType,
        EmployerRosterTableFragment$key
    >(EmployerRosterTableFragment, queryRef);

    const pageSize = 20;
    const [isSortingPending, startSortingTransition] = useTransition();
    const [loadingState, setLoadingState] = useState<LoadingState>(LoadingStates.IDLE);
    const [sortDescriptor, setSortDescriptor] = useState<SortDescriptor>(DEFAULT_SORT_DESCRIPTOR);
    const [hasInitializedFirstPageData, setHasInitializedFirstPageData] = useState(false);
    const isPendingListLoading = useMemo(() => isLoading(loadingState) || isLoadingNext, [loadingState, isLoadingNext]);

    const rows = useMemo<TableData[]>(() => {
        if (isSortingPending) return [];
        return getNodesFromEmployerRosterFragment(data)
            .map((node) => ({
                ...node,
                payrollContact: `${node.payrollContactFirstName ?? ''} ${node.payrollContactLastName ?? ''}`.trim()
            }))
            .filter((node) => !deletedEmployerIds.includes(node.id));
    }, [data, isSortingPending, deletedEmployerIds]);

    const filterCount = useMemo(() => filter.where?.and?.length ?? 0, [filter]);
    const totalCount = useMemo(() => getTotalCountFromEmployerRosterFragment(data), [data]);

    useEffect(() => {
        setHasInitializedFirstPageData(true);
    }, []);

    useEffect(() => {
        setLoadingState(isSortingPending ? LoadingStates.LOADING : LoadingStates.IDLE);
    }, [isSortingPending]);

    const getNextPaginatedQuery = (pageSize: number) => {
        loadNext(pageSize, {
            UNSTABLE_extraVariables: { ...filter },
            onComplete: (args: Error | null) => {
                if (args) {
                    ToastQueue.negative(`Error fetching Employer Roster Data: ${JSON.stringify(args)}`, { timeout: 7000 });
                }
                setLoadingState(LoadingStates.IDLE);
            }
        });
    };

    const onLoadMoreHandler = () => {
        if (!hasInitializedFirstPageData || isPendingListLoading || !hasNext || isSortingPending) {
            return;
        }

        setLoadingState(LoadingStates.LOADING_MORE);
        getNextPaginatedQuery(pageSize);
    };

    const onSortChangeHandler = (sortDescriptor: SortDescriptor) => {
        if (isPendingListLoading) {
            return;
        }

        setLoadingState(LoadingStates.SORTING);
        setSortDescriptor(sortDescriptor);

        startSortingTransition(() => {
            sortListOnServerSide(sortDescriptor);
        });
    };

    const sortListOnServerSide = (sortDescriptor: SortDescriptor) => {
        let field = (sortDescriptor.column as string) || 'employerName';
        field = keyMapForSortingFields[field];

        const direction = sortDescriptor.direction === 'ascending' ? 'ASC' : 'DESC';
        const newOrder: SortOrder = [{ [field]: direction }];

        onRootSortChange(newOrder);

        refetch(
            { order: newOrder, where: filter.where, chapterId: filter.chapterId },
            {
                onComplete: (args: Error | null) => {
                    if (args) {
                        ToastQueue.negative(`Error fetching Employer Roster Data: ${JSON.stringify(args)}`, { timeout: 7000 });
                    }
                }
            }
        );
    };

    const columnsUI = EPRDynamicColumns(columnData, isSortingPending || isPendingListLoading || rows.length === 0);

    return (
        <FlexibleTableWidthContainer>
            <Suspense fallback={<ContainerLoader />}>
                <TableView
                    isQuiet
                    flex
                    flexGrow={1}
                    flexBasis={0}
                    flexShrink={0}
                    onSortChange={onSortChangeHandler}
                    aria-label="Employer Roster Table"
                    sortDescriptor={sortDescriptor}
                    renderEmptyState={() => <EmptyTableState isFilterActive={filterCount > 0} loadingState={loadingState} />}
                >
                    <TableHeader>{columnsUI}</TableHeader>

                    <TableBody items={rows} loadingState={loadingState} onLoadMore={onLoadMoreHandler}>
                        {(item: TableData) => (
                            <Row key={item.id}>{(key) => <Cell>{DynamicCell(key as string, item, onRemoveEmployer)}</Cell>}</Row>
                        )}
                    </TableBody>
                </TableView>

                <InfiniteScrollTableFooter totalCount={totalCount} />
            </Suspense>
        </FlexibleTableWidthContainer>
    );
};

export default EmployerRosterTable;
