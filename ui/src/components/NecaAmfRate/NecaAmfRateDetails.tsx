import { Button } from '@progress/kendo-react-buttons';
import { DatePicker } from '@progress/kendo-react-dateinputs';
import { Grid, GridCellProps, GridColumn as Column, GridExpandChangeEvent, GridRowClickEvent } from '@progress/kendo-react-grid';
import { Loader } from '@progress/kendo-react-indicators';
import { useEffect, useState, useCallback } from 'react';
import { instance } from '@/src/core/http/axios-config';
import { Constants } from '@/src/constants/global';
import DropDownListGridCell from '@/src/components/kendo/DropDownListGridCell';
import NecaAmfRateSteps from './NecaAmfRateSteps';
import { StoreState, useStore } from '@/lib';
import { ClientUtils } from "@/lib/core/ClientUtils";
import { Link, useNavigate, useSearchParams } from 'react-router';
import PageHeading from '@/src/components/UI/PageHeading';

interface RateStep {
    id?: string | number;
    value: number;
    min: number;
    max: number | null;
}

interface Rate {
    id?: number | string;
    benefitId: number;
    categoryId: number;
    calculationId?: number;
    steps: RateStep[];
    benefit?: string;
    category?: string;
    calculation?: string;
    expanded?: boolean;
    handleDetailChange?: (rate: Rate) => void;
    inEdit?: boolean;
}

interface RateSchedule {
    id: number | null;
    effectiveStartDate: Date;
    effectiveEndDate: Date | null;
}

interface BenefitToCategory {
    benefitID: number;
    benefit: string;
    contractID: number;
    contract: string;
}

interface Calculation {
    value: number;
    text: string;
}

const NecaAmfRateDetails = ({ slug }: { slug: string }) => {
    const navigate = useNavigate();
    const [searchParams] = useSearchParams();

    const isDuplicate = useCallback(() => {
        return searchParams.get('duplicate') === 'true';
    }, [searchParams]);

    const [rateSchedules, setRateSchedules] = useState<RateSchedule[]>([]);
    const [editID, setEditID] = useState<string | null>(null);
    const [saving, setSaving] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const [savedRates, setSavedRates] = useState<Rate[]>([]);
    const [originalRates, setOriginalRates] = useState<Rate[]>([]);

    const [rates, setRates] = useState<Rate[]>([]);
    const [benefitsToCategories, setBenefitsToCategories] = useState<BenefitToCategory[]>([]);
    const [calculations, setCalculations] = useState<Calculation[]>([]);

    const [startDate, setStartDate] = useState<Date | null>(null);
    const [endDate, setEndDate] = useState<Date | null>(null);

    const rateScheduleId = slug;

    const duplicateRateSchedule = useCallback((): RateSchedule | undefined => {
        const baseRateSchedule = rateSchedules.find(r => r.id === parseInt(rateScheduleId));
        if (!baseRateSchedule) return undefined;
        const rateScheduleCopy = JSON.parse(JSON.stringify(baseRateSchedule)) as RateSchedule;
        rateScheduleCopy.effectiveStartDate = new Date(rateScheduleCopy.effectiveStartDate);
        rateScheduleCopy.effectiveEndDate = rateScheduleCopy.effectiveEndDate ? new Date(rateScheduleCopy.effectiveEndDate) : null;
        rateScheduleCopy.id = null;
        return rateScheduleCopy;
    }, [rateSchedules, rateScheduleId]);

    const [rateSchedule, setRateSchedule] = useState<RateSchedule | undefined>(() => {
        if (isDuplicate()) {
            return duplicateRateSchedule();
        } else {
            return rateSchedules.find(r => r.id === parseInt(rateScheduleId));
        }
    });

    const user = useStore((state: StoreState) => state.user);
    const selectedChapterGuid = useStore((state: StoreState) => state.selectedChapterGuid);

    useEffect(() => {
        setCalculations(Constants.NecaAmfRateCalculations);
    }, []);

    const getRateSchedules = useCallback(() => {
        let rateSchedulesUrl = '/api/NecaAmfRates?username=' + ClientUtils.getUsername();
        if (selectedChapterGuid !== null) rateSchedulesUrl += '&GUID=' + selectedChapterGuid;
        instance.get(rateSchedulesUrl).then((response) => {
            setRateSchedules(
                (response.data as unknown[]).map((r: unknown) => {
                    const schedule = r as Record<string, unknown>;
                    return {
                        id: schedule.id as number,
                        effectiveStartDate: new Date(schedule.effectiveStartDate as string),
                        effectiveEndDate: schedule.effectiveEndDate === null ? null : new Date(schedule.effectiveEndDate as string)
                    };
                })
            );
        }).catch((error) => {
            console.error('Failed to load rate schedules:', error);
        });
    }, [selectedChapterGuid]);

    const loadGridData = useCallback(() => {
        const getBenefits = () => {
            let benefitsUrl = '/api/Benefits/neca-amf-rates?username=' + ClientUtils.getUsername();
            if (selectedChapterGuid !== null) benefitsUrl += '&GUID=' + selectedChapterGuid;
            instance.get(benefitsUrl).then((response) => {
                setBenefitsToCategories(response.data as BenefitToCategory[]);
            }).catch((error) => {
                console.error('Failed to load benefits:', error);
            });
        };

        const getRates = () => {
            if (rateScheduleId === 'new') return;
            const ratesUrl = '/api/NecaAmfRates/rates?RateScheduleID=' + rateScheduleId;
            instance.get(ratesUrl).then((response) => {
                const rates: Rate[] = [];
                (response.data as unknown[]).forEach((d: unknown) => {
                    const data = d as Record<string, unknown>;
                    let rate: Rate | undefined = rates.find(r => r.id === data.rateID);
                    if (!rate) {
                        rate = {
                            id: data.rateID as number,
                            benefitId: data.benefitID as number,
                            categoryId: data.categoryID as number,
                            calculationId: data.calculationID as number,
                            steps: []
                        };
                        rates.push(rate);
                    }
                    if (data.stepID) {
                        rate.steps.push({
                            id: data.stepID as number,
                            value: data.value as number,
                            min: data.min as number,
                            max: data.max as number | null
                        });
                    }
                });
                setSavedRates(rates);
            });
        };

        getBenefits();
        getRates();
    }, [selectedChapterGuid, rateScheduleId]);

    useEffect(() => {
        if (user && user.roles.includes(Constants.Roles.SystemAdministrator) !== true) {
            getRateSchedules();
            loadGridData();
        }
    }, [user, getRateSchedules, loadGridData]);

    useEffect(() => {
        if (selectedChapterGuid !== null) {
            getRateSchedules();
            loadGridData();
        }
    }, [selectedChapterGuid, getRateSchedules, loadGridData]);

    useEffect(() => {
        if (rateSchedules.length > 0 && !rateSchedule) {
            if (isDuplicate()) {
                setRateSchedule(duplicateRateSchedule());
            } else {
                setRateSchedule(rateSchedules.find(r => r.id === parseInt(rateScheduleId)));
            }
        }
    }, [rateSchedules, rateScheduleId, duplicateRateSchedule, rateSchedule, isDuplicate]);

    useEffect(() => {
        let minimumStartDate = new Date(0);
        if (rateSchedules.length === 0) {
            const newStartDate = new Date();
            newStartDate.setDate(1);
            setStartDate(newStartDate);
        } else if (rateSchedule && !isDuplicate()) {
            setStartDate(rateSchedule.effectiveStartDate);
            setEndDate(rateSchedule.effectiveEndDate);
            rateSchedules.forEach(r => {
                if (rateSchedule && r.effectiveStartDate < rateSchedule.effectiveStartDate && r.effectiveStartDate > minimumStartDate) {
                    minimumStartDate = new Date(r.effectiveStartDate);
                }
            });
            minimumStartDate.setMonth(minimumStartDate.getMonth() + 1);
        } else {
            rateSchedules.forEach(r => {
                if (r.effectiveStartDate > minimumStartDate) {
                    minimumStartDate = new Date(r.effectiveStartDate);
                }
            });
            minimumStartDate.setMonth(minimumStartDate.getMonth() + 1);
            setStartDate(minimumStartDate);
        }
    }, [rateSchedule, rateSchedules, isDuplicate]);

    useEffect(() => {
        if (rates !== undefined && rates !== null && rates.length > 0) {
            setRates(
                rates.map(item => ({
                    ...item,
                    inEdit: item.benefitId.toString() + item.categoryId.toString() === editID
                }))
            );
        }
    }, [editID, rates]);

    useEffect(() => {
        if (benefitsToCategories.length > 0) {
            const ratesTemplate: Rate[] = [];
            benefitsToCategories.forEach(b => {
                const existingRate = savedRates.find(r => r.benefitId === b.benefitID && r.categoryId === b.contractID);
                if (existingRate) {
                    const matchingCalculations = Constants.NecaAmfRateCalculations.filter(c => c.value === existingRate.calculationId);
                    ratesTemplate.push({
                        ...existingRate,
                        benefit: b.benefit,
                        category: b.contract,
                        calculation: matchingCalculations.length > 0 ? matchingCalculations[0].text : ''
                    });
                } else {
                    ratesTemplate.push({
                        benefitId: b.benefitID,
                        benefit: b.benefit,
                        categoryId: b.contractID,
                        category: b.contract,
                        steps: []
                    });
                }
            });
            setRates(ratesTemplate);
            setOriginalRates(JSON.parse(JSON.stringify(ratesTemplate)) as Rate[]);
        }
    }, [benefitsToCategories, savedRates]);

    const getNextRateSchedule = (effectiveStartDate: Date, duplicate: boolean, localRateSchedules: RateSchedule[] | null = null): RateSchedule | null => {
        if (duplicate || rateSchedules.length === 0) {
            return null;
        }
        let nextRateScheduleId: number | null = null;
        let closestNextDate: Date | null = null;
        const schedulesToCheck = localRateSchedules || rateSchedules;
        schedulesToCheck.forEach(r => {
            if (r.effectiveStartDate > effectiveStartDate && (closestNextDate === null || r.effectiveStartDate < closestNextDate)) {
                closestNextDate = new Date(r.effectiveStartDate);
                nextRateScheduleId = r.id;
            }
        });
        return nextRateScheduleId === null ? null : schedulesToCheck.find(r => r.id === nextRateScheduleId) || null;
    };

    const updateRateScheduleDates = (callback: () => void, updatedRateSchedules: RateSchedule[] | null = null) => {
        const rateSchedulesToUpdate: RateSchedule[] = [];
        const schedulesToProcess = updatedRateSchedules || rateSchedules;
        schedulesToProcess.forEach(r => {
            const nextRateSchedule = getNextRateSchedule(r.effectiveStartDate, false, schedulesToProcess);
            if (!nextRateSchedule && r.effectiveEndDate !== null) {
                r.effectiveEndDate = null;
                rateSchedulesToUpdate.push(r);
            } else if (nextRateSchedule) {
                const expectedEndDate = new Date(nextRateSchedule.effectiveStartDate);
                expectedEndDate.setDate(expectedEndDate.getDate() - 1);
                expectedEndDate.setHours(0, 0, 0, 0);
                if (r.effectiveEndDate) {
                    const actualEndDate = new Date(r.effectiveEndDate);
                    actualEndDate.setHours(0, 0, 0, 0);
                    if (expectedEndDate.getTime() !== actualEndDate.getTime()) {
                        r.effectiveEndDate = expectedEndDate;
                        rateSchedulesToUpdate.push(r);
                    }
                }
            }
        });
        const rateSchedulesBody = rateSchedulesToUpdate.map(r => ({
            rateScheduleId: r.id,
            startDate: r.effectiveStartDate,
            endDate: r.effectiveEndDate
        }));
        instance
            .post('/api/NecaAmfRates/rate-schedule-dates', {
                rateSchedules: rateSchedulesBody
            })
            .then(() => {
                callback();
            });
    };

    const saveChanges = () => {
        if (!validateStartDate() || !validateSteps()) return;

        setSaving(true);
        const stepsToDelete = getStepsToDelete();

        const formattedRates = getFormattedRates();
        let updatedEndDate = endDate === null ? null : new Date(endDate);
        const nextRateSchedule = startDate ? getNextRateSchedule(startDate, isDuplicate()) : null;
        if (nextRateSchedule !== null) {
            updatedEndDate = new Date(nextRateSchedule.effectiveStartDate);
            updatedEndDate.setDate(updatedEndDate.getDate() - 1);
        }
        const body: Record<string, unknown> = {
            rates: formattedRates,
            rateScheduleID: rateScheduleId !== 'new' && !isDuplicate() ? rateScheduleId : null,
            startDate: startDate,
            endDate: updatedEndDate,
            stepsToDelete: stepsToDelete,
            username: ClientUtils.getUsername()
        };
        if (selectedChapterGuid !== null) {
            body.guid = selectedChapterGuid;
        }
        instance
            .post('/api/NecaAmfRates', body)
            .then((response) => {
                const updatedRateSchedules = (response.data as unknown[]).map(r => {
                    const schedule = r as Record<string, unknown>;
                    return {
                        id: schedule.id as number,
                        effectiveStartDate: new Date(schedule.effectiveStartDate as string),
                        effectiveEndDate: new Date(schedule.effectiveEndDate as string)
                    };
                });
                updateRateScheduleDates(() => {
                    finishSave();
                }, updatedRateSchedules);
            })
            .catch((e) => {
                setSaving(false);
            });
    };

    const getStepsToDelete = (): RateStep[] => {
        const stepsToDelete: RateStep[] = [];
        originalRates.forEach(o => {
            const rate = rates.find(r => r.id === o.id);
            if (o.steps && rate) {
                o.steps.forEach(os => {
                    if (!rate.steps.find(s => s.id === os.id)) {
                        stepsToDelete.push(os);
                    }
                });
            }
        });
        return stepsToDelete;
    };

    const getFormattedRates = (): Rate[] => {
        const updatedRates: Rate[] = [];
        rates.forEach(r => {
            const rate = JSON.parse(JSON.stringify(r)) as Rate;
            delete rate.benefit;
            delete rate.category;
            delete rate.calculation;
            delete rate.expanded;
            delete rate.handleDetailChange;
            delete rate.inEdit;
            if (isDuplicate()) {
                delete rate.id;
            }

            const originalRate = originalRates.find(o => o.benefitId === rate.benefitId && o.categoryId === rate.categoryId);
            if (rate.steps && originalRate) {
                const updatedSteps: RateStep[] = [];
                rate.steps.forEach(s => {
                    if (typeof s.id === 'string' || isDuplicate()) {
                        delete s.id;
                    }
                    let originalStep: RateStep | undefined;
                    if (originalRate.steps) {
                        originalStep = originalRate.steps.find(o => o.id === s.id);
                    }
                    if (
                        !originalStep ||
                        originalStep.value !== s.value ||
                        originalStep.min !== s.min ||
                        originalStep.max !== s.max
                    ) {
                        updatedSteps.push(s);
                    }
                });

                if (updatedSteps.length > 0) {
                    rate.steps = updatedSteps;
                    updatedRates.push(rate);
                }
            }
            if (
                !updatedRates.find(u => u.benefitId === rate.benefitId && u.categoryId === rate.categoryId) &&
                originalRate && originalRate.calculationId !== rate.calculationId
            ) {
                updatedRates.push(rate);
            }
        });
        return updatedRates;
    };

    const finishSave = () => {
        setSaving(false);
        getRateSchedules();
        window.open('/neca-amf-rates', '_self');
    };

    const validateStartDate = () => {
        if (!startDate) {
            setError('You must set a start date before saving a rate schedule');
            return false;
        }
        if (!ClientUtils.isDateFirstOfMonth(startDate)) {
            setError('Start date must be on the first of the month');
            return false;
        }
        const existingRateSchedule = startDate ? rateSchedules.find(
            r => r.effectiveStartDate.toLocaleDateString() === startDate.toLocaleDateString() && r.id?.toString() !== rateScheduleId
        ) : undefined;
        if (existingRateSchedule) {
            setError('A rate schedule already exists starting on ' + (startDate?.toLocaleDateString() || ''));
            return false;
        }
        return true;
    };

    const validateSteps = (): boolean => {
        let error: string | null = null;
        rates.forEach(r => {
            if (error !== null) return;
            if (!r.steps || r.steps.length === 0) return;
            const zeroStep = r.steps.find(s => s.min === 0);
            if (!zeroStep) {
                error =
                    'Error on rate for benefit ' +
                    r.benefit +
                    ' and category ' +
                    r.category +
                    ': One step must have a minimum value of zero.';
                return;
            }
            r.steps.forEach(s => {
                if (error !== null) return;
                if (r.steps.find(st => st.min === s.min && st.id !== s.id)) {
                    error =
                        'Error on rate for benefit ' +
                        r.benefit +
                        ' and category ' +
                        r.category +
                        ': Two steps cannot have the same min value.';
                }
            });
        });
        setError(error);
        return error === null;
    };

    const handleStartDateChange = (e: { value: Date | null }) => {
        setStartDate(e.value);
    };

    const handleDetailChange = (rate: Rate) => {
        const ratesCopy = rates.slice();
        const index = rates.findIndex(item => item.benefitId === rate.benefitId && item.categoryId === rate.categoryId);
        if (index >= 0) {
            ratesCopy[index] = rate;
        }
        ratesCopy.forEach(r => {
            if (!r.steps) return;
            r.steps.forEach(s => {
                const previousStep = getPreviousStep(r, s.min);
                if (previousStep) {
                    previousStep.max = s.min;
                }
                const nextStep = getNextStep(r, s.min);
                if (!nextStep) {
                    s.max = null;
                }
            });
        });
        setRates(ratesCopy);
    };

    const rowClick = (event: GridRowClickEvent) => {
        setEditID(event.dataItem.benefitId.toString() + event.dataItem.categoryId.toString());
    };

    const onCalculationChange = (props: { dataItem: { benefitId: number; categoryId: number }; value: number; text: string }) => {
        const updatedRates = rates.map(item => ({
            ...item,
            calculationId:
                item.benefitId === props.dataItem.benefitId && item.categoryId === props.dataItem.categoryId
                    ? props.value
                    : item.calculationId,
            calculation:
                item.benefitId === props.dataItem.benefitId && item.categoryId === props.dataItem.categoryId ? props.text : item.calculation
        }));
        setRates(updatedRates);
    };

    const expandChange = async (event: GridExpandChangeEvent) => {
        const ratesCopy = rates.slice();
        ratesCopy[event.dataIndex].expanded = event.value;
        ratesCopy[event.dataIndex].handleDetailChange = handleDetailChange;
        if (!ratesCopy[event.dataIndex].steps) {
            ratesCopy[event.dataIndex].steps = [];
        }
        setRates(ratesCopy);
    };

    const addStep = (rate: Rate) => {
        if (!rate.steps) {
            rate.steps = [];
        }
        const ratesCopy = rates.slice();
        const rateCopy = ratesCopy.find(r => r.benefitId === rate.benefitId && r.categoryId === rate.categoryId);
        if (!rateCopy) return;

        const maxStep = getMaxStep(rate);
        const min = maxStep === null ? 0 : maxStep.min + 1;
        const previousStep = getPreviousStep(rate, min);
        if (previousStep !== null) {
            previousStep.max = min;
        }

        const newStep: RateStep = {
            id: ClientUtils.uuidv4(),
            min: min,
            max: null,
            value: 0
        };
        rateCopy.steps.push(newStep);
        setRates(ratesCopy);
    };

    const getMaxStep = (rate: Rate): RateStep | null => {
        if (!rate.steps) {
            return null;
        }
        let maxStep: RateStep | null = null;
        rate.steps.forEach(s => {
            if (maxStep === null || s.min > maxStep.min) {
                maxStep = s;
            }
        });
        return maxStep;
    };

    const getPreviousStep = (rate: Rate, min: number): RateStep | null => {
        if (!rate.steps) {
            return null;
        }
        let previousStep: RateStep | null = null;
        let closestMin: number | null = null;
        rate.steps.forEach(s => {
            if (s.min < min && (closestMin === null || s.min > closestMin)) {
                closestMin = s.min;
                previousStep = s;
            }
        });
        return previousStep;
    };

    const getNextStep = (rate: Rate, min: number): RateStep | null => {
        if (!rate.steps) {
            return null;
        }
        let nextStep: RateStep | null = null;
        let closestMin: number | null = null;
        rate.steps.forEach(s => {
            if (s.min > min && (closestMin === null || s.min < closestMin)) {
                closestMin = s.min;
                nextStep = s;
            }
        });
        return nextStep;
    };

    const CommandCell = (props: { dataItem: Rate }) => {
        const { dataItem } = props;
        return (
            <td className="k-command-cell">
                <Button style={{ width: '80px' }} onClick={() => addStep(dataItem)}>
                    Add Step
                </Button>
            </td>
        );
    };

    const calculationsGridCell = (props: GridCellProps) => (
        <DropDownListGridCell
            {...props}
            onChange={onCalculationChange}
            onChangeSuper={props.onChange}
            dropDownListData={calculations}
            width="225px"
        />
    );

    return (
        <div style={{ margin: '20px' }}>
            <PageHeading title={isDuplicate() ? "Duplicate Rate Schedule" : (rateScheduleId === 'new' ? "New Rate Schedule" : "Edit Rate Schedule")} />
            {error !== null ? (
                <p
                    style={{
                        color: 'red',
                        fontWeight: 'bold',
                        marginTop: '6px',
                        fontSize: '14px'
                    }}>
                    {error}
                </p>
            ) : null}
            <br />
            <table>
                <tbody>
                    <tr>
                        <td>
                            <h5>Start Date: </h5>
                        </td>
                        <td>
                            <DatePicker value={startDate} onChange={handleStartDateChange} required={true} />
                        </td>
                    </tr>
                    {endDate && (
                        <tr>
                            <td>
                                <h5>End Date:</h5>
                            </td>
                            <td>
                                <DatePicker value={endDate} disabled={true} />
                            </td>
                        </tr>
                    )}
                </tbody>
            </table>
            <table>
                <tbody>
                    <tr>
                        <td>
                            <Button onClick={saveChanges}>
                                Save Changes {saving ? <Loader type="infinite-spinner" size="small" themeColor="light" /> : null}
                            </Button>
                        </td>
                        <td width="12px" />
                        <td>
                            <Button onClick={() => navigate('/neca-amf-rates')}>Back</Button>
                        </td>
                    </tr>
                </tbody>
            </table>


            {/* @ts-ignore The type definitions for Kendo Grid v4.14.1 seem incorrect regarding children */}
            <Grid
                data={rates}
                editField="inEdit"
                onRowClick={rowClick}
                detail={NecaAmfRateSteps}
                expandField="expanded"
                onExpandChange={expandChange}
                style={{ marginTop: '20px' }}
            >
                <Column key="command" cell={CommandCell} sortable={false} width="120px" />
                <Column key="benefit" field="benefit" title="Benefit" editable={false} />
                <Column key="category" field="category" title="Category" editable={false} />
                <Column key="calculation" field="calculation" title="Calculation" cell={calculationsGridCell} />
            </Grid>

        </div>
    );
};

export default NecaAmfRateDetails;
