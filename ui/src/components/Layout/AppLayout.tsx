import { ReactNode, useEffect, useState, useCallback } from 'react';
import { StoreState, useStore } from '@/lib';
import { ClientUtils } from "@/lib/core/ClientUtils";
import { useLocation } from 'react-router';
import ProxyComboboxes from '../ProxyComboboxes/ProxyComboboxes';
import { routesDisplayingProxyHeader } from '../ProxyHeader/WhiteListedRoutes';
import ContainerLoader from '@/src/components/UI/Loader/ContainerLoader/ContainerLoader';
import { Constants } from '@/src/constants/global';

interface AppLayoutProps {
    children: ReactNode;
}

const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {
    const selectedChapterGuid = useStore((state: StoreState) => state.selectedChapterGuid);
    const user = useStore((state: StoreState) => state.user);
    const userChapterId = user?.chapterId || 0;
    const setSelectedChapterGuid = useStore((state: StoreState) => state.setSelectedChapterGuid);

    const selectedEmployerGuid = useStore((state: StoreState) => state.selectedEmployerGuid);
    const setSelectedEmployerGuid = useStore((state: StoreState) => state.setSelectedEmployerGuid);

    const [isInitialized, setIsInitialized] = useState(false);
    const [isInitializing, setIsInitializing] = useState(false);

    // Create stable initialization callback to prevent infinite loops
    const initializeAppState = useCallback(() => {
        // For Site Sponsor (and other roles), ensure we initialize from cookie first
        const cookieChapterId = ClientUtils.getCookie(Constants.Cookies.selectedChapterID);

        // Run initialization if not initialized, OR if we have user roles but no selected chapter
        const shouldInitialize = !isInitialized || (user?.roles && !selectedChapterGuid && cookieChapterId);

        if (shouldInitialize && !isInitializing) {
            setIsInitializing(true);

            try {
                const chapterId = ClientUtils.initializeChapter(
                    user?.roles,
                    userChapterId.toString(),
                    selectedChapterGuid,
                    setSelectedChapterGuid
                );
                const employerGuid = ClientUtils.initializeEmployer(selectedEmployerGuid, setSelectedEmployerGuid);

                // If initialization was successful or we have a stored chapter ID in cookie
                if (chapterId || employerGuid || cookieChapterId) {
                    setIsInitialized(true);
                }
            } catch (error) {
                console.error('[AppLayout] Initialization error:', error);
            } finally {
                setIsInitializing(false);
            }
        }
    }, [
        user?.roles,
        userChapterId,
        selectedChapterGuid,
        selectedEmployerGuid,
        isInitialized,
        isInitializing,
        setSelectedChapterGuid,
        setSelectedEmployerGuid
    ]);

    useEffect(() => {
        initializeAppState();
    }, [initializeAppState]);

    const { pathname } = useLocation();
    const isRouteWithProxyHeader = routesDisplayingProxyHeader.some((route) => route === pathname);

    if (!isInitialized || !user?.username || isInitializing) {
        return <ContainerLoader parentStyles={{ height: '100%', flex: 1 }} />;
    }
    return (
        <>
            {!isRouteWithProxyHeader && <ProxyComboboxes username={user.username} />}
            {children}
        </>
    );
};

export default AppLayout;
