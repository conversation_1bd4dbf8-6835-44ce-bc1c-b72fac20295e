import { forwardRef, ReactNode } from 'react';
import {
  ActionButton as SpectrumActionButton,
  SpectrumActionButtonProps,
  View,
  ViewProps
} from '@adobe/react-spectrum';
import { FocusableRefValue } from '@react-types/shared';

export interface ActionButtonProps extends SpectrumActionButtonProps {
  /** The content of the button */
  children: ReactNode;
  /** Additional props to customize the wrapping View */
  wrapperProps?: ViewProps<5>;
}

const defaultWrapperStyles: Partial<ViewProps<5>> = {
  backgroundColor: 'static-blue-700',
  padding: 'size-0',
  borderRadius: 'regular',
  borderEndWidth: 'none',
  marginY: 'size-200',
  width: 'size-1600'
};

const defaultButtonStyles: SpectrumActionButtonProps = {
  staticColor: 'white',
  width: '100%'
};

export const ActionButton = forwardRef<FocusableRefValue<HTMLButtonElement, HTMLButtonElement>, ActionButtonProps>(
  ({ children, wrapperProps, ...props }, ref) => {
    return (
      <View {...defaultWrapperStyles} {...wrapperProps}>
        <SpectrumActionButton {...defaultButtonStyles} {...props} ref={ref}>
          {children}
        </SpectrumActionButton>
      </View>
    );
  }
);

ActionButton.displayName = 'ActionButton';
