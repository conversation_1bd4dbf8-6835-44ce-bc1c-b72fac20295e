import { Dispatch, SetStateAction } from 'react';
import { getNewReferenceDate } from '@/src/services/date';
import { RadioGroup, Radio, Text } from '@adobe/react-spectrum';
import { EmployerFilterInput } from '@/src/relay/__generated__/TimesheetFilterModifiedByRefetchQuery.graphql';
import { subDays, startOfYear, format } from 'date-fns';
import { DATE_FILTER_OPTIONS } from '@/src/constants/date-filter-options';

type Props = {
    label: string;
    fieldName: string;
    selectedDateRange: string;
    setSelectedDateRange: Dispatch<string>;
    onSelectionFilterUpdate: (fieldName: string, filter: EmployerFilterInput) => void;
};

const DateRadioGroup = ({ fieldName, label, selectedDateRange, setSelectedDateRange, onSelectionFilterUpdate }: Props) => {
    const onChangeHandler = (value: string) => {
        const endDate = new Date();
        let startDate;
        const selectedDateRangeObject = DATE_FILTER_OPTIONS.find((option) => option.value === value);

        if (!selectedDateRangeObject) throw Error('Invalid date range value');

        if (value === 'last_30_days') {
            // Last 30 days - subtract 30 days from current date
            startDate = getNewReferenceDate(subDays(endDate, 30));
        } else if (value === 'year_to_date') {
            // Year to date - January 1st of current year
            startDate = getNewReferenceDate(startOfYear(endDate));
        } else {
            // All Time - use 1970-01-01
            startDate = getNewReferenceDate(new Date('1970-01-01'));
        }

        // Format dates as YYYY-MM-DD for LocalDate scalar type
        const formattedEndDate = format(endDate, 'yyyy-MM-dd');
        const formattedStartDate = format(startDate, 'yyyy-MM-dd');

        // Create the filter object with the date range
        const filter: EmployerFilterInput = {
            or: [
                {
                    [fieldName]: {
                        lte: formattedEndDate,
                        gte: formattedStartDate
                    }
                }
            ]
        };

        // Store the selected value in a separate client-side state
        // This way we can still track which option was selected without sending it to the server

        if (value === '') {
            delete filter.or;
        }

        onSelectionFilterUpdate(fieldName, filter);
        setSelectedDateRange(value);
    };

    return (
        <>
            <Text maxWidth={'static-size-2600'}>{label}</Text>

            <RadioGroup value={selectedDateRange} onChange={onChangeHandler} name={label} aria-label={label} isEmphasized>
                {DATE_FILTER_OPTIONS.map(({ value, label }) => (
                    <Radio key={value} value={value}>
                        {label}
                    </Radio>
                ))}
            </RadioGroup>
        </>
    );
};

export default DateRadioGroup;
