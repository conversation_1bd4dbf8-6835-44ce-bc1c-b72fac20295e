import React from 'react';
import { 
  View, 
  Text, 
  Button, 
  Flex, 
  <PERSON>ing,
  ActionButton,
  Content,
  StatusLight
} from '@adobe/react-spectrum';
import { TimesheetError, ErrorSeverity } from '@/errorHandling';
import AlertCircle from '@spectrum-icons/workflow/AlertCircle';
import Refresh from '@spectrum-icons/workflow/Refresh';

interface ErrorDisplayProps {
  error: TimesheetError;
  onRetry?: () => void;
  onDismiss?: () => void;
  canRetry?: boolean;
  showTechnical?: boolean;
  retryCount?: number;
  maxRetries?: number;
  showTechnicalDetails?: boolean;
}

export function ErrorDisplay({ 
  error, 
  onRetry, 
  onDismiss, 
  canRetry = true,
  showTechnical = false,
  retryCount,
  maxRetries,
  showTechnicalDetails = false
}: ErrorDisplayProps) {
  const { enhancedError } = error;
  
  // Use explicit props if provided, otherwise fall back to error properties
  const currentRetryCount = retryCount ?? enhancedError.retryCount;
  const currentMaxRetries = maxRetries ?? enhancedError.maxRetries;
  
  const getSeverityColor = (severity: ErrorSeverity) => {
    switch (severity) {
      case ErrorSeverity.LOW: return 'info';
      case ErrorSeverity.MEDIUM: return 'notice';
      case ErrorSeverity.HIGH: return 'negative';
      case ErrorSeverity.CRITICAL: return 'negative';
      default: return 'notice';
    }
  };

  const getSeverityLabel = (severity: ErrorSeverity) => {
    switch (severity) {
      case ErrorSeverity.LOW: return 'Info';
      case ErrorSeverity.MEDIUM: return 'Warning';
      case ErrorSeverity.HIGH: return 'Error';
      case ErrorSeverity.CRITICAL: return 'Critical';
      default: return 'Notice';
    }
  };

  return (
    <View
      borderWidth="thin"
      borderColor="gray-400"
      borderRadius="medium"
      padding="size-200"
      backgroundColor="gray-50"
      minHeight="size-1200"
    >
      <Flex direction="column" gap="size-150">
        <Flex alignItems="center" gap="size-100">
          <AlertCircle size="M" />
          <StatusLight variant={getSeverityColor(enhancedError.severity)}>
            {getSeverityLabel(enhancedError.severity)}
          </StatusLight>
        </Flex>
        
        <Heading level={4}>{enhancedError.userMessage}</Heading>
        
        {enhancedError.suggestions.length > 0 && (
          <View>
            <Text>Suggestions:</Text>
            <ul style={{ margin: '8px 0', paddingLeft: '20px' }}>
              {enhancedError.suggestions.map((suggestion, index) => (
                <li key={index}>
                  <Text>{suggestion}</Text>
                </li>
              ))}
            </ul>
          </View>
        )}

        {(showTechnical || showTechnicalDetails) && (
          <View>
            <Text>Technical Details:</Text>
            <View 
              backgroundColor="gray-100" 
              padding="size-100" 
              borderRadius="small"
            >
              <Text>{enhancedError.technical}</Text>
            </View>
          </View>
        )}

        <Flex gap="size-100" justifyContent="end">
          {onDismiss && (
            <Button variant="secondary" onPress={onDismiss}>
              Dismiss
            </Button>
          )}
          {canRetry && onRetry && enhancedError.canRetry && (
            <ActionButton onPress={onRetry} isDisabled={currentRetryCount >= currentMaxRetries}>
              <Refresh />
              <Text>
                Retry {currentRetryCount > 0 && `(${currentRetryCount}/${currentMaxRetries})`}
              </Text>
            </ActionButton>
          )}
        </Flex>
      </Flex>
    </View>
  );
}