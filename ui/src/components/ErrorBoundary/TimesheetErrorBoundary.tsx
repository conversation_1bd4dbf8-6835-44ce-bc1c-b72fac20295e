import React, { Component, ReactNode } from 'react';
import { TimesheetError, ErrorType, ErrorSeverity } from '@/errorHandling';
import { ErrorDisplay } from '../UI/ErrorDisplay';

interface Props {
  children: ReactNode;
  fallback?: (error: TimesheetError) => ReactNode;
  onError?: (error: TimesheetError) => void;
}

interface State {
  hasError: boolean;
  error: TimesheetError | null;
}

export class TimesheetErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): State {
    // Convert to TimesheetError if it isn't already
    const timesheetError = error instanceof TimesheetError 
      ? error 
      : new TimesheetError({
          type: ErrorType.UNKNOWN,
          severity: ErrorSeverity.HIGH,
          message: error.message,
          technical: error.stack || error.message,
          context: { originalError: error }
        });

    return {
      hasError: true,
      error: timesheetError
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    const timesheetError = this.state.error;
    if (timesheetError) {
      timesheetError.enhancedError.context.componentStack = errorInfo.componentStack;
      this.props.onError?.(timesheetError);
      
      // Report to error tracking service
      if (process.env.NODE_ENV === 'production') {
        // Add your error reporting service here
        console.error('TimeSheet Error:', timesheetError);
      }
    }
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: null });
  };

  render() {
    if (this.state.hasError && this.state.error) {
      if (this.props.fallback) {
        return this.props.fallback(this.state.error);
      }

      return (
        <ErrorDisplay
          error={this.state.error}
          onRetry={this.handleRetry}
          canRetry={this.state.error.enhancedError.canRetry}
        />
      );
    }

    return this.props.children;
  }
}