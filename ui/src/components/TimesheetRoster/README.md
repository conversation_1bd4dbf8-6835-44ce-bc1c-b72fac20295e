# Timesheet Module

> **Audience** Timesheet end-users (foremen, payroll clerks) and support staff.

This guide explains how to view, create, edit and troubleshoot timesheets in the web application.  It covers the **Timesheet Roster** (list view) and **Timesheet Detail** (single-timesheet editor).

## Executive Summary

The Timesheet module streamlines payroll data entry and review by combining a high-level roster with a spreadsheet-like detail editor. Role-based permissions, autosave, inline validation, and CSV upload ensure teams can capture hours quickly and accurately while maintaining compliance.

**Key Features**
- **Timesheet Roster** – centralised list of timesheets with advanced filters, custom views and one-click export.
- **Timesheet Detail** – in-place editing of pay stubs with autosum totals and real-time validation.
- **Status Workflow** – Draft → Submitted → Approved lifecycle with audit trail and role gating.
- **Autosave & Draft Recovery** – local persistence guards against connection loss; prompts on reload.
- **Bulk Operations** – CSV upload, keyboard shortcuts and multi-row delete speed up entry.
- **Comprehensive Support Tooling** – built-in validation messages, FAQ, troubleshooting guide and dedicated support channels.


---

## Table of Contents
1. [Quick Start](#quick-start)
2. [Roles & Permissions](#roles--permissions)
3. [Timesheet Roster](#timesheet-roster)
4. [Timesheet Detail](#timesheet-detail)
5. [Status Lifecycle](#status-lifecycle)
6. [Validation & Autosave](#validation--autosave)
7. [Keyboard Shortcuts](#keyboard-shortcuts)
8. [FAQ](#faq)
9. [Troubleshooting](#troubleshooting)
10. [Support Contacts](#support-contacts)

---

## Quick Start

1. Open **Payroll ▶ Timesheets** in the main menu.  
2. Locate your pay period in the **Roster**.  
   • Filter by *Pay Period End*, *Status* or *Modified By* if needed.  
3. Click the row to enter **Detail** mode.  
4. Add or edit pay-stub rows. Changes are autosaved locally and can be **Saved** to the server at any time.  
5. When finished, press **Submit** or **Approve** depending on your role.

---

## Roles & Permissions

| Role            | Roster Access | Detail Editing | Submit | Approve |
|-----------------|--------------|----------------|--------|---------|
| Foreman         | ✔            | ✔              | ✔      | ✖       |
| Payroll Clerk   | ✔            | ✔              | ✔      | ✔       |
| Read-only User  | ✔            | ✖              | ✖      | ✖       |

*Permissions are enforced server-side; greyed-out buttons indicate insufficient privileges.*

---

## Timesheet Roster

At a glance you can list, filter and sort all timesheets.

```mermaid
flowchart TD
  Roster[Timesheet Roster]
  Roster --> Filters
  Roster --> Table
  Roster --> Footer
  Filters -->|Save/Load| Views
  Table -->|Row click| Detail
```

### Key Actions
* **Add Timesheet** – create a blank timesheet for the next pay period.
* **Table View** – switch or save column layouts & filters.
* **Export** – download CSV/Excel respecting current view.
* **Bulk Delete** – select multiple rows then press Delete.

---

## Timesheet Detail

Each timesheet contains one or more pay stubs (employees).  The page is divided into **Toolbar** and **Pay-Stub Tables**.

```mermaid
flowchart TD
  Detail[Timesheet Detail]
  Detail --> Toolbar
  Detail --> StubTables
  StubTables --> StubRow[Pay-Stub Table]
  StubRow --> Rows[Line Items]
```

### Toolbar buttons
| Button  | Purpose |
|---------|---------|
| Save    | Persist all drafts to the server |
| Reset   | Revert unsaved changes |
| Upload  | Import a CSV to add multiple stubs |
| Settings| Toggle column visibility |

### Editing pay-stub tables
* **Inline cells** – click to edit; press **Enter** to commit.
* **Add row** – `+` at bottom of table or **Ctrl + N**.
* **Delete row** – trash-can icon or **Del** key.

---

## Status Lifecycle

```mermaid
stateDiagram-v2
  [*] --> Draft
  Draft --> Submitted : Submit
  Submitted --> Approved : Approve
  Approved --> Reopened : Reopen
  Reopened --> Draft
```

*Only users with the appropriate role may move a timesheet forward/backward in the lifecycle.*

---

## Validation & Autosave

1. **Field-level** – immediate feedback (e.g. hours must be numeric).
2. **Row-level** – totals recalculated on the fly.
3. **Timesheet-level** – server enforces completeness on Save.

Your edits are cached in the browser (localStorage) every few seconds.  If you refresh or lose connection you will be prompted to restore drafts.

---

## Keyboard Shortcuts

| Shortcut | Action |
|----------|--------|
| **Ctrl + S** | Save current timesheet |
| **Ctrl + Z / Y** | Undo / redo cell edit |
| **Ctrl + N** | Add new detail row |
| **Del** | Delete selected row |
| **Esc** | Exit cell edit |

---

## FAQ

**Q • Why is the *Save* button disabled?**  
A • You have no unsaved changes or lack permission.  

**Q • Totals don’t match after editing hours.**  
A • Ensure each row’s **ST/OT/DT** hours sum to the line total; the system highlights mismatches in red.

**Q • Can I edit an *Approved* timesheet?**  
A • Yes, but only after choosing **Reopen** in the toolbar (payroll clerk role required).

---

## Troubleshooting

| Symptom | Possible Cause | Resolution |
|---------|----------------|------------|
| Timesheet won’t load | Backend outage | Check status page; retry later |
| “Validation failed” on Save | Missing required fields | Use **Show Errors** link in toolbar |
| Cannot find employee in drop-down | Employee inactive | Contact HR to reactivate |

For browser-console error logs press **F12 ▶ Console**, copy the message and attach it to your support ticket.

---

## Support Contacts

| Channel      | Availability | Notes |
|--------------|--------------|-------|
| Slack `#timesheet-support` | 08:00-17:00 PST | Immediate chat support |
| Email `<EMAIL>` | 24 × 5 | Ticket tracked |
| Phone 1-855-PAY-HELP | Emergency only | After hours escalation |

Please include: timesheet ID, pay period, steps to reproduce, screenshots and browser console logs.




## 2. Timesheet Roster

The Timesheet Roster is the main landing page for the Timesheet module. It displays a list of all timesheets and provides tools for managing them.

### 2.1. Roster Layout

The roster is composed of three main sections:

1.  **Header**: Contains action buttons, view management tools, and filters.
2.  **Table**: Displays the list of timesheets with customizable columns.
3.  **Footer**: Shows the total number of records.

```mermaid
graph TD
    A[Timesheet Roster] --> B{Header};
    A --> C{Table};
    A --> D{Footer};

    B --> B1[Add Timesheet];
    B --> B2[Export];
    B --> B3[Save View];
    B --> B4[Table View];
    B --> B5[Columns];
    B --> B6[Filters];

    C --> C1[Timesheet Rows];
    C1 --> C2[Dynamic Columns];
    C1 --> C3[Row Actions];

    D --> D1[Total Record Count];
```

### 2.2. Header Actions

The header provides the following actions:

-   **Add Timesheet**: Opens a dialog to create a new timesheet.
-   **Export**: Exports the current view of the roster data to a file (e.g., CSV, Excel).
-   **Save View**: Saves the current column layout, sorting, and filter settings as a custom view.
-   **Table View**: Allows you to switch between saved custom views and system default views.
-   **Columns**: Toggles the visibility of columns in the table.
-   **Filters**: Opens a dialog to apply filters to the timesheet data.

### 2.3. Filtering and Sorting

The roster can be filtered by various criteria, including:

-   Pay Period End Date
-   Status (e.g., Draft, Submitted, Approved)
-   Modified By
-   Creation Date
-   Modification Date

You can sort the data by clicking on any column header. The default sort is by **Pay Period End Date** in descending order.

### 2.4. Custom Views

Custom views allow you to save and reuse specific configurations of the roster table. You can:

-   **Create a new view**: Arrange the columns, apply filters and sorting, and then save the view with a custom name and description.
-   **Switch between views**: Use the "Table View" dropdown to load a saved view.
-   **Update a view**: Make changes to a loaded view and save it to update the existing view.
-   **Set a default view**: Choose a custom or system view to be the default when you open the roster.
-   **Rename or delete a view**: Manage your saved views from the "Table View" dropdown.

---

## 3. Timesheet Detail

The Timesheet Detail page provides an in-depth view of a single timesheet, including all its pay stubs and line-item details.

### 3.1. Detail Page Layout

The detail page is organized as follows:

1.  **Toolbar**: Contains actions related to the entire timesheet (e.g., save, reset, settings).
2.  **Pay Stub Tables**: A series of tables, one for each pay stub associated with the timesheet.

```mermaid
graph TD
    A[Timesheet Detail] --> B{Toolbar};
    A --> C{Pay Stub Tables};

    B --> B1[Save];
    B --> B2[Reset];
    B --> B3[Settings];
    B --> B4[Upload];

    C --> C1[Pay Stub 1];
    C --> C2[Pay Stub 2];
    C --> C3[...];

    C1 --> D1[Detail Rows];
    D1 --> E1[Editable Cells];
    D1 --> E2[Row Actions];
```

### 3.2. Toolbar Actions

-   **Save**: Saves all changes made to the timesheet and its pay stubs.
-   **Reset**: Discards all unsaved changes.
-   **Settings**: Opens a dialog to configure settings for the timesheet, such as column visibility for all pay stub tables.
-   **Upload**: Allows you to upload a timesheet file to populate the data.

### 3.3. Editing Pay Stubs

Each pay stub is displayed in its own table. You can perform the following actions:

-   **Edit Cells**: Click on an editable cell to change its value. Editable columns include `Job Code`, `Agreement`, `Classification`, `ST Hours`, `OT Hours`, etc.
-   **Add/Remove Rows**: Use the row actions to add a new line item or delete an existing one.
-   **Auto-filling**: When you add a new row, some fields may be auto-filled based on the employee's default settings.

### 3.4. Data Validation

The system performs validation at multiple levels to ensure data integrity:

-   **Field-level validation**: Ensures that data entered into cells is in the correct format (e.g., numbers for hours, valid dates).
-   **Row-level validation**: Checks for consistency within a single row (e.g., total hours calculation).
-   **Pay stub-level validation**: Verifies that the sum of detail hours matches the pay stub's total hours.
-   **Timesheet-level validation**: Ensures that all required information is present before saving.

If there are validation errors, the system will highlight the problematic fields and provide error messages to guide you in correcting the data.

---

## 4. Workflows

### 4.1. Creating a New Timesheet

1.  From the **Timesheet Roster**, click the **Add Timesheet** button.
2.  Fill in the required information in the dialog, such as the pay period end date.
3.  Click **Create**. You will be redirected to the **Timesheet Detail** page for the new timesheet.
4.  Add pay stubs and line-item details as needed.
5.  Click **Save** to save the new timesheet.

### 4.2. Editing an Existing Timesheet

1.  From the **Timesheet Roster**, click on the name of the timesheet you want to edit.
2.  This will take you to the **Timesheet Detail** page.
3.  Make the necessary changes to the pay stub details.
4.  Click **Save** to save your changes.

### 4.3. Deleting a Timesheet

1.  From the **Timesheet Roster**, locate the timesheet you want to delete.
2.  Click the delete icon in the actions column for that row.
3.  Confirm the deletion in the confirmation dialog.

---

## 5. Support

If you encounter any issues or have questions about the Timesheet module, please contact the support team with the following information:

-   A clear description of the issue.
-   The steps to reproduce the issue.
-   The name and pay period of the timesheet you were working on.
-   Screenshots of any error messages.
