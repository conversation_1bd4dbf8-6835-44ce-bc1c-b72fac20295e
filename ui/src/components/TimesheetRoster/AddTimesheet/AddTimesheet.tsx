import React, { useState } from 'react';
import { useNavigate } from 'react-router';
import Add from '@spectrum-icons/workflow/Add';
import { Content, Dialog, Divider } from '@adobe/react-spectrum';
import { TIMESHEET_TYPES } from '@/src/constants/timesheet-roster';
import { LABEL_TEXT, BUTTON_TEXT, TIMESHEET_TEXT } from '@/src/constants/text';
import { Form, Heading, Text, Button, Flex, DatePicker } from '@adobe/react-spectrum';
import { ActionButton, ButtonGroup, Picker, Item, DialogContainer } from '@adobe/react-spectrum';
import { useMutation, graphql, ConnectionHandler } from 'react-relay';
import { TimesheetDetailAddMutation, addTimesheetUpdater } from '@/src/hooks/useTimesheetSaver';
import type { AddTimesheetInput, useTimesheetSaverAddMutation$variables, useTimesheetSaverAddMutation$data, useTimesheetSaverAddMutation } from '@/src/types/graphql-timesheet';
import { PayloadError } from 'relay-runtime';
import { useTimesheetRosterFilterStore } from '@/src/store/rosterFilterStore';
import { useStore, StoreState } from '@/lib';

type Props = {};

const AddTimesheet = (props: Props) => {
    const navigate = useNavigate();
    const [isOpen, setOpen] = React.useState(false);
    const [isCreating, setIsCreating] = useState(false);
    const [creationError, setCreationError] = useState<string | null>(null);
    const [commitAddMutation, isMutationInFlight] = useMutation<useTimesheetSaverAddMutation>(TimesheetDetailAddMutation);
    const employerGuid = useStore((state: StoreState) => state.selectedEmployerGuid);
    const activeRosterFilters = useTimesheetRosterFilterStore((state) => state.activeFilters);
    const activeRosterSortOrder = useTimesheetRosterFilterStore((state) => state.activeSortOrder);

    const onCancelHandler = () => {
        setOpen(false);
        setCreationError(null);
        setIsCreating(false);
    };

    const onSubmitHandler = (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();

        setIsCreating(true);
        setCreationError(null);

        const formData = new FormData(e.currentTarget);
        const rawWeekEndDate = formData.get('weekEndDate') as string | null;
        const type = formData.get('type') as string | null;

        if (!rawWeekEndDate || !type) {
            setCreationError('Week End Date and Type are required.');
            setIsCreating(false);
            return;
        }

        const addInput: AddTimesheetInput = {
            employerGuid: employerGuid,
            name: `Timesheet - ${rawWeekEndDate}`,
            payPeriodEndDate: rawWeekEndDate,
            status: 'New',
            type: type,
            showBonusColumn: false,
            showCostCenterColumn: false,
            showDTHoursColumn: false,
            showEarningsCodesColumn: false,
            showExpensesColumn: false,
            payStubs: []
        };

        commitAddMutation({
            variables: {
                input: addInput
            },
            updater: (store) => addTimesheetUpdater(store, activeRosterFilters, activeRosterSortOrder),
            onCompleted: (response, errors) => {
                setIsCreating(false);
                if (errors) {
                    const errorMessage = errors.map((e: PayloadError) => e.message).join('\n');
                    setCreationError(`Failed to create timesheet: ${errorMessage}`);
                } else if (response?.addTimesheet?.timeSheetEdge?.node?.id) {
                    const newId = response.addTimesheet.timeSheetEdge.node.id;
                    setOpen(false);
                    navigate(`/timesheet-roster/detail/?id=${newId}&employerGuid=${employerGuid}`);
                } else {
                    setCreationError('Failed to create timesheet: Invalid response from server.');
                }
            },
            onError: (error) => {
                setIsCreating(false);
                setCreationError(`An error occurred: ${error.message}`);
            }
        });
    };

    return (
        <>
            <ActionButton isQuiet onPress={() => setOpen(true)}>
                <Add size="XS" />
                <Text>{LABEL_TEXT.ADD_TIMESHEET_BTN}</Text>
            </ActionButton>
            {isOpen && (
                <DialogContainer onDismiss={onCancelHandler}>
                    <Dialog size="M">
                        <Heading>
                            <Flex alignItems="center" gap="size-100">
                                {LABEL_TEXT.ADD_TIMESHEET_BTN}
                            </Flex>
                        </Heading>
                        <Divider />
                        <Content>
                            <Text>{TIMESHEET_TEXT.ADD_DIALOG_DESCRIPTION}</Text>

                            <Form validationBehavior="native" onSubmit={onSubmitHandler}>
                                <DatePicker
                                    label={TIMESHEET_TEXT.WEEK_END_DATE_LABEL}
                                    name="weekEndDate"
                                    width={'static-size-2600'}
                                    isRequired
                                />

                                <Picker
                                    label={LABEL_TEXT.TYPE}
                                    name="type"
                                    width={'static-size-2600'}
                                    defaultSelectedKey={TIMESHEET_TYPES[0]}
                                    isRequired>
                                    {TIMESHEET_TYPES.map((type) => (
                                        <Item key={type} textValue={type}>
                                            {type}
                                        </Item>
                                    ))}
                                </Picker>

                                {/* Wrap error and buttons in fragment */}
                                <>
                                    {/* Error display moved outside the Form but inside Content */}
                                    {creationError ? (
                                        <div
                                            style={{
                                                marginTop: 'var(--spectrum-global-dimension-size-200)',
                                                color: 'var(--spectrum-semantic-negative-color-text-small)'
                                            }}>
                                            <Text>{creationError}</Text>
                                        </div>
                                    ) : null}

                                    <ButtonGroup marginTop={'size-500'} alignSelf={'end'} align="end">
                                        <Button
                                            variant="secondary"
                                            type="button"
                                            onPress={onCancelHandler}
                                            isDisabled={isCreating || isMutationInFlight}>
                                            {BUTTON_TEXT.CANCEL}
                                        </Button>
                                        <Button variant={'accent'} type="submit" isDisabled={isCreating || isMutationInFlight}>
                                            {isCreating || isMutationInFlight ? 'Creating...' : BUTTON_TEXT.CONFIRM}
                                        </Button>
                                    </ButtonGroup>
                                </>
                            </Form>
                        </Content>
                    </Dialog>
                </DialogContainer>
            )}
        </>
    );
};

export default AddTimesheet;
