import { cloneDeep } from 'lodash';
import { INITIAL_FILTER_DATA } from '../data';
import { TimeSheetFilterInput } from '@/lib/relay/__generated__/TimesheetRosterQuery.graphql';

// Mock function to test the logic of initializeDataFromFilter
function testInitializeDataFromFilter(
    localFilter: TimeSheetFilterInput,
    modifiedBy: { value: string; label: string | null | undefined }[]
) {
    const updatedData = cloneDeep(INITIAL_FILTER_DATA);

    // Only process if we have a valid filter structure
    if (localFilter?.and && localFilter.and.length > 0) {
        // Process each condition in the filter
        localFilter.and.forEach((condition) => {
            if (condition.or && condition.or.length > 0) {
                try {
                    // Get the field name from the first OR condition
                    const filterField = Object.keys(condition.or[0])[0];
                    // Use type assertion to avoid TypeScript error
                    const filterValueObject = condition.or[0][filterField as keyof (typeof condition.or)[0]] as any;

                    if (!filterField || !filterValueObject) return;

                    switch (filterField) {
                        case 'status':
                            // Handle status filter (typically uses 'contains' or 'eq' operator)
                            if (filterValueObject.contains) {
                                updatedData.status = filterValueObject.contains;
                            } else if (filterValueObject.eq) {
                                updatedData.status = filterValueObject.eq;
                            }
                            break;

                        case 'type':
                            // Handle type filter (typically uses 'contains' or 'eq' operator)
                            if (filterValueObject.contains) {
                                updatedData.type = filterValueObject.contains;
                            } else if (filterValueObject.eq) {
                                updatedData.type = filterValueObject.eq;
                            }
                            break;

                        case 'payPeriodEndDate':
                            // Handle date range filters for pay period end date
                            if (filterValueObject.gte && filterValueObject.lte) {
                                // Determine which predefined date range this corresponds to
                                const now = new Date();
                                const startDate = new Date(filterValueObject.gte);
                                const daysDiff = Math.round((now.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

                                if (daysDiff >= 360 && daysDiff <= 370) {
                                    // Year to date (approximately 365 days)
                                    updatedData.payPeriodEndDate = 'year_to_date';
                                } else if (daysDiff >= 25 && daysDiff <= 35) {
                                    // Last 30 days (approximately 30 days)
                                    updatedData.payPeriodEndDate = 'last_30_days';
                                } else if (startDate.getFullYear() <= 1970) {
                                    // All time (date close to 1970-01-01)
                                    updatedData.payPeriodEndDate = '';
                                }
                            } else if (!filterValueObject.gte && !filterValueObject.lte) {
                                // No date range specified, use 'All Time'
                                updatedData.payPeriodEndDate = '';
                            }
                            break;

                        case 'modificationDate':
                            // Handle date range filters for modification date
                            if (filterValueObject.gte && filterValueObject.lte) {
                                const now = new Date();
                                const startDate = new Date(filterValueObject.gte);
                                const daysDiff = Math.round((now.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

                                if (daysDiff >= 360 && daysDiff <= 370) {
                                    updatedData.modificationDate = 'year_to_date';
                                } else if (daysDiff >= 25 && daysDiff <= 35) {
                                    updatedData.modificationDate = 'last_30_days';
                                } else if (startDate.getFullYear() <= 1970) {
                                    updatedData.modificationDate = '';
                                }
                            } else if (!filterValueObject.gte && !filterValueObject.lte) {
                                updatedData.modificationDate = '';
                            }
                            break;

                        case 'modifiedByUserId':
                            // Handle modifiedByUserId filters (typically uses 'in' operator for multi-select)
                            if (filterValueObject.in && Array.isArray(filterValueObject.in)) {
                                const userSet = new Set<{ value: string; label: string }>();

                                // For each user ID in the filter, find the corresponding user in the modifiedBy options
                                filterValueObject.in.forEach((userId: string) => {
                                    // Try to find the user in the modifiedBy options
                                    const userOption = modifiedBy.find((option) => option.value === userId);

                                    if (userOption && userOption.label) {
                                        // If found, add to the set with proper label
                                        userSet.add({ value: userId, label: userOption.label });
                                    } else {
                                        // If not found, use the ID as both value and label
                                        userSet.add({ value: userId, label: userId });
                                    }
                                });

                                updatedData.modifiedByUserId = userSet;
                            } else if (filterValueObject.eq) {
                                // Handle single selection case (eq operator)
                                const userId = filterValueObject.eq;
                                const userOption = modifiedBy.find((option) => option.value === userId);

                                const userSet = new Set<{ value: string; label: string }>();
                                if (userOption && userOption.label) {
                                    userSet.add({ value: userId, label: userOption.label });
                                } else {
                                    userSet.add({ value: userId, label: userId });
                                }

                                updatedData.modifiedByUserId = userSet;
                            }
                            break;
                    }
                } catch (error) {
                    console.error('Error parsing filter condition:', condition, error);
                }
            }
        });
    }

    return updatedData;
}

// Test cases
describe('initializeDataFromFilter', () => {
    // Test case for status filter
    test('should handle status filter with contains operator', () => {
        const filter: TimeSheetFilterInput = {
            and: [
                {
                    or: [
                        {
                            status: { contains: 'Submitted' }
                        }
                    ]
                }
            ]
        };

        const result = testInitializeDataFromFilter(filter, []);
        expect(result.status).toBe('Submitted');
    });

    // Add more test cases as needed
});
