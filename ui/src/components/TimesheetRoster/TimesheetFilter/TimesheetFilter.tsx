import React, { useMemo, useState, useCallback, useEffect, useRef } from 'react';
import { Form } from '@adobe/react-spectrum';
import { Flex } from '@adobe/react-spectrum';
import _, { cloneDeep } from 'lodash';
import { graphql } from 'relay-runtime';
import { usePaginationFragment } from 'react-relay';
import TableAction from '../../UI/SavedViews/TableAction';
import Properties from '@spectrum-icons/workflow/Properties';
import Accordion from '@/src/components/UI/Accordion/Accordion';
import { INITIAL_FILTER_DATA, TimesheetFilterData } from './data';
import { Content, Text, View } from '@adobe/react-spectrum';
import { DialogTrigger, Dialog, Key } from '@adobe/react-spectrum';
import {
    getModifiedByOptionsFromTimesheetRoster,
    getDefaultTimesheetRosterQueryVariables,
    getFilterWhereByColumnData
} from '@/src/services/timesheet-roster';
import ComboBoxFilter from '@/src/components/UI/ComboBoxFilter/ComboBoxFilter';
import DateRadioGroup from '@/src/components/UI/DateRadioGroup/DateRadioGroup';
import { DATE_FILTER_OPTIONS } from '@/src/constants/date-filter-options';
import { ColumnType } from '@/src/types/rosters';
import StaticFilterSelector from '@/src/components/UI/StaticFilterSelector/StaticFilterSelector';
import { TimeSheetFilterInput } from '@/lib/relay/__generated__/TimesheetRosterQuery.graphql';
import { ViewProps, SpectrumActionButtonProps, ActionButton, Divider } from '@adobe/react-spectrum';
import { TimesheetStatusOptions, TimesheetTypeOptions } from '../TimesheetRosterTable/TimesheetRoster.data';
import { EmployerFilterInput } from '@/lib/relay/__generated__/TimesheetFilterModifiedByRefetchQuery.graphql';
import { filterButtonStylesData, filterButtonWrapperStylesData } from '../../EmployerRoster/EmployerFilter/data';
import DisplayFilterConditionally from '@/src/components/UI/DisplayFilterConditionally/DisplayFilterConditionally';
import { TimesheetFilterModifiedByFragment$key } from '@/lib/relay/__generated__/TimesheetFilterModifiedByFragment.graphql';
import Close from '@spectrum-icons/workflow/Close';
import { TimeSheetFilterInput as ZustandTimeSheetFilterInput } from '@/lib/relay/__generated__/TimesheetRosterQuery.graphql'; // Keep for prop type
import { parseISO, isValid, subDays, isSameDay, format } from 'date-fns';

type Props = {
    employerGuid: string;
    data: TimesheetFilterData;
    setData: React.Dispatch<React.SetStateAction<TimesheetFilterData>>;
    backupData: TimesheetFilterData;
    setBackupData: React.Dispatch<React.SetStateAction<TimesheetFilterData>>;
    localFilter: TimeSheetFilterInput;
    setLocalFilter: React.Dispatch<React.SetStateAction<TimeSheetFilterInput>>;
    backupLocalFilter: TimeSheetFilterInput;
    setBackupLocalFilter: (filter: TimeSheetFilterInput) => void;
    columnData: ColumnType[];
    queryRef: TimesheetFilterModifiedByFragment$key;
    onFilterChange: (filter: TimeSheetFilterInput) => void;
    activeFilters: ZustandTimeSheetFilterInput | null;
};

const TimesheetFilterModifiedByFragment = graphql`
    fragment TimesheetFilterModifiedByFragment on Query
    @argumentDefinitions(
        first: { type: "Int", defaultValue: 20 }
        after: { type: "String" }
        employerGuid: { type: "UUID!" }
        order: { type: "[TimeSheetSortInput!]", defaultValue: null }
        where: { type: "TimeSheetFilterInput", defaultValue: null }
    )
    @refetchable(queryName: "TimesheetFilterModifiedByRefetchQuery") {
        timesheetsByEmployerGuid(first: $first, after: $after, employerGuid: $employerGuid, order: $order, where: $where)
            @connection(key: "TimesheetRosterTableFragment_timesheetsByEmployerGuid") {
            edges {
                node {
                    id
                    modifiedByUserId
                }
            }
            pageInfo {
                hasPreviousPage
                startCursor
            }
            totalCount
        }
    }
`;

const TimesheetFilter = ({
    employerGuid,
    data,
    backupData,
    localFilter,
    backupLocalFilter,
    setData,
    setBackupData,
    setLocalFilter,
    setBackupLocalFilter,
    columnData,
    queryRef,
    onFilterChange,
    activeFilters // Prop received but not used for count/clear
}: Props) => {
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    // Track if the Modified By accordion is open
    const [isModifiedByAccordionOpen, setIsModifiedByAccordionOpen] = useState(false);
    // Track if the user has interacted with the Modified By filter
    const [hasInteractedWithModifiedBy, setHasInteractedWithModifiedBy] = useState(false);

    const {
        data: timesheetFilterModifiedByFragmentData,
        loadNext: loadNextModifiedByTimesheet,
        isLoadingNext: isLoadingModifiedByTimesheet,
        hasNext: hasNextModifiedByTimesheet,
        refetch: refetchModifiedByTimesheet
    } = usePaginationFragment(TimesheetFilterModifiedByFragment, queryRef);

    // Calculate filter count from both local filter and Zustand store
    const filtersCount = useMemo(() => {
        // First check if we have active filters from Zustand store
        if (activeFilters && activeFilters.and && activeFilters.and.length > 0) {
            return getFilterWhereByColumnData(activeFilters, columnData)?.length ?? 0;
        }

        // Fall back to local filter if no active filters in Zustand store
        return (backupLocalFilter && columnData ? getFilterWhereByColumnData(backupLocalFilter, columnData)?.length : 0) ?? 0;
    }, [backupLocalFilter, columnData, activeFilters]);

    const showClearButton: boolean = useMemo(() => filtersCount > 0, [filtersCount]);

    // Use a ref to store the previous modifiedBy options to prevent unnecessary re-renders
    const prevModifiedByRef = useRef<{ value: string; label: string | null | undefined }[]>([]);

    // Only fetch and process modifiedBy data when needed
    const modifiedBy = useMemo(() => {
        // If the user hasn't interacted with the Modified By filter and there are no active filters for it,
        // return an empty array to avoid unnecessary data fetching
        if (
            !hasInteractedWithModifiedBy &&
            (!localFilter?.and ||
                !localFilter.and.some(
                    (condition) => condition.or && condition.or.length > 0 && Object.keys(condition.or[0])[0] === 'modifiedByUserId'
                ))
        ) {
            return [];
        }

        const newModifiedBy = getModifiedByOptionsFromTimesheetRoster(timesheetFilterModifiedByFragmentData) ?? [];

        // If the new options are the same as the previous ones, return the previous reference
        if (_.isEqual(newModifiedBy, prevModifiedByRef.current)) {
            return prevModifiedByRef.current;
        }

        // Otherwise, update the ref and return the new options
        prevModifiedByRef.current = newModifiedBy;
        return newModifiedBy;
    }, [timesheetFilterModifiedByFragmentData, hasInteractedWithModifiedBy, localFilter]);
    const filterButtonWrapperStyles: ViewProps<5> = showClearButton ? filterButtonWrapperStylesData : {};
    const filterButtonStyles: SpectrumActionButtonProps = showClearButton ? filterButtonStylesData : {};
    const isDisabled = useMemo(() => _.isEqual(localFilter, backupLocalFilter), [localFilter, backupLocalFilter]);

    // Function to initialize data state from localFilter
    const initializeDataFromFilter = useCallback(() => {
        // Create a fresh copy of initial data
        const updatedData = cloneDeep(INITIAL_FILTER_DATA);

        // Create a single 'now' instance to be used throughout this function
        const now = new Date();

        // Only process if we have a valid filter structure
        if (localFilter?.and && localFilter.and.length > 0) {
            // Process each condition in the filter
            localFilter.and.forEach((condition) => {
                if (condition.or && condition.or.length > 0) {
                    try {
                        // Get the field name from the first OR condition
                        const filterField = Object.keys(condition.or[0])[0];

                        // Define more specific types for filter value objects
                        type StringFilterOperators = {
                            contains?: string;
                            eq?: string;
                            in?: string[];
                        };

                        type DateFilterOperators = {
                            gte?: string;
                            lte?: string;
                            _key?: string; // For storing original option key
                        };

                        // Use a more specific type assertion with union type
                        const filterValueObject = condition.or[0][filterField as keyof (typeof condition.or)[0]] as
                            | StringFilterOperators
                            | DateFilterOperators;

                        // Type guard functions to check filter value object structure
                        const hasStringOperators = (obj: any): obj is StringFilterOperators =>
                            'contains' in obj || 'eq' in obj || 'in' in obj;

                        const hasDateOperators = (obj: any): obj is DateFilterOperators => 'gte' in obj || 'lte' in obj || '_key' in obj;

                        if (!filterField || !filterValueObject) return;

                        switch (filterField) {
                            case 'status':
                                // Handle status filter (typically uses 'contains' or 'eq' operator)
                                if (hasStringOperators(filterValueObject)) {
                                    if (filterValueObject.contains) {
                                        updatedData.status = filterValueObject.contains;
                                    } else if (filterValueObject.eq) {
                                        updatedData.status = filterValueObject.eq;
                                    }
                                }
                                break;

                            case 'type':
                                // Handle type filter (typically uses 'contains' or 'eq' operator)
                                if (hasStringOperators(filterValueObject)) {
                                    if (filterValueObject.contains) {
                                        updatedData.type = filterValueObject.contains;
                                    } else if (filterValueObject.eq) {
                                        updatedData.type = filterValueObject.eq;
                                    }
                                }
                                break;

                            case 'payPeriodEndDate':
                                // Handle date range filters for pay period end date
                                if (hasDateOperators(filterValueObject)) {
                                    // First check if we have the _key property which stores the original option value
                                    if (filterValueObject._key !== undefined) {
                                        // If we have the _key property, use it directly
                                        updatedData.payPeriodEndDate = filterValueObject._key;
                                    } else if (filterValueObject.gte && filterValueObject.lte) {
                                        // If no _key, try to determine which predefined date range this corresponds to
                                        // Using the shared 'now' instance

                                        // For LocalDate format (YYYY-MM-DD), we need to check the string format
                                        const startDateStr: string = filterValueObject.gte;

                                        // Parse and validate the date using date-fns
                                        const parsedStartDate = parseISO(startDateStr);
                                        if (!isValid(parsedStartDate)) {
                                            console.error(
                                                `Invalid date string encountered in payPeriodEndDate filter: '${startDateStr}'. Defaulting to All Time.`
                                            );
                                            updatedData.payPeriodEndDate = ''; // Default to All Time
                                            break;
                                        }

                                        const currentYear = now.getFullYear();

                                        // Check if the start date is January 1st of the current year (Year to date)
                                        const yearStartDateStr: string = `${currentYear}-01-01`;
                                        const isYearToDate = startDateStr === yearStartDateStr;

                                        // Calculate the date 30 days ago using date-fns
                                        const thirtyDaysAgoDate = subDays(now, 30);

                                        // Format to string for comparison if needed
                                        const thirtyDaysAgoStr = format(thirtyDaysAgoDate, 'yyyy-MM-dd');

                                        // Use isSameDay for precise date comparison, ignoring time components
                                        const isLast30Days = isSameDay(parsedStartDate, thirtyDaysAgoDate);

                                        // Check if it's 1970-01-01 (All time)
                                        const isAllTime = startDateStr === '1970-01-01';

                                        if (isYearToDate) {
                                            // Year to date - start date is January 1st of current year
                                            updatedData.payPeriodEndDate = 'year_to_date';
                                        } else if (isLast30Days) {
                                            // Last 30 days
                                            updatedData.payPeriodEndDate = 'last_30_days';
                                        } else if (isAllTime) {
                                            // All time
                                            updatedData.payPeriodEndDate = '';
                                        } else {
                                            // If we can't determine the predefined range, default to empty (All Time)
                                            // This is a safer default than showing an incorrect selection
                                            updatedData.payPeriodEndDate = '';
                                            console.warn(
                                                'Could not determine predefined date range for payPeriodEndDate filter',
                                                filterValueObject
                                            );
                                        }
                                    } else if (filterValueObject.gte && !filterValueObject.lte) {
                                        // Handle case with only start date (from X to now)
                                        // Using the shared 'now' instance

                                        // For LocalDate format (YYYY-MM-DD), we need to check the string format
                                        const startDateStr: string = filterValueObject.gte;

                                        // Parse and validate the date using date-fns
                                        const parsedStartDate = parseISO(startDateStr);
                                        if (!isValid(parsedStartDate)) {
                                            console.error(
                                                `Invalid date string encountered in payPeriodEndDate filter: '${startDateStr}'. Defaulting to All Time.`
                                            );
                                            updatedData.payPeriodEndDate = ''; // Default to All Time
                                            break;
                                        }

                                        const currentYear = now.getFullYear();

                                        // Check if the start date is January 1st of the current year (Year to date)
                                        const yearStartDateStr: string = `${currentYear}-01-01`;
                                        const isYearToDate = startDateStr === yearStartDateStr;

                                        // Calculate the date 30 days ago using date-fns
                                        const thirtyDaysAgoDate = subDays(now, 30);

                                        // Format to string for comparison if needed
                                        const thirtyDaysAgoStr = format(thirtyDaysAgoDate, 'yyyy-MM-dd');

                                        // Use isSameDay for precise date comparison, ignoring time components
                                        const isLast30Days = isSameDay(parsedStartDate, thirtyDaysAgoDate);

                                        // Check if it's 1970-01-01 (All time)
                                        const isAllTime = startDateStr === '1970-01-01';

                                        if (isYearToDate) {
                                            // Year to date - start date is January 1st of current year
                                            updatedData.payPeriodEndDate = 'year_to_date';
                                        } else if (isLast30Days) {
                                            // Last 30 days
                                            updatedData.payPeriodEndDate = 'last_30_days';
                                        } else if (isAllTime) {
                                            // All time
                                            updatedData.payPeriodEndDate = '';
                                        } else {
                                            updatedData.payPeriodEndDate = '';
                                        }
                                    } else if (!filterValueObject.gte && filterValueObject.lte) {
                                        // Handle case with only end date (everything before X)
                                        // This doesn't match our predefined options well, so default to All Time
                                        updatedData.payPeriodEndDate = '';
                                    } else {
                                        // No date range specified, use 'All Time'
                                        updatedData.payPeriodEndDate = '';
                                    }
                                }
                                break;

                            case 'modificationDate':
                                // Handle date range filters for modification date
                                if (hasDateOperators(filterValueObject)) {
                                    // First check if we have the _key property which stores the original option value
                                    if (filterValueObject._key !== undefined) {
                                        // If we have the _key property, use it directly
                                        updatedData.modificationDate = filterValueObject._key;
                                    } else if (filterValueObject.gte && filterValueObject.lte) {
                                        // If no _key, try to determine which predefined date range this corresponds to
                                        // Using the shared 'now' instance

                                        // For LocalDate format (YYYY-MM-DD), we need to check the string format
                                        const startDateStr: string = filterValueObject.gte;

                                        // Parse and validate the date using date-fns
                                        const parsedStartDate = parseISO(startDateStr);
                                        if (!isValid(parsedStartDate)) {
                                            console.error(
                                                `Invalid date string encountered in modificationDate filter: '${startDateStr}'. Defaulting to All Time.`
                                            );
                                            updatedData.modificationDate = ''; // Default to All Time
                                            break;
                                        }

                                        const currentYear = now.getFullYear();

                                        // Check if the start date is January 1st of the current year (Year to date)
                                        const yearStartDateStr: string = `${currentYear}-01-01`;
                                        const isYearToDate = startDateStr === yearStartDateStr;

                                        // Calculate the date 30 days ago using date-fns
                                        const thirtyDaysAgoDate = subDays(now, 30);

                                        // Format to string for comparison if needed
                                        const thirtyDaysAgoStr = format(thirtyDaysAgoDate, 'yyyy-MM-dd');

                                        // Use isSameDay for precise date comparison, ignoring time components
                                        const isLast30Days = isSameDay(parsedStartDate, thirtyDaysAgoDate);

                                        // Check if it's 1970-01-01 (All time)
                                        const isAllTime = startDateStr === '1970-01-01';

                                        if (isYearToDate) {
                                            // Year to date - start date is January 1st of current year
                                            updatedData.modificationDate = 'year_to_date';
                                        } else if (isLast30Days) {
                                            // Last 30 days
                                            updatedData.modificationDate = 'last_30_days';
                                        } else if (isAllTime) {
                                            // All time
                                            updatedData.modificationDate = '';
                                        } else {
                                            // If we can't determine the predefined range, default to empty (All Time)
                                            // This is a safer default than showing an incorrect selection
                                            updatedData.modificationDate = '';
                                            console.warn(
                                                'Could not determine predefined date range for modificationDate filter',
                                                filterValueObject
                                            );
                                        }
                                    } else if (filterValueObject.gte && !filterValueObject.lte) {
                                        // For LocalDate format (YYYY-MM-DD), we need to check the string format
                                        const startDateStr: string = filterValueObject.gte;

                                        // Parse and validate the date using date-fns
                                        const parsedStartDate = parseISO(startDateStr);
                                        if (!isValid(parsedStartDate)) {
                                            console.error(
                                                `Invalid date string encountered in modificationDate filter: '${startDateStr}'. Defaulting to All Time.`
                                            );
                                            updatedData.modificationDate = ''; // Default to All Time
                                            break;
                                        }

                                        // Using the shared 'now' instance
                                        const currentYear = now.getFullYear();

                                        // Check if the start date is January 1st of the current year (Year to date)
                                        const yearStartDateStr: string = `${currentYear}-01-01`;
                                        const isYearToDate = startDateStr === yearStartDateStr;

                                        // Calculate the date 30 days ago using date-fns
                                        const thirtyDaysAgoDate = subDays(now, 30);

                                        // Format to string for comparison if needed
                                        const thirtyDaysAgoStr = format(thirtyDaysAgoDate, 'yyyy-MM-dd');

                                        // Use isSameDay for precise date comparison, ignoring time components
                                        const isLast30Days = isSameDay(parsedStartDate, thirtyDaysAgoDate);

                                        // Check if it's 1970-01-01 (All time)
                                        const isAllTime = startDateStr === '1970-01-01';

                                        if (isYearToDate) {
                                            // Year to date - start date is January 1st of current year
                                            updatedData.modificationDate = 'year_to_date';
                                        } else if (isLast30Days) {
                                            // Last 30 days
                                            updatedData.modificationDate = 'last_30_days';
                                        } else if (isAllTime) {
                                            // All time
                                            updatedData.modificationDate = '';
                                        } else {
                                            updatedData.modificationDate = '';
                                        }
                                    } else if (!filterValueObject.gte && filterValueObject.lte) {
                                        // Handle case with only end date (everything before X)
                                        // This doesn't match our predefined options well, so default to All Time
                                        updatedData.modificationDate = '';
                                    } else {
                                        // No date range specified, use 'All Time'
                                        updatedData.modificationDate = '';
                                    }
                                }
                                break;

                            case 'modifiedByUserId':
                                // Handle modifiedByUserId filters (typically uses 'in' operator for multi-select)
                                if (hasStringOperators(filterValueObject)) {
                                    if (filterValueObject.in && Array.isArray(filterValueObject.in)) {
                                        const userSet = new Set<{ value: string; label: string }>();

                                        // For each user ID in the filter, find the corresponding user in the modifiedBy options
                                        filterValueObject.in.forEach((userId: string) => {
                                            // Try to find the user in the modifiedBy options
                                            const userOption = modifiedBy.find((option) => option.value === userId);

                                            if (userOption && userOption.label) {
                                                // If found, add to the set with proper label
                                                userSet.add({ value: userId, label: userOption.label });
                                            } else {
                                                // If not found, use the ID as both value and label
                                                userSet.add({ value: userId, label: userId });
                                            }
                                        });

                                        updatedData.modifiedByUserId = userSet;
                                    } else if (filterValueObject.eq) {
                                        // Handle single selection case (eq operator)
                                        const userId = filterValueObject.eq;
                                        const userOption = modifiedBy.find((option) => option.value === userId);

                                        const userSet = new Set<{ value: string; label: string }>();
                                        if (userOption && userOption.label) {
                                            userSet.add({ value: userId, label: userOption.label });
                                        } else {
                                            userSet.add({ value: userId, label: userId });
                                        }

                                        updatedData.modifiedByUserId = userSet;
                                    }
                                }
                                break;
                        }
                    } catch (error) {
                        console.error('Error parsing filter condition:', condition, error);
                    }
                }
            });
        }

        // Update the data state
        setData(updatedData);
        setBackupData(cloneDeep(updatedData));
    }, [localFilter, setData, setBackupData, modifiedBy]);

    // Track dialog open state changes to prevent unnecessary re-initialization
    const prevDialogOpenState = React.useRef(false);

    // Initialize data from filter when dialog is opened
    useEffect(() => {
        // Only initialize when dialog changes from closed to open
        if (isDialogOpen && !prevDialogOpenState.current) {
            initializeDataFromFilter();
        }
        // Update the previous state
        prevDialogOpenState.current = isDialogOpen;
    }, [isDialogOpen, initializeDataFromFilter]);

    // Keep track of the last filter data to prevent unnecessary refetches
    const lastFilterDataRef = React.useRef<{ [key: string]: any }>({});

    const onLocalFilterChangeHandler = useCallback(
        (fieldName: string, filterData: EmployerFilterInput) => {
            switch (fieldName) {
                case 'modifiedByUserId':
                    // Mark that the user has interacted with the Modified By filter
                    setHasInteractedWithModifiedBy(true);

                    // Check if the filter data is actually different to avoid unnecessary refetches
                    const lastFilterData = lastFilterDataRef.current[fieldName];
                    if (lastFilterData && _.isEqual(lastFilterData, filterData)) {
                        return;
                    }

                    // Update the last filter data
                    lastFilterDataRef.current[fieldName] = filterData;

                    refetchModifiedByTimesheet({ ...getDefaultTimesheetRosterQueryVariables(employerGuid), where: filterData });
                    break;
            }
        },
        [employerGuid, refetchModifiedByTimesheet, setHasInteractedWithModifiedBy]
    );

    const onSetInputChangeHandler = useCallback((fieldName: string, value: Set<{ value: string; label: string }> | string) => {
        setData((prevData: TimesheetFilterData) => {
            // Check if the value is actually different to avoid unnecessary updates
            const prevValue = prevData[fieldName as keyof TimesheetFilterData];

            // For string values, simple equality check
            if (typeof value === 'string' && value === prevValue) {
                return prevData;
            }

            // For Set values, check if they're equal
            if (value instanceof Set && prevValue instanceof Set) {
                // If both sets have the same size and all items in value are in prevValue
                if (
                    value.size === prevValue.size &&
                    Array.from(value).every((item) =>
                        Array.from(prevValue).some((prevItem) => prevItem.value === item.value && prevItem.label === item.label)
                    )
                ) {
                    return prevData;
                }
            }

            return {
                ...prevData,
                [fieldName]: value
            };
        });
    }, [setData]);

    const onApplyChangesHandler = (close: Function) => {
        setBackupData(cloneDeep(data));
        setBackupLocalFilter(localFilter);
        onFilterChange(localFilter);

        close();
        setIsDialogOpen(false);
    };

    const onClearFiltersHandler = () => {
        // Reset local state
        const initialData = cloneDeep(INITIAL_FILTER_DATA);
        setData(initialData);
        setBackupData(initialData);

        // Reset local filter - create a new object reference to ensure change detection
        const emptyFilter = { and: [] };
        setLocalFilter(emptyFilter);
        setBackupLocalFilter(emptyFilter);

        // Directly notify parent to update Zustand store with a new object reference
        // Create a fresh object to ensure change detection
        onFilterChange({ ...emptyFilter });
    };

    const onCancelChangesHandler = (close: Function) => {
        setData(cloneDeep(backupData));
        setLocalFilter(backupLocalFilter);

        close();
        setIsDialogOpen(false);
    };

    const onSelectionFilterUpdate = useCallback((fieldName: string, filterData: TimeSheetFilterInput) => {
        setLocalFilter((prevFilter) => {
            // First check if the filter data is actually different to avoid unnecessary updates
            const prevAnd = prevFilter.and || [];
            const existingFilterIndex = prevAnd.findIndex(
                (query) => query.or && query.or.length > 0 && Object.keys(query.or[0])[0] === fieldName
            );

            // If the filter already exists and is the same, don't update
            if (existingFilterIndex >= 0 && _.isEqual(prevAnd[existingFilterIndex], filterData)) {
                return prevFilter;
            }

            const updatedFilter = cloneDeep(prevFilter);
            updatedFilter.and = updatedFilter.and ?? [];

            let updatedAnd = [...updatedFilter.and];
            let found = false;

            for (let i = 0; i < updatedAnd.length; i++) {
                const query = updatedAnd[i];

                if (query.or && query.or.length > 0) {
                    const queryFieldKey = Object.keys(query?.or[0])[0];
                    if (queryFieldKey === fieldName) {
                        updatedAnd[i] = filterData;
                        found = true;
                    }
                }
            }

            if (!found) {
                updatedAnd.push(filterData);
            }

            updatedAnd = updatedAnd.filter((query) => query.or && query.or.length > 0);
            updatedFilter.and = updatedAnd;

            return updatedFilter;
        });
    }, [setLocalFilter]);

    return (
        <Flex gap={'size-0'}>
            <DialogTrigger
                type="popover"
                placement="left top"
                onOpenChange={(isOpen) => {
                    setIsDialogOpen(isOpen);
                }}>
                <View {...filterButtonWrapperStyles}>
                    <ActionButton isQuiet {...filterButtonStyles}>
                        <Properties />
                        <Text>Filters{showClearButton ? ` (${filtersCount})` : ''}</Text>
                    </ActionButton>
                </View>

                {(close) => (
                    <Dialog>
                        <Content>
                            <Flex direction={'column'}>
                                <DisplayFilterConditionally columnData={columnData} fieldName="type">
                                    <Divider size="S" marginTop={8} marginBottom={8} />
                                    <Accordion title={'Type'} defaultOpen>
                                        <Flex direction="column" gap="size-150">
                                            <StaticFilterSelector
                                                fieldName="type"
                                                options={TimesheetTypeOptions}
                                                selectedValue={data.type}
                                                setSelectedValue={(value: string) => onSetInputChangeHandler('type', value)}
                                                onSelectionFilterUpdate={onSelectionFilterUpdate}
                                            />
                                        </Flex>
                                    </Accordion>
                                </DisplayFilterConditionally>

                                <DisplayFilterConditionally columnData={columnData} fieldName="status">
                                    <Divider size="S" marginTop={8} marginBottom={8} />
                                    <Accordion title={'Status'}>
                                        <Flex direction="column" gap="size-150">
                                            <StaticFilterSelector
                                                fieldName="status"
                                                options={TimesheetStatusOptions}
                                                selectedValue={data.status}
                                                setSelectedValue={(value: string) => onSetInputChangeHandler('status', value)}
                                                onSelectionFilterUpdate={onSelectionFilterUpdate}
                                            />
                                        </Flex>
                                    </Accordion>
                                </DisplayFilterConditionally>

                                <DisplayFilterConditionally columnData={columnData} fieldName="payPeriodEndDate">
                                    <Divider size="S" marginTop={8} marginBottom={8} />
                                    <Accordion title={'Period'}>
                                        <Flex direction="column" gap="size-150">
                                            <DateRadioGroup
                                                fieldName="payPeriodEndDate"
                                                selectedDateRange={data.payPeriodEndDate}
                                                setSelectedDateRange={(value: string) => onSetInputChangeHandler('payPeriodEndDate', value)}
                                                onSelectionFilterUpdate={onSelectionFilterUpdate}
                                                label="Filter timesheets by pay period end date."
                                            />
                                        </Flex>
                                    </Accordion>
                                </DisplayFilterConditionally>

                                <DisplayFilterConditionally columnData={columnData} fieldName="modificationDate">
                                    <Divider size="S" marginTop={8} marginBottom={8} />
                                    <Accordion title={'Last Modified'}>
                                        <Flex direction="column" gap="size-150">
                                            <DateRadioGroup
                                                fieldName="modificationDate"
                                                selectedDateRange={data.modificationDate}
                                                setSelectedDateRange={(value: string) => onSetInputChangeHandler('modificationDate', value)}
                                                onSelectionFilterUpdate={onSelectionFilterUpdate}
                                                label="Filter timesheets by the date they were last updated."
                                            />
                                        </Flex>
                                    </Accordion>
                                </DisplayFilterConditionally>

                                <DisplayFilterConditionally columnData={columnData} fieldName="modifiedByUserId">
                                    <Divider size="S" marginTop={8} marginBottom={8} />
                                    <Accordion
                                        title={'Modified By'}
                                        onStateChange={(isOpen) => {
                                            setIsModifiedByAccordionOpen(isOpen);
                                            // Only set interaction flag if the accordion is being opened
                                            // and we haven't already interacted with it
                                            if (isOpen && !hasInteractedWithModifiedBy && data.modifiedByUserId.size === 0) {
                                                setHasInteractedWithModifiedBy(true);
                                            }
                                        }}>
                                        <Flex direction="column" gap="size-150">
                                            <ComboBoxFilter
                                                items={modifiedBy}
                                                fieldName="modifiedByUserId"
                                                selectionFilterFieldName="modifiedByUserId"
                                                hasNext={hasNextModifiedByTimesheet}
                                                selectedOptions={data.modifiedByUserId}
                                                setSelectedOptions={(options) => {
                                                    // Mark that the user has interacted with the filter
                                                    if (!hasInteractedWithModifiedBy) {
                                                        setHasInteractedWithModifiedBy(true);
                                                    }
                                                    onSetInputChangeHandler('modifiedByUserId', options);
                                                }}
                                                loadNext={loadNextModifiedByTimesheet}
                                                isLoadingNext={isLoadingModifiedByTimesheet}
                                                onFilterChange={onLocalFilterChangeHandler}
                                                onSelectionFilterUpdate={onSelectionFilterUpdate}
                                                label="Filter timesheets by the user who last modified them."
                                            />
                                        </Flex>
                                    </Accordion>
                                    <Divider size="S" marginTop={8} marginBottom={8} />
                                </DisplayFilterConditionally>
                            </Flex>

                            <Flex justifyContent="end">
                                <TableAction
                                    close={close}
                                    disabled={isDisabled}
                                    onApplyChanges={onApplyChangesHandler}
                                    onCancelChanges={onCancelChangesHandler}
                                />
                            </Flex>
                        </Content>
                    </Dialog>
                )}
            </DialogTrigger>

            {showClearButton && (
                <ActionButton isQuiet onPress={() => onClearFiltersHandler()}>
                    <Close size="XS" />
                    <Text>Clear All</Text>
                </ActionButton>
            )}
        </Flex>
    );
};

export default TimesheetFilter;
