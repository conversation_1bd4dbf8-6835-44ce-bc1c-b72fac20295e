import { cloneDeep, isEqual } from 'lodash';
import { graphql } from 'relay-runtime';
import { Suspense, useEffect, useState } from 'react';
import { useLazyLoadQuery } from 'react-relay';
import { ColumnType } from '@/src/types/rosters';
import { LABEL_TEXT } from '@/src/constants/text';
import { SavedCustomView, BaseViewOption } from '@/src/types/views';
import RenameView from '../../UI/SavedViews/RenameView';
import AddTimesheet from '../AddTimesheet/AddTimesheet';
import { Flex, Heading, View } from '@adobe/react-spectrum';
import SaveView from '../../EmployerRoster/SaveView/SaveView';
import TimesheetFilter from '../TimesheetFilter/TimesheetFilter';
import TableView from '../../EmployerRoster/TableView/TableView';
import ShowViewDetails from '../../UI/SavedViews/ShowViewDetails';
import ExportFile from '@/src/components/UI/ExportFile/ExportFile';
import TableColumns from '../../UI/TableHeader/Columns/TableColumns';
import { INITIAL_FILTER_DATA, TimesheetFilterData } from '../TimesheetFilter/data';
import ChooseDefaultViewToDelete from '../../UI/SavedViews/ChooseDefaultViewToDelete';
import { getDefaultTimesheetRosterQueryVariables } from '@/src/services/timesheet-roster';
import { TimesheetsRosterViews, TimesheetsRosterViewsOptions } from '@/src/constants/views';
import { TimeSheetFilterInput } from '@/lib/relay/__generated__/TimesheetFilterModifiedByRefetchQuery.graphql';
import { TimesheetRosterHeaderQuery$variables } from '@/lib/relay/__generated__/TimesheetRosterHeaderQuery.graphql';
import { TimesheetRosterHeaderQuery as TimesheetRosterHeaderQueryType } from '@/lib/relay/__generated__/TimesheetRosterHeaderQuery.graphql';
import { useTimesheetRosterFilterStore } from '@/src/store/rosterFilterStore';
import ErrorBoundary from '@/src/components/ErrorBoundary/ErrorBoundary';

type Props = {
    employerGuid: string;
    defaultViewId: string;
    customSavedViews: SavedCustomView[];
    columnData: ColumnType[];
    selectedSystemView: string;
    isSavedViewDisabled: boolean;
    onExport: (format: string) => void;
    onRootDeleteView: (id: string) => void;
    selectedCustomView: SavedCustomView | null;
    onRootChangeDefaultView: (id: string) => void;
    onToggleColumnShow: (values: string[]) => void;
    onRootSelectCustomView: (id: string | null) => void;
    onRootSystemViewChange: (view: string | null) => void;
    onRootUpdateView: (id: string, updatedLocalView: any) => void;
    onRootRenameView: (id: string, name: string, description: string) => void;
    onDeleteAndChangeDefaultView: (id: string, newDefaultViewId: string) => void;
    onFilterChange: (filter: TimesheetRosterHeaderQuery$variables['where']) => void;
    onRootSaveView: ({ savedLocalView, name, description }: { savedLocalView: any; name: string; description: string }) => void;
};

export const TimesheetRosterHeaderQuery = graphql`
    query TimesheetRosterHeaderQuery($employerGuid: UUID!, $order: [TimeSheetSortInput!], $where: TimeSheetFilterInput!) {
        ...TimesheetFilterModifiedByFragment @arguments(employerGuid: $employerGuid, order: $order, where: $where)
    }
`;

const TimesheetRosterHeader = ({
    employerGuid,
    columnData,
    onExport,
    selectedSystemView,
    defaultViewId,
    onFilterChange,
    onRootSaveView,
    customSavedViews,
    onRootUpdateView,
    onRootDeleteView,
    onRootRenameView,
    selectedCustomView,
    onToggleColumnShow,
    isSavedViewDisabled,
    onRootSystemViewChange,
    onRootSelectCustomView,
    onRootChangeDefaultView,
    onDeleteAndChangeDefaultView
}: Props) => {
    const DEFAULT_TIMESHEET_ROSTER_ROOT_QUERY_PARAMS = getDefaultTimesheetRosterQueryVariables(employerGuid);
    const timesheetFilterQuery = useLazyLoadQuery<TimesheetRosterHeaderQueryType>(
        TimesheetRosterHeaderQuery,
        DEFAULT_TIMESHEET_ROSTER_ROOT_QUERY_PARAMS
    );
    const activeFilters = useTimesheetRosterFilterStore((state) => state.activeFilters);

    const [data, setData] = useState<TimesheetFilterData>(cloneDeep(INITIAL_FILTER_DATA));
    const [backupData, setBackupData] = useState<TimesheetFilterData>(cloneDeep(INITIAL_FILTER_DATA));
    const [localFilter, setLocalFilter] = useState<TimeSheetFilterInput>({ and: [] });
    const [backupLocalFilter, setBackupLocalFilter] = useState<TimeSheetFilterInput>({ and: [] });
    const [isOpenDeleteDefaultViewDialog, setOpenDeleteDefaultViewDialog] = useState(false);
    const [selectedViewToShowDetails, setSelectedViewToShowDetails] = useState<BaseViewOption | null>(null);
    const [hasViewToRename, setHasViewToRename] = useState<SavedCustomView | null>(null);

    useEffect(() => {
        if (selectedSystemView === TimesheetsRosterViews.TIMESHEET_ROSTER && !selectedCustomView) {
            setData(cloneDeep(INITIAL_FILTER_DATA));
            setBackupData(cloneDeep(INITIAL_FILTER_DATA));
            setLocalFilter({ and: [] });
            setBackupLocalFilter({ and: [] });
        }
    }, [selectedSystemView, selectedCustomView]);

    useEffect(() => {
        if (selectedCustomView) {
            onLoadSavedViewWithLocalFilterStateHandler(selectedCustomView);
        }
    }, [selectedCustomView]);

    // Synchronize local filter state with Zustand store when activeFilters changes
    useEffect(() => {
        // Only sync if we're not in a custom view
        if (!selectedCustomView || selectedSystemView === TimesheetsRosterViews.TIMESHEET_ROSTER) {
            // Handle both cases: when filters are active and when they are cleared
            const emptyFilter = { and: [] };
            const currentActiveFilter = activeFilters || emptyFilter;
            const currentBackupFilter = backupLocalFilter || emptyFilter;

            // Check if the filters are different before updating to prevent infinite loops
            if (!isEqual(currentBackupFilter, currentActiveFilter)) {
                // Update local filter state
                setLocalFilter(cloneDeep(currentActiveFilter));
                setBackupLocalFilter(cloneDeep(currentActiveFilter));

                // We no longer manually reconstruct the TimesheetFilterData object here
                // The TimesheetFilter component will be responsible for initializing its own UI state
                // based on the localFilter prop when the filter dialog is opened
            }
        }
    }, [activeFilters, backupLocalFilter, selectedCustomView, selectedSystemView]);

    const onSaveViewWithLocalFilterStateHandler = ({ name, description }: { name: string; description: string }) => {
        const savedLocalView = {
            data: {
                ...data,
                modifiedByUserId: Array.from(data.modifiedByUserId)
            },
            localFilter
        };
        onRootSaveView({ savedLocalView, name, description });
    };

    const onUpdateViewWithLocalFilterStateHandler = (id: string) => {
        const updatedLocalView = {
            data: {
                ...data,
                modifiedByUserId: Array.from(data.modifiedByUserId)
            },
            localFilter
        };
        onRootUpdateView(id, updatedLocalView);
    };

    const onLoadSavedViewWithLocalFilterStateHandler = (customView: any) => {
        if (customView) {
            if (customView && customView.type === TimesheetsRosterViews.TIMESHEET_ROSTER) {
                const data: TimesheetFilterData = cloneDeep(customView.savedLocalView.data);
                const localFilter: TimeSheetFilterInput = cloneDeep(customView.savedLocalView.localFilter);
                data.modifiedByUserId = new Set(data.modifiedByUserId);

                setData(cloneDeep(data));
                setBackupData(cloneDeep(data));
                setLocalFilter(localFilter);
                setBackupLocalFilter(cloneDeep(localFilter));
            }
        }
    };

    const onDeleteDefaultViewHandler = () => {
        setOpenDeleteDefaultViewDialog(true);
    };

    const onOpenRenameViewDialogHandler = (id: string) => {
        const viewToRename = customSavedViews?.find((view) => view.id === id);
        if (viewToRename) {
            setHasViewToRename(viewToRename);
        }
    };

    const onCloseRenameViewDialogHandler = () => {
        setHasViewToRename(null);
    };

    const onSelectAndOpenViewDetailsDialogHandler = (id: string) => {
        // For system views, find the view in TimesheetsRosterViewsOptions
        const systemView = TimesheetsRosterViewsOptions.find((option) => option.id === id);
        
        if (systemView) {
            // System view needs columns property for getReadableViewDetails
            const enhancedSystemView = {
                ...systemView,
                columns: columnData // Use current column data for system views
            };
            setSelectedViewToShowDetails(enhancedSystemView);
        } else if (customSavedViews && Array.isArray(customSavedViews)) {
            // For custom views, find in customSavedViews
            const customView = customSavedViews.find((view) => view.id === id);
            if (customView) {
                setSelectedViewToShowDetails(customView);
            }
        }
    };

    const onCloseViewDetailsDialogHandler = () => {
        setSelectedViewToShowDetails(null);
    };

    return (
        <>
            <Heading level={1} marginBottom={'size-400'}>
                {LABEL_TEXT.TIMESHEETS}
            </Heading>

            <View
                colorVersion={6}
                borderWidth="none"
                borderColor="gray-200"
                borderRadius="regular"
                width="100%"
                paddingBottom={'size-75'}
                paddingX={'size-75'}>
                <Flex justifyContent="space-between" alignItems="center" width="100%">
                    <Flex marginBottom="size-100" columnGap="size-100" alignItems="center">
                        <View colorVersion={6} borderWidth="thin" borderRadius="regular" borderColor="gray-300">
                            <AddTimesheet />
                        </View>

                        <ExportFile onExport={onExport} />

                        <SaveView
                            customSavedViews={customSavedViews}
                            builtInViews={TimesheetsRosterViewsOptions}
                            isSavedViewDisabled={isSavedViewDisabled}
                            onCreateView={onSaveViewWithLocalFilterStateHandler}
                            onRootUpdateView={onUpdateViewWithLocalFilterStateHandler}
                        />

                        <TableView
                            selectedSystemView={selectedSystemView}
                            defaultViewId={defaultViewId}
                            customSavedViews={customSavedViews}
                            onRootDeleteView={onRootDeleteView}
                            builtInViews={TimesheetsRosterViewsOptions}
                            selectedCustomView={selectedCustomView}
                            onRootSystemViewChange={onRootSystemViewChange}
                            onRootSelectCustomView={onRootSelectCustomView}
                            onDeleteDefaultView={onDeleteDefaultViewHandler}
                            onRootChangeDefaultView={onRootChangeDefaultView}
                            onOpenRenameViewDialog={onOpenRenameViewDialogHandler}
                            onShowViewDetails={onSelectAndOpenViewDetailsDialogHandler}
                        />
                        <TableColumns columnData={columnData} onToggleColumnShow={onToggleColumnShow} />
                        <TimesheetFilter
                            employerGuid={employerGuid}
                            data={data}
                            setData={setData}
                            backupData={backupData}
                            setBackupData={setBackupData}
                            localFilter={localFilter}
                            setLocalFilter={setLocalFilter}
                            backupLocalFilter={backupLocalFilter}
                            setBackupLocalFilter={setBackupLocalFilter}
                            columnData={columnData}
                            queryRef={timesheetFilterQuery}
                            onFilterChange={onFilterChange}
                            activeFilters={activeFilters}
                        />
                    </Flex>
                </Flex>
            </View>

            {isOpenDeleteDefaultViewDialog && (
                <ChooseDefaultViewToDelete
                    defaultViewId={defaultViewId}
                    customSavedViews={customSavedViews}
                    builtInViews={TimesheetsRosterViewsOptions}
                    isOpen={isOpenDeleteDefaultViewDialog}
                    selectedCustomView={selectedCustomView}
                    onRootSystemViewChange={onRootSystemViewChange}
                    onRootSelectCustomView={onRootSelectCustomView}
                    onDeleteAndChangeDefaultView={onDeleteAndChangeDefaultView}
                    onCloseDialog={() => setOpenDeleteDefaultViewDialog(false)}
                />
            )}

            {hasViewToRename && (
                <RenameView
                    view={hasViewToRename}
                    isOpen={!!hasViewToRename}
                    onUpdateView={onRootRenameView}
                    builtInViews={TimesheetsRosterViewsOptions}
                    onCloseDialog={onCloseRenameViewDialogHandler}
                />
            )}

            {selectedViewToShowDetails && (
                <ErrorBoundary>
                    <ShowViewDetails view={selectedViewToShowDetails} onClose={onCloseViewDetailsDialogHandler} />
                </ErrorBoundary>
            )}
        </>
    );
};

export default TimesheetRosterHeader;
