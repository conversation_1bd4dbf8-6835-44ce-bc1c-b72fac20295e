import { graphql } from 'react-relay';

/**
 * GraphQL query to fetch timesheet data specifically for duplication purposes.
 * This query includes PayStubs data that was removed from the roster table for performance,
 * but is still needed for the duplication functionality.
 */
export const TimesheetDuplicationQuery = graphql`
    query TimesheetDuplicationQuery($timeSheetId: ID!) {
        timeSheetById(id: $timeSheetId) {
            id
            name
            numericId
            payPeriodEndDate
            status
            type
            showBonusColumn
            showCostCenterColumn
            showDTHoursColumn
            showEarningsCodesColumn
            showExpensesColumn
            #  Fetch up to 10 000 stubs – operational ceiling well above existing data volume
            payStubs(first: 10000) {
                edges {
                    node {
                        id
                        employeeId
                        name
                        totalHours
                        details {
                            id
                            workDate
                            jobCode
                            costCenter
                            stHours
                            otHours
                            dtHours
                            bonus
                            expenses
                            hourlyRate
                            earningsCode
                            agreementId
                            classificationId
                            subClassificationId
                            reportLineItemId
                        }
                    }
                }
            }
        }
    }
`;