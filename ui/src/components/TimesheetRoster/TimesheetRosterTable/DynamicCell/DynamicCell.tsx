import { useState } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { useNavigate } from 'react-router';
import { ToastQueue } from '@react-spectrum/toast';
import { instance } from '@/src/core/http/axios-config';
import { format } from 'date-fns';
import { ClientUtils } from "@/lib/core/ClientUtils";
import { useStore } from '@/lib';
import { CalendarDate, DateValue } from '@internationalized/date';
import { useMutation, graphql, useRelayEnvironment, fetchQuery } from 'react-relay';
import { TimesheetDuplicationQuery } from './TimesheetDuplicationQuery';
import type { TimesheetDuplicationQuery as TimesheetDuplicationQueryType } from '@/src/relay/__generated__/TimesheetDuplicationQuery.graphql';
import { TimesheetDetailAddMutation, addTimesheetUpdater } from '@/src/hooks/useTimesheetSaver';
import { TableData } from '../TimesheetRoster.data';
import { TimesheetsURLs } from '@/src/constants/in-app-urls';
import { Constants } from '@/src/constants/global';
import { TIMESHEET_TYPES } from '@/src/constants/timesheet-roster';
import { BUTTON_TEXT } from '@/src/constants/text';
import { getDateTimeLogFormat, getMonthWithDateFormat, getPeriod } from '@/src/services/date';
import Papa from 'papaparse';
import Text from '@/src/components/UI/Text/Text';

import {
    Key,
    Flex,
    Link,
    DialogTrigger,
    AlertDialog,
    ActionButton,
    Content,
    Form,
    Heading,
    DatePicker,
    Checkbox,
    ButtonGroup,
    Dialog,
    DialogContainer,
    Divider,
    ActionMenu,
    Item,
    Section,
    Picker,
    Button
} from '@adobe/react-spectrum';

import type {
    AddTimesheetInput,
    AddPayStubInput,
    AddPayStubDetailInput,
    useTimesheetSaverAddMutation$data
} from '@/src/types/graphql-timesheet';

type PayStubDetailType = {
    workDate?: Date | string;
    jobCode?: string | null;
    costCenter?: string | null;
    stHours?: number | null;
    otHours?: number | null;
    dtHours?: number | null;
    totalHours?: number | null;
    bonus?: number | null;
    expenses?: number | null;
    [key: string]: any;
};

type ExtendedPayStub = {
    employeeId: number;
    id: any;
    name: string | null | undefined;
    totalHours: number | null | undefined;
    details?: PayStubDetailType[];
};

type DuplicatedTimesheetData = {
    id: string;
    name: string | null | undefined;
    numericId: number;
    payPeriodEndDate: any;
    status: string;
    type: string;
    showBonusColumn: boolean | null | undefined;
    showCostCenterColumn: boolean | null | undefined;
    showDTHoursColumn: boolean | null | undefined;
    showEarningsCodesColumn: boolean | null | undefined;
    showExpensesColumn: boolean | null | undefined;
    payStubs?: ExtendedPayStub[];
    createdByUserId?: string;
    modifiedByUserId?: string;
};

type EPRTableRowActionProps = {
    timesheet: TableData;
    onDeleteTimesheet: (timesheetId: string) => void;
};

/**
 * Formats a Date or CalendarDate object to YYYY-MM-DD string format
 * Uses the centralized date utility function for consistent date formatting
 */
const formatDateToString = (date: Date | CalendarDate): string => {
    if (date instanceof Date) {
        return format(date, 'yyyy-MM-dd');
    } else {
        // For CalendarDate objects, create a Date object first
        return format(new Date(date.year, date.month - 1, date.day), 'yyyy-MM-dd');
    }
};

/**
 * Creates a CalendarDate from a Date or string
 */
const createCalendarDate = (date: Date | string): CalendarDate => {
    const d = typeof date === 'string' ? new Date(date) : date;
    return new CalendarDate(d.getFullYear(), d.getMonth() + 1, d.getDate());
};

/**
 * Calculate day difference between new date and original date
 */
const calculateDayDifference = (newDate: CalendarDate, origDate: Date | string): number => {
    const origCalendarDate = createCalendarDate(origDate);
    // Calculate difference in days - approximate by converting to epoch days
    const MS_PER_DAY = 24 * 60 * 60 * 1000;
    const newDateMs = new Date(newDate.year, newDate.month - 1, newDate.day).getTime();
    const origDateMs = new Date(origCalendarDate.year, origCalendarDate.month - 1, origCalendarDate.day).getTime();
    return Math.round((newDateMs - origDateMs) / MS_PER_DAY);
};

/**
 * Creates synthetic details when source paystub has no details
 */
const createSyntheticDetails = (
    endDate: CalendarDate,
    totalHours: number | null | undefined,
    includeHours: boolean
): AddPayStubDetailInput[] => {
    const detailsInput: AddPayStubDetailInput[] = [];

    for (let i = 0; i < 7; i++) {
        // Create a date that is (6-i) days before end date
        // Convert to JS Date for date arithmetic
        const jsEndDate = new Date(endDate.year, endDate.month - 1, endDate.day);
        jsEndDate.setDate(jsEndDate.getDate() - (6 - i));

        const detailDate = formatDateToString(jsEndDate);
        const hasHours = i === 0 && totalHours && totalHours > 0;

        const detailInput: AddPayStubDetailInput = {
            workDate: detailDate,
            stHours: hasHours && includeHours ? totalHours : null,
            otHours: null,
            dtHours: null,
            jobCode: null,
            costCenter: null,
            bonus: null,
            expenses: null,
            hourlyRate: null,
            earningsCode: null,
            agreementId: null,
            classificationId: null,
            subClassificationId: null,
            reportLineItemId: null
        };

        if (hasHours || i === 0) {
            detailsInput.push(detailInput);
        }
    }

    return detailsInput;
};

/**
 * Creates details from source paystub's existing details
 */
const createDetailsFromSource = (
    sourceDetails: PayStubDetailType[],
    dayDifference: number,
    includeJobCodes: boolean,
    includeHours: boolean
): AddPayStubDetailInput[] => {
    const detailsInput: AddPayStubDetailInput[] = [];

    sourceDetails.forEach((detail: PayStubDetailType) => {
        if (!detail.workDate) return;

        // Convert to JS Date for date arithmetic
        const workDate = typeof detail.workDate === 'string' ? new Date(detail.workDate) : new Date(detail.workDate);

        // Add the day difference
        workDate.setDate(workDate.getDate() + dayDifference);

        const formattedWorkDate = formatDateToString(workDate);

        const detailInput: AddPayStubDetailInput = {
            workDate: formattedWorkDate,
            jobCode: includeJobCodes ? detail.jobCode : null,
            costCenter: includeJobCodes ? detail.costCenter : null,
            stHours: includeHours && detail.stHours ? detail.stHours : null,
            otHours: includeHours && detail.otHours ? detail.otHours : null,
            dtHours: includeHours && detail.dtHours ? detail.dtHours : null,
            bonus: includeHours && detail.bonus ? detail.bonus : null,
            expenses: includeHours && detail.expenses ? detail.expenses : null,
            hourlyRate: detail.hourlyRate,
            earningsCode: detail.earningsCode,
            agreementId: detail.agreementId,
            classificationId: detail.classificationId,
            subClassificationId: detail.subClassificationId,
            reportLineItemId: detail.reportLineItemId
        };

        if (
            (detailInput.stHours ?? 0) > 0 ||
            (detailInput.otHours ?? 0) > 0 ||
            (detailInput.dtHours ?? 0) > 0 ||
            (detailInput.bonus ?? 0) > 0 ||
            (detailInput.expenses ?? 0) > 0 ||
            detailInput.jobCode ||
            detailInput.costCenter
        ) {
            detailsInput.push(detailInput);
        }
    });

    return detailsInput;
};

/**
 * Parses and returns a user-friendly error message
 */
const parseErrorMessage = (error: any, errors?: any[]): string => {
    if (errors && errors.length > 0) {
        return errors.map((e) => e.message).join('\n');
    }

    if (error instanceof Error) {
        return error.message;
    }

    return String(error);
};

/**
 * Formats GraphQL errors into a user-friendly message
 */
const formatGraphQLErrors = (errors: any[]): string => {
    if (!errors || errors.length === 0) {
        return 'Unknown error occurred';
    }

    // Check for field-specific validation errors
    const validationErrors = errors.filter(
        (e) => e.extensions?.code === 'VALIDATION_ERROR' || e.message.includes('validation') || e.message.includes('invalid')
    );

    if (validationErrors.length > 0) {
        return validationErrors.map((e) => e.message).join('\n');
    }

    // Check for specific error types we know about
    const sourceTimesheetError = errors.find((e) => e.message.includes('Source timesheet'));
    if (sourceTimesheetError) {
        return sourceTimesheetError.message;
    }

    const duplicateError = errors.find((e) => e.message.includes('already exists') || e.message.includes('duplicate'));
    if (duplicateError) {
        return duplicateError.message;
    }

    // Fall back to joining all messages
    return errors.map((e) => e.message).join('\n');
};

/**
 * Creates the AddTimesheetInput for the GraphQL mutation
 */
const createTimesheetInput = (
    employerGuid: string,
    formattedDate: string,
    newTimeSheetType: string,
    duplicatedTimesheet: any,
    payStubsInput: AddPayStubInput[],
    includeJobCodes: boolean,
    includeHours: boolean
): AddTimesheetInput => {
    return {
        employerGuid: employerGuid,
        name: `Timesheet - ${formattedDate}`,
        payPeriodEndDate: formattedDate,
        status: 'New',
        type: newTimeSheetType,
        showBonusColumn: duplicatedTimesheet.showBonusColumn || false,
        showCostCenterColumn: duplicatedTimesheet.showCostCenterColumn || false,
        showDTHoursColumn: duplicatedTimesheet.showDTHoursColumn || false,
        showEarningsCodesColumn: duplicatedTimesheet.showEarningsCodesColumn || false,
        showExpensesColumn: duplicatedTimesheet.showExpensesColumn || false,
        payStubs: payStubsInput,
        sourceTimesheetId: duplicatedTimesheet.numericId,
        isCopy: true,
        includeJobCodes: includeJobCodes,
        includeHours: includeHours
    };
};

/**
 * Creates paystub inputs based on source paystubs
 */
const createPayStubInputs = (
    sourcePayStubs: any[],
    dayDifference: number,
    formattedDate: string,
    includeJobCodes: boolean,
    includeHours: boolean
): AddPayStubInput[] => {
    const payStubsInput: AddPayStubInput[] = [];

    if (!sourcePayStubs || sourcePayStubs.length === 0) {
        return payStubsInput;
    }

    sourcePayStubs.forEach((payStub: any) => {
        if (!payStub.employeeId) return;

        let detailsInput: AddPayStubDetailInput[] = [];

        if (!payStub.details || payStub.details.length === 0) {
            // Convert the formatted date string to a CalendarDate
            const endDate = new Date(formattedDate);
            const calendarEndDate = new CalendarDate(endDate.getFullYear(), endDate.getMonth() + 1, endDate.getDate());
            detailsInput = createSyntheticDetails(calendarEndDate, payStub.totalHours, includeHours);
        } else {
            detailsInput = createDetailsFromSource(payStub.details, dayDifference, includeJobCodes, includeHours);
        }

        const calcSTHours = payStub.totalHours || (includeHours ? detailsInput.reduce((sum, d) => sum + (d.stHours || 0), 0) : 0);

        const payStubInput: AddPayStubInput = {
            employeeId: payStub.employeeId,
            name: payStub.name || '',
            stHours: includeHours && calcSTHours > 0 ? calcSTHours : null,
            otHours: null,
            dtHours: null,
            bonus: null,
            expenses: null,
            details: detailsInput.length > 0 ? detailsInput : null
        };

        payStubsInput.push(payStubInput);
    });

    return payStubsInput;
};

const TypeCell = (item: TableData) =>
    item.type === 'Primary' ? (
        <Text className="fontWeightSemiBold " isSelectable>
            {item.type}
        </Text>
    ) : (
        <Text isSelectable>{item.type}</Text>
    );

const PeriodCell = (item: TableData, employerGuid?: string) => {
    const date = item.payPeriodEndDate;

    if (!date) {
        return <Divider size="M" width={'0.8em'} />;
    }

    return (
        <Link
            UNSAFE_className="fontWeightSemiBold"
            UNSAFE_style={{ textOverflow: 'ellipsis', overflow: 'hidden' }}
            isQuiet
            href={`${TimesheetsURLs.TIMESHEET_ROSTER_URL}/detail?id=${encodeURIComponent(item.id)}&employerGuid=${encodeURIComponent(employerGuid || '')}`}>
            <Flex justifyContent={'space-between'}>
                <Flex alignItems={'start'} flex={0.6}>
                    <Text>{getPeriod(item?.period?.year, item?.period?.weekNumber)}</Text>
                </Flex>
                <Flex justifyContent={'left'} flex={0.4}>
                    <Text className="fontWeightSemiBold">E:&nbsp;</Text>
                    <Text>{getMonthWithDateFormat(item?.period?.endDate)}</Text>
                </Flex>
            </Flex>
        </Link>
    );
};

const LastModifiedCell = (item: TableData) => {
    const date = item.modificationDate ?? '';
    return date ? <Text isSelectable>{getDateTimeLogFormat(date)}</Text> : <Divider size="M" width={'0.8em'} />;
};

const NumberOfPayStubsCell = (item: TableData) => <Text isSelectable>{item.payStubCount}</Text>;

const IdCell = (item: TableData) => <Text isSelectable>{item.numericId}</Text>;

const HoursWorkedCell = (item: TableData) => <Text isSelectable>{item?.hoursWorked}</Text>;

const StatusCell = (item: TableData) =>
    item.status === 'Submitted' ? (
        <Text className="fontWeightSemiBold" isSelectable>
            {item.status}
        </Text>
    ) : (
        <Text className="fontStyleItalic" isSelectable>
            {item.status}
        </Text>
    );

const LastModifiedByCell = (item: TableData) => (
    <Text isUppercase isSelectable>
        {item.modifiedByUserId}
    </Text>
);

const EPRTableRowAction = ({ timesheet, onDeleteTimesheet }: EPRTableRowActionProps) => {
    const navigate = useNavigate();
    const relayEnvironment = useRelayEnvironment();
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [isDuplicateDialogOpen, setIsDuplicateDialogOpen] = useState(false);
    const [duplicatedTimesheet, setDuplicatedTimesheet] = useState<DuplicatedTimesheetData | null>(null);
    const currentDate = new Date();
    const [newTimeSheetDate, setNewTimeSheetDate] = useState<DateValue>(
        new CalendarDate(currentDate.getFullYear(), currentDate.getMonth() + 1, currentDate.getDate())
    );
    const [newTimeSheetType, setNewTimeSheetType] = useState<string>('');
    const [includeJobCodes, setIncludeJobCodes] = useState(true);
    const [includeHours, setIncludeHours] = useState(true);
    const [duplicateError, setDuplicateError] = useState<string | null>(null);
    const [isCreating, setIsCreating] = useState(false);

    const loggedInUserId = useStore((state: any) => state.email || 'system');
    const employerGuid = useStore((state: any) => state.selectedEmployerGuid);

    const [commitAddMutation, isMutationInFlight] = useMutation(TimesheetDetailAddMutation);

    let commandButtonClicked = false;

    const exportCSV = async (dataItem: TableData) => {
        commandButtonClicked = true;
        const fields = [
            'Key',
            'Name',
            'LaborValue7',
            'LaborValue9',
            'LaborValue1',
            'LaborValue2',
            'E_*_ORRate',
            'E_*_ORPremium',
            'E_Regular_Hours',
            'E_Overtime_Hours',
            'E_Double Time_Hours',
            'E_Expenses_Dollars'
        ];
        if (dataItem.type === 'Additional') {
            fields[1] = 'CheckType';
        }
        const timeSheetId = dataItem.numericId;

        if (!timeSheetId) {
            ToastQueue.negative('Cannot export timesheet: Invalid timesheet ID.', { timeout: 5000 });
            return;
        }

        try {
            const exportResponse = await instance.get(`/api/TimeSheets/export/${timeSheetId}`);

            let earningsCodeFieldsCount = 0;
            exportResponse.data.forEach((e: any) => {
                if (e.earningsCode !== null) {
                    const earningsCodeText = Constants.TimesheetEarningCodesList.find((ea: any) => ea.value === e.earningsCode);
                    if (earningsCodeText) {
                        const earningsCode = earningsCodeText.text;
                        if (e.stHours !== null) {
                            const hoursColumnName = 'E_' + earningsCodeText + '_Hours';
                            if (!fields.includes(hoursColumnName)) {
                                fields.push(hoursColumnName);
                                earningsCodeFieldsCount++;
                            }
                        }
                        if (e.bonus !== null) {
                            const dollarsColumnName = 'E_' + earningsCodeText + '_Dollars';
                            if (!fields.includes(dollarsColumnName)) {
                                fields.push(dollarsColumnName);
                                earningsCodeFieldsCount++;
                            }
                        }
                    }
                }
            });

            const taxableBenefits = await instance.get(`/api/TimeSheets/export/taxablebenefits/${timeSheetId}`);
            taxableBenefits.data.forEach((b: any) => {
                const name = `E_${b.name}_Dollars`;
                if (!fields.includes(name)) {
                    fields.push(name);
                }
            });

            const unionEmployerPaidBenefits = await instance.get(`/api/TimeSheets/export/unionemployerpaidbenefits/${timeSheetId}`);

            const local3ChapterID = 'someLocal3ChapterId';

            unionEmployerPaidBenefits.data.forEach((b: any) => {
                if (b.chapterID === local3ChapterID) {
                    const name = `E_${b.name}_Dollars`;
                    if (!fields.includes(name)) {
                        fields.push(name);
                    }
                }
            });
            fields.push('E_UNION_Dollars');
            const employeeDeductibleBenefits = await instance.get(`/api/TimeSheets/export/employeedeductiblebenefits/${timeSheetId}`);
            employeeDeductibleBenefits.data.forEach((b: any) => {
                const name = `D_${b.name}`;
                if (!fields.includes(name)) {
                    fields.push(name);
                }
            });

            const exportValues: any[] = [];
            const processedReportLineItems: string[] = [];
            for (let i = 0; i < exportResponse.data.length; i++) {
                const data = exportResponse.data[i];
                const earningsCode = Constants.TimesheetEarningCodesList.find((e: any) => e.value === data.earningsCode);
                const exportData = [
                    data.ssn,
                    data.name,
                    data.unionClassificationCode,
                    new Date(data.date).toLocaleDateString('en-us'),
                    data.jobCode,
                    data.costCenter,
                    data.hourlyRate,
                    null,
                    data.earningsCode ? null : data.stHours,
                    data.otHours,
                    data.dtHours,
                    data.expenses
                ];
                if (earningsCode) {
                    const startingIndex = exportData.length;
                    for (let i = startingIndex; i < startingIndex + earningsCodeFieldsCount; i++) {
                        if (fields[i].substring(2, earningsCode.text.length + 2) !== earningsCode.text) {
                            exportData.push(null);
                            continue;
                        }
                        if (fields[i].includes('Hours')) {
                            exportData.push(data.stHours);
                        } else if (fields[i].includes('Dollars')) {
                            exportData.push(data.bonus);
                        }
                    }
                }

                if (!processedReportLineItems.includes(data.reportLineItemID)) {
                    processedReportLineItems.push(data.reportLineItemID);
                    const taxBenefits = taxableBenefits.data.filter((b: any) => b.reportLineItemID === data.reportLineItemID);
                    taxBenefits.forEach((b: any) => {
                        const index = fields.findIndex((d) => d === `E_${b.name}_Dollars`);
                        if (index === -1) exportData.push(b.amount);
                        else exportData[index] = b.amount;
                    });
                    let unionDollars = 0;
                    const paidBenefits = unionEmployerPaidBenefits.data.filter((b: any) => b.reportLineItemID === data.reportLineItemID);
                    paidBenefits.forEach((b: any) => {
                        unionDollars += b.amount;
                        if (b.chapterID === local3ChapterID) {
                            const index = fields.findIndex((d) => d === `E_${b.name}_Dollars`);
                            if (index === -1) exportData.push(b.amount);
                            else exportData[index] = b.amount;
                        }
                    });

                    const index = fields.findIndex((d) => d === 'E_UNION_Dollars');
                    if (index === -1) exportData.push(unionDollars);
                    else exportData[index] = unionDollars;

                    const deductibleBenefits = employeeDeductibleBenefits.data.filter(
                        (b: any) => b.reportLineItemID === data.reportLineItemID
                    );
                    deductibleBenefits.forEach((b: any) => {
                        const index = fields.findIndex((d) => d === `D_${b.name}`);
                        if (index === -1) exportData.push(b.amount);
                        else exportData[index] = b.amount;
                    });
                }
                exportValues.push(exportData);
            }

            const csvDownloadFileName = 'timesheet_export.csv';

            const csv = Papa.unparse({
                fields: fields,
                data: exportValues
            });
            const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            if (link.download !== undefined) {
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', csvDownloadFileName);
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
        } catch (error) {
            ToastQueue.negative('Failed to export timesheet.', { timeout: 5000 });
        }
    };

    const duplicateTimesheet = async (dataItem: TableData) => {
        commandButtonClicked = true;
        setDuplicateError(null);
        setIsCreating(true);

        try {
            // Fetch the full timesheet data with PayStubs for duplication
            const timesheetData = await fetchQuery<TimesheetDuplicationQueryType>(
                relayEnvironment,
                TimesheetDuplicationQuery,
                { timeSheetId: dataItem.id }
            ).toPromise();

            if (!timesheetData?.timeSheetById) {
                throw new Error('Unable to load timesheet data for duplication. Please try again.');
            }

            const sourceTimesheet = timesheetData.timeSheetById;
            const timeSheetCopy = JSON.parse(JSON.stringify(sourceTimesheet));

            if (timeSheetCopy.payPeriodEndDate) {
                timeSheetCopy.payPeriodEndDate = new Date(timeSheetCopy.payPeriodEndDate);
            }

            // Convert PayStubs structure to match expected format
            if (timeSheetCopy.payStubs?.edges) {
                timeSheetCopy.payStubs = timeSheetCopy.payStubs.edges.map((edge: any) => {
                    const payStub = edge.node;
                    if (payStub.details) {
                        payStub.details = payStub.details.map((detail: PayStubDetailType) => {
                            if (detail.workDate) {
                                detail.workDate = new Date(detail.workDate);
                            }
                            return { ...detail };
                        });
                    }
                    return { ...payStub };
                });
            }

            const duplicatedTimesheet = {
                ...timeSheetCopy,
                id: uuidv4(),
                status: 'New',
                createdByUserId: loggedInUserId,
                modifiedByUserId: loggedInUserId
            };

            setDuplicatedTimesheet(duplicatedTimesheet);

            const defaultEndDate =
                timeSheetCopy.type === 'Primary'
                    ? new Date(timeSheetCopy.payPeriodEndDate.getTime() + 7 * 24 * 60 * 60 * 1000)
                    : timeSheetCopy.payPeriodEndDate;

            const dateObj = defaultEndDate;
            const calendarDate = new CalendarDate(dateObj.getFullYear(), dateObj.getMonth() + 1, dateObj.getDate());

            setNewTimeSheetDate(calendarDate);
            setNewTimeSheetType(dataItem.type);
            setIncludeJobCodes(true);
            setIncludeHours(true);
            setIsCreating(false);

            setIsDuplicateDialogOpen(true);
        } catch (error) {
            setIsCreating(false);
            const errorMessage = error instanceof Error ? error.message : 'Failed to load timesheet data for duplication';
            setDuplicateError(errorMessage);
            ToastQueue.negative(errorMessage, { timeout: 5000 });
        }
    };

    const handleDuplicateConfirm = () => {
        if (!duplicatedTimesheet || !newTimeSheetDate) {
            setDuplicateError('Missing required data for duplication.');
            return;
        }

        try {
            setIsCreating(true);

            // Create a regular JS Date from CalendarDate
            const newDate = new Date(newTimeSheetDate.year, newTimeSheetDate.month - 1, newTimeSheetDate.day);

            const formattedDate = formatDateToString(newDate);

            const origDate = new Date(duplicatedTimesheet.payPeriodEndDate);

            // Calculate day difference the simple way - in days
            const MS_PER_DAY = 24 * 60 * 60 * 1000;
            const dayDifference = Math.round((newDate.getTime() - origDate.getTime()) / MS_PER_DAY);

            // PayStubs should already be in the correct format from the duplication query
            const payStubs = duplicatedTimesheet.payStubs || [];

            const payStubsInput = createPayStubInputs(payStubs, dayDifference, formattedDate, includeJobCodes, includeHours);

            const addInput = createTimesheetInput(
                employerGuid,
                formattedDate,
                newTimeSheetType,
                duplicatedTimesheet,
                payStubsInput,
                includeJobCodes,
                includeHours
            );

            commitAddMutation({
                variables: {
                    input: addInput
                },
                updater: addTimesheetUpdater,
                onCompleted: (response, errors) => {
                    setIsCreating(false);
                    setIsDuplicateDialogOpen(false);

                    if (errors && errors.length > 0) {
                        const errorMessage = formatGraphQLErrors(errors);
                        setDuplicateError(`Failed to duplicate timesheet: ${errorMessage}`);
                        ToastQueue.negative(`Failed to duplicate timesheet: ${errorMessage}`, { timeout: 5000 });
                    } else if ((response as useTimesheetSaverAddMutation$data)?.addTimesheet?.timeSheetEdge?.node?.id) {
                        const timeSheetEdge = (response as useTimesheetSaverAddMutation$data).addTimesheet.timeSheetEdge;
                        const newTimesheetId = timeSheetEdge?.node?.id || '';

                        navigate(`/timesheet-roster/detail?id=${newTimesheetId}&employerGuid=${encodeURIComponent(employerGuid)}`);
                        ToastQueue.positive('Timesheet copied successfully.', { timeout: 5000 });
                    } else {
                        setDuplicateError('Failed to create timesheet: Invalid response from server.');
                        ToastQueue.negative('Failed to create timesheet: Invalid response from server.', { timeout: 5000 });
                    }
                },
                onError: (error) => {
                    setIsCreating(false);

                    // Try to extract more specific error information from GraphQL errors if available
                    let errorMessage: string;

                    // Check if this is a GraphQL error with graphQLErrors property
                    if (error && typeof error === 'object' && 'graphQLErrors' in error && Array.isArray(error.graphQLErrors) && error.graphQLErrors.length > 0) {
                        errorMessage = formatGraphQLErrors(error.graphQLErrors);
                    } else if (error instanceof Error) {
                        errorMessage = error.message;
                    } else {
                        errorMessage = String(error);
                    }

                    // Check for specific types of errors
                    if (errorMessage.includes('date') || errorMessage.includes('Date')) {
                        errorMessage = 'Invalid date format. Please check the date selection.';
                    } else if (errorMessage.toLowerCase().includes('timesheet')) {
                        // Keep the original message as it's likely specific to timesheet issues
                    } else if (!(error && typeof error === 'object' && 'graphQLErrors' in error && Array.isArray(error.graphQLErrors) && error.graphQLErrors.length > 0)) {
                        // For generic errors without GraphQL details, provide more context
                        errorMessage = `Unexpected error: ${errorMessage}`;
                    }

                    setDuplicateError(`Failed to duplicate timesheet: ${errorMessage}`);
                    ToastQueue.negative(`Failed to duplicate timesheet: ${errorMessage}`, { timeout: 5000 });
                }
            });
        } catch (error) {
            setIsCreating(false);

            let errorMessage: string;
            if (error instanceof Error) {
                errorMessage = error.message;
            } else {
                errorMessage = String(error);
            }

            // Check for specific types of errors
            if (errorMessage.includes('date') || errorMessage.includes('Date')) {
                errorMessage = 'Invalid date format. Please check the date selection.';
            } else if (errorMessage.toLowerCase().includes('timesheet')) {
                // Keep the original message as it's likely specific to timesheet issues
            } else {
                // For generic errors, provide more context
                errorMessage = `Unexpected error: ${errorMessage}`;
            }

            setDuplicateError(`Failed to duplicate timesheet: ${errorMessage}`);
            ToastQueue.negative(`Failed to duplicate timesheet: ${errorMessage}`, { timeout: 5000 });
        }
    };

    const handleCancelDuplicate = () => {
        setIsDuplicateDialogOpen(false);
        setDuplicatedTimesheet(null);
        setDuplicateError(null);
        setIsCreating(false);
    };

    const handleAction = (key: Key) => {
        if (key === 'delete') {
            setIsDeleteDialogOpen(true);
        } else if (key === 'copy') {
            duplicateTimesheet(timesheet);
        } else if (key === 'download') {
            exportCSV(timesheet);
        }
    };

    const handleDeleteConfirm = () => {
        onDeleteTimesheet(timesheet.id);
        setIsDeleteDialogOpen(false);
    };

    const disabledKeys = timesheet.status === 'Submitted' ? ['delete'] : [];

    return (
        <>
            <ActionMenu isQuiet onAction={handleAction} disabledKeys={disabledKeys}>
                <Section key={'timesheetActions'}>
                    <Item key="copy" textValue="Copy">
                        <Text>Copy</Text>
                    </Item>
                    <Item key="delete" textValue="Delete">
                        <Text>Delete</Text>
                    </Item>
                    <Item key="download" textValue="Download">
                        <Text>Download</Text>
                    </Item>
                </Section>
            </ActionMenu>

            <DialogTrigger isOpen={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                {/* Minimal trigger element */}
                <ActionButton isQuiet aria-label="Delete timesheet dialog trigger" UNSAFE_style={{ display: 'none' }} />
                <AlertDialog
                    variant="destructive"
                    title="Delete Timesheet"
                    primaryActionLabel="Delete"
                    cancelLabel="Cancel"
                    onPrimaryAction={handleDeleteConfirm}>
                    Are you sure you want to permanently delete this timesheet? This action cannot be undone.
                </AlertDialog>
            </DialogTrigger>

            {isDuplicateDialogOpen && (
                <DialogContainer onDismiss={handleCancelDuplicate}>
                    <Dialog size="M">
                        <Heading>
                            <Flex alignItems="center" gap="size-100">
                                Duplicate Timesheet
                            </Flex>
                        </Heading>
                        <Divider />
                        <Content>
                            <Text>Please confirm the details for the duplicated timesheet:</Text>

                            <Form validationBehavior="native">
                                <DatePicker
                                    label="Week End Date"
                                    value={newTimeSheetDate}
                                    onChange={(value) => {
                                        if (value !== null) {
                                            setNewTimeSheetDate(value);
                                        }
                                    }}
                                    width={'static-size-2600'}
                                    isRequired
                                />

                                <Picker
                                    label="Type"
                                    selectedKey={newTimeSheetType}
                                    onSelectionChange={(selected) => setNewTimeSheetType(selected as string)}
                                    width={'static-size-2600'}
                                    isRequired>
                                    {TIMESHEET_TYPES.map((type) => (
                                        <Item key={type} textValue={type}>
                                            {type}
                                        </Item>
                                    ))}
                                </Picker>

                                <Flex direction="column" gap="size-100" marginTop="size-200">
                                    <Checkbox isSelected={includeJobCodes} onChange={setIncludeJobCodes}>
                                        Include job codes
                                    </Checkbox>

                                    <Checkbox isSelected={includeHours} onChange={setIncludeHours}>
                                        Include hour/cost data
                                    </Checkbox>
                                </Flex>
                            </Form>

                            {duplicateError && (
                                <div
                                    style={{
                                        marginTop: 'var(--spectrum-global-dimension-size-200)',
                                        color: 'var(--spectrum-semantic-negative-color-text-small)'
                                    }}>
                                    <Text>{duplicateError}</Text>
                                </div>
                            )}

                            <ButtonGroup marginTop={'size-500'} alignSelf={'end'} align="end">
                                <Button variant="secondary" onPress={handleCancelDuplicate} isDisabled={isCreating || isMutationInFlight}>
                                    {BUTTON_TEXT.CANCEL}
                                </Button>
                                <Button variant="accent" onPress={handleDuplicateConfirm} isDisabled={isCreating || isMutationInFlight}>
                                    {isCreating || isMutationInFlight ? 'Creating...' : BUTTON_TEXT.CONFIRM}
                                </Button>
                            </ButtonGroup>
                        </Content>
                    </Dialog>
                </DialogContainer>
            )}
        </>
    );
};

export const DynamicCell = (key: Key, item: TableData, onDeleteTimesheet: (timesheetId: string) => void, employerGuid?: string) => {
    switch (key) {
        case 'numericId':
            return <IdCell {...item} />;
        case 'payPeriodEndDate':
            return PeriodCell(item, employerGuid);
        case 'hoursWorked':
            return <HoursWorkedCell {...item} />;
        case 'type':
            return <TypeCell {...item} />;
        case 'modificationDate':
            return <LastModifiedCell {...item} />;
        case 'modifiedByUserId':
            return <LastModifiedByCell {...item} />;
        case 'payStubCount':
            return <NumberOfPayStubsCell {...item} />;
        case 'status':
            return <StatusCell {...item} />;
        case 'actions':
            return <EPRTableRowAction timesheet={item} onDeleteTimesheet={onDeleteTimesheet} />;
    }
};
