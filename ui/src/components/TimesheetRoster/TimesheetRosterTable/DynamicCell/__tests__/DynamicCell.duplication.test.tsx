import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { MockEnvironment, createMockEnvironment } from 'relay-test-utils';
import { RelayEnvironmentProvider } from 'react-relay';
import { Provider, defaultTheme } from '@adobe/react-spectrum';
import { DynamicCell } from '../DynamicCell';
import { TimesheetDuplicationQuery } from '../TimesheetDuplicationQuery';
import type { TableData } from '../../TimesheetRoster.data';

// Mock the store
jest.mock('@/lib/core/Store', () => ({
    useStore: () => ({
        email: '<EMAIL>',
        selectedEmployerGuid: 'test-employer-guid'
    })
}));

// Mock the navigate function
const mockNavigate = jest.fn();
jest.mock('react-router', () => ({
    useNavigate: () => mockNavigate
}));

// Mock the ToastQueue
jest.mock('@react-spectrum/toast', () => ({
    ToastQueue: {
        negative: jest.fn(),
        positive: jest.fn()
    }
}));

describe('DynamicCell Duplication Functionality', () => {
    let environment: MockEnvironment;
    const mockTimesheet: TableData = {
        id: 'test-timesheet-id',
        numericId: 12345,
        name: 'Test Timesheet',
        payPeriodEndDate: '2024-01-07',
        status: 'Saved',
        type: 'Primary',
        hoursWorked: 40,
        payStubCount: 2,
        showBonusColumn: false,
        showDTHoursColumn: false,
        showEarningsCodesColumn: false,
        showExpensesColumn: false,
        modificationDate: '2024-01-05T10:00:00Z',
        modifiedByUserId: 'test-user',
        creationDate: '2024-01-05T09:00:00Z',
        oldId: null,
        period: {
            year: 2024,
            weekNumber: 1,
            endDate: '2024-01-07'
        },
        uid: 'test-uid'
    };

    beforeEach(() => {
        environment = createMockEnvironment();
        jest.clearAllMocks();
    });

    it('should render actions cell with ActionMenu', () => {
        const mockDeleteTimesheet = jest.fn();

        const TestComponent = () => (
            <Provider theme={defaultTheme}>
                <RelayEnvironmentProvider environment={environment}>
                    {DynamicCell('actions', mockTimesheet, mockDeleteTimesheet, 'test-employer-guid')}
                </RelayEnvironmentProvider>
            </Provider>
        );

        render(<TestComponent />);

        // Verify that the action menu button is rendered
        const actionMenuButton = screen.getByRole('button', { name: /more actions/i });
        expect(actionMenuButton).toBeInTheDocument();
        expect(actionMenuButton).toHaveAttribute('aria-haspopup', 'true');
        expect(actionMenuButton).toHaveAttribute('aria-expanded', 'false');
    });

    it('should have duplication query properly defined', () => {
        // Test that the TimesheetDuplicationQuery is properly formed
        // This verifies that the query structure exists and can be used
        expect(TimesheetDuplicationQuery).toBeDefined();
        expect(typeof TimesheetDuplicationQuery).toBe('object');
        
        // Verify that the query object has the expected Relay structure
        expect(TimesheetDuplicationQuery).toHaveProperty('params');
    });
});