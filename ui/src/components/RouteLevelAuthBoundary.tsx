import { useRouteError, isRouteErrorResponse } from 'react-router-dom';
import { AuthError } from "@/lib/core/errors"; // Path based on your tsconfig alias

export function RouteLevelAuthBoundary() {
    const error = useRouteError(); // This hook captures the error bubbled to the route

    if (error instanceof AuthError) {
        // The global ErrorBoundary is already displaying the auth modal.
        // This route-level boundary's job is to "catch" the error for React Router
        // and prevent React Router from rendering its default error UI or logging further.
        // Rendering null here means this route contributes nothing to the UI when an AuthError
        // has been globally handled by our modal.
        return null;
    }

    // Handle other types of errors that might specifically be caught at the route level
    // (e.g., errors from loaders, actions, or non-AuthErrors thrown during render)
    // This provides a fallback UI for errors that *aren't* AuthError.
    let errorMessage = 'An unexpected error occurred on this route.';
    if (isRouteErrorResponse(error)) {
        errorMessage = error.data?.message || error.statusText || `Error ${error.status}`;
    } else if (error instanceof Error) {
        errorMessage = error.message;
    }

    if (process.env.NODE_ENV === 'development') {
        console.error('[RouteLevelAuthBoundary] Non-AuthError caught. Rendering route error fallback:', errorMessage);
    }

    // You can make this a more styled component
    return (
        <div role="alert" style={{ padding: '20px', color: 'red' }}>
            <h1>Oops! Route Error</h1>
            <p>Something went wrong while trying to display this page.</p>
            <p>
                <i>{errorMessage}</i>
            </p>
        </div>
    );
}
