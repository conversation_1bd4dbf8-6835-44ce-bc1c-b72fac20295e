/**
 * Unified ErrorBoundary component that consolidates functionality from:
 * - TimesheetErrorBoundary
 * - EnhancedErrorBoundary
 * - Generic ErrorBoundary components
 */

import React, { Component, ReactNode } from 'react';
import { TimesheetError } from './TimesheetError';
import { ErrorType, ErrorSeverity } from './errorTypes';
import { ErrorService } from './errorService';
import { ErrorDisplay } from '../components/UI/ErrorDisplay';

interface Props {
  children: ReactNode;
  fallback?: (error: TimesheetError, retry: () => void, reset: () => void) => ReactNode;
  onError?: (error: TimesheetError, errorInfo: React.ErrorInfo) => void;
  maxRetries?: number;
  resetOnPropsChange?: boolean;
  resetKeys?: Array<string | number>;
  component?: string; // For error context
}

interface State {
  hasError: boolean;
  error: TimesheetError | null;
  retryCount: number;
  errorId: string | null;
}

export class ErrorBoundary extends Component<Props, State> {
  private resetTimeoutId: ReturnType<typeof setTimeout> | null = null;

  constructor(props: Props) {
    super(props);
    this.state = { 
      hasError: false, 
      error: null, 
      retryCount: 0,
      errorId: null
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Convert to TimesheetError if it isn't already
    const timesheetError = error instanceof TimesheetError 
      ? error 
      : new TimesheetError({
          type: ErrorType.UNKNOWN,
          severity: ErrorSeverity.HIGH,
          message: error.message,
          technical: error.stack || error.message,
          context: { 
            originalError: error,
            errorBoundaryCapture: true
          }
        });

    const errorId = `boundary_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    return {
      hasError: true,
      error: timesheetError,
      retryCount: 0,
      errorId
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    const timesheetError = this.state.error;
    
    if (timesheetError) {
      // Add error info to context
      timesheetError.enhancedError.context = {
        ...timesheetError.enhancedError.context,
        componentStack: errorInfo.componentStack,
        errorBoundary: this.props.component || 'ErrorBoundary',
        retryCount: this.state.retryCount
      };

      // Use consolidated error service for logging
      ErrorService.handleComponentError(error, errorInfo, {
        component: this.props.component || 'ErrorBoundary',
        action: 'componentDidCatch',
        metadata: {
          errorId: this.state.errorId,
          retryCount: this.state.retryCount
        }
      });

      // Call custom error handler
      this.props.onError?.(timesheetError, errorInfo);
      
      // Report to error tracking service in production
      if (process.env.NODE_ENV === 'production') {
        // This would integrate with error reporting service
        console.error('ErrorBoundary caught error:', {
          errorId: this.state.errorId,
          error: timesheetError.getSafeErrorForLogging(),
          componentStack: errorInfo.componentStack
        });
      }
    }
  }

  componentDidUpdate(prevProps: Props) {
    const { resetOnPropsChange, resetKeys } = this.props;
    const { hasError } = this.state;
    
    if (hasError && (resetOnPropsChange || resetKeys)) {
      if (resetKeys) {
        const hasResetKeyChanged = resetKeys.some((key, index) => 
          key !== (prevProps.resetKeys && prevProps.resetKeys[index])
        );
        
        if (hasResetKeyChanged) {
          this.resetErrorBoundary();
        }
      }
    }
  }

  componentWillUnmount() {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
    }
  }

  resetErrorBoundary = () => {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
    }
    
    this.setState({
      hasError: false,
      error: null,
      retryCount: 0,
      errorId: null
    });
  };

  handleRetry = () => {
    const maxRetries = this.props.maxRetries || 3;
    const { retryCount, error } = this.state;
    
    if (retryCount < maxRetries && error?.canRetryNow()) {
      // Increment retry count on the error
      const updatedError = error.withIncrementedRetry();
      
      this.setState(prevState => ({
        hasError: false,
        error: null,
        retryCount: prevState.retryCount + 1,
        errorId: null
      }));

      // Optional: Add delay before retry for better UX
      this.resetTimeoutId = setTimeout(() => {
        // Error boundary will naturally re-render children
        // If error persists, it will be caught again
      }, 100);
    }
  };

  render() {
    if (this.state.hasError && this.state.error) {
      const { error, retryCount } = this.state;
      const maxRetries = this.props.maxRetries || 3;
      const canRetry = error.canRetryNow() && retryCount < maxRetries;

      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback(error, this.handleRetry, this.resetErrorBoundary);
      }

      // Default error display using existing ErrorDisplay component
      // Type assertion for compatibility during transition
      return (
        <ErrorDisplay
          error={error as any}
          onRetry={canRetry ? this.handleRetry : undefined}
          canRetry={canRetry}
          retryCount={retryCount}
          maxRetries={maxRetries}
          showTechnicalDetails={process.env.NODE_ENV === 'development'}
        />
      );
    }

    return this.props.children;
  }
}

/**
 * Hook-based error handler for functional components
 * Provides similar functionality to error boundaries but for imperative error handling
 */
export function useErrorHandler() {
  const handleError = React.useCallback((error: unknown, context?: { component?: string; action?: string }) => {
    let timesheetError: TimesheetError;

    if (error instanceof TimesheetError) {
      timesheetError = error;
    } else if (error instanceof Error) {
      timesheetError = new TimesheetError({
        type: ErrorType.UNKNOWN,
        severity: ErrorSeverity.MEDIUM,
        message: error.message,
        technical: error.stack || error.message,
        context: { 
          originalError: error,
          hookCapture: true,
          component: context?.component,
          action: context?.action
        }
      });
    } else {
      timesheetError = new TimesheetError({
        type: ErrorType.UNKNOWN,
        severity: ErrorSeverity.MEDIUM,
        message: 'Unknown error occurred',
        technical: String(error),
        context: { 
          originalError: error,
          hookCapture: true,
          component: context?.component,
          action: context?.action
        }
      });
    }

    // Log error using error service
    ErrorService.handleComponentError(
      timesheetError, 
      { componentStack: 'Hook-based error' }, 
      {
        component: context?.component || 'useErrorHandler',
        action: context?.action || 'handleError'
      }
    );

    // In a real implementation, you might want to:
    // 1. Show a toast notification
    // 2. Update global error state
    // 3. Trigger error reporting
    
    return timesheetError;
  }, []);

  return { handleError };
}

/**
 * Higher-order component for wrapping components with error boundary
 */
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<Props, 'children'>
): React.ComponentType<P> {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps} component={Component.displayName || Component.name}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;

  return WrappedComponent;
}