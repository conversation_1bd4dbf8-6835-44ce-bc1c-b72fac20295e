/**
 * Unified TimesheetError class that consolidates all error types
 * Incorporates functionality from TimesheetError, EnhancedError, AuthError, and ValidationError
 */

import { ErrorType, ErrorSeverity, ErrorMetadata } from './errorTypes';

export class TimesheetError extends Error {
  public readonly enhancedError: ErrorMetadata;
  
  // Auth error properties (from AuthError)
  public readonly code?: string;
  public readonly originalMessage?: string;
  
  // Validation error properties (from ValidationError)
  public readonly field?: string;

  constructor(error: Partial<ErrorMetadata> & { message: string; code?: string; field?: string }) {
    super(error.message);
    this.name = 'TimesheetError';
    
    // Set prototype chain for proper inheritance (ES5 compatibility)
    Object.setPrototypeOf(this, TimesheetError.prototype);
    
    // Auth error properties
    this.code = error.code;
    this.originalMessage = error.message;
    
    // Validation error properties
    this.field = error.field;
    
    this.enhancedError = {
      id: error.id || `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: error.type || ErrorType.UNKNOWN,
      severity: error.severity || ErrorSeverity.MEDIUM,
      message: error.message,
      userMessage: error.userMessage || this.getDefaultUserMessage(error.type),
      technical: error.technical || error.message,
      suggestions: error.suggestions || this.getDefaultSuggestions(error.type),
      canRetry: error.canRetry ?? this.getDefaultRetryability(error.type),
      retryCount: error.retryCount || 0,
      maxRetries: error.maxRetries || 3,
      timestamp: error.timestamp || new Date(),
      context: error.context || {}
    };
  }

  private getDefaultUserMessage(type?: ErrorType): string {
    const messages = {
      [ErrorType.NETWORK]: 'Connection issue occurred. Please check your internet connection.',
      [ErrorType.VALIDATION]: 'Please check your input and try again.',
      [ErrorType.PERMISSION]: 'You don\'t have permission to perform this action.',
      [ErrorType.MUTATION]: 'Unable to save changes at this time.',
      [ErrorType.FRAGMENT]: 'Data loading issue occurred.',
      [ErrorType.AUTH]: 'Authentication required. Please log in.',
      [ErrorType.UNKNOWN]: 'An unexpected error occurred.'
    };
    return messages[type || ErrorType.UNKNOWN];
  }

  private getDefaultSuggestions(type?: ErrorType): string[] {
    const suggestions = {
      [ErrorType.NETWORK]: [
        'Check your internet connection',
        'Try refreshing the page',
        'Contact support if the issue persists'
      ],
      [ErrorType.VALIDATION]: [
        'Verify all required fields are filled',
        'Check for invalid characters or formats',
        'Ensure values are within acceptable ranges'
      ],
      [ErrorType.PERMISSION]: [
        'Contact your administrator for access',
        'Verify you\'re logged in with the correct account',
        'Check if your session has expired'
      ],
      [ErrorType.MUTATION]: [
        'Try saving again',
        'Refresh the page and retry',
        'Check if data was actually saved'
      ],
      [ErrorType.FRAGMENT]: [
        'Refresh the page',
        'Try again in a few moments',
        'Clear browser cache if problem persists'
      ],
      [ErrorType.AUTH]: [
        'Please log in again',
        'Check if your session has expired',
        'Contact support if login issues persist'
      ],
      [ErrorType.UNKNOWN]: [
        'Try refreshing the page',
        'Try again in a few moments',
        'Contact support if the issue continues'
      ]
    };
    return suggestions[type || ErrorType.UNKNOWN];
  }

  private getDefaultRetryability(type?: ErrorType): boolean {
    const retryable = {
      [ErrorType.NETWORK]: true,
      [ErrorType.VALIDATION]: false,
      [ErrorType.PERMISSION]: false,
      [ErrorType.MUTATION]: true,
      [ErrorType.FRAGMENT]: true,
      [ErrorType.AUTH]: false,
      [ErrorType.UNKNOWN]: true
    };
    return retryable[type || ErrorType.UNKNOWN];
  }

  /**
   * Factory method to create an AuthError-compatible instance
   */
  static createAuthError(message: string, code: string, originalMessage: string): TimesheetError {
    return new TimesheetError({
      type: ErrorType.AUTH,
      severity: ErrorSeverity.HIGH,
      message,
      code,
      userMessage: 'Please log in to continue.',
      technical: `Auth error [${code}]: ${originalMessage}`,
      canRetry: false,
      context: { authErrorCode: code, originalMessage }
    });
  }

  /**
   * Factory method to create a ValidationError-compatible instance
   */
  static createValidationError(message: string, field?: string): TimesheetError {
    return new TimesheetError({
      type: ErrorType.VALIDATION,
      severity: ErrorSeverity.MEDIUM,
      message,
      field,
      userMessage: field ? `Invalid ${field}: ${message}` : message,
      technical: `Validation error${field ? ` on field '${field}'` : ''}: ${message}`,
      canRetry: false,
      context: { validationField: field }
    });
  }

  /**
   * Check if error is retryable based on current state
   */
  canRetryNow(): boolean {
    const { enhancedError } = this;
    return enhancedError.canRetry && 
           enhancedError.retryCount < enhancedError.maxRetries &&
           enhancedError.severity !== ErrorSeverity.CRITICAL;
  }

  /**
   * Create a new instance with incremented retry count
   */
  withIncrementedRetry(): TimesheetError {
    return new TimesheetError({
      ...this.enhancedError,
      retryCount: this.enhancedError.retryCount + 1,
      timestamp: new Date(),
      code: this.code,
      field: this.field
    });
  }

  /**
   * Get a safe error representation for logging (without sensitive data)
   */
  getSafeErrorForLogging(): Record<string, any> {
    return {
      id: this.enhancedError.id,
      type: this.enhancedError.type,
      severity: this.enhancedError.severity,
      message: this.enhancedError.message,
      userMessage: this.enhancedError.userMessage,
      canRetry: this.enhancedError.canRetry,
      retryCount: this.enhancedError.retryCount,
      timestamp: this.enhancedError.timestamp,
      code: this.code,
      field: this.field
    };
  }
}