/**
 * Consolidated error handling module - barrel export
 * 
 * This module provides a unified interface for all error handling functionality,
 * consolidating what was previously scattered across multiple files and services.
 */

import type { ErrorInfo } from 'react';
import { ErrorService } from './errorService';
import { TimesheetError } from './TimesheetError';
import type { ErrorMetadata, ErrorContext } from './errorTypes';

// Core error types and interfaces
export type {
  ErrorMetadata,
  ErrorContext,
  ErrorHandlingResult,
  ErrorReport,
  ErrorReportingConfig
} from './errorTypes';

export {
  ErrorType,
  ErrorSeverity
} from './errorTypes';

// Main error class
export { TimesheetError } from './TimesheetError';

// Error service functions
export { ErrorService } from './errorService';

// Error boundary components and hooks
export { 
  ErrorBoundary, 
  useErrorHandler, 
  withErrorBoundary 
} from './ErrorBoundary';

// Convenience re-exports for backward compatibility and ease of use
export const parseError = (error: any) => ErrorService.parseRelayError(error);
export const parseRelayError = (error: any) => ErrorService.parseRelayError(error);
export const handleMutationError = (error: unknown, context: ErrorContext) => ErrorService.handleMutationError(error, context);
export const handleComponentError = (error: Error, errorInfo: ErrorInfo, context: ErrorContext) => ErrorService.handleComponentError(error, errorInfo, context);
export const handleValidationError = (field: string, value: unknown, context: ErrorContext) => ErrorService.handleValidationError(field, value, context);
export const shouldRetryError = (error: TimesheetError) => ErrorService.shouldRetryError(error);
export const incrementRetryCount = (error: TimesheetError) => ErrorService.incrementRetryCount(error);
export const createTimesheetError = (error: Partial<ErrorMetadata> & { message: string }) => ErrorService.createTimesheetError(error);

// Factory functions for specific error types (backward compatibility)
export const createAuthError = (message: string, code: string, originalMessage: string) => TimesheetError.createAuthError(message, code, originalMessage);
export const createValidationError = (message: string, field?: string) => TimesheetError.createValidationError(message, field);