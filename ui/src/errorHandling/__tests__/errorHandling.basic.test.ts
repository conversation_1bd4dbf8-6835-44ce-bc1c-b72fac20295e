/**
 * Basic tests for the consolidated error handling system
 */

import { TimesheetError, ErrorType, ErrorSeverity, ErrorService } from '../index';

describe('Consolidated Error Handling', () => {
  describe('TimesheetError', () => {
    it('should create a basic TimesheetError', () => {
      const error = new TimesheetError({
        message: 'Test error',
        type: ErrorType.VALIDATION,
        severity: ErrorSeverity.MEDIUM
      });

      expect(error.message).toBe('Test error');
      expect(error.enhancedError.type).toBe(ErrorType.VALIDATION);
      expect(error.enhancedError.severity).toBe(ErrorSeverity.MEDIUM);
      expect(error.enhancedError.userMessage).toBe('Please check your input and try again.');
    });

    it('should create AuthError-compatible instance', () => {
      const authError = TimesheetError.createAuthError(
        'Authentication failed',
        'INVALID_TOKEN',
        'Token expired'
      );

      expect(authError.message).toBe('Authentication failed');
      expect(authError.code).toBe('INVALID_TOKEN');
      expect(authError.originalMessage).toBe('Authentication failed');
      expect(authError.enhancedError.type).toBe(ErrorType.AUTH);
    });

    it('should create ValidationError-compatible instance', () => {
      const validationError = TimesheetError.createValidationError(
        'Invalid email format',
        'email'
      );

      expect(validationError.message).toBe('Invalid email format');
      expect(validationError.field).toBe('email');
      expect(validationError.enhancedError.type).toBe(ErrorType.VALIDATION);
    });

    it('should handle retry logic correctly', () => {
      const error = new TimesheetError({
        message: 'Network error',
        type: ErrorType.NETWORK,
        retryCount: 0,
        maxRetries: 3
      });

      expect(error.canRetryNow()).toBe(true);

      const retriedError = error.withIncrementedRetry();
      expect(retriedError.enhancedError.retryCount).toBe(1);
      expect(retriedError.canRetryNow()).toBe(true);
    });
  });

  describe('ErrorService', () => {
    it('should parse network errors correctly', () => {
      const networkError = new TypeError('fetch failed');
      const parsedError = ErrorService.parseRelayError(networkError);

      expect(parsedError.enhancedError.type).toBe(ErrorType.NETWORK);
      expect(parsedError.enhancedError.canRetry).toBe(true);
    });

    it('should handle GraphQL validation errors', () => {
      const mockRelayError = {
        source: {
          errors: [{
            message: 'Validation failed',
            extensions: { code: 'VALIDATION_ERROR' }
          }]
        }
      };

      const parsedError = ErrorService.parseRelayError(mockRelayError);
      expect(parsedError.enhancedError.type).toBe(ErrorType.VALIDATION);
      expect(parsedError.enhancedError.canRetry).toBe(false);
    });

    it('should handle mutation errors with proper context', () => {
      const mockError = {
        graphQLErrors: [{
          message: 'Unauthorized access',
          extensions: { code: 'UNAUTHORIZED' }
        }]
      };

      const result = ErrorService.handleMutationError(mockError, {
        component: 'TestComponent',
        action: 'mutation'
      });

      expect(result.message).toBe('You do not have permission for this action.');
      expect(result.canRetry).toBe(false);
      expect(result.severity).toBe('error');
    });
  });
});