/**
 * Unified error types, enums, and interfaces for the error handling system
 */

export enum ErrorType {
  NETWORK = 'NETWORK',
  VALIDATION = 'VALIDATION',
  PERMISSION = 'PERMISSION',
  MUTATION = 'MUTATION',
  FRAGMENT = 'FRAGMENT',
  AUTH = 'AUTH',
  UNKNOWN = 'UNKNOWN'
}

export enum ErrorSeverity {
  LOW = 'LOW',       // User can continue, just show message
  MEDIUM = 'MEDIUM', // User should retry, but functionality works
  HIGH = 'HIGH',     // Feature broken, needs immediate attention
  CRITICAL = 'CRITICAL' // App broken, needs emergency fix
}

export interface ErrorMetadata {
  id: string;
  type: ErrorType;
  severity: ErrorSeverity;
  message: string;
  userMessage: string;
  technical: string;
  suggestions: string[];
  canRetry: boolean;
  retryCount: number;
  maxRetries: number;
  timestamp: Date;
  context: Record<string, any>;
}

export interface ErrorContext {
  component: string;
  action: string;
  metadata?: Record<string, unknown>;
}

export interface ErrorHandlingResult {
  message: string;
  canRetry: boolean;
  severity: 'error' | 'warning' | 'info';
}

export interface ErrorReport {
  id: string;
  timestamp: Date;
  error: any; // Will be TimesheetError after circular dependency resolution
  userAgent: string;
  url: string;
  userId?: string;
  sessionId?: string;
  buildVersion?: string;
  environment: string;
}

export interface ErrorReportingConfig {
  apiEndpoint?: string;
  apiKey?: string;
  enableConsoleLogging: boolean;
  enableRemoteLogging: boolean;
  batchSize: number;
  flushInterval: number;
  maxRetries: number;
}