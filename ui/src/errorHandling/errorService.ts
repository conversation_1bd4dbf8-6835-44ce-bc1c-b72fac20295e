/**
 * Consolidated error service that merges functionality from:
 * - ErrorHandlingService
 * - parseRelayError function
 * - AuthErrorHandler
 * - Error reporting capabilities
 */

import { TimesheetError } from './TimesheetError';
import { ErrorType, ErrorSeverity, ErrorContext, ErrorHandlingResult } from './errorTypes';
import { getFieldDisplayName } from '../utils/fieldDisplayUtils';

export class ErrorService {
  /**
   * Parse and classify Relay/GraphQL errors into TimesheetError instances
   */
  static parseRelayError(relayError: any): TimesheetError {
    // Check for specific Relay error patterns
    if (relayError.message?.includes('fragment reference')) {
      return new TimesheetError({
        type: ErrorType.FRAGMENT,
        severity: ErrorSeverity.HIGH,
        message: relayError.message,
        userMessage: 'Data loading error occurred. Please refresh the page.',
        technical: `Fragment reference error: ${relayError.message}`,
        suggestions: [
          'Refresh the page',
          'Clear browser cache',
          'Contact support if problem persists'
        ],
        canRetry: true,
        context: { originalError: relayError }
      });
    }

    if (relayError.source?.errors) {
      const graphqlError = relayError.source.errors[0];
      
      if (graphqlError.extensions?.code === 'VALIDATION_ERROR') {
        return new TimesheetError({
          type: ErrorType.VALIDATION,
          severity: ErrorSeverity.MEDIUM,
          message: graphqlError.message,
          userMessage: 'Please check your input and correct any errors.',
          technical: `Validation error: ${graphqlError.message}`,
          suggestions: [
            'Check all required fields are filled',
            'Verify data formats are correct',
            'Check for any highlighted errors'
          ],
          canRetry: false,
          context: { validationErrors: graphqlError.extensions.validationErrors }
        });
      }

      if (graphqlError.extensions?.code === 'UNAUTHORIZED') {
        return new TimesheetError({
          type: ErrorType.PERMISSION,
          severity: ErrorSeverity.HIGH,
          message: graphqlError.message,
          userMessage: 'You don\'t have permission to perform this action.',
          technical: `Authorization error: ${graphqlError.message}`,
          canRetry: false
        });
      }
    }

    // Network-related errors
    if (relayError.name === 'TypeError' && relayError.message?.includes('fetch')) {
      return new TimesheetError({
        type: ErrorType.NETWORK,
        severity: ErrorSeverity.MEDIUM,
        message: relayError.message,
        userMessage: 'Connection issue occurred. Please check your internet connection.',
        technical: `Network error: ${relayError.message}`,
        canRetry: true
      });
    }

    // Default case
    return new TimesheetError({
      type: ErrorType.UNKNOWN,
      severity: ErrorSeverity.MEDIUM,
      message: relayError.message || 'Unknown error occurred',
      technical: this.safeJsonStringify(relayError),
      context: { originalError: relayError }
    });
  }

  /**
   * Handle GraphQL/Relay mutation errors with user-friendly messages
   */
  static handleMutationError(error: unknown, context: ErrorContext): ErrorHandlingResult {
    // Log the error for debugging
    this.logError(error, context);

    // Priority 1: GraphQL error extensions (most reliable)
    if (
      error &&
      typeof error === 'object' &&
      'graphQLErrors' in error &&
      Array.isArray(error.graphQLErrors) &&
      error.graphQLErrors.length > 0
    ) {
      const gqlError = error.graphQLErrors[0] as unknown;
      return this.handleGraphQLError(gqlError, context, error);
    }

    // Priority 2: Relay error source
    if (
      error &&
      typeof error === 'object' &&
      'source' in error &&
      error.source &&
      typeof error.source === 'object' &&
      'errors' in error.source &&
      Array.isArray(error.source.errors) &&
      error.source.errors.length > 0
    ) {
      const relayError = error.source.errors[0] as unknown;
      return this.handleRelayError(relayError, context, error);
    }

    // Priority 3: Network/fetch errors (by type, not string)
    if (error instanceof TypeError && error.message && error.message.includes('fetch')) {
      return {
        message: 'Network error. Please check your connection.',
        canRetry: true,
        severity: 'warning'
      };
    }

    // Priority 4: Browser network errors
    if (
      (error && typeof error === 'object' && 'name' in error && error.name === 'NetworkError') ||
      (error && typeof error === 'object' && 'code' in error && error.code === 'NETWORK_ERROR')
    ) {
      return {
        message: 'Network connection lost. Please check your internet connection.',
        canRetry: true,
        severity: 'warning'
      };
    }

    // Priority 5: Timeout errors (by error type)
    if (
      (error && typeof error === 'object' && 'name' in error && error.name === 'TimeoutError') ||
      (error && typeof error === 'object' && 'code' in error && error.code === 'TIMEOUT')
    ) {
      return {
        message: 'Request timed out. Please try again.',
        canRetry: true,
        severity: 'warning'
      };
    }

    // Fallback generic error handling
    return this.handleGenericError(error, context);
  }

  /**
   * Handle GraphQL errors using error extensions codes (GraphQL spec compliant)
   */
  private static handleGraphQLError(gqlError: unknown, context: ErrorContext, originalError: unknown): ErrorHandlingResult {
    const code =
      gqlError &&
      typeof gqlError === 'object' &&
      'extensions' in gqlError &&
      gqlError.extensions &&
      typeof gqlError.extensions === 'object' &&
      'code' in gqlError.extensions
        ? gqlError.extensions.code
        : undefined;

    const message =
      gqlError && typeof gqlError === 'object' && 'message' in gqlError && typeof gqlError.message === 'string'
        ? gqlError.message
        : 'Operation failed.';

    switch (code) {
      case 'BAD_USER_INPUT':
      case 'VALIDATION_ERROR':
        return {
          message: message || 'Invalid input. Please check your data.',
          canRetry: false,
          severity: 'error'
        };

      case 'UNAUTHENTICATED':
        return {
          message: 'Please log in to continue.',
          canRetry: false,
          severity: 'error'
        };

      case 'FORBIDDEN':
      case 'UNAUTHORIZED':
        return {
          message: 'You do not have permission for this action.',
          canRetry: false,
          severity: 'error'
        };

      case 'NOT_FOUND':
        return {
          message: 'The requested resource was not found.',
          canRetry: false,
          severity: 'error'
        };

      case 'INTERNAL_ERROR':
      case 'INTERNAL_SERVER_ERROR':
        return {
          message: 'Server error. Please try again in a moment.',
          canRetry: true,
          severity: 'error'
        };

      case 'TIMEOUT':
        return {
          message: 'Request timed out. Please try again.',
          canRetry: true,
          severity: 'warning'
        };

      case 'RATE_LIMITED':
        return {
          message: 'Too many requests. Please wait a moment and try again.',
          canRetry: true,
          severity: 'warning'
        };

      case 'CONFLICT':
        return {
          message: 'Data conflict detected. Please refresh and try again.',
          canRetry: true,
          severity: 'warning'
        };

      case 'PERSISTED_QUERY_NOT_FOUND':
        return {
          message: 'Operation not found. Please refresh the page.',
          canRetry: true,
          severity: 'warning'
        };

      default:
        return {
          message: message || 'Operation failed.',
          canRetry: true,
          severity: 'warning'
        };
    }
  }

  /**
   * Handle Relay-specific errors
   */
  private static handleRelayError(relayError: unknown, context: ErrorContext, originalError: unknown): ErrorHandlingResult {
    const code =
      relayError &&
      typeof relayError === 'object' &&
      'extensions' in relayError &&
      relayError.extensions &&
      typeof relayError.extensions === 'object' &&
      'code' in relayError.extensions
        ? relayError.extensions.code
        : undefined;

    // Check for Relay-specific error codes
    if (code) {
      return this.handleGraphQLError(relayError, context, originalError);
    }

    const message =
      relayError && typeof relayError === 'object' && 'message' in relayError && typeof relayError.message === 'string'
        ? relayError.message
        : '';

    // Handle Relay error patterns
    if (message.includes('Fragment')) {
      return {
        message: 'Data structure error. Please refresh the page.',
        canRetry: true,
        severity: 'warning'
      };
    }

    if (message.includes('Connection')) {
      return {
        message: 'Data connection error. Please refresh the page.',
        canRetry: true,
        severity: 'warning'
      };
    }

    return {
      message: this.extractRelayErrorMessage(relayError),
      canRetry: this.isRetryableRelayError(relayError),
      severity: 'warning'
    };
  }

  /**
   * Handle generic errors as fallback
   */
  private static handleGenericError(error: unknown, context: ErrorContext): ErrorHandlingResult {
    let message = 'An unexpected error occurred';
    let canRetry = false;
    let severity: 'error' | 'warning' | 'info' = 'error';

    // Fallback string matching only for non-GraphQL errors
    const errorMessage =
      error && typeof error === 'object' && 'message' in error && typeof error.message === 'string' ? error.message : '';

    if (errorMessage) {
      if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
        message = 'Network error. Please check your connection and try again.';
        canRetry = true;
        severity = 'warning';
      } else if (errorMessage.includes('timeout')) {
        message = 'Request timed out. Please try again.';
        canRetry = true;
        severity = 'warning';
      } else if (errorMessage.includes('500') || errorMessage.includes('server error')) {
        message = 'Server error. Please try again in a moment.';
        canRetry = true;
        severity = 'error';
      } else {
        message = errorMessage;
      }
    }

    return { message, canRetry, severity };
  }

  /**
   * Handle component-level errors (from error boundaries)
   */
  static handleComponentError(error: Error, errorInfo: React.ErrorInfo, context: ErrorContext): ErrorHandlingResult {
    let message = 'A component error occurred';
    let canRetry = true;
    const severity: 'error' | 'warning' | 'info' = 'error';

    // Check for common component error patterns
    if (error.message?.includes('Cannot read property') || error.message?.includes('Cannot read properties')) {
      message = 'Data loading error. Please refresh the page.';
      canRetry = true;
    } else if (error.message?.includes('Relay')) {
      message = 'Data synchronization error. Please refresh the page.';
      canRetry = true;
    } else if (error.message?.includes('Fragment')) {
      message = 'Data structure error. Please refresh the page.';
      canRetry = true;
    }

    // Log component error with stack trace
    this.logError(error, {
      ...context,
      metadata: {
        ...context.metadata,
        componentStack: errorInfo.componentStack,
        errorBoundary: true
      }
    });

    return { message, canRetry, severity };
  }

  /**
   * Handle timesheet-specific validation errors
   */
  static handleValidationError(field: string, value: unknown, context: ErrorContext): string {
    const fieldDisplayName = getFieldDisplayName(field);

    // Common validation patterns
    if (value === null || value === undefined || value === '') {
      return `${fieldDisplayName} is required`;
    }

    if (field.includes('Hours') && typeof value === 'number' && (value < 0 || value > 24)) {
      return `${fieldDisplayName} must be between 0 and 24`;
    }

    if (field.includes('amount') || field.includes('rate')) {
      if (typeof value === 'number') {
        if (value < 0) {
          return `${fieldDisplayName} cannot be negative`;
        }
        if (value > 999999) {
          return `${fieldDisplayName} is too large`;
        }
      }
    }

    return `Invalid value for ${fieldDisplayName}`;
  }

  /**
   * Extract user-friendly message from Relay error
   */
  private static extractRelayErrorMessage(relayError: unknown): string {
    if (relayError && typeof relayError === 'object' && 'message' in relayError && typeof relayError.message === 'string') {
      // Clean up technical Relay messages for users
      return relayError.message
        .replace(/GraphQL error:/gi, '')
        .replace(/Relay error:/gi, '')
        .trim();
    }

    return 'Operation failed. Please try again.';
  }

  /**
   * Determine if a Relay error is retryable
   */
  private static isRetryableRelayError(relayError: unknown): boolean {
    if (
      !(
        relayError &&
        typeof relayError === 'object' &&
        'extensions' in relayError &&
        relayError.extensions &&
        typeof relayError.extensions === 'object' &&
        'code' in relayError.extensions
      )
    ) {
      return false;
    }

    const code = relayError.extensions.code;
    const retryableCodes = ['NETWORK_ERROR', 'TIMEOUT', 'SERVER_ERROR', 'RATE_LIMITED'];

    return typeof code === 'string' && retryableCodes.includes(code);
  }

  /**
   * Log errors with context information
   */
  private static logError(error: unknown, context: ErrorContext): void {
    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error(`[${context.component}] ${context.action}:`, error, context.metadata);
    }

    // In production, this would send to error tracking service (e.g., Sentry)
    // window.Sentry?.captureException(error, {
    //     tags: { component: context.component, action: context.action },
    //     extra: context.metadata
    // });
  }

  /**
   * Safe JSON stringify that handles circular references
   */
  private static safeJsonStringify(obj: any, space?: string | number): string {
    const seen = new WeakSet();
    return JSON.stringify(obj, (key, value) => {
      if (typeof value === 'object' && value !== null) {
        if (seen.has(value)) {
          return '[Circular Reference]';
        }
        seen.add(value);
      }
      return value;
    }, space);
  }

  /**
   * Utility functions for error classification
   */
  static shouldRetryError(error: TimesheetError): boolean {
    return error.canRetryNow();
  }

  /**
   * Increments the retry count for an error and returns a new TimesheetError instance.
   * 
   * IMPORTANT: This method follows an immutable pattern - it returns a new error instance
   * rather than modifying the existing one. Callers must replace their error reference
   * with the returned value for the retry count to be updated.
   * 
   * @param error The TimesheetError to increment retry count for
   * @returns A new TimesheetError instance with incremented retry count
   * 
   * @example
   * ```typescript
   * // Correct usage - replace the reference
   * currentError = ErrorService.incrementRetryCount(currentError);
   * 
   * // Incorrect usage - retry count won't be updated
   * ErrorService.incrementRetryCount(currentError); // ❌ Return value ignored
   * ```
   */
  static incrementRetryCount(error: TimesheetError): TimesheetError {
    return error.withIncrementedRetry();
  }

  static createTimesheetError(error: Partial<import('./errorTypes').ErrorMetadata> & { message: string }): TimesheetError {
    return new TimesheetError(error);
  }
}