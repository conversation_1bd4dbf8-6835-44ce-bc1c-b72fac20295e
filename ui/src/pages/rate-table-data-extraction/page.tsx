import { State } from '@progress/kendo-data-query';
import { Button } from '@progress/kendo-react-buttons';
import { useEffect, useRef, useState, useCallback } from 'react';
import { instance } from '@/src/core/http/axios-config';
import { Constants } from '@/src/constants/global';
import { ClientUtils } from "@/lib/core/ClientUtils";
import FilterableComboBox from '@/src/components/kendo/FilterableComboBox';
import MonthPicker from '@/src/components/kendo/MonthPicker';
import MultiSelectCheckbox from '@/src/components/kendo/MultiSelectCheckbox';
import { Loader } from '@progress/kendo-react-indicators';
import EPRReportGrid from '@/src/components/kendo/EPRReportGrid';
import EPRReportColumn from '@/src/components/kendo/EPRReportColumn';
import { useStore, StoreState } from '@/lib';
import PageHeading from '@/src/components/UI/PageHeading';
import AppLayout from '@/src/components/Layout/AppLayout';

const initialDataState: State = {
    filter: {
        filters: [],
        logic: 'and'
    },
    sort: []
};

interface SiteArea {
    text: string;
    value: number;
    toString(): string;
}

interface Agreement {
    text: string;
    value: number;
}

const RateTableDataExtractionReport = () => {
    const [error, setError] = useState<string | null>(null);
    const [loadingReport, setLoadingReport] = useState(false);
    const [dataState, setDataState] = useState(initialDataState);

    const [siteAreas, setSiteAreas] = useState<SiteArea[]>([]);
    const [agreements, setAgreements] = useState<Agreement[]>([]);
    const [rateTableDataExtractResults, setRateTableDataExtractResults] = useState<any[]>([]);

    const [selectedSiteAreaID, setSelectedSiteAreaID] = useState<number | null>(null);
    const [workMonth, setWorkMonth] = useState<Date | null>(null);
    const [selectedAgreements, setSelectedAgreements] = useState<Agreement[]>([]);

    const user = useStore((state: StoreState) => state.user);
    const selectedThirdPartyGuid = useStore((state: StoreState) => state.selectedThirdPartyGuid);

    const fetchSiteAreas = useCallback(() => {
        let url = '/api/RateTableDataExtraction/site-area?username=' + ClientUtils.getUsername();
        if (selectedThirdPartyGuid) url += '&GUID=' + selectedThirdPartyGuid;
        instance
            .get(url)
            .then((response) => {
                if (Array.isArray(response.data)) {
                    const mappedAreas = response.data.map((s: any) => ({
                        text: s.Name || s.name || '', // Handle both casing possibilities
                        value: s.ID || s.id || 0,     // Handle both casing possibilities and provide default
                        toString: function () { return this.text; } // Add toString method for Kendo UI
                    }));
                    setSiteAreas(mappedAreas);
                    // Reset selections when new data is loaded
                    setSelectedSiteAreaID(null);
                    setAgreements([]);
                    setSelectedAgreements([]);
                    setRateTableDataExtractResults([]);
                } else {
                    console.error('Unexpected API response format:', response.data);
                    setSiteAreas([]);
                }
            })
            .catch((e) => {
                setError('Error occurred while fetching site areas');
                setSiteAreas([]);
                setSelectedSiteAreaID(null);
                setAgreements([]);
                setSelectedAgreements([]);
                setRateTableDataExtractResults([]);
            });
    }, [selectedThirdPartyGuid]);

    // Initial fetch for non-admin users
    useEffect(() => {
        if (user && !user.roles.includes(Constants.Roles.SystemAdministrator)) {
            fetchSiteAreas();
        }
    }, [user, fetchSiteAreas]);

    useEffect(() => {
        if (selectedThirdPartyGuid !== null) {
            fetchSiteAreas();
        } else {
            setSiteAreas([]);
            setSelectedSiteAreaID(null);
            setAgreements([]);
            setSelectedAgreements([]);
            setRateTableDataExtractResults([]);
        }
    }, [selectedThirdPartyGuid, fetchSiteAreas]);

    useEffect(() => {
        if (selectedSiteAreaID !== null && workMonth !== null) {
            const url = '/api/RateTableDataExtraction/agreements?ChapterID=' +
                selectedSiteAreaID +
                '&WorkMonth=' +
                workMonth.toLocaleDateString('en-US');
            instance
                .get(url)
                .then((response) => {
                    setAgreements(
                        response.data.map((a: any) => ({
                            text: a.name,
                            value: a.id
                        }))
                    );
                })
                .catch((e) => {
                    setError('Error occurred while fetching agreements');
                    setAgreements([]);
                });
        }
    }, [selectedSiteAreaID, workMonth]);

    const runReport = () => {
        if (selectedSiteAreaID === null) {
            setError('You must select a site area before running the report');
        } else if (workMonth === null) {
            setError('You must select a work month before running the report');
        } else if (selectedAgreements.length === 0) {
            setError('You must select at least one agreement before running the report');
        } else {
            setLoadingReport(true);
            setError(null);
            const agreementIDs = selectedAgreements.map((a: any) => a.value);
            instance
                .post('/api/RateTableDataExtraction', {
                    workMonth: workMonth,
                    agreementIDs: agreementIDs
                })
                .then((response) => {
                    setRateTableDataExtractResults(response.data);
                    setLoadingReport(false);
                })
                .catch((e) => {
                    setError('Error occurred while generating report');
                    setLoadingReport(false);
                });
        }
    };

    const handleSiteAreaChange = (e: any) => {
        // Check if e.value is an object (with value property) or a direct value
        if (e.value && typeof e.value === 'object' && e.value.value !== undefined) {
            // When item is selected from dropdown (e.value is the complete object)
            setSelectedSiteAreaID(e.value.value);
        } else if (e.value !== undefined) {
            // When value is directly provided
            setSelectedSiteAreaID(e.value);
        } else {
            setSelectedSiteAreaID(null);
        }
    };

    const handleWorkMonthChange = (e: any) => {
        setWorkMonth(e.value);
    };

    return (
        <>
            <PageHeading title="Rate Table Data Extract" />
            {error !== null ? (
                <p
                    style={{
                        color: 'red',
                        fontWeight: 'bold',
                        marginTop: '6px',
                        fontSize: '14px'
                    }}>
                    {error}
                </p>
            ) : null}
            <div className="rate-table-data-extract-container">
                <table>
                    <tbody>
                        <tr>
                            <td>
                                <h4>Site Area</h4>
                                <FilterableComboBox
                                    data={siteAreas}
                                    selectedValue={selectedSiteAreaID}
                                    width="300px"
                                    height="37px"
                                    onChange={handleSiteAreaChange}
                                />
                            </td>
                            <td>
                                <h4>Work Month</h4>
                                <MonthPicker value={workMonth} onChange={handleWorkMonthChange} />
                            </td>
                            <td>
                                <h4>Agreements</h4>
                                <MultiSelectCheckbox
                                    data={agreements}
                                    selectedData={selectedAgreements}
                                    setSelectedData={setSelectedAgreements}
                                    style={{ width: '300px' }}
                                />
                            </td>
                        </tr>
                    </tbody>
                </table>
                <table style={{ marginBottom: '12px', marginTop: '12px' }}>
                    <tbody>
                        <tr>
                            <td>
                                <Button onClick={runReport}>
                                    Run Report {loadingReport ? <Loader type="infinite-spinner" size="small" themeColor="light" /> : null}
                                </Button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <EPRReportGrid
                data={rateTableDataExtractResults}
                dataState={dataState}
                setDataState={setDataState}
                fileName={'RateTableDataExtract.xlsx'}>
                <EPRReportColumn field="agreement" title="Agreement" />
                <EPRReportColumn field="classification" title="Classification" />
                <EPRReportColumn field="subClassification" title="Sub-Classification" />
                <EPRReportColumn field="benefit" title="Benefit" />
                <EPRReportColumn field="value" title="Value" />
                <EPRReportColumn field="calculation" title="Calculation" />
                <EPRReportColumn field="calculationModifier" title="Calculation Modifier" />
                <EPRReportColumn field="minimum" title="Minimum" />
                <EPRReportColumn field="maximum" title="Maximum" />
            </EPRReportGrid>
        </>
    );
};

export default function RateTableDataExtractionPage() {
    return (
        <AppLayout>
            <RateTableDataExtractionReport />
        </AppLayout>
    );
}
