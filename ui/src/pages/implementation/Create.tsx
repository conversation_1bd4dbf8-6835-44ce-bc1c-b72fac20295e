import { useState } from 'react';
import ChapterProxy<PERSON>ombobox, { ProxyComboboxChapterQuery } from '@/src/components/ProxyComboboxes/ChapterProxyCombobox';
import { PreloadedQuery } from 'react-relay/hooks';
import { Constants } from '@/src/constants/global';
import { ClientUtils } from "@/lib/core/ClientUtils";
import { graphql } from 'relay-runtime';
import { useMutation, ConnectionHandler } from 'react-relay';
import { useStore, StoreState } from '@/lib';
import {
    Flex,
    Text,
    Form,
    ComboBox,
    Item,
    TextField,
    Button,
    Heading,
    Content,
    ButtonGroup,
    DialogContainer,
    Dialog,
    Divider
} from '@adobe/react-spectrum';
import Add from '@spectrum-icons/workflow/Add';
import { ActionButton } from '@/src/components/UI/ActionButton/ActionButton';

const CreateChapterMutation = graphql`
    mutation CreateChapterMutation($input: AddChapterInput!, $connections: [ID!]!) {
        addChapter(chapterInput: $input) {
            chaptersInfoDtoEdge @appendEdge(connections: $connections) {
                cursor
                node {
                    value
                    label
                    guid
                }
            }
        }
    }
`;

const CreateFundAdminMutation = graphql`
    mutation CreateFundAdminMutation($input: AddFundAdministratorInput!, $connections: [ID!]!) {
        addFundAdministrator(input: $input) {
            thirdPartyInfoDtoEdge @appendEdge(connections: $connections) {
                cursor
                node {
                    value
                    label
                    guid
                }
            }
        }
    }
`;

const CreateUnionMutation = graphql`
    mutation CreateUnionMutation($input: AddUnionInput!, $connections: [ID!]!) {
        addUnion(input: $input) {
            thirdPartyInfoDtoEdge @appendEdge(connections: $connections) {
                cursor
                node {
                    value
                    label
                    guid
                }
            }
        }
    }
`;

interface SiteData {
    id: string;
    name: string;
}

interface CreateProps {
    setShowErrorDialog: (show: boolean) => void;
    onSuccess: (message: string) => void;
    chapterQueryRef: any;
    onOrganizationCreated: () => void; // Callback after FundAdmin/Union creation
    onSiteCreated: () => void; // Add prop for callback after Site/Limited creation
}

// Define types for the dialog state
type DialogType = 'confirm' | 'duplicate' | 'emptyInput' | null;

const Create = ({ setShowErrorDialog, onSuccess, chapterQueryRef, onOrganizationCreated, onSiteCreated }: CreateProps) => {
    const [newOrganizationName, setNewOrganizationName] = useState<string>('');
    const [organizationTypeId, setOrganizationTypeId] = useState<string>(Constants.OrganizationTypes.Site);
    const [organizationType, setOrganizationType] = useState<string>(Constants.OrganizationTypes.Site);
    const [duplicateMessage, setDuplicateMessage] = useState('');
    const [errorMessage, setErrorMessage] = useState('');
    const [selectedSiteId, setSelectedSiteId] = useState<number | null>(null);
    const [selectedSiteName, setSelectedSiteName] = useState<string | null>(null);
    const [siteFilterText, setSiteFilterText] = useState('');

    // New state for managing which dialog is open
    const [activeDialog, setActiveDialog] = useState<DialogType>(null);

    const selectedChapterGuid = useStore((state: StoreState) => state.selectedChapterGuid);
    const setSelectedChapterGuid = useStore((state: StoreState) => state.setSelectedChapterGuid);

    const proxyChaptersConnectionId = useStore((state: StoreState) => state.proxyChaptersConnectionId);
    const proxyThirdPartiesConnectionId = useStore((state: StoreState) => state.proxyThirdPartiesConnectionId);
    const proxyEmployersConnectionId = useStore((state: StoreState) => state.proxyEmployersConnectionId);

    const [commitChapterMutation, chapterMutationInFlight] = useMutation(CreateChapterMutation);
    const [commitFundAdminMutation, fundAdminMutationInFlight] = useMutation(CreateFundAdminMutation);
    const [commitUnionMutation, unionMutationInFlight] = useMutation(CreateUnionMutation);

    const organizationTypes = [
        { id: Constants.OrganizationTypes.Site, name: Constants.OrganizationTypes.Site },
        {
            id: Constants.OrganizationTypes.FundAdministrator,
            name: Constants.OrganizationTypes.FundAdministrator
        },
        { id: Constants.OrganizationTypes.Union, name: Constants.OrganizationTypes.Union },
        { id: Constants.OrganizationTypes.Limited, name: Constants.OrganizationTypes.Limited }
    ];

    function handleOrganizationTypeChange(selectedId: React.Key | null) {
        const selectedIdStr = selectedId as string;
        if (selectedIdStr === null) return;

        const selectedTypeObj = organizationTypes.find(t => t.id === selectedIdStr);
        const newType = selectedTypeObj ? selectedTypeObj.name : '';
        setOrganizationTypeId(selectedIdStr);
        setOrganizationType(newType);
        setSiteFilterText(''); // Reset filter text when type changes

        if (newType !== Constants.OrganizationTypes.FundAdministrator && newType !== Constants.OrganizationTypes.Union) {
            setSelectedSiteId(null);
            setSelectedSiteName(null);
        }
    }

    function handleNewOrganizationChange(value: string) {
        setNewOrganizationName(value);
    }

    const handleCreate = () => {
        setActiveDialog(null); // Close dialog before mutation
        const CreateSuccessMessage = `You have succesfully created an organization named ${newOrganizationName}`;

        const chapterIdForMutation = (organizationType === Constants.OrganizationTypes.FundAdministrator || organizationType === Constants.OrganizationTypes.Union)
            ? selectedSiteId
            : null;

        if (
            organizationType === Constants.OrganizationTypes.Site ||
            organizationType === Constants.OrganizationTypes.Limited
        ) {
            const duplicateChapterMessage = `A Site Sponsor with the name ${newOrganizationName} has already been created`;
            const limited = organizationType === Constants.OrganizationTypes.Limited;
            commitChapterMutation({
                variables: {
                    input: {
                        name: newOrganizationName,
                        limited: limited,
                        username: ClientUtils.getUsername()
                    },
                    connections: [ConnectionHandler.getConnectionID(proxyChaptersConnectionId, 'ChapterProxyComboboxFragment_chapters')]
                },
                onCompleted(response: any) {
                    setSelectedChapterGuid(
                        response.addChapter.chaptersInfoDtoEdge.node.value,
                        response.addChapter.chaptersInfoDtoEdge.node.id,
                        response.addChapter.chaptersInfoDtoEdge.node.value
                    );
                    onSuccess(CreateSuccessMessage);
                    onSiteCreated(); // Call the callback to refetch chapters
                },
                onError(error: any) {
                    if (error.cause?.[0]?.extensions?.message === 'Chapter already exists') {
                        setDuplicateMessage(duplicateChapterMessage);
                        setActiveDialog('duplicate'); // Show duplicate dialog
                    } else {
                        setErrorMessage('An unexpected error occurred while creating the chapter.');
                        setShowErrorDialog(true); // Use parent error dialog for unexpected
                    }
                }
            });
        } else if (organizationType === Constants.OrganizationTypes.FundAdministrator) {
            const duplicateFundAdminMessage = `A Fund Administrator with the name ${newOrganizationName} has already been created`;
            commitFundAdminMutation({
                variables: {
                    input: {
                        input: {
                            name: newOrganizationName,
                            chapterId: chapterIdForMutation,
                            username: ClientUtils.getUsername()
                        }
                    },
                    connections: [
                        ConnectionHandler.getConnectionID(proxyThirdPartiesConnectionId, 'ThirdPartyProxyComboboxFragment_thirdParties', {
                            chapterId: chapterIdForMutation
                        }),
                        ConnectionHandler.getConnectionID(proxyEmployersConnectionId, 'EmployerProxyComboboxFragment_employers', {
                            chapterId: chapterIdForMutation
                        })
                    ]
                },
                onCompleted() {
                    onSuccess(CreateSuccessMessage);
                    onOrganizationCreated(); // Call the callback to refetch orgs
                },
                onError(error: any) {
                    if (error.cause?.[0]?.extensions?.message === 'Fund Administrator already exists') {
                        setDuplicateMessage(duplicateFundAdminMessage);
                        setActiveDialog('duplicate'); // Show duplicate dialog
                    } else {
                        setErrorMessage('An unexpected error occurred while creating the fund administrator.');
                        setShowErrorDialog(true);
                    }
                }
            });
        } else if (organizationType === Constants.OrganizationTypes.Union) {
            const duplicateUnionMessage = `A Union with the name ${newOrganizationName} has already been created`;
            commitUnionMutation({
                variables: {
                    input: {
                        input: {
                            name: newOrganizationName,
                            chapterId: chapterIdForMutation,
                            username: ClientUtils.getUsername()
                        }
                    },
                    connections: [
                        ConnectionHandler.getConnectionID(proxyThirdPartiesConnectionId, 'ThirdPartyProxyComboboxFragment_thirdParties', {
                            chapterId: chapterIdForMutation
                        }),
                        ConnectionHandler.getConnectionID(proxyEmployersConnectionId, 'EmployerProxyComboboxFragment_employers', {
                            chapterId: chapterIdForMutation
                        })
                    ]
                },
                onCompleted() {
                    onSuccess(CreateSuccessMessage);
                    onOrganizationCreated(); // Call the callback to refetch orgs
                },
                onError(error: any) {
                    if (error.cause?.[0]?.extensions?.message === 'Union already exists') {
                        setDuplicateMessage(duplicateUnionMessage);
                        setActiveDialog('duplicate'); // Show duplicate dialog
                    } else {
                        setErrorMessage('An unexpected error occurred while creating the union.');
                        setShowErrorDialog(true);
                    }
                }
            });
        }

        setNewOrganizationName('');
        setOrganizationTypeId(Constants.OrganizationTypes.Site);
        setOrganizationType(Constants.OrganizationTypes.Site);
        setSelectedSiteId(null);
        setSelectedSiteName(null);
        setSiteFilterText(''); // Reset filter text after creation
    };

    const handleAddClick = () => {
        if (newOrganizationName.trim() === '' || !organizationTypeId) {
            setErrorMessage("You must specify the organization's type and name.");
            setActiveDialog('emptyInput'); // Show empty input dialog
            return;
        }
        if (
            (organizationType === Constants.OrganizationTypes.FundAdministrator ||
                organizationType === Constants.OrganizationTypes.Union) &&
            !selectedSiteId
        ) {
            setErrorMessage('You must select a Site Sponsor when creating a Fund Administrator or Union.');
            setActiveDialog('emptyInput'); // Show empty input dialog
            return;
        }
        setErrorMessage('');
        setActiveDialog('confirm'); // Show confirmation dialog
    };

    // Close handler for DialogContainer
    const handleDialogClose = () => {
        setActiveDialog(null);
        setErrorMessage(''); // Clear message on any close
        setDuplicateMessage(''); // Clear message on any close
    };

    const isLoading = chapterMutationInFlight || fundAdminMutationInFlight || unionMutationInFlight;

    const isAddDisabled =
        newOrganizationName.trim() === '' ||
        (
            (organizationType === Constants.OrganizationTypes.FundAdministrator ||
                organizationType === Constants.OrganizationTypes.Union) &&
            !selectedSiteId
        );

    // Filtering logic removed; replaced by ChapterProxyCombobox

    // Conditionally create the Site Sponsor ComboBox element
    let siteComboBoxElement: React.ReactElement | null = null;
    if (organizationType === Constants.OrganizationTypes.FundAdministrator ||
        organizationType === Constants.OrganizationTypes.Union) {
        siteComboBoxElement = (
            <ChapterProxyCombobox
                queryRef={chapterQueryRef}
                currentValue={selectedSiteId?.toString() || null}
                onChange={(selected) => {
                    if (selected) {
                        setSelectedSiteId(selected.guid);
                        setSelectedSiteName(selected.label);
                    } else {
                        setSelectedSiteId(null);
                        setSelectedSiteName(null);
                    }
                }}
            />
        );
    }

    return (
        <div className="CreateForImplementation">
            <Heading level={2} marginBottom="size-200">Add Organization</Heading>
            <Text>
            <strong>Use this section to add non-employer organizations</strong> that will need access to the administrative responsibilities within EPRLive. <br/>
            These include <strong>Unions, Fund Administrators, Sites, and Limited Sites</strong>.
            <br/>
            <br/>
            <strong>Only System Administrators have permission to create these non-employer organizations.</strong>
            </Text>
            <Form marginTop="size-200" width="auto" validationBehavior="native">
                <Flex direction="row" gap="size-300" alignItems="start" marginTop="size-200">
                    <ComboBox<SiteData>
                        label={<strong style={{ marginRight: '6px' }}>Organization type:</strong>}
                        labelPosition="side"
                        labelAlign="start"
                        items={organizationTypes}
                        selectedKey={organizationTypeId}
                        onSelectionChange={handleOrganizationTypeChange}
                        width="size-4600"
                        isDisabled={isLoading}
                    >
                        {(item) => <Item key={item.id}>{item.name}</Item>}
                    </ComboBox>

                    {(organizationType === Constants.OrganizationTypes.FundAdministrator || organizationType === Constants.OrganizationTypes.Union) && (
                        <Flex direction="row" alignItems="center" width="size-4600">
                            <Flex direction="column" flex={1}>
                                <ChapterProxyCombobox
                                    label={<strong>Site Sponsor:</strong>}
                                    labelPosition="side"
                                    labelAlign="start"
                                    queryRef={chapterQueryRef}
                                    currentValue={selectedSiteId?.toString() || null}
                                    onChange={(selected) => {
                                        if (selected) {
                                            setSelectedSiteId(selected.value);
                                        } else {
                                            setSelectedSiteId(null);
                                        }
                                    }}
                                    title=""
                                />
                            </Flex>
                        </Flex>
                    )}
                </Flex>

                <Flex direction="row" gap="size-300" alignItems="start" marginTop="size-200">
                    <TextField
                        label={<strong>Organization name:</strong>}
                        labelPosition="side"
                        labelAlign="start"
                        value={newOrganizationName}
                        onChange={handleNewOrganizationChange}
                        width="size-4600"
                        minWidth="size-4600"
                        isDisabled={isLoading}
                    />
                </Flex>
            </Form>

            <ActionButton
                wrapperProps={{
                    width: 'size-2000'
                }}
                isDisabled={isAddDisabled || isLoading}
                onPress={!isLoading ? handleAddClick : () => { }}
            >
                <Flex alignItems="center" justifyContent="center" gap="size-100">
                    <Add size="S" />
                    <Text>Add Organization</Text>
                </Flex>
            </ActionButton>

            <DialogContainer onDismiss={handleDialogClose}>
                {activeDialog === 'emptyInput' && (
                    <Dialog>
                        <Heading>Input Required</Heading>
                        <Divider />
                        <Content><Text>{errorMessage}</Text></Content>
                        <ButtonGroup>
                            <Button variant="primary" onPress={handleDialogClose} autoFocus>Ok</Button>
                        </ButtonGroup>
                    </Dialog>
                )}
                {activeDialog === 'confirm' && (
                    <Dialog>
                        <Heading>Creating new organization</Heading>
                        <Divider />
                        <Content>
                            <Text>
                                {`Are you sure you want to create ${organizationType} named ${newOrganizationName}`}
                                {(organizationType === Constants.OrganizationTypes.FundAdministrator ||
                                    organizationType === Constants.OrganizationTypes.Union) && selectedSiteName
                                    ? ` and link it to Site Sponsor ${selectedSiteName}`
                                    : ''}
                                ?
                            </Text>
                        </Content>
                        <ButtonGroup>
                            <Button variant="secondary" onPress={handleDialogClose}>No</Button>
                            <Button variant="primary" onPress={handleCreate} isDisabled={isLoading} autoFocus>Yes</Button>
                        </ButtonGroup>
                    </Dialog>
                )}
                {activeDialog === 'duplicate' && (
                    <Dialog>
                        <Heading>Unable to create organization</Heading>
                        <Divider />
                        <Content><Text>{duplicateMessage}</Text></Content>
                        <ButtonGroup>
                            <Button variant="primary" onPress={handleDialogClose} autoFocus>Ok</Button>
                        </ButtonGroup>
                    </Dialog>
                )}
            </DialogContainer>
        </div>
    );
};

export default Create;
