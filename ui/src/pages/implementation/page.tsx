import { useState, useEffect, useCallback } from 'react';
import { Dialog } from '@progress/kendo-react-dialogs';
import Create from './Create';
import Link from './Link';
import { Heading, Button, ButtonGroup, Content, Text, Flex } from '@adobe/react-spectrum';
import { useQueryLoader } from 'react-relay/hooks';
import { ProxyComboboxChapterQuery } from '@/src/components/ProxyComboboxes/ChapterProxyCombobox';
import type { ChapterProxyComboboxQuery as ChapterProxyComboboxQueryType } from '@/lib/relay/__generated__/ChapterProxyComboboxQuery.graphql'; // Import the query type
import { ClientUtils } from "@/lib/core/ClientUtils";
import { instance } from '@/src/core/http/axios-config'; // Import Axios instance
import { ProxyComboboxOption } from '@/src/components/ProxyComboboxes/types'; // Import common option type

// Define the structure for Site Data (matching Create.tsx)
interface SiteData {
    id: string; // Corresponds to Chapter GUID
    name: string; // Corresponds to Chapter Label/Name
}

// Define the expected structure from the /api/Organizations/all endpoint
interface BackendOrgOption extends ProxyComboboxOption {
    isLinked: boolean;
}

// Remove unused ChapterRosterItem interface if not needed
// interface ChapterRosterItem { ... }

const ClientImplementation = () => {
    const [showErrorDialog, setShowErrorDialog] = useState(false);
    const [successMessage, setSuccessMessage] = useState('');
    const [showSuccessDialog, setShowSuccessDialog] = useState(false);
    // Update state type to BackendOrgOption[]
    const [chapterOrganizations, setChapterOrganizations] = useState<BackendOrgOption[]>([]);
    const [isChapterOrgsLoading, setIsChapterOrgsLoading] = useState<boolean>(false);
    const [chapterRefetchKey, setChapterRefetchKey] = useState(0); // Key to force re-render

    const [chapterQueryRef, loadChapterQuery, disposeChapterQuery] = useQueryLoader<ChapterProxyComboboxQueryType>(ProxyComboboxChapterQuery); // Get dispose function

    // Load the chapters query on mount
    useState(() => {
        loadChapterQuery({});
    });

    // Function to fetch organizations for a specific chapter
    const fetchOrgsForChapter = useCallback(async (chapterId: number | null) => {
        // If chapterId is null (e.g., deselected), clear the list and stop loading
        if (chapterId === null) {
            setChapterOrganizations([]);
            setIsChapterOrgsLoading(false);
            return;
        }

        setIsChapterOrgsLoading(true);
        setChapterOrganizations([]); // Clear previous results immediately
        try {
            // Fetch organizations, expecting BackendOrgOption[]
            const response = await instance.get<BackendOrgOption[]>(`/api/Organizations/all?chapterId=${chapterId}`);
            setChapterOrganizations(response.data ?? []);
        } catch (error) {
            console.error(`Failed to fetch organizations for chapter ${chapterId}:`, error);
            setShowErrorDialog(true); // Show generic error dialog
            setChapterOrganizations([]); // Clear list on error
        } finally {
            setIsChapterOrgsLoading(false);
        }
    }, [setShowErrorDialog]); // Include setShowErrorDialog in dependencies

    // Removed useEffect that fetched all orgs on mount

    // Function to refetch the chapter query
    const refetchChapters = useCallback(() => {
        // Dispose of the old query reference before loading a new one
        // This ensures the combobox gets the fresh data
        disposeChapterQuery(); // Dispose old query
        loadChapterQuery({}, { fetchPolicy: 'network-only' }); // Load new query
        setChapterRefetchKey(prev => prev + 1); // Increment key to force re-render downstream
    }, [loadChapterQuery, disposeChapterQuery]);

    // Handler for when a new Org (Union/FundAdmin) is created in Create.tsx
    // We might not need to do anything here now, as the Link component fetches
    // based on the selected chapter. If Create needs to update Link's view,
    // a more complex state management or callback might be needed.
    // For now, let's keep it simple.
    const handleOrganizationCreated = () => {
        // Optionally: Could clear chapterOrganizations if the new org might be relevant
        // setChapterOrganizations([]);
        // Or trigger a refetch if a chapter is currently selected in Link (needs state lifting)
    };


    const handleClose = () => {
        setShowErrorDialog(false);
        setShowSuccessDialog(false);
    };

    const handleSuccess = (message: string) => {
        setShowSuccessDialog(true);
        setSuccessMessage(message);
    };

    return (
        <div className="ClientImplementation">
            <div id="implementation-main-body">
                <Heading level={1} marginBottom="size-400">
                    Client Implementation
                </Heading>

                {chapterQueryRef && (
                    <>
                        <Create
                            setShowErrorDialog={setShowErrorDialog}
                           onSuccess={handleSuccess}
                           chapterQueryRef={chapterQueryRef}
                           onOrganizationCreated={handleOrganizationCreated} // Updated handler
                           onSiteCreated={refetchChapters} // Pass chapter refetch function
                       />
                       <Link
                           setShowErrorDialog={setShowErrorDialog}
                           onSuccess={handleSuccess}
                           chapterQueryRef={chapterQueryRef}
                           // Pass down chapter-specific state and fetch function
                           chapterOrganizations={chapterOrganizations}
                           isChapterOrgsLoading={isChapterOrgsLoading}
                           fetchOrgsForChapter={fetchOrgsForChapter}
                           key={`link-component-${chapterRefetchKey}`} // Add key prop
                        />
                    </>
                )}
            </div>

            {showErrorDialog && (
                <Dialog title="An Error Occurred" onClose={handleClose}>
                    <Content><Text>Something went wrong. Unable to process request.</Text></Content>
                    <ButtonGroup>
                        <Button variant="primary" onPress={handleClose}>Ok</Button>
                    </ButtonGroup>
                </Dialog>
            )}
            {showSuccessDialog && (
                <Dialog title="Success!" onClose={handleClose}>
                    <Content><Text>{successMessage}</Text></Content>
                    <ButtonGroup>
                        <Button variant="primary" onPress={handleClose}>Ok</Button>
                    </ButtonGroup>
                </Dialog>
            )}
        </div>
    );
};

export default ClientImplementation;
