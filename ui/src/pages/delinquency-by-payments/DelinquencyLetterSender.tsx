import { saveAs } from 'file-saver';
import parse from 'html-react-parser';
import { Constants } from '@/src/constants/global';
import { instance } from '@/src/core/http/axios-config';
import { ClientUtils } from "@/lib/core/ClientUtils";
import { useStore, StoreState } from '@/lib';
import { Fade } from '@progress/kendo-react-animation';
import { Button } from '@progress/kendo-react-buttons';
import { Dialog } from '@progress/kendo-react-dialogs';
import { useState, useEffect } from 'react';
import { Loader } from '@progress/kendo-react-indicators';
import { Checkbox, Input } from '@progress/kendo-react-inputs';
import FilterableComboBox from '@/src/components/kendo/FilterableComboBox';

interface Payment {
    workMonth: Date;
    agreement: string;
    benefit: string;
    amount: number;
    weekly: boolean;
    employer: string;
    employerGUID: string;
    employerEIN: string;
    address1?: string;
    address2?: string;
    city?: string;
    state?: string;
    zipCode?: string;
    reportID: string;
    benefitID: string;
    agreementID: string;
    employerID: string;
}

interface PaymentObject {
    reportPeriod: Date;
    agreement: string;
    benefit: string;
    amount: number;
    weekly: boolean;
}

interface PaymentsByEmployer {
    employer: string;
    payments: PaymentObject[];
}

interface Employer {
    text: string;
    value: string;
    address: string;
    ein: string;
}

interface EmployerContact {
    guid: string;
    employerEmail?: string;
    payrollContactEmail?: string;
    primaryContactEmail?: string;
}

interface Letter {
    body: string | null;
    employer: string;
}

interface LetterResult {
    id: number;
    employer: string;
    successful: boolean;
    hasInvalidEmail: boolean;
    hasInvalidPayrollContact: boolean;
    hasInvalidPrimaryContact: boolean;
}

interface Settings {
    sendToEmployer: boolean;
    sendToPayrollContact: boolean;
    sendToPrimaryContact: boolean;
    ccList: string;
    subject: string;
}

interface DelinquencyLetterSenderProps {
    content: string;
    payments: Payment[];
    settings: Settings;
    setSettings: (settings: Settings) => void;
    savedLogoSrc: string;
    isDisabled: boolean;
    setError: (error: string) => void;
}

const DelinquencyLetterSender = ({ content, payments, settings, setSettings, savedLogoSrc, isDisabled, setError }: DelinquencyLetterSenderProps) => {
    const [previewOpen, setPreviewOpen] = useState(false);
    const [confirmationOpen, setConfirmationOpen] = useState(false);
    const [resultsOpen, setResultsOpen] = useState(false);
    const [formattedContent, setFormattedContent] = useState('');
    const [paymentsByEmployer, setPaymentsByEmployer] = useState<PaymentsByEmployer[]>([]);
    const [employers, setEmployers] = useState<Employer[]>([]);
    const [employer, setEmployer] = useState<Employer | null>(null);
    const [employerContacts, setEmployerContacts] = useState<EmployerContact[]>([]);
    const [recipients, setRecipients] = useState<string[]>([]);
    const [ccList, setCCList] = useState<string[]>([]);
    const [letters, setLetters] = useState<Letter[]>([]);
    const [sendingLetters, setSendingLetters] = useState(false);
    const [lettersResults, setLettersResults] = useState<LetterResult[]>([]);

    const user = useStore((state: StoreState) => state.user);
    const orgGuid = user?.orgGUID || '';
    const orgName = user?.orgName || '';
    const selectedChapterGuid = useStore((state: StoreState) => state.selectedChapterGuid);
    const selectedThirdPartyGuid = useStore((state: StoreState) => state.selectedThirdPartyGuid);

    const getLetter = (emp: Employer): Letter | undefined => {
        if (!content) return undefined;
        const letter: Letter = {
            body: null,
            employer: emp.value
        };
        const relevantPayments = paymentsByEmployer.find(p => p.employer === emp.text);
        const tdStyle = 'min-width: 1em;border: 1px solid #ddd;padding: 6px 8px';
        let paymentsTable =
            '<table style="border-collapse: collapse; margin: 0; width: 100%; table-layout: fixed"><tbody><tr><td style="' +
            tdStyle +
            '">Report Period</td><td style="' +
            tdStyle +
            '">Agreement</td><td style="' +
            tdStyle +
            '">Benefit</td><td style="' +
            tdStyle +
            '">Amount</td></tr>';
        let paymentsTotal = 0;
        const formatter = new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        });
        if (relevantPayments) {
            relevantPayments.payments.forEach(p => {
                const formattedAmount = formatter.format(p.amount);
                const formattedDate = p.weekly
                    ? p.reportPeriod.toLocaleDateString('en-US')
                    : p.reportPeriod.getMonth() + 1 + '/' + p.reportPeriod.getFullYear();
                paymentsTable +=
                    '<tr><td style="' +
                    tdStyle +
                    '">' +
                    formattedDate +
                    '</td><td style="' +
                    tdStyle +
                    '">' +
                    p.agreement +
                    '</td><td style="' +
                    tdStyle +
                    '">' +
                    p.benefit +
                    '</td><td style="' +
                    tdStyle +
                    '">' +
                    formattedAmount +
                    '</td></tr>';

                paymentsTotal += p.amount;
            });
        }
        paymentsTable += '</tbody></table>';
        const companyName = emp.text;
        const companyAddress = emp.address;
        const ein = emp.ein;
        const logo = '<img src=' + savedLogoSrc + ' />';
        let formatted = content
            .replaceAll('{{Payments}}', paymentsTable)
            .replaceAll('{{Total}}', formatter.format(paymentsTotal))
            .replaceAll('{{CompanyName}}', companyName)
            .replaceAll('{{CompanyAddress}}', companyAddress)
            .replaceAll('{{CompanyLogo}}', logo)
            .replaceAll('{{FEIN}}', ein);
        formatted = '<div style="page-break-after: always; page-break-inside: avoid;">' + formatted;
        formatted = formatted + '</div>';
        letter.body = formatted;
        return letter;
    };

    useEffect(() => {
        const newPaymentsByEmployer: PaymentsByEmployer[] = [];
        const newEmployers: Employer[] = [];
        payments.forEach(p => {
            const paymentObject: PaymentObject = {
                reportPeriod: p.workMonth,
                agreement: p.agreement,
                benefit: p.benefit,
                amount: p.amount,
                weekly: p.weekly
            };
            let employerObject = newPaymentsByEmployer.find(n => n.employer === p.employer);
            if (!employerObject) {
                employerObject = {
                    employer: p.employer,
                    payments: [paymentObject]
                };
                newPaymentsByEmployer.push(employerObject);
                const employerAddress = formatEmployerAddress(p);
                newEmployers.push({
                    text: p.employer,
                    value: p.employerGUID,
                    address: employerAddress,
                    ein: p.employerEIN
                });
            } else {
                employerObject.payments.push(paymentObject);
            }
        });
        setPaymentsByEmployer(newPaymentsByEmployer);
        const employerGUIDs = employers.map(e => e.value);
        const newEmployerGUIDs = newEmployers.map(e => e.value);
        if (!ClientUtils.arrayContentsAreEqual(employerGUIDs, newEmployerGUIDs)) {
            setEmployers(newEmployers);
        }
        if (newEmployers.length > 0 && !employer) {
            setEmployer(newEmployers[0]);
        }
    }, [payments]); // eslint-disable-line react-hooks/exhaustive-deps

    useEffect(() => {
        if (employer !== null) {
            const letter = letters.find(l => l.employer === employer.value);
            if (letter && letter.body) {
                setFormattedContent(letter.body);
            }
        } else {
            setFormattedContent('');
        }
    }, [employer, letters]);

    const getEmployerContactInfo = () => {
        let callsFinished = 0;
        const newContactInfo: EmployerContact[] = [];
        if (employers.length === 0) {
            setEmployerContacts([]);
        }
        employers.forEach(e => {
            const contactInfo = employerContacts.filter(c => c.guid === e.value);
            if (contactInfo.length > 0) {
                newContactInfo.push(contactInfo[0]);
                callsFinished++;
                if (callsFinished === employers.length) {
                    setEmployerContacts(newContactInfo);
                }
            } else {
                instance
                    .get('/api/Employers/contacts?GUID=' + e.value)
                    .then((response) => {
                        const employerContactInfo: EmployerContact = {
                            guid: e.value,
                            ...response.data as Record<string, unknown>
                        };
                        newContactInfo.push(employerContactInfo);
                        callsFinished++;
                        if (callsFinished === employers.length) {
                            setEmployerContacts(newContactInfo);
                        }
                    });
            }
        });
    };

    useEffect(() => {
        getEmployerContactInfo();

        const selectedEmployer = employers.find(e => {
            return e.value === employer?.value;
        });
        if (!selectedEmployer) {
            if (employers.length > 0) {
                setEmployer(employers[0]);
            } else {
                setEmployer(null);
            }
        }
    }, [employers]); // eslint-disable-line react-hooks/exhaustive-deps

    useEffect(() => {
        const newLetters: Letter[] = [];
        employers.forEach(e => {
            const email = getLetter(e);
            if (email) {
                newLetters.push(email);
            }
        });
        setLetters(newLetters);
    }, [content, paymentsByEmployer]); // eslint-disable-line react-hooks/exhaustive-deps

    useEffect(() => {
        if (confirmationOpen) {
            const newCCList = settings.ccList ? settings.ccList.split(';') : [];
            setCCList(newCCList);

            const newRecipients: string[] = [];
            employerContacts.forEach(c => {
                if (settings.sendToEmployer && c.employerEmail) {
                    newRecipients.push(c.employerEmail);
                }
                if (settings.sendToPayrollContact && c.payrollContactEmail) {
                    newRecipients.push(c.payrollContactEmail);
                }
                if (settings.sendToPrimaryContact && c.primaryContactEmail) {
                    newRecipients.push(c.primaryContactEmail);
                }
            });
            setRecipients(newRecipients);
        } else {
            setCCList([]);
            setRecipients([]);
        }
    }, [confirmationOpen]); // eslint-disable-line react-hooks/exhaustive-deps

    const closeDialog = () => {
        setPreviewOpen(false);
        setConfirmationOpen(false);
        setResultsOpen(false);
    };

    const formatEmployerAddress = (payment: Payment): string => {
        let employerAddress = '';
        if (!payment.address1 && !payment.address2 && !payment.city && !payment.state && !payment.zipCode) {
            employerAddress = '<br />';
        } else {
            employerAddress = payment.address1 ? `<p style="margin: 0">${payment.address1}</p>` : '';
            employerAddress += payment.address2 ? `<p style="margin: 0">${payment.address2}</p>` : '';
            if (payment.city || payment.state || payment.zipCode) {
                employerAddress += '<p style="margin: 0">';
                if (payment.city) {
                    employerAddress += payment.city;
                    if (payment.state) employerAddress += ', ';
                    else employerAddress += ' ';
                }
                if (payment.state) {
                    employerAddress += payment.state + ' ';
                }
                if (payment.zipCode) {
                    employerAddress += payment.zipCode;
                }
                employerAddress += '</p>';
            }
        }
        employerAddress += '</p>';
        return employerAddress;
    };

    const openDelinquencyLetterPopup = () => {
        if (isDisabled) {
            setError('Please select at least one item from the grid before sending a delinquency letter.');
        } else {
            setPreviewOpen(true);
        }
    };

    const openConfirmationDialog = () => {
        saveSettings();
        setPreviewOpen(false);
        setConfirmationOpen(true);
    };

    const openResultsDialog = () => {
        setConfirmationOpen(false);
        setResultsOpen(true);
    };

    const handleEmployerChange = (event: { value: Employer | null }) => {
        if (event.value) {
            setEmployer(event.value);
        }
    };

    const previousEmployer = () => {
        if (!employer) return;
        const employerIndex = employers.findIndex(e => e.value === employer.value);
        const previousEmployerObj = employers[employerIndex - 1];
        setEmployer(previousEmployerObj);
    };

    const isPreviousButtonDisabled = (): boolean => {
        if (!employer) return true;
        const employerIndex = employers.findIndex(e => e.value === employer.value);
        return employerIndex === 0;
    };

    const nextEmployer = () => {
        if (!employer) return;
        const employerIndex = employers.findIndex(e => e.value === employer.value);
        const nextEmployerObj = employers[employerIndex + 1];
        setEmployer(nextEmployerObj);
    };

    const isNextButtonDisabled = (): boolean => {
        if (!employer) return true;
        const employerIndex = employers.findIndex(e => e.value === employer.value);
        return employerIndex === employers.length - 1;
    };

    const saveSettings = () => {
        const settingsBody: Record<string, unknown> = {
            SendToEmployer: settings.sendToEmployer,
            SendToPayrollContact: settings.sendToPayrollContact,
            SendToPrimaryContact: settings.sendToPrimaryContact,
            CCList: settings.ccList,
            username: ClientUtils.getUsername()
        };
        
        if (selectedChapterGuid) {
            settingsBody.OrgID = selectedChapterGuid;
        }
        
        instance.post('/api/Delinquencies/payments/settings', settingsBody);
    };

    const handleSendToEmployerChange = (event: { value: boolean }) => {
        updateSettings({ sendToEmployer: event.value });
    };

    const handleSendToPayrollContactChange = (event: { value: boolean }) => {
        updateSettings({ sendToPayrollContact: event.value });
    };

    const handleSendToPrimaryContactChange = (event: { value: boolean }) => {
        updateSettings({ sendToPrimaryContact: event.value });
    };

    const handleCCListChange = (event: { value: string }) => {
        updateSettings({ ccList: event.value });
    };

    const handleSubjectChange = (event: { value: string }) => {
        updateSettings({ subject: event.value });
    };

    const updateSettings = (newSettings: Partial<Settings>) => {
        const settingsCopy = JSON.parse(JSON.stringify(settings)) as Settings;
        Object.assign(settingsCopy, newSettings);
        setSettings(settingsCopy);
    };

    const confirm = () => {
        let callsFinished = 0;
        const results: LetterResult[] = [];
        const employersWithInvalidEmail: string[] = [];
        const employersWithInvalidPayrollContactEmail: string[] = [];
        const employersWithInvalidPrimaryContactEmail: string[] = [];
        setSendingLetters(true);
        letters.forEach(l => {
            const employerData = employers.find(e => e.value === l.employer);
            if (!employerData) return;
            const employerName = employerData.text;
            const contacts = employerContacts.find(c => c.guid === l.employer);
            if (!contacts) return;
            const toList: string[] = [];
            const ccListCopy = JSON.parse(JSON.stringify(ccList)) as string[];
            if (settings.sendToEmployer) {
                if (contacts.employerEmail && ClientUtils.validateEmail(contacts.employerEmail)) {
                    toList.push(contacts.employerEmail);
                } else {
                    employersWithInvalidEmail.push(employerName);
                }
            }
            if (settings.sendToPayrollContact) {
                if (contacts.payrollContactEmail && ClientUtils.validateEmail(contacts.payrollContactEmail)) {
                    toList.push(contacts.payrollContactEmail);
                } else {
                    employersWithInvalidPayrollContactEmail.push(employerName);
                }
            }
            if (settings.sendToPrimaryContact) {
                if (contacts.primaryContactEmail && ClientUtils.validateEmail(contacts.primaryContactEmail)) {
                    toList.push(contacts.primaryContactEmail);
                } else {
                    employersWithInvalidPrimaryContactEmail.push(employerName);
                }
            }
            if (process.env.REACT_APP_DELINQUENCIES_TEST_EMAILS_ONLY == 'true') {
                for (let i = toList.length - 1; i >= 0; i--) {
                    if (!toList[i] || !toList[i].includes('@corelliansoft.com')) {
                        toList.splice(i, 1);
                    }
                }
                for (let i = ccListCopy.length - 1; i >= 0; i--) {
                    if (!ccListCopy[i] || !ccListCopy[i].includes('@corelliansoft.com')) {
                        ccListCopy.splice(i, 1);
                    }
                }
            }

            instance
                .post(
                    '/api/Mail/send',
                    {
                        toEmail: toList,
                        ccList: ccListCopy,
                        subject: settings.subject,
                        body: l.body,
                        useAuth: false,
                        username: ClientUtils.getUsername()
                    }
                )
                .then(() => {
                    results.push({
                        id: callsFinished,
                        employer: employerName,
                        successful: true,
                        hasInvalidEmail: employersWithInvalidEmail.includes(employerName),
                        hasInvalidPayrollContact: employersWithInvalidPayrollContactEmail.includes(employerName),
                        hasInvalidPrimaryContact: employersWithInvalidPrimaryContactEmail.includes(employerName)
                    });
                    callsFinished++;
                    if (callsFinished === letters.length) {
                        handleAllLettersSent(results);
                    }

                    if (toList.length > 0) {
                        createComment(l, toList);
                    }
                })
                .catch(() => {
                    results.push({
                        id: callsFinished,
                        employer: employerName,
                        successful: false,
                        hasInvalidEmail: employersWithInvalidEmail.includes(employerName),
                        hasInvalidPayrollContact: employersWithInvalidPayrollContactEmail.includes(employerName),
                        hasInvalidPrimaryContact: employersWithInvalidPrimaryContactEmail.includes(employerName)
                    });
                    callsFinished++;
                    if (callsFinished === letters.length) {
                        handleAllLettersSent(results);
                    }
                });

            let notes = 'To List: ';
            toList.forEach(r => {
                notes += r + ', ';
            });
            notes = notes.substring(0, notes.length - 2);
            notes += '; CC List: ';
            ccListCopy.forEach(r => {
                notes += r + ', ';
            });
            notes = notes.substring(0, notes.length - 2);
            instance.post(
                '/api/LoggedEvents',
                {
                    EventDateTime: new Date(),
                    DEventTypeID: Constants.EventLogTypeIDs.DelinquencyLetterSent,
                    DEventSubTypeID: Constants.EventLogSubTypeIDs.SinglePointEvent,
                    UserName: ClientUtils.getUsername(),
                    Target: 'Delinquency letter sent for employer ' + employerName,
                    Notes: notes,
                    SessionID: null
                }
            );
        });
    };

    const handleAllLettersSent = (results: LetterResult[]) => {
        setLettersResults(results);
        openResultsDialog();
        setSendingLetters(false);
    };

    const confirmBack = () => {
        setConfirmationOpen(false);
        setPreviewOpen(true);
    };

    const downloadPDF = () => {
        const pdfLetters = letters.map(l => l.body).filter(body => body !== null);
        instance
            .post(
                '/api/Delinquencies/pdf',
                {
                    Letters: pdfLetters
                },
                {
                    responseType: 'arraybuffer'
                }
            )
            .then((res) => {
                const blob = new Blob([res.data as ArrayBuffer], { type: 'application/pdf' });
                saveAs(blob, 'Delinquency_Emails.pdf');
                const fundAdminGUID = selectedThirdPartyGuid || orgGuid;
                const fundAdminName = orgName;
                instance.post(
                    '/api/LoggedEvents',
                    {
                        EventDateTime: new Date(),
                        DEventTypeID: Constants.EventLogTypeIDs.DelinquencyLetterPdfDownloaded,
                        DEventSubTypeID: Constants.EventLogSubTypeIDs.SinglePointEvent,
                        UserName: ClientUtils.getUsername(),
                        Target: 'Fund Admin Name: ' + fundAdminName + ', ID: ' + fundAdminGUID,
                        Notes: '',
                        SessionID: null
                    }
                );
            });
    };

    const createComment = (letter: Letter, toList: string[]) => {
        const paymentsForLetter = payments.filter(p => p.employerGUID === letter.employer);
        let comment = 'Delinquency letter emailed to: ';
        toList.forEach(e => {
            comment += e + ', ';
        });
        comment = comment.substring(0, comment.length - 2);
        const orgGUID = selectedThirdPartyGuid || orgGuid;

        paymentsForLetter.forEach(p => {
            instance.post(
                '/api/Comments',
                {
                    reportID: p.reportID,
                    benefitID: p.benefitID,
                    username: ClientUtils.getUsername(),
                    comment: comment,
                    organizationID: orgGUID,
                    agreementID: p.agreementID,
                    employerID: p.employerID,
                    workMonth: p.workMonth
                }
            );
        });
    };

    const previewDialog = previewOpen ? (
        <Dialog title={'Preview Delinquency Letter'} width={800} height={800} onClose={closeDialog}>
            <div style={{ marginBottom: '12px' }}>
                <FilterableComboBox
                    data={employers}
                    selectedValue={!employer ? null : employer.value}
                    clearButton={false}
                    onChange={handleEmployerChange}
                    width="500px"
                />
            </div>
            <div style={{ marginBottom: '12px' }}>
                <Button onClick={previousEmployer} disabled={isPreviousButtonDisabled()}>
                    Previous
                </Button>
                <Button style={{ marginLeft: '12px' }} onClick={nextEmployer} disabled={isNextButtonDisabled()}>
                    Next
                </Button>
                <span style={{ marginLeft: '12px' }}>
                    Viewing letter {employer ? employers.findIndex(e => e.value === employer.value) + 1 : 0} out of {employers.length}
                </span>
            </div>
            <div
                style={{
                    padding: '8px',
                    border: '1px solid black',
                    height: '450px',
                    overflowX: 'auto'
                }}>
                {parse(formattedContent)}
            </div>
            <div style={{ marginTop: '8px', marginBottom: '16px' }}>
                <div style={{ float: 'right' }}>
                    <table>
                        <tbody>
                            <tr>
                                <td>
                                    <Input
                                        label="Subject"
                                        value={settings.subject}
                                        style={{ marginBottom: '18px' }}
                                        onChange={handleSubjectChange}
                                    />
                                </td>
                                <td style={{ width: '10px' }}></td>
                                <td>
                                    <Input label="CC" value={settings.ccList} onChange={handleCCListChange} />
                                    <h5
                                        style={{
                                            marginTop: '0',
                                            marginBottom: '0'
                                        }}>
                                        Ex: <EMAIL>;<EMAIL>
                                    </h5>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <Checkbox label="Send to Employer Email" value={settings.sendToEmployer} onChange={handleSendToEmployerChange} />
                <br />
                <Checkbox
                    label="Send to Payroll Contact"
                    value={settings.sendToPayrollContact}
                    onChange={handleSendToPayrollContactChange}
                />
                <br />
                <Checkbox
                    label="Send to Primary Contact"
                    value={settings.sendToPrimaryContact}
                    onChange={handleSendToPrimaryContactChange}
                />
            </div>
            <div
                style={{
                    float: 'right'
                }}>
                <Button onClick={openConfirmationDialog}>Send</Button>
                <Button onClick={closeDialog} style={{ marginLeft: '8px' }}>
                    Cancel
                </Button>
            </div>
            <Button onClick={downloadPDF}>Download PDF</Button>
        </Dialog>
    ) : null;

    const confirmationDialog = confirmationOpen ? (
        <Dialog title={'Confirm Recipients'} width={800} height={660} onClose={closeDialog}>
            <div>
                <p>
                    You are about to send {employers.length} {employers.length === 1 ? 'letter' : 'letters'} to the following recipients:
                </p>
                <table style={{ width: '100%' }}>
                    <tbody>
                        <tr>
                            <td style={{ width: '45%' }}>
                                <p>
                                    <b>To:</b>
                                </p>
                                <div
                                    style={{
                                        height: '400px',
                                        overflow: 'auto'
                                    }}>
                                    {recipients.map((value) => {
                                        return value && <p key={value}>{value}</p>;
                                    })}
                                </div>
                            </td>
                            <td style={{ width: '10%' }} />
                            <td style={{ width: '45%' }}>
                                <p>
                                    <b>Cc:</b>
                                </p>
                                <div
                                    style={{
                                        height: '400px',
                                        overflow: 'auto'
                                    }}>
                                    {ccList.map((value) => {
                                        return value && <p key={value}>{value}</p>;
                                    })}
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <div
                    style={{
                        position: 'absolute',
                        right: 0,
                        marginRight: '18px',
                        bottom: 0,
                        marginBottom: '40px'
                    }}>
                    <Button onClick={confirm} style={{ float: 'right', marginLeft: '12px' }}>
                        Confirm {sendingLetters ? <Loader type="infinite-spinner" size="small" themeColor="light" /> : null}
                    </Button>
                    <Button onClick={confirmBack} style={{ float: 'right' }}>
                        Back
                    </Button>
                </div>
                <Button onClick={downloadPDF}>Download PDF</Button>
            </div>
        </Dialog>
    ) : null;

    const resultsDialog = resultsOpen ? (
        <Dialog title={'Results'} width={800} height={600} onClose={closeDialog}>
            <div style={{ overflow: 'auto', height: '450px' }}>
                <h4>
                    {lettersResults.filter(r => r.successful === true).length} out of {letters.length} letters sent successfully.
                </h4>
                {lettersResults.filter(r => r.hasInvalidEmail === true).length > 0 && settings.sendToEmployer ? (
                    <>
                        <p>The following employers have a blank or invalid email address: </p>
                        <ul>
                            {lettersResults
                                .filter(r => r.hasInvalidEmail === true)
                                .map(r => (
                                    <li key={r.id}>{r.employer}, </li>
                                ))}
                        </ul>
                    </>
                ) : null}
                {lettersResults.filter(r => r.hasInvalidPayrollContact === true).length > 0 && settings.sendToPayrollContact ? (
                    <>
                        <p>The following employers have a payroll contact with a blank or invalid email address: </p>
                        <ul>
                            {lettersResults
                                .filter(r => r.hasInvalidPayrollContact === true)
                                .map(r => (
                                    <li key={r.id}>{r.employer}</li>
                                ))}
                        </ul>
                    </>
                ) : null}
                {lettersResults.filter(r => r.hasInvalidPrimaryContact === true).length > 0 && settings.sendToPrimaryContact ? (
                    <>
                        <p>The following employers have a primary contact with a blank or invalid email address: </p>
                        <ul>
                            {lettersResults
                                .filter(r => r.hasInvalidPrimaryContact === true)
                                .map(r => (
                                    <li key={r.id}>{r.employer}, </li>
                                ))}
                        </ul>
                    </>
                ) : null}
            </div>
            <div
                style={{
                    position: 'absolute',
                    right: 0,
                    marginRight: '18px',
                    bottom: 0,
                    marginBottom: '20px'
                }}>
                <Button onClick={closeDialog} style={{ float: 'right' }}>
                    Close
                </Button>
            </div>
        </Dialog>
    ) : null;

    return (
        <>
            <Button onClick={openDelinquencyLetterPopup}>Send Delinquency Letter</Button>
            {previewDialog}
            {confirmationDialog}
            {resultsDialog}
        </>
    );
};

export default DelinquencyLetterSender;
