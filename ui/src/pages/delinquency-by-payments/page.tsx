import { But<PERSON> } from '@progress/kendo-react-buttons';
import { ComboBox } from '@progress/kendo-react-dropdowns';
import { getSelectedState } from '@progress/kendo-react-grid';
import { getter } from '@progress/kendo-react-common';
import { Checkbox } from '@progress/kendo-react-inputs';
import { useState, useEffect, useCallback } from 'react';
import { dateRanges } from './DateRanges';
import './DelinquencyByPayment.css';
import NumericNoSpinnerGridCell from '@/src/components/kendo/NumericNoSpinnerGridCell';
import { State } from '@progress/kendo-data-query';
import { Loader } from '@progress/kendo-react-indicators';
import DelinquencyLetterEditor from './DelinquencyLetterEditor';
import DelinquencyLetterSender from './DelinquencyLetterSender';
import { DatePicker } from '@progress/kendo-react-dateinputs';
import { Label } from '@progress/kendo-react-labels';
import EPRReportGrid from '@/src/components/kendo/EPRReportGrid';
import EPRReportColumn from '@/src/components/kendo/EPRReportColumn';
import LogoUploader from './LogoUploader';
import { Constants } from '@/src/constants/global';
import { ClientUtils } from "@/lib/core/ClientUtils";
import { useStore, StoreState } from '@/lib';
import { instance } from '@/src/core/http/axios-config';
import PageHeading from '@/src/components/UI/PageHeading';
import AppLayout from '@/src/components/Layout/AppLayout';

const DATA_ITEM_KEY = 'id';
const SELECTED_FIELD = 'selected';
const idGetter = getter(DATA_ITEM_KEY);

const STATIC_DELINQUENCY_COLUMNS = [
    'address1',
    'address2',
    'agreement',
    'city',
    'ein',
    'employer',
    'employerGUID',
    'fundedAmount',
    'id',
    'selected',
    'state',
    'submissionDate',
    'union',
    'weekly',
    'workMonth',
    'zipCode',
    'reportID',
    'benefitID',
    'agreementID',
    'employerID'
];

const initialDataState: State = {
    sort: [
        {
            field: 'employer',
            dir: 'desc'
        }
    ],
    filter: {
        filters: [],
        logic: 'and'
    }
};

const defaultLetterTemplate =
    '<p>{{CompanyAddress}}</p><p></p><p>Dear {{CompanyName}},</p><p>You are overdue on the following payments:</p><p>{{Payments}}</p><p><strong>Total: </strong>{{Total}}</p><p>Sincerely,</p><p>Corellian Software Inc.</p>';
const defaultSubject = 'EPRLive Delinquencies';

const DelinquencyByPayment = () => {
    // Grid Data
    const [delinquencies, setDelinquencies] = useState([]);
    const [pivotedDelinquencies, setPivotedDelinquencies] = useState([]);
    const [benefitColumns, setBenefitColumns] = useState([]);

    // Flags
    const [oneBenefitPerRow, setOneBenefitPerRow] = useState(true);
    const [includeCABenefits, setIncludeCABenefits] = useState(false);
    const [showDelinquencyLetters, setShowDelinquencyLetters] = useState(true);

    // Grid State
    const [selectedState, setSelectedState] = useState<any>({});
    const [dataState, setDataState] = useState(initialDataState);

    // UI Flags
    const [error, setError] = useState<any>(null);
    const [loadingDelinquencies, setLoadingDelinquencies] = useState(false);

    // Delinquency Letters
    const [letterContent, setLetterContent] = useState(defaultLetterTemplate);
    const [letterSettings, setLetterSettings] = useState<any>({
        sendToEmployer: false,
        sendToPayrollContact: false,
        sendToPrimaryContact: false,
        ccList: null,
        subject: defaultSubject
    });

    // Date Range
    const [startDate, setStartDate] = useState<any>(null);
    const [endDate, setEndDate] = useState<any>(null);
    const [dateRange, setDateRange] = useState<any>(null);

    // Org Logo
    const [savedLogoSrc, setSavedLogoSrc] = useState(null);

    const user = useStore((state: StoreState) => state.user);
    const orgGuid = user?.orgGUID || '';
    const email = user?.email || '';
    const orgName = user?.orgName || '';
    const selectedChapterGuid = useStore((state: StoreState) => state.selectedChapterGuid);
    const selectedThirdPartyGuid = useStore((state: StoreState) => state.selectedThirdPartyGuid);

    useEffect(() => {
        const cachedOneBenefitPerRow = ClientUtils.getCookie(Constants.Cookies.paymentDelinquencyOneBenefitPerRow);
        if (cachedOneBenefitPerRow !== null) {
            setOneBenefitPerRow(cachedOneBenefitPerRow === 'true' || cachedOneBenefitPerRow === 'True');
        }

        // Load logo
        instance
            .get('/api/Organizations/logo?username=' + ClientUtils.getUsername())
            .then((response: any) => {
                if (response.data && response.data.logoFileName) {
                    setSavedLogoSrc(response.data.logoFileName);
                }
            })
            .catch((error) => {
                // Silently handle logo loading errors
                console.error('Failed to load organization logo', error);
            });

        // Always show delinquency letters by default
        setShowDelinquencyLetters(true);
    }, []);

    useEffect(() => {
        if (
            !(user && (user.roles.includes(Constants.Roles.SystemAdministrator) || user.roles.includes(Constants.Roles.FundAdministrator)))
        ) {
            window.location.href = Constants.LANDING_PAGE_URL;
            return;
        }
    }, [user]);

    useEffect(() => {
        const benefitColumnNames: any = [];
        delinquencies.forEach((d: any) => {
            if (!benefitColumnNames.includes(d.benefit)) {
                benefitColumnNames.push(d.benefit);
            }
        });
        benefitColumnNames.sort();
        setBenefitColumns(benefitColumnNames);

        const delinquenciesClone = JSON.parse(JSON.stringify(delinquencies));
        delinquenciesClone.forEach((d: any) => {
            delete d.benefit;
            delete d.amount;
        });

        const pivotedDelinquenciesTable: any = [];
        for (let i = 0; i < delinquenciesClone.length; i++) {
            const delinquency: any = delinquencies[i];
            const delinquencyClone = delinquenciesClone[i];
            const filterResult = pivotedDelinquenciesTable.filter(
                (d: any) =>
                    d.employer === delinquencyClone.employer &&
                    d.ein === delinquencyClone.ein &&
                    d.agreement === delinquencyClone.agreement &&
                    d.union === delinquencyClone.union &&
                    d.workMonth === delinquencyClone.workMonth &&
                    d.submissionDate === delinquencyClone.submissionDate &&
                    d.fundedAmount === delinquencyClone.fundedAmount &&
                    d.address1 === delinquencyClone.address1 &&
                    d.address2 === delinquencyClone.address2 &&
                    d.city === delinquencyClone.city &&
                    d.state === delinquencyClone.state &&
                    d.zipCode === delinquencyClone.zipCode
            );

            if (filterResult.length === 0) {
                const pivotedDelinquency = JSON.parse(JSON.stringify(delinquencyClone));
                benefitColumnNames.forEach((b: any) => {
                    if (delinquency.benefit === b) {
                        pivotedDelinquency[b] = delinquency.amount - delinquency.fundedAmount;
                    } else {
                        pivotedDelinquency[b] = null;
                    }
                });
                pivotedDelinquenciesTable.push(pivotedDelinquency);
            } else {
                filterResult[0][delinquency.benefit] = delinquency.amount - delinquency.fundedAmount;
            }
        }
        let id = 0;
        pivotedDelinquenciesTable.forEach((d: any) => {
            d.workMonth = new Date(d.workMonth);
            d.submissionDate = new Date(d.submissionDate);
            d.id = id;
            d.selected = false;
            id++;
        });
        setPivotedDelinquencies(pivotedDelinquenciesTable);
    }, [delinquencies]);

    // Helper function to set default letter template and settings
    const useDefaultLetterTemplate = useCallback(() => {
        setLetterContent(defaultLetterTemplate);
        setLetterSettings({
            sendToEmployer: false,
            sendToPayrollContact: false,
            sendToPrimaryContact: false,
            ccList: email,
            subject: defaultSubject
        });
    }, [email]);

    const getDelinquencyLetter = useCallback(() => {
        if (!showDelinquencyLetters) return;

        // Try to get letter template, but use default if API fails
        let getURL = '/api/Delinquencies/payments/letter?username=' + ClientUtils.getUsername();
        if (selectedThirdPartyGuid) {
            getURL += '&GUID=' + selectedThirdPartyGuid;
        }

        instance
            .get(getURL)
            .then((response: any) => {
                if (response.data && response.data.text) {
                    setLetterContent(response.data.text);
                    const settings: any = {
                        sendToEmployer: response.data.sendToEmployer || false,
                        sendToPayrollContact: response.data.sendToPayrollContact || false,
                        sendToPrimaryContact: response.data.sendToPrimaryContact || false,
                        ccList: response.data.ccList || email,
                        subject: defaultSubject
                    };
                    setLetterSettings(settings);
                } else {
                    // Use defaults if response has no data
                    useDefaultLetterTemplate();
                }
            })
            .catch(() => {
                // Use defaults if API call fails
                useDefaultLetterTemplate();
            });
    }, [showDelinquencyLetters, email, selectedThirdPartyGuid, useDefaultLetterTemplate]);

    useEffect(() => {
        if (showDelinquencyLetters) {
            getDelinquencyLetter();
        }
    }, [showDelinquencyLetters, getDelinquencyLetter]);

    const getChapter = () => {
        // Set default value to always show delinquency letters instead of calling the API
        setShowDelinquencyLetters(true);
    };

    const runReport = () => {
        if (startDate === null) {
            setError('Please select a date range');
            return;
        } else if (loadingDelinquencies) {
            return;
        }
        setError(null);

        setLoadingDelinquencies(true);
        const fundAdminGUID = selectedThirdPartyGuid || orgGuid;
        instance
            .get('/api/Delinquencies/payments', {
                params: {
                    fundAdministratorGUID: fundAdminGUID,
                    includeSuppressedReports: false,
                    startDate: startDate.toLocaleDateString('en-US'),
                    endDate: endDate.toLocaleDateString('en-US'),
                    includeCABenefits: includeCABenefits
                }
            })
            .then((response: any) => {
                if (response && response.data) {
                    const delinquencies = response.data;
                    let id = 0;
                    delinquencies.forEach((d: any) => {
                        d.workMonth = new Date(d.workMonth);
                        d.submissionDate = new Date(d.submissionDate);
                        d.id = id;
                        d.selected = false;
                        id++;
                    });
                    setDelinquencies(delinquencies);
                }
                setLoadingDelinquencies(false);
            })
            .catch((err: any) => {
                if (err.response) {
                    // The request was made and the server responded with a status code
                    if (err.response.status === 408) {
                        setError('Report timed out, please use a shorter date range');
                    } else if (err.response.status === 404) {
                        setError('The requested data could not be found. Please verify your selections.');
                    } else if (err.response.data && err.response.data.message) {
                        setError(err.response.data.message);
                    } else {
                        setError('Something went wrong, please contact EPRLive support');
                    }
                } else if (err.request) {
                    // The request was made but no response was received
                    setError('No response received from server. Please check your connection.');
                } else {
                    // Something else happened in setting up the request
                    setError('Something went wrong, please contact EPRLive support');
                }
                setLoadingDelinquencies(false);
            });

        const fundAdminName = orgName;
        instance
            .post('/api/LoggedEvents', {
                EventDateTime: new Date(),
                DEventTypeID: Constants.EventLogTypeIDs.DelinquencyByPaymentReportRun,
                DEventSubTypeID: Constants.EventLogSubTypeIDs.SinglePointEvent,
                UserName: ClientUtils.getUsername(),
                Target: 'Fund Admin Name: ' + fundAdminName + ', ID: ' + fundAdminGUID,
                Notes: 'Start Date: ' + startDate.toLocaleDateString('en-US') + '; End Date: ' + endDate.toLocaleDateString('en-US'),
                SessionID: null
            })
            .catch((err) => {
                // Silently fail on logging errors
                console.error('Failed to log event:', err);
            });
    };

    const currentDataSet = () => {
        return oneBenefitPerRow ? delinquencies : pivotedDelinquencies;
    };

    const getSelectedPayments = () => {
        if (oneBenefitPerRow) {
            return delinquencies.filter((d: any) => {
                return selectedState[d.id] === true;
            });
        } else {
            const selectedBenefitRows: any = [];
            pivotedDelinquencies.forEach((d: any) => {
                if (selectedState[d.id] !== true) return;
                for (const prop in d) {
                    if (STATIC_DELINQUENCY_COLUMNS.includes(prop) || d[prop] === null) continue;

                    const delinquencyRow = {
                        address1: d.address1,
                        address2: d.address2,
                        agreement: d.agreement,
                        agreementID: d.agreementID,
                        amount: d[prop],
                        benefit: prop,
                        benefitID: d.benefitID,
                        city: d.city,
                        employer: d.employer,
                        employerEIN: d.ein,
                        employerGUID: d.employerGUID,
                        employerID: d.employerID,
                        reportID: d.reportID,
                        state: d.state,
                        weekly: d.weekly,
                        workMonth: d.workMonth,
                        zipCode: d.zipCode
                    };
                    selectedBenefitRows.push(delinquencyRow);
                }
            });
            return selectedBenefitRows;
        }
    };

    const handleOneBenefitPerRowChange = (event: any) => {
        setSelectedState({});
        ClientUtils.setCookie(
            Constants.Cookies.paymentDelinquencyOneBenefitPerRow,
            event.value.toString(),
            10000 // Cookie should not expire
        );
        setOneBenefitPerRow(event.value);
    };

    const handleIncludeCABenefitsChange = (event: any) => {
        setIncludeCABenefits(event.value);
    };

    const handleDateRangeChange = (event: any) => {
        if (event.value.startDate) {
            setStartDate(event.value.startDate());
        }
        if (event.value.endDate) {
            setEndDate(event.value.endDate());
        }
        setDateRange(event.value);
        setError(null);
    };

    const handleStartDateChange = (event: any) => {
        setStartDate(event.value);
        const customDateRange = dateRanges.filter((d) => d.label === 'Custom');
        setDateRange(customDateRange[0]);
    };

    const handleEndDateChange = (event: any) => {
        setEndDate(event.value);
    };

    const handleSelectionChange = useCallback(
        (event: any) => {
            const newSelectedState = getSelectedState({
                event,
                selectedState: selectedState,
                dataItemKey: DATA_ITEM_KEY
            });
            setSelectedState(newSelectedState);
        },
        [selectedState]
    );

    const handleHeaderSelectionChange = useCallback((event: any) => {
        const checkboxElement = event.syntheticEvent.target;
        const checked = checkboxElement.checked;
        const newSelectedState: any = {};
        event.dataItems.forEach((item: any) => {
            newSelectedState[idGetter(item)] = checked;
        });
        setSelectedState(newSelectedState);
    }, []);

    return (
        <>
            <PageHeading title="Payroll Reports by Payments Due" />
            <script
                dangerouslySetInnerHTML={{
                    __html: `
                !(function (e, t, n) {
                    function a() {
                        var e = t.getElementsByTagName('script')[0],
                            n = t.createElement('script')
                        ;(n.type = 'text/javascript'),
                            (n.async = !0),
                            (n.src = 'https://beacon-v2.helpscout.net'),
                            e.parentNode.insertBefore(n, e)
                    }
                    if (
                        ((e.Beacon = n =
                            function (t, n, a) {
                                e.Beacon.readyQueue.push({
                                    method: t,
                                    options: n,
                                    data: a,
                                })
                            }),
                        (n.readyQueue = []),
                        'complete' === t.readyState)
                    )
                        return a()
                    e.attachEvent
                        ? e.attachEvent('onload', a)
                        : e.addEventListener('load', a, !1)
                })(window, document, window.Beacon || function () {})
            `
                }}
            />
            <script
                dangerouslySetInnerHTML={{
                    __html: `
                        window.Beacon('init', 'fb1226ff-aa5b-4f0f-9fe7-14d3582e2d7e')
                    `
                }}
            />
            {error !== null ? (
                <p
                    style={{
                        color: 'red',
                        fontWeight: 'bold',
                        marginTop: '6px',
                        fontSize: '14px'
                    }}>
                    {error}
                </p>
            ) : null}
            <div style={{ marginBottom: '8px' }}>
                <Checkbox label="Report each benefit on its own row" value={oneBenefitPerRow} onChange={handleOneBenefitPerRowChange} />
            </div>
            <div style={{ marginBottom: '8px' }}>
                <Checkbox
                    label="Include benefits that I'm a Collecting Agent for"
                    value={includeCABenefits}
                    onChange={handleIncludeCABenefitsChange}
                />
            </div>
            <div style={{ marginBottom: '20px' }}>
                <table>
                    <tbody>
                        <tr>
                            <td>
                                <ComboBox
                                    data={dateRanges}
                                    value={dateRange}
                                    textField="label"
                                    groupField="group"
                                    label="Date Range"
                                    onChange={handleDateRangeChange}
                                    clearButton={false}
                                />
                            </td>
                            <td style={{ width: '12px' }}> </td>
                            <td style={{ verticalAlign: 'bottom' }}>
                                <Label editorId="startDatePaymentDelinquencies">Start Date: &nbsp;</Label>
                                <DatePicker id="startDatePaymentDelinquencies" value={startDate} onChange={handleStartDateChange} />
                            </td>
                            <td style={{ width: '12px' }}> </td>
                            <td style={{ verticalAlign: 'bottom' }}>
                                <Label editorId="endDatePaymentDelinquencies">End Date: &nbsp;</Label>
                                <DatePicker id="endDatePaymentDelinquencies" value={endDate} onChange={handleEndDateChange} />
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div>
                {showDelinquencyLetters === true && (
                    <table style={{ marginBottom: '12px' }}>
                        <tbody>
                            <tr>
                                <td style={{ paddingRight: '12px' }}>
                                    <DelinquencyLetterEditor content={letterContent} setContent={setLetterContent} />
                                </td>
                                <td style={{ paddingRight: '12px' }}>
                                    <DelinquencyLetterSender
                                        content={letterContent}
                                        payments={getSelectedPayments()}
                                        settings={letterSettings}
                                        setSettings={setLetterSettings}
                                        savedLogoSrc={savedLogoSrc || ''}
                                        isDisabled={
                                            Object.keys(selectedState).length === 0 || !Object.values(selectedState).some((v) => v === true)
                                        }
                                        setError={setError}
                                    />
                                </td>
                                <td>
                                    <LogoUploader savedLogoSrc={savedLogoSrc} setSavedLogoSrc={setSavedLogoSrc} />
                                </td>
                            </tr>
                        </tbody>
                    </table>
                )}
            </div>
            <table style={{ marginBottom: '12px' }}>
                <tbody>
                    <tr>
                        <td>
                            <Button onClick={runReport}>
                                Run Report{' '}
                                {loadingDelinquencies ? <Loader type="infinite-spinner" size="small" themeColor="light" /> : null}
                            </Button>
                        </td>
                    </tr>
                </tbody>
            </table>
            <EPRReportGrid
                style={{ marginBottom: '32px' }}
                data={currentDataSet().map((item: any) => ({
                    ...item,
                    [SELECTED_FIELD]: selectedState[idGetter(item)]
                }))}
                dataState={dataState}
                dataItemKey={DATA_ITEM_KEY}
                selectedField={SELECTED_FIELD}
                selectable={{
                    enabled: true,
                    drag: false,
                    cell: false,
                    mode: 'multiple'
                }}
                className="delinquencyByPaymentGrid"
                setDataState={setDataState}
                onSelectionChange={handleSelectionChange}
                onHeaderSelectionChange={handleHeaderSelectionChange}
                noRecordsText="Click the Run Report button.">
                {showDelinquencyLetters === true && (
                    <EPRReportColumn
                        field={SELECTED_FIELD}
                        width="50px"
                        excelExport={false}
                        filterable={false}
                        sortable={false}
                        headerSelectionValue={currentDataSet().findIndex((item) => !selectedState[idGetter(item)]) === -1}
                    />
                )}
                <EPRReportColumn field="employer" title="Employer" />
                <EPRReportColumn field="ein" title="EIN" width="130px" />
                <EPRReportColumn field="agreement" title="Agreement" width="150px" />
                <EPRReportColumn field="union" title="Union" />
                <EPRReportColumn field="workMonth" title="Report Period" format="{0:d}" filter="date" excelWidth={100} />
                <EPRReportColumn field="submissionDate" title="Submission Date" format="{0:d}" filter="date" excelWidth={100} />
                {oneBenefitPerRow ? <EPRReportColumn field="benefit" title="Benefit" /> : null}
                {oneBenefitPerRow ? (
                    <EPRReportColumn field="amount" title="Amount" cell={NumericNoSpinnerGridCell} format="c2" filter="numeric" />
                ) : null}
                <EPRReportColumn field="fundedAmount" title="Funded Amount" cell={NumericNoSpinnerGridCell} format="c2" filter="numeric" />
                <EPRReportColumn field="address1" title="Address1" />
                <EPRReportColumn field="address2" title="Address2" />
                <EPRReportColumn field="city" title="City" />
                <EPRReportColumn field="state" title="State" />
                <EPRReportColumn field="zipCode" title="Zip Code" />
                {!oneBenefitPerRow ? benefitColumns.map((b) => <EPRReportColumn key={b} field={b} title={b} filterable={false} />) : null}
            </EPRReportGrid>
            <div style={{ height: '32px' }}></div>
        </>
    );
};

export default function DelinquencyByPaymentsPage() {
    return (
        <AppLayout>
            <DelinquencyByPayment />
        </AppLayout>
    );
}
