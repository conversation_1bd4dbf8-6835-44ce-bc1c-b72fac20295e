import { useState } from 'react';
import { ClientUtils } from "@/lib/core/ClientUtils";
import { useStore, StoreState } from '@/lib';
import { Constants } from '@/src/constants/global';
import Editor from '@/src/components/UI/Editor/Editor';
import { But<PERSON> } from '@progress/kendo-react-buttons';
import { Dialog } from '@progress/kendo-react-dialogs';
import { Fade } from '@progress/kendo-react-animation';
import { instance } from '@/src/core/http/axios-config';
import { Loader } from '@progress/kendo-react-indicators';

const DelinquencyLetterEditor = ({ content, setContent }: any) => {
    const [open, setOpen] = useState(false);
    const [saving, setSaving] = useState(false);
    const [error, setError] = useState<any>(null);

    const user = useStore((state: StoreState) => state.user);
    const orgGuid = user?.orgGUID || '';
    const orgName = user?.orgName || '';
    const selectedThirdPartyGuid = useStore((state: StoreState) => state.selectedThirdPartyGuid);

    const toggleDialog = () => {
        setError(null);
        setOpen(!open);
    };

    const openDelinquencyLetterPopup = () => {
        setOpen(true);
    };

    const saveLetter = () => {
        if (saving) return;
        setSaving(true);
        const body: any = {
            Text: content,
            username: ClientUtils.getUsername()
        };
        
        if (selectedThirdPartyGuid) {
            body['guid'] = selectedThirdPartyGuid;
        }
        
        instance
            .post('/api/Delinquencies/payments/letter', body)
            .then((response: any) => {
                setSaving(false);
                if (response.status >= 200 && response.status < 300) {
                    toggleDialog();
                }
            })
            .catch(() => {
                setError('An unknown error occurred while saving your delinquency letter template');
                setSaving(false);
            });
        
        const fundAdminGUID = selectedThirdPartyGuid || orgGuid;
        const fundAdminName = orgName;
        
        instance.post(
            '/api/LoggedEvents',
            {
                EventDateTime: new Date(),
                DEventTypeID: Constants.EventLogTypeIDs.DelinquencyLetterTemplateChanged,
                DEventSubTypeID: Constants.EventLogSubTypeIDs.SinglePointEvent,
                UserName: ClientUtils.getUsername(),
                Target: 'Fund Admin Name: ' + fundAdminName + ', ID: ' + fundAdminGUID,
                Notes: '',
                SessionID: null
            },

        );
    };

    // Create a dialog that lets users know that the editor is not available yet.
    const dialog = open ? (
        <Dialog title={'Delinquency Letter'} width={800} height={650} onClose={toggleDialog}>
            <Editor content={content} setContent={setContent} />
            {error !== null ? <p style={{ color: 'red' }}>{error}</p> : null}
            <Button onClick={saveLetter} style={{ marginTop: '12px' }}>
                Save {saving ? <Loader type="infinite-spinner" size="small" themeColor="light" /> : null}
            </Button>
        </Dialog>
    ) : null;

    return (
        <>
            <Button onClick={openDelinquencyLetterPopup}>Edit Delinquency Letter</Button>
            {dialog}
        </>
    );
};

export default DelinquencyLetterEditor;
