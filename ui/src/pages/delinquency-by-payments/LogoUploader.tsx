import { useState, useEffect } from 'react';
import { Fade } from '@progress/kendo-react-animation';
import { Button } from '@progress/kendo-react-buttons';
import { Dialog } from '@progress/kendo-react-dialogs';
import { Loader } from '@progress/kendo-react-indicators';
import { instance } from '@/src/core/http/axios-config';
import { ClientUtils } from "@/lib/core/ClientUtils";

const MAX_LOGO_WIDTH = 600;
const MAX_LOGO_HEIGHT = 400;

const LogoUploader = ({ savedLogoSrc, setSavedLogoSrc }: any) => {
    const [open, setOpen] = useState(false);
    const [error, setError] = useState<any>(null);
    const [uploadingLogo, setUploadingLogo] = useState(false);
    const [imageSrc, setImageSrc] = useState(null);
    const [imageFile, setImageFile] = useState(null);

    const toggleDialog = () => {
        setError(null);
        savedLogoSrc ? setImageSrc(savedLogoSrc) : setImageSrc(null);
        setImageFile(null);
        setOpen(!open);
    };

    const showPreview = (e: any) => {
        if (e.target.files && e.target.files[0]) {
            setError(null);
            const imageFile = e.target.files[0];
            const reader = new FileReader();
            reader.onload = async (x: any) => {
                if (await validateImage(x)) {
                    setImageFile(imageFile);
                    setImageSrc(x.target.result);
                } else {
                    clearForm();
                }
            };
            reader.readAsDataURL(imageFile);
        } else {
            clearForm();
        }
    };

    const validateImage = (x: any) => {
        return new Promise<boolean>((resolve, reject) => {
            const image = new window.Image();
            image.src = x.target.result.toString();
            image.onload = () => {
                if (image.width > MAX_LOGO_WIDTH || image.height > MAX_LOGO_HEIGHT) {
                    setError('Image cannot be more than ' + MAX_LOGO_WIDTH + 'px width and ' + MAX_LOGO_HEIGHT + 'px height');
                    resolve(false);
                } else {
                    resolve(true);
                }
            };
        });
    };

    const uploadImage = () => {
        if (!imageFile || !imageSrc) {
            setError('Please choose a valid image file before uploading');
            return;
        }

        setUploadingLogo(true);
        setError(null);

        const formData = new FormData();
        formData.append('fileName', imageSrc);
        formData.append('formFile', imageFile);
        formData.append('username', ClientUtils.getUsername());

        instance
            .post('/api/Organizations/logo', formData)
            .then(() => {
                setUploadingLogo(false);
                setSavedLogoSrc(imageSrc);
            })
            .catch(() => {
                setUploadingLogo(false);
                setError('Something went wrong while uploading your image');
            });
    };

    const clearForm = () => {
        savedLogoSrc ? setImageSrc(savedLogoSrc) : setImageSrc(null);
        setImageFile(null);
        (document.getElementById('image-uploader') as HTMLInputElement).value = '';
    };

    useEffect(() => {
        if (imageSrc !== null) {
            const image = new window.Image();
            image.src = imageSrc;
            image.onload = () => {
                if (image.width > MAX_LOGO_WIDTH || image.height > MAX_LOGO_HEIGHT) {
                    setError('Image cannot be more than ' + MAX_LOGO_WIDTH + 'px width and ' + MAX_LOGO_HEIGHT + 'px height');
                }
            };
        }
    }, [imageSrc]);

    const dialog = open ? (
        <Dialog title={'Upload Organization Logo'} width={800} height={600} onClose={toggleDialog}>
            {error !== null ? (
                <p
                    style={{
                        color: 'red',
                        fontWeight: 'bold',
                        marginTop: '6px',
                        fontSize: '14px'
                    }}>
                    {error}
                </p>
            ) : null}

            {imageSrc !== null ? <img src={imageSrc} alt="Uploaded logo" width={MAX_LOGO_WIDTH} height={MAX_LOGO_HEIGHT} /> : null}

            <form>
                <input type="file" accept="image/*" onChange={showPreview} id="image-uploader" />
            </form>
            <br />

            <Button onClick={uploadImage}>
                Upload {uploadingLogo ? <Loader type="infinite-spinner" size="small" themeColor="light" /> : null}
            </Button>
        </Dialog>
    ) : null;

    return (
        <>
            <Button onClick={toggleDialog}>Upload Logo</Button>
            {dialog}
        </>
    );
};

export default LogoUploader;
