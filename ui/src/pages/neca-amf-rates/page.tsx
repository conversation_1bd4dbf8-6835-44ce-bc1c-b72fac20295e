import { Button } from '@progress/kendo-react-buttons';
import { Dialog } from '@progress/kendo-react-dialogs';
import { Grid, GridColumn as Column } from '@progress/kendo-react-grid';
import { useEffect, useState, useCallback } from 'react';
import { instance } from '@/src/core/http/axios-config';
import DuplicateDeleteCommandCell from '@/src/components/kendo/DuplicateDeleteCommandCell';
import { useStore, StoreState } from '@/lib';
import { Constants } from '@/src/constants/global';
import { ClientUtils } from "@/lib/core/ClientUtils";
import { Link, useNavigate } from 'react-router';
import AppLayout from '@/src/components/Layout/AppLayout';
import PageHeading from '@/src/components/UI/PageHeading';

const NecaAmfRatesRoster = () => {
    const navigate = useNavigate();
    const [rateSchedules, setRateSchedules] = useState([]);
    const [showDeleteRateScheduleDialog, setShowDeleteRateScheduleDialog] = useState(false);
    const [deletingRateScheduleId, setDeletingRateScheduleId] = useState(null);

    const user = useStore((state: StoreState) => state.user);
    const selectedChapterGuid = useStore((state: StoreState) => state.selectedChapterGuid);

    const getRateSchedules = useCallback(() => {
        let rateSchedulesUrl = '/api/NecaAmfRates?username=' + ClientUtils.getUsername();
        if (selectedChapterGuid !== null) {
            rateSchedulesUrl += '&GUID=' + selectedChapterGuid;
        }
        instance.get(rateSchedulesUrl).then((response) => {
            setRateSchedules(
                response.data.map((r: any) => ({
                    id: r.id,
                    name: r.name
                }))
            );
        });
    }, [selectedChapterGuid]);

    useEffect(() => {
        getRateSchedules();
    }, [getRateSchedules]);

    useEffect(() => {
        if (user && user.roles.includes(Constants.Roles.SystemAdministrator) !== true) {
            getRateSchedules();
        }
    }, [user, getRateSchedules]);

    useEffect(() => {
        if (selectedChapterGuid !== null) {
            getRateSchedules();
        }
    }, [selectedChapterGuid, getRateSchedules]);

    const handleRowClick = (e: any) => {
        navigate('/neca-amf-rates/detail?id=' + e.dataItem.id);
    };

    const remove = (dataItem: any) => {
        setDeletingRateScheduleId(dataItem.id);
        setShowDeleteRateScheduleDialog(true);
    };

    const getNextRateSchedule = (effectiveStartDate: any, duplicate: any, localRateSchedules: any = null) => {
        if (duplicate || rateSchedules.length === 0) {
            return null;
        }
        let nextRateScheduleId: any = null;
        let closestNextDate: any = null;
        if (localRateSchedules === null) {
            localRateSchedules = rateSchedules;
        }
        localRateSchedules.forEach((r: any) => {
            if (r.effectiveStartDate > effectiveStartDate && (closestNextDate === null || r.effectiveStartDate < closestNextDate)) {
                closestNextDate = new Date(r.effectiveStartDate);
                nextRateScheduleId = r.id;
            }
        });
        return nextRateScheduleId === null ? null : localRateSchedules.find((r: any) => r.id === nextRateScheduleId);
    };

    const updateRateScheduleDates = (callback: any, updatedRateSchedules: any = null) => {
        const rateSchedulesToUpdate: any = [];
        if (updatedRateSchedules === null) {
            updatedRateSchedules = rateSchedules;
        }
        updatedRateSchedules.forEach((r: any) => {
            const nextRateSchedule = getNextRateSchedule(r.effectiveStartDate, false, updatedRateSchedules);
            if (!nextRateSchedule && r.effectiveEndDate !== null) {
                r.effectiveEndDate = null;
                rateSchedulesToUpdate.push(r);
            } else if (nextRateSchedule) {
                const expectedEndDate = new Date(nextRateSchedule.effectiveStartDate);
                expectedEndDate.setDate(expectedEndDate.getDate() - 1);
                expectedEndDate.setHours(0, 0, 0, 0);
                const actualEndDate = new Date(r.effectiveEndDate);
                actualEndDate.setHours(0, 0, 0, 0);
                if (expectedEndDate.getTime() !== actualEndDate.getTime()) {
                    r.effectiveEndDate = expectedEndDate;
                    rateSchedulesToUpdate.push(r);
                }
            }
        });
        const rateSchedulesBody = rateSchedulesToUpdate.map((r: any) => ({
            rateScheduleId: r.id,
            startDate: r.effectiveStartDate,
            endDate: r.effectiveEndDate
        }));
        instance
            .post('/api/NecaAmfRates/rate-schedule-dates', {
                rateSchedules: rateSchedulesBody
            })
            .then(() => {
                callback();
            });
    };

    const deleteRateSchedule = () => {
        setShowDeleteRateScheduleDialog(false);
        instance.delete('/api/NecaAmfRates?RateScheduleID=' + deletingRateScheduleId).then((response) => {
            if (response.status >= 200 && response.status < 300) {
                const updatedRateSchedules = rateSchedules.filter((r: any) => r.id !== deletingRateScheduleId);
                updateRateScheduleDates(() => {
                    getRateSchedules();
                    setDeletingRateScheduleId(null);
                }, updatedRateSchedules);
            }
        });
    };

    const cancelDelete = () => {
        setDeletingRateScheduleId(null);
        setShowDeleteRateScheduleDialog(false);
    };

    const duplicate = (dataItem: any) => {
        navigate('/neca-amf-rates/detail?id=' + dataItem.id + '&duplicate=true');
    };

    const goBack = () => {
        // Using the external URL redirect but preserve comboboxes by opening in same window
        window.open(import.meta.env.VITE_OLD_APP_URL + '/CustomReports/NECA_AMF_Report.aspx', '_self');
    };

    const CommandCell = (props: any) => (
        <DuplicateDeleteCommandCell {...props} remove={remove} duplicate={duplicate} includeDuplicateButton={true} />
    );

    return (
        <div style={{ margin: '20px' }}>
            <PageHeading title="NECA AMF Rate Schedules" />
            <div style={{ marginTop: '20px' }}>
                <Link to="/neca-amf-rates/detail?id=new">
                    <Button icon="add" style={{ marginRight: '12px' }}>
                        Create New
                    </Button>
                </Link>
                <Button onClick={goBack}>Back</Button>
            </div>

            {/* @ts-ignore The type definitions for Kendo Grid v4.14.1 seem incorrect regarding children */}
            <Grid data={rateSchedules} onRowClick={handleRowClick} style={{ marginTop: '12px' }}>
                <Column cell={CommandCell} sortable={false} width="60px" />
                <Column field="effectiveStartDate" title="Start Date" format="{0:MM/dd/yyyy}" />
                <Column field="effectiveEndDate" title="End Date" format="{0:MM/dd/yyyy}" />
            </Grid>
            {showDeleteRateScheduleDialog ? (
                <Dialog title="Confirm Delete Rate Schedule" onClose={cancelDelete}>
                    <p>Are you sure you want to delete this rate schedule?</p>
                    <Button onClick={deleteRateSchedule} style={{ marginRight: '8px' }}>
                        Confirm
                    </Button>
                    <Button onClick={cancelDelete}>Cancel</Button>
                </Dialog>
            ) : null}
        </div>
    );
};

export default function NecaAmfRatesRosterPage() {
    return (
        <AppLayout>
            <NecaAmfRatesRoster />
        </AppLayout>
    );
}
