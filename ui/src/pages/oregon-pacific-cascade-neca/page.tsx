import { But<PERSON> } from '@progress/kendo-react-buttons';
import { Loader } from '@progress/kendo-react-indicators';
import { useEffect, useState, useCallback, useMemo } from 'react';
import { instance } from '@/src/core/http/axios-config';
import FilterableComboBox from '@/src/components/kendo/FilterableComboBox';
import MonthPicker from '@/src/components/kendo/MonthPicker';
import MultiSelectCheckbox from '@/src/components/kendo/MultiSelectCheckbox';
import { MultiSelectXmlRequests } from "@/lib/core/MultiSelectXmlRequests";
import { State } from '@progress/kendo-data-query';
import { ColumnFilterCheckboxMenu } from '@/src/components/kendo/ColumnFilterMenu';
import EPRReportGrid from '@/src/components/kendo/EPRReportGrid';
import EPRReportColumn from '@/src/components/kendo/EPRReportColumn';
import { XMLBuilder } from 'fast-xml-parser';
import { ClientUtils } from "@/lib/core/ClientUtils";
import { Heading, View, Flex, Text } from '@adobe/react-spectrum';
import PageHeading from '@/src/components/UI/PageHeading';
import AppLayout from '@/src/components/Layout/AppLayout';
import { useStore, StoreState } from '@/lib';
import { Constants } from '@/src/constants/global';

const initialDataState: State = {
    sort: [],
    filter: {
        filters: [],
        logic: 'and'
    }
};

const xmlBuilder = new XMLBuilder({ ignoreAttributes: false });

const agreementTypes = [
    { text: 'Collective Bargaining', value: 1 },
    { text: 'Non-Bargaining', value: 2 },
    { text: 'Special', value: 3 },
    { text: 'Supplemental', value: 4 },
    { text: 'Portability', value: 5 }
];

const OregonPacificCascadeNECA = ({ chapter }: any) => {
    // Get the selected chapter GUID from the store
    const selectedChapterGuid = useStore((state: StoreState) => state.selectedChapterGuid);
    
    const [unions, setUnions] = useState([
        { text: 'IBEW 280', value: 142796 },
        { text: 'IBEW 659', value: 31291 },
        { text: 'IBEW 932', value: 142806 }
    ]);
    
    const fetchUnions = useCallback(() => {
        let url = '/api/Unions?username=' + ClientUtils.getUsername();
        if (selectedChapterGuid) {
            url += '&GUID=' + selectedChapterGuid;
        }
        instance
            .get(url)
            .then((response) => {
                setUnions(
                    response.data.map((u: any) => {
                        return {
                            text: u.name,
                            value: u.id
                        };
                    })
                );
            })
            .catch((e) => {
                setError('Error occurred while fetching unions');
            });
    }, [selectedChapterGuid]);

    useEffect(() => {
        fetchUnions();
    }, [selectedChapterGuid, fetchUnions]);
    
    const associationMemberOptions = useMemo(
        () => [
            { text: 'Yes', value: 0 },
            { text: 'No', value: 1 }
        ],
        []
    );
    const OregonPacificChapterGUID = 'AA0350D3-7AA2-492E-9BD5-D711F9E57975';

    const [error, setError] = useState<any>(null);
    const [loadingReport, setLoadingReport] = useState(false);
    const [dataState, setDataState] = useState(initialDataState);

    const [employers, setEmployers] = useState([]);
    const [agreements, setAgreements] = useState([]);
    const [classifications, setClassifications] = useState([]);
    const [benefits, setBenefits] = useState([]);

    const [startingMonth, setStartingMonth] = useState(null);
    const [endingMonth, setEndingMonth] = useState(null);
    const [selectedUnions, setSelectedUnions] = useState([]);
    const [selectedAssociationMembers, setSelectedAssociationMembers] = useState<any>([]);
    const [selectedEmployers, setSelectedEmployers] = useState([]);
    const [selectedAgreements, setSelectedAgreements] = useState([]);
    const [selectedClassifications, setSelectedClassifications] = useState([]);
    const [selectedBenefitID, setSelectedBenefitID] = useState(null);

    const [reportLineItems, setReportLineItems] = useState([]);

    const fetchEmployers = useCallback(() => {
        const employersRequestXml = MultiSelectXmlRequests.employersByUnion(selectedUnions, selectedAssociationMembers);
        const url =
            '/api/Employers/by-union?EmployerXml=' +
            xmlBuilder.build(employersRequestXml) +
            '&GUID=' +
            (selectedChapterGuid || OregonPacificChapterGUID) +
            '&username=' +
            ClientUtils.getUsername();
        instance
            .get(url)
            .then((response) => {
                setEmployers(
                    response.data.map((e: any) => {
                        return {
                            text: e.name,
                            value: e.id
                        };
                    })
                );
            })
            .catch((e) => {
                setError('Error occurred while fetching employers');
            });
    }, [selectedUnions, selectedAssociationMembers, selectedChapterGuid]);

    const fetchAgreements = useCallback(() => {
        const agreementsRequestXml = MultiSelectXmlRequests.agreementsByEmployer(selectedEmployers, selectedUnions, agreementTypes);

        const url = '/api/Agreements/by-employer';
        const body = {
            agreementXml: xmlBuilder.build(agreementsRequestXml),
            GUID: selectedChapterGuid || OregonPacificChapterGUID,
            username: ClientUtils.getUsername()
        };
        instance
            .post(url, body)
            .then((response) => {
                setAgreements(
                    response.data.map((a: any) => {
                        return {
                            text: a.name,
                            value: a.id
                        };
                    })
                );
            })
            .catch((e) => {
                setError('Error occurred while fetching agreements');
            });
    }, [selectedEmployers, setAgreements, selectedUnions, selectedChapterGuid]);

    const fetchClassifications = useCallback(() => {
        const classificationsRequestXml = MultiSelectXmlRequests.getAgreementIds(selectedAgreements);

        const url = '/api/Classifications/by-agreement';
        const body = {
            agreements: xmlBuilder.build({
                Agreements: classificationsRequestXml
            })
        };
        instance
            .post(url, body)
            .then((response) => {
                setClassifications(
                    response.data.map((c: any) => {
                        return {
                            text: c.name,
                            value: c.id
                        };
                    })
                );
            })
            .catch((e) => {
                setError('Error occurred while fetching classifications');
            });
    }, [selectedAgreements]);

    const fetchBenefits = useCallback(() => {
        const beneiftsRequestXml = MultiSelectXmlRequests.getAgreementIds(selectedAgreements);

        const url = '/api/Benefits/by-agreement';
        const body = {
            agreements: xmlBuilder.build({ Agreements: beneiftsRequestXml })
        };
        instance
            .post(url, body)
            .then((response) => {
                setBenefits(
                    response.data.map((b: any) => {
                        return {
                            text: b.name,
                            value: b.id
                        };
                    })
                );
            })
            .catch((e) => {
                setError('Error occurred while fetching benefits');
            });
    }, [selectedAgreements]);

    useEffect(() => {
        setSelectedAssociationMembers(associationMemberOptions);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
        setSelectedEmployers([]);
        if (selectedUnions.length > 0 && selectedAssociationMembers.length > 0) {
            fetchEmployers();
        } else {
            setEmployers([]);
        }
    }, [selectedUnions, selectedAssociationMembers, fetchEmployers, selectedChapterGuid]);

    useEffect(() => {
        setSelectedAgreements([]);
        if (selectedEmployers.length > 0) {
            fetchAgreements();
        } else {
            setAgreements([]);
        }
    }, [selectedEmployers, setAgreements, fetchAgreements]);

    useEffect(() => {
        setSelectedClassifications([]);
        setSelectedBenefitID(null);
        if (selectedAgreements.length > 0) {
            fetchClassifications();
            fetchBenefits();
        } else {
            setClassifications([]);
            setBenefits([]);
        }
    }, [selectedAgreements, setClassifications, setBenefits, fetchClassifications, fetchBenefits]);

    useEffect(() => {
        fetchEmployers();
        fetchAgreements();
        fetchBenefits();
        fetchClassifications();
    }, [fetchEmployers, fetchAgreements, fetchBenefits, fetchClassifications]);

    const runReport = () => {
        if (
            !startingMonth ||
            !endingMonth ||
            selectedEmployers.length === 0 ||
            selectedAgreements.length === 0 ||
            selectedClassifications.length === 0 ||
            !selectedBenefitID
        ) {
            setError('Please make sure all fields are filled before running the report');
            return;
        }

        setLoadingReport(true);
        const employersXml = {
            Employers: MultiSelectXmlRequests.getEmployerIdsXml(selectedEmployers)
        };
        const agreementsXml = {
            Agreements: MultiSelectXmlRequests.getAgreementIds(selectedAgreements)
        };
        const classificationsXml = {
            Classifications: MultiSelectXmlRequests.getClassificationIdsXml(selectedClassifications)
        };

        const url = '/api/OregonPacificCascadeNeca';
        const body = {
            startDate: startingMonth,
            endDate: endingMonth,
            employerIds: xmlBuilder.build(employersXml),
            agreementIds: xmlBuilder.build(agreementsXml),
            classificationIds: xmlBuilder.build(classificationsXml),
            benefitId: selectedBenefitID
        };
        instance.post(url, body).then((response) => {
            setReportLineItems(
                response.data.map((r: any) => ({
                    ...r,
                    workMonth: new Date(r.workMonth)
                }))
            );
            setError(null);
            setLoadingReport(false);
        });
    };

    const handleStartingMonthChange = (e: any) => {
        setStartingMonth(e.value);
    };

    const handleEndingMonthChange = (e: any) => {
        setEndingMonth(e.value);
    };

    const handleSelectedBenefitChange = (e: any) => {
        if (e.value) {
            setSelectedBenefitID(e.value.value);
        } else {
            setSelectedBenefitID(null);
        }
    };

    const NECACheckboxFilterMenu = (props: any) => <ColumnFilterCheckboxMenu {...props} data={reportLineItems} />;

    return (
        <>
            <PageHeading title="Status Report" />
            {error !== null ? (
                <p
                    style={{
                        color: 'red',
                        fontWeight: 'bold',
                        marginTop: '6px',
                        fontSize: '14px'
                    }}>
                    {error}
                </p>
            ) : null}
            <View colorVersion={6} borderWidth="none" borderColor="gray-200" borderRadius="regular" width="100%" paddingBottom={'size-75'} paddingX={'size-75'} marginBottom="size-300">
                <Flex direction="row" gap="size-300">
                    <Flex direction="column" width="20%">
                        <Text UNSAFE_className="fontWeightSemiBold" marginBottom="size-100">Starting Month</Text>
                        <MonthPicker value={startingMonth} onChange={handleStartingMonthChange} />

                        <Text UNSAFE_className="fontWeightSemiBold" marginTop="size-200" marginBottom="size-100">Ending Month</Text>
                        <MonthPicker value={endingMonth} onChange={handleEndingMonthChange} />

                        <Text UNSAFE_className="fontWeightSemiBold" marginTop="size-200" marginBottom="size-100">Unions</Text>
                        <MultiSelectCheckbox data={unions} selectedData={selectedUnions} setSelectedData={setSelectedUnions} />

                        <Text UNSAFE_className="fontWeightSemiBold" marginTop="size-200" marginBottom="size-100">Association Members</Text>
                        <MultiSelectCheckbox
                            data={associationMemberOptions}
                            selectedData={selectedAssociationMembers}
                            setSelectedData={setSelectedAssociationMembers}
                        />
                    </Flex>

                    <Flex direction="column" width="20%">
                        <Text UNSAFE_className="fontWeightSemiBold" marginBottom="size-100">Employers</Text>
                        <MultiSelectCheckbox data={employers} selectedData={selectedEmployers} setSelectedData={setSelectedEmployers} />
                    </Flex>

                    <Flex direction="column" width="20%">
                        <Text UNSAFE_className="fontWeightSemiBold" marginBottom="size-100">Agreements</Text>
                        <MultiSelectCheckbox
                            data={agreements}
                            selectedData={selectedAgreements}
                            setSelectedData={setSelectedAgreements}
                        />
                    </Flex>

                    <Flex direction="column" width="20%">
                        <Text UNSAFE_className="fontWeightSemiBold" marginBottom="size-100">Classifications</Text>
                        <MultiSelectCheckbox
                            data={classifications}
                            selectedData={selectedClassifications}
                            setSelectedData={setSelectedClassifications}
                        />
                    </Flex>

                    <Flex direction="column" width="20%">
                        <Text UNSAFE_className="fontWeightSemiBold" marginBottom="size-100">Benefits</Text>
                        <FilterableComboBox
                            allowNulls={true}
                            data={benefits}
                            selectedValue={selectedBenefitID}
                            onChange={handleSelectedBenefitChange}
                        />
                    </Flex>
                </Flex>
            </View>
            <Flex marginBottom="size-300">
                <Button primary onClick={runReport}>
                    Run Report {loadingReport ? <Loader type="infinite-spinner" size="small" /> : null}
                </Button>
            </Flex>
            <EPRReportGrid
                data={reportLineItems}
                dataState={dataState}
                setDataState={setDataState}
                fileName={'OregonPacificCascadeNECA.xlsx'}>
                <EPRReportColumn field="employer" title="Employer" />
                <EPRReportColumn field="associationMember" title="NECA" columnMenu={NECACheckboxFilterMenu} width="100px" />
                <EPRReportColumn field="agreement" title="Agreement" />
                <EPRReportColumn field="workMonth" title="Work Month" format="{0:MM/yyyy}" filter="date" excelWidth={100} />
                <EPRReportColumn field="classification" title="Classification" />
                <EPRReportColumn field="eeClassificationCount" title="Class Count" filter="numeric" />
                <EPRReportColumn field="totalHoursReported" title="Total Hours Reported" filter="numeric" />
                <EPRReportColumn field="totalPayrollReported" title="Total Payroll Reported" filter="numeric" />
                <EPRReportColumn field="benefitTotal" title="Benefit Total" filter="numeric" />
            </EPRReportGrid>
        </>
    );
};

export default function OregonPacificCascadeNECAPage() {
    return (
        <AppLayout>
            <OregonPacificCascadeNECA />
        </AppLayout>
    );
}
