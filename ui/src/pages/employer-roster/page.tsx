import { StoreState, useStore } from '@/lib';
import EmployerRosterGridContainer from '@/src/container/EmployerRosterGrid/EmployerRosterGrid';
import ContainerLoader from '@/src/components/UI/Loader/ContainerLoader/ContainerLoader';
import AppLayout from '@/src/components/Layout/AppLayout';
import { useHelpScoutBeacon } from '@/src/hooks/useHelpScoutBeacon';

export default function Page() {
    // Fetch both potential chapter ID formats from the store
    const selectedChapterEncodedId = useStore((state: StoreState) => state.selectedChapterEncodedId);

    // Initialize HelpScout Beacon with the employer-roster specific GUID
    useHelpScoutBeacon('150f892a-c41e-474f-8f32-bb27bc15a1ad');

    return (
        <AppLayout>
            {!selectedChapterEncodedId ? (
                <ContainerLoader parentStyles={{ height: '100%', flex: 1 }} />
            ) : (
                <EmployerRosterGridContainer chapterIdEncoded={selectedChapterEncodedId} />
            )}
        </AppLayout>
    );
}
