import { StoreState, useStore } from '@/lib';
import TimesheetRoster from '@/src/container/TimesheetRoster/TimesheetRoster';
import ContainerLoader from '@/src/components/UI/Loader/ContainerLoader/ContainerLoader';
import AppLayout from '@/src/components/Layout/AppLayout';
import { useHelpScoutBeacon } from '@/src/hooks/useHelpScoutBeacon';

export default function Page() {
    const employerGuid = useStore((state: StoreState) => state.selectedEmployerGuid);

    // Initialize HelpScout Beacon with the timesheet-roster specific GUID
    useHelpScoutBeacon('1b34a0a2-ebac-4fe9-90b3-ce994bb75bab');

    return (
        <AppLayout>
            {!employerGuid ? (
                <ContainerLoader parentStyles={{ height: '100%', flex: 1 }} />
            ) : (
                <TimesheetRoster employerGuid={employerGuid} />
            )}
        </AppLayout>
    );
};