import React, { Suspense } from 'react';
import { useSearchParams } from 'react-router-dom';
import { Flex, ProgressCircle } from '@adobe/react-spectrum';
import TimesheetDetailContainer from '@/src/container/TimesheetDetail/TimesheetDetail';

/**
 * Timesheet detail page that loads immediately without any blocking queries
 * All data fetching is handled by child components when needed
 */
const TimesheetDetailPageContent = () => {
    const [searchParams] = useSearchParams();
    const timeSheetId = searchParams.get('id');
    const employerGuid = searchParams.get('employerGuid');

    if (!timeSheetId || !employerGuid) {
        return (
            <Flex justifyContent="center" alignItems="center" height="100vh" marginTop="size-400">
                <div>Missing timesheet ID or employer GUID in URL parameters</div>
            </Flex>
        );
    }

    // Pass parameters to TimesheetDetailContainer - no blocking queries here
    return <TimesheetDetailContainer employerGuid={employerGuid} timeSheetId={timeSheetId} />;
};

/**
 * Timesheet detail page that uses router-level query pattern.
 * This follows Relay best practices without requiring advanced EntryPoint configuration.
 * 
 * Benefits:
 * - Complete query at route level includes all necessary fragments
 * - Handles both navigation scenarios (roster→detail and direct access)
 * - Relay's normalization prevents duplicate network requests when data is cached
 * - Simpler than EntryPoints while still following Relay patterns
 */
export default function Page() {
    return (
        <Suspense
            fallback={
                <Flex justifyContent="center" alignItems="center" height="100vh" marginTop="size-400">
                    <ProgressCircle aria-label="Loading timesheet detail" isIndeterminate size="L" />
                </Flex>
            }>
            <TimesheetDetailPageContent />
        </Suspense>
    );
}