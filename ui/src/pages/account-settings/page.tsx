import { useEffect, useState } from 'react';
import { instance } from '@/src/core/http/axios-config';
import { Constants } from '@/src/constants/global';
import { ClientUtils } from "@/lib/core/ClientUtils";
import { StoreState, useStore } from '@/lib';
import { useUserInfo } from '@/src/context/UserInfoContext';
import { Tooltip } from 'react-tooltip';
import { ToastQueue } from '@react-spectrum/toast';
import {
    DialogContainer,
    Dialog,
    Heading,
    Content,
    ButtonGroup,
    Button,
    Divider,
    ProgressCircle,
    TextField,
    Checkbox as SpectrumCheckbox,
    ComboBox,
    Item,
    Key
} from '@adobe/react-spectrum';

const PreferredMFAOptions = [
    {
        text: 'SMS',
        value: 'SMS'
    },
    {
        text: 'Voice',
        value: 'Voice'
    }
];

const UserSettings = () => {
    // Get user info from GraphQL context
    const userInfo = useUserInfo();

    // Initialize state from GraphQL user data
    const [email, setEmail] = useState<string | null>(userInfo.email);
    const [phoneNumber, setPhoneNumber] = useState<string | null>(
        userInfo.phoneNumber ? ClientUtils.formatPhoneNumber(userInfo.phoneNumber) : null
    );
    const [phoneNumberExtension, setPhoneNumberExtension] = useState<string | null>(userInfo.phoneNumberExtension ?? null);
    const [phoneTypeID, setPhoneTypeID] = useState(userInfo.phoneTypeId ?? 5);
    const [preferredMFAMethod, setPreferredMFAMethod] = useState<string | null>(userInfo.preferredMfamethod ?? null);
    const [routingNumber, setRoutingNumber] = useState('');
    const [accountNumber, setAccountNumber] = useState('');

    const [termsAndConditionsVisible, setTermsAndConditionsVisible] = useState(false);
    const [hasReadTermsAndConditions, setHasReadTermsAndConditions] = useState(false);
    const [saving, setSaving] = useState(false);
    const [savingUser, setSavingUser] = useState(false);
    const [savingEFTPayments, setSavingEFTPayments] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [showUnsavedDataWarning, setShowUnsavedDataWarning] = useState(false);
    const [showMFASettings, setShowMFASettings] = useState(false);
    const [originalRoutingNumber, setOriginalRoutingNumber] = useState('');
    const [originalAccountNumber, setOriginalAccountNumber] = useState('');
    const [originalMFAMethod, setOriginalMFAMethod] = useState<string | null>(userInfo.preferredMfamethod ?? null);
    const [originalHasReadTermsAndConditions, setOriginalHasReadTermsAndConditions] = useState(false);
    const [confirmDeleteDialogVisible, setConfirmDeleteDialogVisible] = useState(false);
    const [confirmUpdateBankingInfoDialogVisible, setConfirmUpdateBankingInfoDialogVisible] = useState(false);
    const [tooltipTarget, setTooltipTarget] = useState<string | null>(null);
    const [tooltipText, setTooltipText] = useState<string | null>(null);

    const roleGroups = userInfo.roleGroups;

    useEffect(() => {
        // Load EFT payment methods (still REST API)
        instance.get('/api/EFTPaymentMethods?username=' + ClientUtils.getUsername()).then((response) => {
            if (response.data.length > 0) {
                setRoutingNumber(response.data[0].routingNumber);
                setAccountNumber(response.data[0].accountNumber);
                setHasReadTermsAndConditions(true);
                setOriginalHasReadTermsAndConditions(true);
                setOriginalRoutingNumber(response.data[0].routingNumber);
                setOriginalAccountNumber(response.data[0].accountNumber);
            }
        });
        // User info is now loaded from GraphQL context, no REST call needed
    }, []);

    useEffect(() => {
        if (roleGroups.indexOf(Constants.Roles.SystemAdministrator) >= 0) {
            setShowMFASettings(true);
        }
    }, [roleGroups]);

    useEffect(() => {
        setShowUnsavedDataWarning(
            preferredMFAMethod !== originalMFAMethod ||
                routingNumber !== originalRoutingNumber ||
                accountNumber !== originalAccountNumber ||
                hasReadTermsAndConditions !== originalHasReadTermsAndConditions
        );
    }, [
        preferredMFAMethod,
        routingNumber,
        accountNumber,
        hasReadTermsAndConditions,
        originalMFAMethod,
        originalRoutingNumber,
        originalAccountNumber,
        originalHasReadTermsAndConditions
    ]);

    useEffect(() => {
        setSaving(savingUser || savingEFTPayments);
        if (!savingUser && !savingEFTPayments) {
            setShowUnsavedDataWarning(false);
        }
    }, [savingUser, savingEFTPayments]);

    const onRoutingNumberChange = (value: string) => {
        if (!ClientUtils.isOnlyNumbers(value) && value !== '') return;
        setRoutingNumber(value);
    };

    const onAccountNumberChange = (value: string) => {
        if (!ClientUtils.isOnlyNumbers(value) && value !== '') return;
        setAccountNumber(value);
    };

    const save = () => {
        if (preferredMFAMethod !== originalMFAMethod) {
            savePreferredMFAMethod();
        }
        if (
            routingNumber !== originalRoutingNumber ||
            accountNumber !== originalAccountNumber ||
            hasReadTermsAndConditions !== originalHasReadTermsAndConditions
        ) {
            if (!hasReadTermsAndConditions) {
                toggleConfirmDeleteDialog();
                return;
            }
            if (!areRoutingAndAccountNumbersValid()) {
                return;
            }
            toggleConfirmUpdateBankingInfoDialog();
        }
    };

    const savePreferredMFAMethod = () => {
        setSavingUser(true);
        instance
            .post('/api/Users', {
                username: ClientUtils.getUsername(),
                email: email,
                phoneNumber: phoneNumber,
                phoneNumberExtension: phoneNumberExtension,
                preferredMFAMethod: preferredMFAMethod
            })
            .then((response) => {
                setSavingUser(false);
                setOriginalMFAMethod(preferredMFAMethod);
                ToastQueue.positive('Preferred MFA method updated successfully.', { timeout: 7000 });
                instance.post('/api/LoggedEvents', {
                    EventDateTime: new Date(),
                    DEventTypeID: Constants.EventLogTypeIDs.PreferredMFAMethodChanged,
                    DEventSubTypeID: Constants.EventLogSubTypeIDs.SinglePointEvent,
                    UserName: ClientUtils.getUsername(),
                    Target: 'User ' + ClientUtils.getUsername(),
                    Notes: null,
                    SessionID: null
                });
            })
            .catch((err) => {
                setError('Something went wrong, please contact EPRLive support');
                setSavingUser(false);
            });
    };

    const saveEFTPaymentSettings = (deleting: boolean) => {
        setSavingEFTPayments(true);
        if (deleting) {
            instance
                .delete('/api/EFTPaymentMethods?Username=' + ClientUtils.getUsername())
                .then(() => {
                    setSavingEFTPayments(false);
                    setRoutingNumber('');
                    setAccountNumber('');
                    setOriginalRoutingNumber('');
                    setOriginalAccountNumber('');
                    setOriginalHasReadTermsAndConditions(false);
                    ToastQueue.positive('Electronic payment method deleted successfully.', { timeout: 7000 });
                    instance.post('/api/LoggedEvents', {
                        EventDateTime: new Date(),
                        DEventTypeID: Constants.EventLogTypeIDs.ElectronicPaymentMethodDeleted,
                        DEventSubTypeID: Constants.EventLogSubTypeIDs.SinglePointEvent,
                        UserName: ClientUtils.getUsername(),
                        Target: 'User ' + ClientUtils.getUsername(),
                        Notes: null,
                        SessionID: null
                    });
                })
                .catch(() => {
                    setError('Something went wrong, please contact EPRLive support');
                    setSavingEFTPayments(false);
                });
        } else {
            instance
                .post('/api/EFTPaymentMethods', {
                    username: ClientUtils.getUsername(),
                    routingNumber: routingNumber,
                    accountNumber: accountNumber
                })
                .then((response) => {
                    setSavingEFTPayments(false);
                    setError(null);
                    setOriginalRoutingNumber(routingNumber);
                    setOriginalAccountNumber(accountNumber);
                    setOriginalHasReadTermsAndConditions(true);
                    ToastQueue.positive('Electronic payment method saved successfully.', { timeout: 7000 });
                    instance.post('/api/LoggedEvents', {
                        EventDateTime: new Date(),
                        DEventTypeID: Constants.EventLogTypeIDs.ElectronicPaymentMethodSaved,
                        DEventSubTypeID: Constants.EventLogSubTypeIDs.SinglePointEvent,
                        UserName: ClientUtils.getUsername(),
                        Target: 'User ' + ClientUtils.getUsername(),
                        Notes: null,
                        SessionID: null
                    });
                })
                .catch(() => {
                    setError('Something went wrong, please contact EPRLive support');
                    setSavingEFTPayments(false);
                });
        }
    };

    const toggleTermsAndConditionsDialog = () => {
        setTermsAndConditionsVisible(!termsAndConditionsVisible);
    };

    const toggleConfirmUpdateBankingInfoDialog = () => {
        setConfirmUpdateBankingInfoDialogVisible(!confirmUpdateBankingInfoDialogVisible);
    };

    const toggleConfirmDeleteDialog = () => {
        setConfirmDeleteDialogVisible(!confirmDeleteDialogVisible);
    };

    const areRoutingAndAccountNumbersValid = () => {
        if (!routingNumber) {
            setError('Bank Routing Number cannot be blank');
            return false;
        } else if (routingNumber.length !== 9) {
            setError('Routing number must be 9 digits long.');
            return false;
        } else if (!isRoutingNumberChecksumValid(routingNumber)) {
            setError('Routing number is not valid.');
            return false;
        } else if (!accountNumber) {
            setError('Bank Account Number cannot be blank');
            return false;
        }

        return true;
    };

    function isRoutingNumberChecksumValid(routingNumber: any) {
        let checksum = 0,
            len = 0,
            sum = 0,
            mod = 0;
        len = routingNumber.length;
        if (len != 9) {
            return false;
        } else {
            const newString = routingNumber.substring(len - 1);
            checksum = parseInt(newString);
            sum =
                7 * (parseInt('' + routingNumber[0]) + parseInt('' + routingNumber[3]) + parseInt('' + routingNumber[6])) +
                3 * (parseInt('' + routingNumber[1]) + parseInt('' + routingNumber[4]) + parseInt('' + routingNumber[7])) +
                9 * (parseInt('' + routingNumber[2]) + parseInt('' + routingNumber[5]));
            mod = sum % 10;
            return mod == checksum;
        }
    }

    const showTooltip = (target: string, tooltipText: string) => {
        if (!hasReadTermsAndConditions) {
            setTooltipText(tooltipText);
            setTooltipTarget(target);
        }
    };

    const hideTooltip = () => {
        setTooltipText(null);
        setTooltipTarget(null);
    };

    return (
        <div style={{ margin: '20px' }}>
            <h2 style={{ marginBottom: '50px' }}>Account Settings</h2>
            {error !== null ? (
                <p
                    style={{
                        color: 'red',
                        fontWeight: 'bold',
                        marginTop: '6px',
                        fontSize: '14px'
                    }}>
                    {error}
                </p>
            ) : null}
            {showMFASettings ? (
                <>
                    <h4 style={{ marginBlockStart: '1.33em', marginBlockEnd: '1.33em' }}>MFA Settings</h4>
                    <div>
                        <span style={{ marginRight: '12px', display: 'inline-block', width: '160px' }}>Preferred MFA Method:</span>
                        <ComboBox
                            selectedKey={preferredMFAMethod || undefined}
                            onSelectionChange={(key) => setPreferredMFAMethod(key as string)}
                            width="160px"
                            aria-label="Preferred MFA Method">
                            {PreferredMFAOptions.map((option) => (
                                <Item key={option.value}>{option.text}</Item>
                            ))}
                        </ComboBox>
                    </div>
                </>
            ) : null}
            <h4 style={{ marginBlockStart: '1.33em', marginBlockEnd: '1.33em' }}>EFT Payments</h4>
            <div>
                <div style={{ marginBottom: '10px' }}>
                    <span style={{ marginRight: '12px', display: 'inline-block', width: '160px' }}>Bank Routing Number:</span>
                    <span
                        onMouseOver={() =>
                            showTooltip(
                                '.routing-number-input',
                                'You must accept the terms and conditions before entering a routing number'
                            )
                        }
                        onMouseLeave={hideTooltip}>
                        <TextField
                            value={routingNumber}
                            onChange={onRoutingNumberChange}
                            isDisabled={!hasReadTermsAndConditions}
                            UNSAFE_className="routing-number-input"
                            width="160px"
                            aria-label="Bank Routing Number"
                        />
                    </span>
                </div>
                <div style={{ marginBottom: '10px' }}>
                    <span style={{ marginRight: '12px', display: 'inline-block', width: '160px' }}>Account Number:</span>
                    <span
                        onMouseOver={() =>
                            showTooltip(
                                '.account-number-input',
                                'You must accept the terms and conditions before entering an account number'
                            )
                        }
                        onMouseLeave={hideTooltip}>
                        <TextField
                            value={accountNumber}
                            onChange={onAccountNumberChange}
                            isDisabled={!hasReadTermsAndConditions}
                            UNSAFE_className="account-number-input"
                            width="160px"
                            aria-label="Account Number"
                        />
                    </span>
                </div>
            </div>
            <div style={{ marginBottom: '16px' }}>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                    <SpectrumCheckbox
                        isSelected={hasReadTermsAndConditions}
                        onChange={setHasReadTermsAndConditions}
                        aria-label="Accept Terms and Conditions"
                    />
                    <span>
                        I have read the{' '}
                        <a
                            href="#"
                            onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                toggleTermsAndConditionsDialog();
                            }}>
                            Terms and Conditions
                        </a>{' '}
                        for electronic payments and agree
                    </span>
                </div>
            </div>
            <div style={{ marginTop: '16px' }}>
                {showUnsavedDataWarning ? (
                    <p
                        style={{
                            color: '#ffc107',
                            fontWeight: 'bold',
                            marginTop: '6px',
                            fontSize: '14px'
                        }}>
                        WARNING: You have unsaved data on this page.
                    </p>
                ) : null}
                <Button variant="accent" onPress={save} aria-label="Save settings">
                    Save {saving ? <ProgressCircle size="S" aria-label="Saving..." isIndeterminate /> : null}
                </Button>
            </div>
            {/* React Spectrum Dialog Containers */}
            <DialogContainer onDismiss={() => setTermsAndConditionsVisible(false)}>
                {termsAndConditionsVisible && (
                    <Dialog>
                        <Heading>Terms and Conditions</Heading>
                        <Divider />
                        <Content>
                            <p>
                                By entering my banking information below and when I elect to pay electronically by clicking the "Pay by EFT"
                                option during submission of my monthy payroll report, I authorize the COLLECTING AGENT OF RECORD for the
                                &quot;BENEFIT(S)&quot; to initiate an electronic payment in the amount specified in the filed payroll report
                                from the bank account on this record. I also authorize my financial institution to honor this electronic
                                payment. Please be advised that by submitting this payment, I hereby authorize the COLLECTING AGENT OF
                                RECORD to originate an ACH debit to the bank account provided.
                            </p>
                            <p>
                                Furthermore, I acknowledge that the payment relationship I am entering into does not involve Corellian
                                Software, Inc. and therefore I agree to defend and indemnify Corellian against all claims, damages,
                                liabilities and expenses incurred or imposed on Corellian Software Inc. in conjunction with the COLLECTING
                                AGENT OF RECORD&apos;s performance of electronic payment services; and to hold Corellian Software Inc.
                                harmless for all claims, damages, liabilities and expenses incurred by or imposed against myself or my
                                organization in connection with any actual or threatened claims, suits or proceedings concerning the myself
                                or my organization.
                            </p>
                        </Content>
                        <ButtonGroup>
                            <Button variant="secondary" onPress={() => setTermsAndConditionsVisible(false)}>
                                Close
                            </Button>
                        </ButtonGroup>
                    </Dialog>
                )}
            </DialogContainer>

            <DialogContainer onDismiss={() => setConfirmUpdateBankingInfoDialogVisible(false)}>
                {confirmUpdateBankingInfoDialogVisible && (
                    <Dialog>
                        <Heading>Confirm Banking Information Update</Heading>
                        <Divider />
                        <Content>
                            <p>Please confirm that you want to update your banking information on future EFT transactions.</p>
                            <p>
                                Note: Clicking OK doesn&apos;t update banking information on any electronic payment authorizations already
                                in progress.
                            </p>
                        </Content>
                        <ButtonGroup>
                            <Button variant="secondary" onPress={() => setConfirmUpdateBankingInfoDialogVisible(false)}>
                                Cancel
                            </Button>
                            <Button
                                variant="accent"
                                onPress={() => {
                                    saveEFTPaymentSettings(false);
                                    setConfirmUpdateBankingInfoDialogVisible(false);
                                }}>
                                Ok
                            </Button>
                        </ButtonGroup>
                    </Dialog>
                )}
            </DialogContainer>

            <DialogContainer onDismiss={() => setConfirmDeleteDialogVisible(false)}>
                {confirmDeleteDialogVisible && (
                    <Dialog>
                        <Heading>Confirm Delete Banking Information</Heading>
                        <Divider />
                        <Content>
                            <p>Please confirm that you want to turn off electronic payment options and delete your banking information.</p>
                            <p>Note: Clicking OK doesn&apos;t remove any electronic payment authorizations already in progress.</p>
                        </Content>
                        <ButtonGroup>
                            <Button variant="secondary" onPress={() => setConfirmDeleteDialogVisible(false)}>
                                Cancel
                            </Button>
                            <Button
                                variant="negative"
                                onPress={() => {
                                    saveEFTPaymentSettings(true);
                                    setConfirmDeleteDialogVisible(false);
                                }}>
                                Ok
                            </Button>
                        </ButtonGroup>
                    </Dialog>
                )}
            </DialogContainer>

            {tooltipTarget && (
                <Tooltip anchorSelect={tooltipTarget} place="top" isOpen={true}>
                    {tooltipText}
                </Tooltip>
            )}
        </div>
    );
};

export default UserSettings;
