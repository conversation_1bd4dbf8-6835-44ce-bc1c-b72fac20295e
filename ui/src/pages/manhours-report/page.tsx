import { State } from '@progress/kendo-data-query';
import { Button } from '@progress/kendo-react-buttons';
import { GridFooterCellProps } from '@progress/kendo-react-grid';
import { Checkbox, NumericTextBox } from '@progress/kendo-react-inputs';
import { Label } from '@progress/kendo-react-labels';
import { useEffect, useState, useCallback, useMemo, useRef } from 'react';
import { instance } from '@/src/core/http/axios-config';
import { Constants } from '@/src/constants/global';
import MultiSelectCheckbox from '@/src/components/kendo/MultiSelectCheckbox';
import './ManhoursReport.css';
import { Loader } from '@progress/kendo-react-indicators';
import { formatNumber } from '@telerik/kendo-intl';
import { MultiSelectXmlRequests } from "@/lib/core/MultiSelectXmlRequests";
import EPRReportGrid from '@/src/components/kendo/EPRReportGrid';
import EPRReportColumn from '@/src/components/kendo/EPRReportColumn';
import { XMLBuilder } from 'fast-xml-parser';
import { useStore, StoreState } from '@/lib';
import { ClientUtils } from "@/lib/core/ClientUtils";
import PageHeading from '@/src/components/UI/PageHeading';
import AppLayout from '@/src/components/Layout/AppLayout';
import ContainerLoader from '@/src/components/UI/Loader/ContainerLoader/ContainerLoader';

const initialDataState: State = {
    sort: [],
    filter: {
        filters: [],
        logic: 'and'
    }
};

const xmlBuilder = new XMLBuilder({ ignoreAttributes: false });

const ManhoursReport = () => {
    const associationMemberOptions = useMemo(
        () => [
            { text: 'Yes', value: 0 },
            { text: 'No', value: 1 }
        ],
        []
    );

    const relationshipTypeOptions = useMemo(
        () => [
            { text: 'Non-Reporting', value: 0 },
            { text: 'Permanent', value: 1 },
            { text: 'Traveling', value: 2 }
        ],
        []
    );

    const agreementTypeOptions = useMemo(
        () => [
            { text: 'Collective Bargaining', value: 1 },
            { text: 'Non-Bargaining', value: 2 },
            { text: 'Special', value: 3 },
            { text: 'Portability', value: 5 },
            { text: 'Non-Signatory', value: 6 },
        ],
        []
    );

    const [dataState, setDataState] = useState(initialDataState);
    const [loadingReport, setLoadingReport] = useState(false);
    const [error, setError] = useState<any>(null);

    const [unions, setUnions] = useState([]);
    const [employers, setEmployers] = useState([]);
    const [agreements, setAgreements] = useState([]);
    const [classifications, setClassifications] = useState([]);

    const [year, setYear] = useState(new Date().getFullYear());
    const [selectedUnions, setSelectedUnions] = useState<any[]>([]);
    const [selectedAssociationMembers, setSelectedAssociationMembers] = useState<any[]>([]);
    const [selectedRelationshipTypes, setSelectedRelationshipTypes] = useState<{ text: string; value: number }[]>([
        { text: 'Non-Reporting', value: 0 }
    ]);
    const [selectedAgreementTypes, setSelectedAgreementTypes] = useState<{ text: string; value: number }[]>([
        { text: 'Inside', value: 1 },
        { text: 'Outside', value: 2 },
        { text: 'Residential', value: 3 }
    ]);
    const [selectedEmployers, setSelectedEmployers] = useState<any[]>([]);
    const [selectedAgreements, setSelectedAgreements] = useState([]);
    const [selectedClassifications, setSelectedClassifications] = useState([]);

    const [includePaperReports, setIncludePaperReports] = useState(false);

    const [manhoursReportRows, setManhoursReportRows] = useState([]);

    const user = useStore((state: StoreState) => state.user);
    const selectedChapterGuid = useStore((state: StoreState) => state.selectedChapterGuid);
    const selectedChapterNumericId = useStore((state: StoreState) => state.selectedChapterNumericId);

    useEffect(() => {
        setSelectedAssociationMembers(associationMemberOptions);
        setSelectedRelationshipTypes(relationshipTypeOptions);
        setSelectedAgreementTypes(agreementTypeOptions);
    }, [agreementTypeOptions, associationMemberOptions, relationshipTypeOptions]);

    const fetchUnions = useCallback(() => {
        let url = '/api/Unions?username=' + ClientUtils.getUsername();
        if (selectedChapterGuid) {
            url += '&GUID=' + selectedChapterGuid;
        }
        instance
            .get(url)
            .then((response) => {
                setUnions(
                    response.data.map((u: any) => {
                        return {
                            text: u.name,
                            value: u.id
                        };
                    })
                );
            })
            .catch((e) => {
                setError('Error occurred while fetching unions');
            });
    }, [selectedChapterGuid]);

    useEffect(() => {
        if (user && user.roles.includes(Constants.Roles.SystemAdministrator) !== true) {
            fetchUnions();
        }
    }, [user, fetchUnions]);

    useEffect(() => {
        if (selectedChapterGuid !== null) {
            setSelectedUnions([]);
            fetchUnions();
        }
    }, [selectedChapterGuid, fetchUnions]);

    const fetchEmployers = useCallback(() => {
        const employerXml = MultiSelectXmlRequests.employersByUnion(selectedUnions, selectedAssociationMembers, selectedRelationshipTypes);
        let url = '/api/Employers/by-union?EmployerXml=' + xmlBuilder.build(employerXml) + '&username=' + ClientUtils.getUsername();
        if (selectedChapterGuid !== null) {
            url += '&GUID=' + selectedChapterGuid;
        }
        instance
            .get(url)
            .then((response) => {
                setEmployers(
                    response.data.map((e: any) => {
                        return {
                            text: e.name,
                            value: e.id
                        };
                    })
                );
            })
            .catch((e) => {
                setError('Error occurred while fetching employers');
            });
    }, [selectedUnions, selectedAssociationMembers, selectedRelationshipTypes, selectedChapterGuid]);

    useEffect(() => {
        setSelectedEmployers([]);
        if (selectedUnions.length === 0 || selectedAssociationMembers.length === 0 || selectedRelationshipTypes.length === 0) {
            setEmployers([]);
        } else {
            fetchEmployers();
        }
    }, [selectedUnions, selectedAssociationMembers, selectedRelationshipTypes, fetchEmployers]);

    const fetchAgreements = useCallback(() => {
        const agreementsXml = MultiSelectXmlRequests.agreementsByEmployer(selectedEmployers, selectedUnions, selectedAgreementTypes);

        const url = '/api/Agreements/by-employer';
        const body: any = {
            year: year,
            agreementXml: xmlBuilder.build(agreementsXml),
            username: ClientUtils.getUsername()
        };
        if (selectedChapterGuid !== null) {
            body['GUID'] = selectedChapterGuid;
        }
        instance
            .post(url, body)
            .then((response) => {
                setAgreements(
                    response.data.map((a: any) => {
                        return {
                            text: a.name,
                            value: a.id
                        };
                    })
                );
            })
            .catch((e) => {
                setError('Error occurred while fetching agreements');
            });
    }, [selectedEmployers, selectedUnions, selectedAgreementTypes, year, selectedChapterGuid]);

    useEffect(() => {
        setSelectedAgreements([]);
        if (selectedEmployers.length === 0 || selectedAgreementTypes.length === 0) {
            setAgreements([]);
        } else {
            fetchAgreements();
        }
    }, [selectedEmployers, selectedAgreementTypes, fetchAgreements]);

    const fetchClassifications = useCallback(() => {
        const url = '/api/Classifications/by-agreement';
        const agreementsXml = MultiSelectXmlRequests.getAgreementIds(selectedAgreements);
        const body: {
            agreements: string;
            username: string;
            GUID?: string;
        } = {
            agreements: xmlBuilder.build({ Agreements: agreementsXml }),
            username: ClientUtils.getUsername()
        };
        if (selectedChapterGuid !== null) {
            body.GUID = selectedChapterGuid;
        }
        instance
            .post(url, body)
            .then((response) => {
                setClassifications(
                    response.data.map((c: any) => {
                        return {
                            text: c.name,
                            value: c.id
                        };
                    })
                );
            })
            .catch((e) => {
                setError('Error occurred while fetching classifications');
            });
    }, [selectedAgreements, selectedChapterGuid]);

    useEffect(() => {
        setSelectedClassifications([]);
        if (selectedAgreements.length === 0) {
            setClassifications([]);
        } else {
            fetchClassifications();
        }
    }, [selectedAgreements, fetchClassifications]);

    useEffect(() => {
        fetchUnions();
        fetchEmployers();
        fetchAgreements();
    }, [fetchUnions, fetchEmployers, fetchAgreements]);

    const runReport = () => {
        if (!year || selectedEmployers.length === 0 || selectedAgreements.length === 0 || selectedClassifications.length === 0) {
            setError('Please select at least one employer, agreement and classification before running the manhours report');
            return;
        }
        setLoadingReport(true);

        const startDate = new Date(year, 0, 1);
        const endDate = new Date(year, 11, 31);
        const employersXml = {
            Employers: MultiSelectXmlRequests.getEmployerIdsXml(selectedEmployers)
        };
        const agreementsXml = {
            Agreements: MultiSelectXmlRequests.getAgreementIds(selectedAgreements)
        };
        const classificationsXml = {
            Classifications: MultiSelectXmlRequests.getClassificationIdsXml(selectedClassifications)
        };

        const url = '/api/ManhoursReport';
        const body: any = {
            startDate: startDate,
            endDate: endDate,
            employerIds: xmlBuilder.build(employersXml),
            agreementIds: xmlBuilder.build(agreementsXml),
            classificationIds: xmlBuilder.build(classificationsXml),
            includePaperReports: includePaperReports,
            username: ClientUtils.getUsername()
        };
        if (selectedChapterGuid !== null) {
            body['GUID'] = selectedChapterGuid;
        }
        instance
            .post(url, body)
            .then((response) => {
                setError(null);
                setManhoursReportRows(response.data);
                setLoadingReport(false);
            })
            .catch((e) => {
                setError('Something went wrong while retrieving manhours report');
                setLoadingReport(false);
            });
    };

    const handleYearChange = (e: any) => {
        setYear(e.value);
    };

    const handleIncludePaperReportsChange = (e: any) => {
        setIncludePaperReports(e.value);
    };

    const isYearValid = () => {
        if (!year) return false;
        if (year.toString().length !== 4) return false;

        return true;
    };

    const employerFooterCell = () => <td className="footer-Employer">Total</td>;

    const totalFooterCell = (props: GridFooterCellProps) => {
        const fieldName = props.field ? props.field.charAt(0).toUpperCase() + props.field.slice(1) : '';
        return (
            <td className={`footer-${fieldName}`}>
                {formatNumber(
                    manhoursReportRows.reduce(function (accumulator, row) {
                        return props.field && row[props.field] ? accumulator + parseInt(row[props.field] ?? 0) : accumulator;
                    }, 0),
                    'n2'
                )}
            </td>
        );
    };

    if (!selectedChapterGuid) {
        return <ContainerLoader parentStyles={{ height: '100%', flex: 1 }} />;
    }

    return (
        <>
            <PageHeading title="Manhours Report" />
            {error !== null ? (
                <p
                    style={{
                        color: 'red',
                        fontWeight: 'bold',
                        marginTop: '6px',
                        fontSize: '14px'
                    }}>
                    {error}
                </p>
            ) : null}
            <div style={{ marginBottom: '20px', display: 'flex', alignItems: 'center', gap: '16px' }}>
                <div>
                    <Label editorId="yearInput" style={{ marginRight: '8px' }}>Year:</Label>
                    <NumericTextBox
                        id="yearInput"
                        value={year}
                        onChange={handleYearChange}
                        format="#"
                        spinners={false}
                        step={0}
                        width="75px"
                        valid={isYearValid()}
                    />
                </div>
                <Checkbox
                    label="Include Paper Reports"
                    value={includePaperReports}
                    onChange={handleIncludePaperReportsChange}
                />
            </div>
            <table
                style={{
                    width: '100%',
                    tableLayout: 'fixed',
                    marginBottom: '20px'
                }}>
                <tbody>
                    <tr>
                        <td style={{ width: '25%' }}>
                            <table style={{ width: '100%' }}>
                                <tbody>
                                    <tr>
                                        <td>
                                            <p>Unions:</p>
                                            <MultiSelectCheckbox
                                                data={unions}
                                                selectedData={selectedUnions}
                                                setSelectedData={setSelectedUnions}
                                                style={{ marginBottom: '12px' }}
                                            />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <p>Association Members:</p>
                                            <MultiSelectCheckbox
                                                data={associationMemberOptions}
                                                selectedData={selectedAssociationMembers}
                                                setSelectedData={setSelectedAssociationMembers}
                                                style={{ marginBottom: '12px' }}
                                            />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <p>Relationship Types:</p>
                                            <MultiSelectCheckbox
                                                data={relationshipTypeOptions}
                                                selectedData={selectedRelationshipTypes}
                                                setSelectedData={setSelectedRelationshipTypes}
                                                style={{ marginBottom: '12px' }}
                                            />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <p>Agreement Types:</p>
                                            <MultiSelectCheckbox
                                                data={agreementTypeOptions}
                                                selectedData={selectedAgreementTypes}
                                                setSelectedData={setSelectedAgreementTypes}
                                                style={{ marginBottom: '12px' }}
                                            />
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                        <td
                            style={{
                                width: '25%',
                                verticalAlign: 'top',
                                paddingTop: '4px'
                            }}>
                            <p>Employers:</p>
                            <MultiSelectCheckbox data={employers} selectedData={selectedEmployers} setSelectedData={setSelectedEmployers} />
                        </td>
                        <td
                            style={{
                                width: '25%',
                                verticalAlign: 'top',
                                paddingTop: '4px'
                            }}>
                            <p>Agreements</p>
                            <MultiSelectCheckbox
                                data={agreements}
                                selectedData={selectedAgreements}
                                setSelectedData={setSelectedAgreements}
                            />
                        </td>
                        <td
                            style={{
                                width: '25%',
                                verticalAlign: 'top',
                                paddingTop: '4px'
                            }}>
                            <p>Classifications</p>
                            <MultiSelectCheckbox
                                data={classifications}
                                selectedData={selectedClassifications}
                                setSelectedData={setSelectedClassifications}
                            />
                        </td>
                    </tr>
                </tbody>
            </table>
            <table style={{ marginBottom: '12px' }}>
                <tbody>
                    <tr>
                        <td>
                            <Button onClick={runReport}>
                                Run Report {loadingReport ? <Loader type="infinite-spinner" size="small" themeColor="light" /> : null}
                            </Button>
                        </td>
                    </tr>
                </tbody>
            </table>
            <EPRReportGrid data={manhoursReportRows} dataState={dataState} fileName={'Manhours.xlsx'} setDataState={setDataState}>
                <EPRReportColumn field="employer" title="Employer" footerCell={employerFooterCell} />
                <EPRReportColumn field="neca" title="NECA" />
                <EPRReportColumn field="relationship" title="Relationship" />
                <EPRReportColumn field="jan" title="Jan" footerCell={totalFooterCell} />
                <EPRReportColumn field="feb" title="Feb" footerCell={totalFooterCell} />
                <EPRReportColumn field="mar" title="Mar" footerCell={totalFooterCell} />
                <EPRReportColumn field="apr" title="Apr" footerCell={totalFooterCell} />
                <EPRReportColumn field="may" title="May" footerCell={totalFooterCell} />
                <EPRReportColumn field="jun" title="Jun" footerCell={totalFooterCell} />
                <EPRReportColumn field="jul" title="Jul" footerCell={totalFooterCell} />
                <EPRReportColumn field="aug" title="Aug" footerCell={totalFooterCell} />
                <EPRReportColumn field="sep" title="Sep" footerCell={totalFooterCell} />
                <EPRReportColumn field="oct" title="Oct" footerCell={totalFooterCell} />
                <EPRReportColumn field="nov" title="Nov" footerCell={totalFooterCell} />
                <EPRReportColumn field="dec" title="Dec" footerCell={totalFooterCell} />
                <EPRReportColumn field="yearToDate" title="Year To Date" footerCell={totalFooterCell} />
            </EPRReportGrid>
        </>
    );
}

export default function ManhoursReportPage() {
    return (
        <AppLayout>
            <ManhoursReport />
        </AppLayout>
    );
}
