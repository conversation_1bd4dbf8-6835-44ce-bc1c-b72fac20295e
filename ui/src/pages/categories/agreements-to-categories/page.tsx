import { useEffect, useState, useCallback } from 'react';
import { instance } from '@/src/core/http/axios-config';
import { Constants } from '@/src/constants/global';
import { State, process as processState } from '@progress/kendo-data-query';
import { Grid, GridCellProps, GridColumn as Column, GridRowClickEvent } from '@progress/kendo-react-grid';
import DropDownListGridCell from '@/src/components/kendo/DropDownListGridCell';
import { Button } from '@progress/kendo-react-buttons';
import { Loader } from '@progress/kendo-react-indicators';
import { useStore, StoreState } from '@/lib';
import { ClientUtils } from "@/lib/core/ClientUtils";
import { Link, useNavigate } from 'react-router';
import AppLayout from '@/src/components/Layout/AppLayout';
import PageHeading from '@/src/components/UI/PageHeading';

const initialDataState: State = {
    filter: {
        filters: [],
        logic: 'and'
    },
    sort: []
};

const AgreementsToCategories = () => {
    const navigate = useNavigate();
    const [categories, setCategories] = useState<any>([]);
    const [agreementsToCategories, setAgreementsToCategories] = useState<any>([]);
    const [originalAgreementsToCategories, setOriginalAgreementsToCategories] = useState<any>([]);
    const [dataState, setDataState] = useState(initialDataState);
    const [editID, setEditID] = useState(null);
    const [saving, setSaving] = useState(false);
    const [error, setError] = useState<any>(null);
    const [categoriesDropDownItems, setCategoriesDropDownItems] = useState<any>([]);

    const user = useStore((state: StoreState) => state.user);
    const selectedChapterGuid = useStore((state: StoreState) => state.selectedChapterGuid);

    const getCategories = useCallback(() => {
        let contractsUrl = '/api/Contracts?username=' + ClientUtils.getUsername();
        if (selectedChapterGuid !== null) contractsUrl += '&GUID=' + selectedChapterGuid;
        instance.get(contractsUrl).then((response) => {
            setCategories(response.data);
        });
    }, [selectedChapterGuid]);

    const getAgreementsToCategories = useCallback(() => {
        let agreementsToCategoriesUrl = '/api/Contracts/agreements-to-contracts?username=' + ClientUtils.getUsername();
        if (selectedChapterGuid !== null) agreementsToCategoriesUrl += '&GUID=' + selectedChapterGuid;
        instance.get(agreementsToCategoriesUrl).then((response) => {
            const agreementsToCategoriesResponse = response.data;
            setAgreementsToCategories(agreementsToCategoriesResponse);
            setOriginalAgreementsToCategories(agreementsToCategoriesResponse);
        });
    }, [selectedChapterGuid]);

    useEffect(() => {
        if (user && user.roles.includes(Constants.Roles.SystemAdministrator) !== true) {
            getCategories();
            getAgreementsToCategories();
        }
    }, [user, getCategories, getAgreementsToCategories]);

    useEffect(() => {
        if (selectedChapterGuid !== null) {
            getCategories();
            getAgreementsToCategories();
        }
    }, [selectedChapterGuid, getCategories, getAgreementsToCategories]);

    useEffect(() => {
        if (agreementsToCategories) {
            setAgreementsToCategories((prevAgreementsToCategories: any[]) => 
                prevAgreementsToCategories.map((item: any) => ({
                    ...(item),
                    inEdit: item.agreementID === editID
                }))
            );
        }
    }, [editID, agreementsToCategories, setAgreementsToCategories]);

    useEffect(() => {
        setCategoriesDropDownItems(
            categories.map((c: any) => {
                return {
                    text: c.name,
                    value: c.id
                };
            })
        );
    }, [categories, setCategoriesDropDownItems]);

    const rowClick = (event: GridRowClickEvent) => {
        setEditID(event.dataItem.agreementID);
    };

    const handleDataStateChange = (e: any) => {
        setDataState(e.dataState);
    };

    const handleCategoryChange = (props: any) => {
        const updatedAgreementsToCategories = agreementsToCategories.map((item: any) => ({
            ...(item),
            contractId: item.agreementId === props.dataItem.agreementId ? props.value : item.contractId,
            contractName: item.agreementId === props.dataItem.agreementId ? props.text : item.contractName
        }));
        setAgreementsToCategories(updatedAgreementsToCategories);
    };

    const saveAgreementsToCategories = () => {
        setSaving(true);
        setError(null);
        const updatedRows: any = [];
        agreementsToCategories.forEach((a: any) => {
            if (originalAgreementsToCategories.find((o: any) => o.agreementId === a.agreementId).contractId !== a.contractId) {
                updatedRows.push(a);
            }
        });

        instance
            .post('/api/Contracts/agreements-to-contracts', {
                AgreementsToContracts: updatedRows
            })
            .then(() => {
                setOriginalAgreementsToCategories(agreementsToCategories);
                setSaving(false);
            })
            .catch((e) => {
                setSaving(false);
                setError('Something went wrong while trying to save categories.');
            });
    };

    const goBack = () => {
        // Using the external URL redirect but preserve comboboxes by opening in same window
        window.open(import.meta.env.VITE_OLD_APP_URL + '/CustomReports/NECA_AMF_Report.aspx', '_self');
    };

    const navigateToManage = () => {
        navigate('/categories/manage');
    };

    const CategoryCustomCell = (props: GridCellProps) => (
        <DropDownListGridCell {...props} onChange={handleCategoryChange} dropDownListData={categoriesDropDownItems} width="200px" />
    );

    return (
        <div style={{ margin: '20px', paddingBottom: '20px' }}>
            <PageHeading title="Agreements to Categories" />
            {error !== null ? (
                <p
                    style={{
                        color: 'red',
                        fontWeight: 'bold',
                        marginTop: '6px',
                        fontSize: '14px'
                    }}>
                    {error}
                </p>
            ) : null}
            <div style={{ marginBottom: '12px' }}>
                <Button onClick={saveAgreementsToCategories} style={{ marginRight: '12px' }}>
                    Save {saving ? <Loader type="infinite-spinner" size="small" themeColor="light" /> : null}
                </Button>
                <Button 
                    onClick={navigateToManage}
                    style={{ marginRight: '12px' }}>
                    Manage Categories
                </Button>
                <Button onClick={goBack}>Back</Button>
            </div>
	    {/* @ts-ignore The type definitions for Kendo Grid v4.14.1 seem incorrect regarding children */}
            <Grid
                data={processState(agreementsToCategories, dataState)}
                {...dataState}
                sortable={{
                    mode: 'multiple'
                }}
                onDataStateChange={handleDataStateChange}
                onRowClick={rowClick}
                editField="inEdit">
                <Column field="agreementName" title="Agreement" editable={false} />
                <Column field="contractName" title="Category" cell={CategoryCustomCell} />
            </Grid>
        </div>
    );
};

export default function AgreementsToCategoriesPage() {
    return (
        <AppLayout>
            <AgreementsToCategories />
        </AppLayout>
    );
}
