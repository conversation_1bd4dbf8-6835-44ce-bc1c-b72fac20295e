import { process, State } from '@progress/kendo-data-query';
import { But<PERSON> } from '@progress/kendo-react-buttons';
import { Dialog } from '@progress/kendo-react-dialogs';
import { Grid, GridCellProps, GridColumn as Column, GridRowClickEvent } from '@progress/kendo-react-grid';
import { Loader } from '@progress/kendo-react-indicators';
import { useEffect, useState, useCallback } from 'react';
import { instance } from '@/src/core/http/axios-config';
import DropDownListGridCell from '@/src/components/kendo/DropDownListGridCell';
import CategoriesCommandCell from './CategoriesCommandCell';
import NewCategoryDialog from './NewCategoryDialog';
import { useStore, StoreState } from '@/lib';
import { Constants } from '@/src/constants/global';
import { ClientUtils } from "@/lib/core/ClientUtils";
import AppLayout from '@/src/components/Layout/AppLayout';
import PageHeading from '@/src/components/UI/PageHeading';
import { useNavigate } from 'react-router';

//TODO: Future improvements
// Auto saving both grids

interface Category {
    id: number;
    name: string;
    chapterID: string | null;
    necaBenefitID: number | null;
    necaBenefit: string | null;
    amfBenefitID: number | null;
    amfBenefit: string | null;
    inEdit?: boolean;
}

interface Benefit {
    text: string;
    value: number;
}

const initialDataState: State = {
    filter: {
        filters: [],
        logic: 'and'
    },
    sort: []
};

const ManageCategories = () => {
    const navigate = useNavigate();
    const [categories, setCategories] = useState<Category[]>([]);
    const [benefits, setBenefits] = useState<Benefit[]>([]);
    const [dataState, setDataState] = useState<State>(initialDataState);
    const [editID, setEditID] = useState<number | null>(null);
    const [deletingCategoryID, setDeletingCategoryID] = useState<number | null>(null);
    const [deleting, setDeleting] = useState<boolean>(false);
    const [showDeleteCategoryDialog, setShowDeleteCategoryDialog] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const [savingCategories, setSavingCategories] = useState<boolean>(false);
    const [originalCategories, setOriginalCategories] = useState<Category[]>([]);

    const user = useStore((state: StoreState) => state.user);
    const userChapterId = user?.chapterId || 0;
    const selectedChapterGuid = useStore((state: StoreState) => state.selectedChapterGuid);
    const setSelectedChapterGuid = useStore((state: StoreState) => state.setSelectedChapterGuid);
    const chapterId = ClientUtils.initializeChapter(user?.roles || [], userChapterId.toString(), selectedChapterGuid, setSelectedChapterGuid);

    const getCategories = useCallback(() => {
        if (!chapterId) return;

        let contractsUrl = '/api/Contracts?username=' + ClientUtils.getUsername();
        contractsUrl += '&GUID=' + chapterId;

        instance.get<Category[]>(contractsUrl).then((response) => {
            setCategories(response.data);
            setOriginalCategories(response.data);
        });
    }, [chapterId]);

    const getBenefits = useCallback(() => {
        if (!chapterId) return;

        let benefitsUrl = '/api/Benefits?username=' + ClientUtils.getUsername();
        benefitsUrl += '&GUID=' + chapterId;

        instance.get(benefitsUrl).then((response) => {
            const benefitsResponse = response.data.map((b: any) => ({
                text: b.name,
                value: b.id
            }));
            setBenefits(benefitsResponse);
        });
    }, [chapterId]);

    useEffect(() => {
        if (user && user.roles.includes(Constants.Roles.SystemAdministrator) !== true && chapterId) {
            getCategories();
            getBenefits();
        }
    }, [user, getBenefits, getCategories, chapterId]);

    useEffect(() => {
        if (chapterId) {
            getCategories();
            getBenefits();
        }
    }, [chapterId, getBenefits, getCategories]);

    useEffect(() => {
        if (categories) {
            setCategories((prevCategories) =>
                prevCategories.map((item: Category) => ({
                    ...item,
                    inEdit: item.id === editID
                }))
            );
        }
    }, [editID, categories, setCategories]);

    const handleDataStateChange = (e: { dataState: State }) => {
        setDataState(e.dataState);
    };

    const rowClick = (event: GridRowClickEvent) => {
        setEditID(event.dataItem.id);
    };

    const itemChange = (e: { field?: string; dataItem: Category; value: any }) => {
        const field = e.field || '';
        setCategories((prevCategories) => prevCategories.map((c) => (c.id === e.dataItem.id ? { ...c, [field]: e.value } : c)));
    };

    const handleNECABenefitChange = (props: { dataItem: Category; value: number; text: string }) => {
        setCategories((prevCategories) =>
            prevCategories.map((item) => ({
                ...item,
                necaBenefitID: item.id === props.dataItem.id ? props.value : item.necaBenefitID,
                necaBenefit: item.id === props.dataItem.id ? props.text : item.necaBenefit
            }))
        );
    };

    const handleAMFBenefitChange = (props: { dataItem: Category; value: number; text: string }) => {
        setCategories((prevCategories) =>
            prevCategories.map((item) => ({
                ...item,
                amfBenefitID: item.id === props.dataItem.id ? props.value : item.amfBenefitID,
                amfBenefit: item.id === props.dataItem.id ? props.text : item.amfBenefit
            }))
        );
    };

    const saveCategories = () => {
        setSavingCategories(true);
        setError(null);
        const updatedRows = categories.filter((c) => {
            const originalCategory = originalCategories.find((o) => o.id === c.id);
            return (
                originalCategory &&
                (originalCategory.name !== c.name ||
                    originalCategory.necaBenefitID !== c.necaBenefitID ||
                    originalCategory.amfBenefitID !== c.amfBenefitID)
            );
        });

        instance
            .post('/api/Contracts/update', {
                Contracts: updatedRows
            })
            .then(() => {
                setOriginalCategories(categories);
                setSavingCategories(false);
            })
            .catch(() => {
                setSavingCategories(false);
                setError('Something went wrong while trying to update categories');
            });
    };

    const handleDeleteCategory = (id: any) => {
        setDeletingCategoryID(id);
        setShowDeleteCategoryDialog(true);
    };

    const cancelDelete = () => {
        setDeletingCategoryID(null);
        setShowDeleteCategoryDialog(false);
    };

    const deleteCategory = () => {
        const url = '/api/Contracts?Id=' + deletingCategoryID;
        setDeleting(true);
        setShowDeleteCategoryDialog(false);
        instance.delete(url).then((response) => {
            if (response.status >= 200 && response.status < 300) {
                const updatedCategories = categories.filter((c: any) => {
                    return c.id !== deletingCategoryID;
                });
                setCategories(updatedCategories);
            }
            setDeletingCategoryID(null);
            setDeleting(false);
        });
    };

    const NECABenefitCustomCell = (props: GridCellProps) => (
        <DropDownListGridCell {...props} onChange={handleNECABenefitChange} dropDownListData={benefits} width="200px" />
    );

    const AMFBenefitCustomCell = (props: GridCellProps) => (
        <DropDownListGridCell {...props} onChange={handleAMFBenefitChange} dropDownListData={benefits} width="200px" />
    );

    const CommandCell = (props: any) => (
        <CategoriesCommandCell
            {...props}
            handleDeleteCategory={handleDeleteCategory}
            deleting={deletingCategoryID === props.dataItem.id && deleting}
        />
    );

    const navigateToAgreementsCategories = () => {
        navigate('/categories/agreements-to-categories');
    };

    return (
        <div style={{ margin: '20px', paddingBottom: '20px' }}>
            <PageHeading title="Manage Categories" />
            <p>
                When calculating year to date hours, the report will accumulate hours for AMF by category, NECA will be across categories.
            </p>
            {error !== null ? (
                <p
                    style={{
                        color: 'red',
                        fontWeight: 'bold',
                        marginTop: '6px',
                        fontSize: '14px'
                    }}>
                    {error}
                </p>
            ) : null}
            <div style={{ marginBottom: '12px' }}>
                <NewCategoryDialog benefits={benefits} chapter={chapterId} categoriesUpdated={getCategories} />
            </div>
            <div style={{ marginBottom: '12px' }}>
                <Button onClick={saveCategories} style={{ marginRight: '12px' }}>
                    SAVE {savingCategories ? <Loader type="infinite-spinner" size="small" themeColor="light" /> : null}
                </Button>
                <Button onClick={navigateToAgreementsCategories}>
                    BACK
                </Button>
            </div>
            {/* @ts-ignore The type definitions for Kendo Grid v4.14.1 seem incorrect regarding children */}
            <Grid
                data={process(
                    categories.filter((c: any) => c.chapterID !== null),
                    dataState
                )}
                {...dataState}
                sortable={{
                    mode: 'multiple'
                }}
                onDataStateChange={handleDataStateChange}
                onItemChange={itemChange}
                onRowClick={rowClick}
                editField="inEdit">
                <Column field="id" title="ID" width="50px" editable={false} filterable={false} />
                <Column field="name" title="Name" />
                <Column field="necaBenefit" title="NECA Benefit" cell={NECABenefitCustomCell} />
                <Column field="amfBenefit" title="AMF Benefit" cell={AMFBenefitCustomCell} />
                <Column cell={CommandCell} width="160px" filterable={false} sortable={false} />
            </Grid>
            {showDeleteCategoryDialog ? (
                <Dialog title="Confirm Delete Category" onClose={cancelDelete}>
                    <p>Are you sure you want to delete this category?</p>
                    <p>Note that this will remove all associations with agreements mapped to this category.</p>
                    <Button onClick={deleteCategory} style={{ marginRight: '8px' }}>
                        Confirm
                    </Button>
                    <Button onClick={cancelDelete}>Cancel</Button>
                </Dialog>
            ) : null}
        </div>
    );
};

export default function ManageCategoriesPage() {
    return (
        <AppLayout>
            <ManageCategories />
        </AppLayout>
    );
}
