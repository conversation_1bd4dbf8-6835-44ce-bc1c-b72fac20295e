import { Button } from '@progress/kendo-react-buttons';
import { Dialog } from '@progress/kendo-react-dialogs';
import { Input } from '@progress/kendo-react-inputs';
import { useState } from 'react';
import { instance } from '@/src/core/http/axios-config';
import FilterableComboBox from '@/src/components/kendo/FilterableComboBox';
import { ClientUtils } from "@/lib/core/ClientUtils";

const NewCategoryDialog = ({ benefits, chapter, categoriesUpdated }: any) => {
    const [name, setName] = useState<any>(null);
    const [necaBenefitID, setNECABenefitID] = useState(null);
    const [amfBenefitID, setAMFBenefitID] = useState(null);

    const [open, setOpen] = useState(false);
    const [saving, setSaving] = useState(false);
    const [error, setError] = useState<any>(null);

    const toggleDialog = () => {
        setError(null);
        setOpen(!open);
    };

    const openNewCategoryPopup = () => {
        setOpen(true);
    };

    const save = () => {
        setSaving(true);
        const url = '/api/Contracts';
        const body: any = {
            Name: name,
            NECABenefitID: necaBenefitID,
            AMFBenefitID: amfBenefitID,
            username: ClientUtils.getUsername()
        };
        if (chapter !== null) {
            body['GUID'] = chapter;
        }
        instance
            .post(url, body)
            .then((response) => {
                setSaving(false);
                categoriesUpdated();
                toggleDialog();
            })
            .catch((e) => {
                setSaving(false);
                setError('Something went wrong while saving category');
            });
    };

    const closeDialog = () => {
        setName(null);
        setNECABenefitID(null);
        setAMFBenefitID(null);
        toggleDialog();
    };

    const dialog = open ? (
        <Dialog title={'New Category'} width={400} height={400} onClose={toggleDialog}>
            {error !== null ? (
                <p
                    style={{
                        color: 'red',
                        fontWeight: 'bold',
                        marginTop: '6px',
                        fontSize: '14px'
                    }}>
                    {error}
                </p>
            ) : null}
            <div style={{ marginBottom: '12px' }}>
                <Input
                    placeholder="Name"
                    style={{ width: '300px' }}
                    onChange={(e) => {
                        setName(e.value);
                    }}
                />
            </div>
            <div style={{ marginBottom: '12px' }}>
                <h4>NECA Benefit</h4>
                <FilterableComboBox
                    data={benefits}
                    width="300px"
                    selectedValue={necaBenefitID}
                    onChange={(e) => {
                        if (e.value) {
                            setNECABenefitID(e.value.value);
                        }
                    }}
                />
            </div>
            <div style={{ marginBottom: '24px' }}>
                <h4>AMF Benefit</h4>
                <FilterableComboBox
                    data={benefits}
                    width="300px"
                    selectedValue={amfBenefitID}
                    onChange={(e) => {
                        if (e.value) {
                            setAMFBenefitID(e.value.value);
                        }
                    }}
                />
            </div>
            <div style={{ float: 'right', marginBottom: '12px' }}>
                <Button onClick={save}>Save</Button>
                <Button onClick={closeDialog} style={{ marginLeft: '8px' }}>
                    Cancel
                </Button>
            </div>
        </Dialog>
    ) : null;

    return (
        <>
            <Button onClick={openNewCategoryPopup}>Add Category</Button>
            {dialog}
        </>
    );
};

export default NewCategoryDialog;
