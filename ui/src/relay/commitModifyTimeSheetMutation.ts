import { graphql } from 'react-relay';

const commitModifyTimeSheetMutation = graphql`
    mutation commitModifyTimeSheetMutation($input: ModifyTimeSheetInput!) {
        modifyTimeSheet(input: $input) {
            # This mutation returns the ModifyTimeSheetPayload type
            # Access the 'timeSheet' field directly within the payload
            timeSheet {
                # This is the TimeSheet object
                # We need to select the fields that were modified to update the store
                # Also include the fragment spread for TimeSheetSettings to ensure consistency
                id
                ...TimeSheetSettings_timeSheet # Re-added fragment spread
            }
            # If clientMutationId were present (it should be with conventions, but isn't yet),
            # you would select it here too:
            # clientMutationId
        }
    }
`;

export { commitModifyTimeSheetMutation };
