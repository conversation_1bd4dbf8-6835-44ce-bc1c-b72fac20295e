"""
The `@defer` directive may be provided for fragment spreads and inline fragments
to inform the executor to delay the execution of the current fragment to
indicate deprioritization of the current fragment. A query with `@defer`
directive will cause the request to potentially return multiple responses, where
non-deferred data is delivered in the initial response and data deferred is
delivered in a subsequent response. `@include` and `@skip` take precedence over `@defer`.
"""
directive @defer(
  """
  If this argument label has a value other than null, it will be passed on to
  the result of this defer directive. This label is intended to give client
  applications a way to identify to which fragment a deferred result belongs to.
  """
  label: String

  """Deferred when true."""
  if: Boolean
) on FRAGMENT_SPREAD | INLINE_FRAGMENT

"""
The `@stream` directive may be provided for a field of `List` type so that the
backend can leverage technology such as asynchronous iterators to provide a
partial list in the initial response, and additional list items in subsequent
responses. `@include` and `@skip` take precedence over `@stream`.
"""
directive @stream(
  """
  If this argument label has a value other than null, it will be passed on to
  the result of this stream directive. This label is intended to give client
  applications a way to identify to which fragment a streamed result belongs to.
  """
  label: String

  """The initial elements that shall be send down to the consumer."""
  initialCount: Int! = 0

  """Streamed when true."""
  if: Boolean
) on FIELD

"""
The purpose of the `cost` directive is to define a `weight` for GraphQL types,
fields, and arguments. Static analysis can use these weights when calculating
the overall cost of a query or response.
"""
directive @cost(
  """
  The `weight` argument defines what value to add to the overall cost for every
  appearance, or possible appearance, of a type, field, argument, etc.
  """
  weight: String!
) on SCALAR | OBJECT | FIELD_DEFINITION | ARGUMENT_DEFINITION | ENUM | INPUT_FIELD_DEFINITION

"""
The purpose of the `@listSize` directive is to either inform the static analysis
about the size of returned lists (if that information is statically available),
or to point the analysis to where to find that information.
"""
directive @listSize(
  """
  The `assumedSize` argument can be used to statically define the maximum length of a list returned by a field.
  """
  assumedSize: Int

  """
  The `slicingArguments` argument can be used to define which of the field's
  arguments with numeric type are slicing arguments, so that their value
  determines the size of the list returned by that field. It may specify a list
  of multiple slicing arguments.
  """
  slicingArguments: [String!]

  """
  The `slicingArgumentDefaultValue` argument can be used to define a default
  value for a slicing argument, which is used if the argument is not present in a query.
  """
  slicingArgumentDefaultValue: Int

  """
  The `sizedFields` argument can be used to define that the value of the
  `assumedSize` argument or of a slicing argument does not affect the size of a
  list returned by a field itself, but that of a list returned by one of its sub-fields.
  """
  sizedFields: [String!]

  """
  The `requireOneSlicingArgument` argument can be used to inform the static
  analysis that it should expect that exactly one of the defined slicing
  arguments is present in a query. If that is not the case (i.e., if none or
  multiple slicing arguments are present), the static analysis may throw an error.
  """
  requireOneSlicingArgument: Boolean! = true
) on FIELD_DEFINITION

"""
The `@specifiedBy` directive is used within the type system definition language
to provide a URL for specifying the behavior of custom scalar definitions.
"""
directive @specifiedBy(
  """
  The specifiedBy URL points to a human-readable specification. This field will only read a result for scalar types.
  """
  url: String!
) on SCALAR

input AddChapterInput {
  name: String!
  limited: Boolean!
  username: String!
}

type AddChapterPayload {
  chaptersInfoDtoEdge: EdgeOfChaptersInfoDto
}

input AddEmployerInput {
  employer: EmployerInput!
  chapterId: ID!
}

type AddEmployerPayload {
  employerRosterViewEdge: EdgeOfEmployerRosterView
}

input AddEmptyPayStubInput {
  timeSheetId: ID!
  employeeId: ID!
}

type AddEmptyPayStubPayload {
  payStubEdge: EdgeOfPayStub
  errors: [String!]!
}

input AddFundAdministratorInput {
  input: AddThirdPartyInput!
}

type AddFundAdministratorPayload {
  thirdPartyInfoDtoEdge: EdgeOfThirdPartyInfoDto
}

input AddPayStubDetailInput {
  id: ID
  name: String
  workDate: LocalDate!
  otHours: Float
  stHours: Float
  dtHours: Float
  jobCode: String
  earningsCode: String
  agreementId: Int
  classificationId: Int
  subClassificationId: Int
  costCenter: String
  hourlyRate: Float
  bonus: Float
  expenses: Float
  payStubId: ID
  reportLineItemId: Int
  delete: Boolean
}

input AddPayStubInput {
  id: ID
  employeeId: ID!
  employeeName: String
  inEdit: Boolean
  expanded: Boolean
  name: String
  details: [AddPayStubDetailInput!]
  delete: Boolean
  stHours: Float
  otHours: Float
  dtHours: Float
  bonus: Float
  expenses: Float
}

type Address {
  id: Int!
  addressLines: String
  city: String
  province: String
  postalCode: String
  county: String
  dcountryId: UUID!
  daddressTypeId: Int!
  daddressType: DaddressType!
  dcountry: Dcountry!
  idNavigation: ContactMechanism!
}

input AddressFilterInput {
  and: [AddressFilterInput!]
  or: [AddressFilterInput!]
  id: IntOperationFilterInput
  addressLines: StringOperationFilterInput
  city: StringOperationFilterInput
  province: StringOperationFilterInput
  postalCode: StringOperationFilterInput
  county: StringOperationFilterInput
  dcountryId: UuidOperationFilterInput
  daddressTypeId: IntOperationFilterInput
  daddressType: DaddressTypeFilterInput
  dcountry: DcountryFilterInput
  idNavigation: ContactMechanismFilterInput
}

input AddressInput {
  id: Int!
  addressLines: String
  city: String
  province: String
  postalCode: String
  county: String
  dcountryId: UUID!
  daddressTypeId: Int!
  daddressType: DaddressTypeInput!
  dcountry: DcountryInput!
  idNavigation: ContactMechanismInput!
}

input AddressSortInput {
  id: SortEnumType
  addressLines: SortEnumType
  city: SortEnumType
  province: SortEnumType
  postalCode: SortEnumType
  county: SortEnumType
  dcountryId: SortEnumType
  daddressTypeId: SortEnumType
  daddressType: DaddressTypeSortInput
  dcountry: DcountrySortInput
  idNavigation: ContactMechanismSortInput
}

input AddThirdPartyInput {
  name: String!
  chapterId: Int!
  username: String!
}

input AddTimesheetInput {
  employerGuid: UUID!
  name: String
  payPeriodEndDate: LocalDate!
  status: String!
  type: String!
  showDTHoursColumn: Boolean
  showCostCenterColumn: Boolean
  showBonusColumn: Boolean
  showExpensesColumn: Boolean
  showEarningsCodesColumn: Boolean
  readOnly: Boolean
  payStubs: [AddPayStubInput!]
  sourceTimesheetId: Int
  isCopy: Boolean
  includeJobCodes: Boolean
  includeHours: Boolean
}

type AddTimesheetPayload {
  timeSheetEdge: EdgeOfTimeSheet
}

input AddUnionInput {
  input: AddThirdPartyInput!
}

type AddUnionPayload {
  thirdPartyInfoDtoEdge: EdgeOfThirdPartyInfoDto
}

type Agreement implements Node {
  id: ID!
  name: String!
  chapterId: Int!
  unionId: Int!
  dagreementTypeId: Int!
  effectiveStartDate: DateTime!
  effectiveEndDate: DateTime
  suppressFromEmployerRoster: Boolean!
  requiresSignatoryStatus: Boolean
  allowCurrentMonthReporting: Boolean
  adHoc: Boolean!
  allowPublishing: Boolean
  unionContactId: Int
  weekly: Boolean!
  certificationLanguage: String
  allowDuplicateLineItems: Boolean!
  disableEmployerAmendments: Boolean!
  agreementsToBenefits: [AgreementsToBenefit!]!
  chapter: Chapter!
  costCodes: [CostCode!]!
  dagreementType: DagreementType!
  employersToAgreements: [EmployersToAgreement!]!
  fundingComments: [FundingComment!]!
  idNavigation: Root!
  rateSchedules: [RateSchedule!]!
  reports: [Report!]!
  union: Union!
  unionContact: Employee
  organizations: [Organization!]!
}

"""A connection to a list of items."""
type AgreementConnection {
  """Information to aid in pagination."""
  pageInfo: PageInfo!

  """A list of edges."""
  edges: [AgreementEdge!]

  """A flattened list of the nodes."""
  nodes: [Agreement!]

  """Identifies the total count of items in the connection."""
  totalCount: Int!
}

"""An edge in a connection."""
type AgreementEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of the edge."""
  node: Agreement!
}

input AgreementFilterInput {
  and: [AgreementFilterInput!]
  or: [AgreementFilterInput!]
  id: IntOperationFilterInput
  name: StringOperationFilterInput
  chapterId: IntOperationFilterInput
  unionId: IntOperationFilterInput
  dagreementTypeId: IntOperationFilterInput
  effectiveStartDate: DateTimeOperationFilterInput
  effectiveEndDate: DateTimeOperationFilterInput
  suppressFromEmployerRoster: BooleanOperationFilterInput
  requiresSignatoryStatus: BooleanOperationFilterInput
  allowCurrentMonthReporting: BooleanOperationFilterInput
  adHoc: BooleanOperationFilterInput
  allowPublishing: BooleanOperationFilterInput
  unionContactId: IntOperationFilterInput
  weekly: BooleanOperationFilterInput
  certificationLanguage: StringOperationFilterInput
  allowDuplicateLineItems: BooleanOperationFilterInput
  disableEmployerAmendments: BooleanOperationFilterInput
  agreementsToBenefits: ListFilterInputTypeOfAgreementsToBenefitFilterInput
  chapter: ChapterFilterInput
  costCodes: ListFilterInputTypeOfCostCodeFilterInput
  dagreementType: DagreementTypeFilterInput
  employersToAgreements: ListFilterInputTypeOfEmployersToAgreementFilterInput
  fundingComments: ListFilterInputTypeOfFundingCommentFilterInput
  idNavigation: RootFilterInput
  rateSchedules: ListFilterInputTypeOfRateScheduleFilterInput
  reports: ListFilterInputTypeOfReportFilterInput
  union: UnionFilterInput
  unionContact: EmployeeFilterInput
  organizations: ListFilterInputTypeOfOrganizationFilterInput
}

input AgreementInput {
  id: Int!
  name: String!
  chapterId: Int!
  unionId: Int!
  dagreementTypeId: Int!
  effectiveStartDate: DateTime!
  effectiveEndDate: DateTime
  suppressFromEmployerRoster: Boolean!
  requiresSignatoryStatus: Boolean
  allowCurrentMonthReporting: Boolean
  adHoc: Boolean!
  allowPublishing: Boolean
  unionContactId: Int
  weekly: Boolean!
  certificationLanguage: String
  allowDuplicateLineItems: Boolean!
  disableEmployerAmendments: Boolean!
  agreementsToBenefits: [AgreementsToBenefitInput!]!
  chapter: ChapterInput!
  costCodes: [CostCodeInput!]!
  dagreementType: DagreementTypeInput!
  employersToAgreements: [EmployersToAgreementInput!]!
  fundingComments: [FundingCommentInput!]!
  idNavigation: RootInput!
  rateSchedules: [RateScheduleInput!]!
  reports: [ReportInput!]!
  union: UnionInput!
  unionContact: EmployeeInput
  organizations: [OrganizationInput!]!
}

input AgreementsByEmployerInput {
  employerIds: [Int!]!
  year: Int
  chapterId: Int
}

type AgreementSimpleId {
  id: Int!
  name: String!
}

"""A connection to a list of items."""
type AgreementSimpleIdConnection {
  """Information to aid in pagination."""
  pageInfo: PageInfo!

  """A list of edges."""
  edges: [AgreementSimpleIdEdge!]

  """A flattened list of the nodes."""
  nodes: [AgreementSimpleId!]

  """Identifies the total count of items in the connection."""
  totalCount: Int!
}

"""An edge in a connection."""
type AgreementSimpleIdEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of the edge."""
  node: AgreementSimpleId!
}

input AgreementSimpleIdFilterInput {
  and: [AgreementSimpleIdFilterInput!]
  or: [AgreementSimpleIdFilterInput!]
  id: IntOperationFilterInput
  name: StringOperationFilterInput
}

input AgreementSimpleIdSortInput {
  id: SortEnumType
  name: SortEnumType
}

input AgreementSortInput {
  id: SortEnumType
  name: SortEnumType
  chapterId: SortEnumType
  unionId: SortEnumType
  dagreementTypeId: SortEnumType
  effectiveStartDate: SortEnumType
  effectiveEndDate: SortEnumType
  suppressFromEmployerRoster: SortEnumType
  requiresSignatoryStatus: SortEnumType
  allowCurrentMonthReporting: SortEnumType
  adHoc: SortEnumType
  allowPublishing: SortEnumType
  unionContactId: SortEnumType
  weekly: SortEnumType
  certificationLanguage: SortEnumType
  allowDuplicateLineItems: SortEnumType
  disableEmployerAmendments: SortEnumType
  chapter: ChapterSortInput
  dagreementType: DagreementTypeSortInput
  idNavigation: RootSortInput
  union: UnionSortInput
  unionContact: EmployeeSortInput
}

type AgreementsToBenefit {
  agreementId: Int!
  benefitId: Int!
  collectingAgentId: Int
  fundAdministratorId: Int
  remitToInstructions: String
  agreement: Agreement!
  benefit: Benefit!
  collectingAgent: Organization
  fundAdministrator: Organization
}

input AgreementsToBenefitFilterInput {
  and: [AgreementsToBenefitFilterInput!]
  or: [AgreementsToBenefitFilterInput!]
  agreementId: IntOperationFilterInput
  benefitId: IntOperationFilterInput
  collectingAgentId: IntOperationFilterInput
  fundAdministratorId: IntOperationFilterInput
  remitToInstructions: StringOperationFilterInput
  agreement: AgreementFilterInput
  benefit: BenefitFilterInput
  collectingAgent: OrganizationFilterInput
  fundAdministrator: OrganizationFilterInput
}

input AgreementsToBenefitInput {
  agreementId: Int!
  benefitId: Int!
  collectingAgentId: Int
  fundAdministratorId: Int
  remitToInstructions: String
  agreement: AgreementInput!
  benefit: BenefitInput!
  collectingAgent: OrganizationInput
  fundAdministrator: OrganizationInput
}

scalar Any

"""Defines when a policy shall be executed."""
enum ApplyPolicy {
  """Before the resolver was executed."""
  BEFORE_RESOLVER

  """After the resolver was executed."""
  AFTER_RESOLVER

  """The policy is applied in the validation step before the execution."""
  VALIDATION
}

type AspnetApplication {
  applicationName: String!
  loweredApplicationName: String!
  applicationId: UUID!
  description: String
  aspnetMemberships: [AspnetMembership!]!
  aspnetPaths: [AspnetPath!]!
  aspnetRoles: [AspnetRole!]!
  aspnetUsers: [AspnetUser!]!
}

input AspnetApplicationFilterInput {
  and: [AspnetApplicationFilterInput!]
  or: [AspnetApplicationFilterInput!]
  applicationName: StringOperationFilterInput
  loweredApplicationName: StringOperationFilterInput
  applicationId: UuidOperationFilterInput
  description: StringOperationFilterInput
  aspnetMemberships: ListFilterInputTypeOfAspnetMembershipFilterInput
  aspnetPaths: ListFilterInputTypeOfAspnetPathFilterInput
  aspnetRoles: ListFilterInputTypeOfAspnetRoleFilterInput
  aspnetUsers: ListFilterInputTypeOfAspnetUserFilterInput
}

input AspnetApplicationInput {
  applicationName: String!
  loweredApplicationName: String!
  applicationId: UUID!
  description: String
  aspnetMemberships: [AspnetMembershipInput!]!
  aspnetPaths: [AspnetPathInput!]!
  aspnetRoles: [AspnetRoleInput!]!
  aspnetUsers: [AspnetUserInput!]!
}

type AspnetMembership {
  applicationId: UUID!
  userId: UUID!
  passwordFormat: Int!
  passwordSalt: String!
  mobilePin: String
  email: String
  loweredEmail: String
  passwordQuestion: String
  passwordAnswer: String
  isApproved: Boolean!
  isLockedOut: Boolean!
  createDate: DateTime!
  lastLoginDate: DateTime!
  lastPasswordChangedDate: DateTime!
  lastLockoutDate: DateTime!
  failedPasswordAttemptCount: Int!
  failedPasswordAttemptWindowStart: DateTime!
  failedPasswordAnswerAttemptCount: Int!
  failedPasswordAnswerAttemptWindowStart: DateTime!
  comment: String
  password: String!
  application: AspnetApplication!
  user: AspnetUser!
}

input AspnetMembershipFilterInput {
  and: [AspnetMembershipFilterInput!]
  or: [AspnetMembershipFilterInput!]
  applicationId: UuidOperationFilterInput
  userId: UuidOperationFilterInput
  passwordFormat: IntOperationFilterInput
  passwordSalt: StringOperationFilterInput
  mobilePin: StringOperationFilterInput
  email: StringOperationFilterInput
  loweredEmail: StringOperationFilterInput
  passwordQuestion: StringOperationFilterInput
  passwordAnswer: StringOperationFilterInput
  isApproved: BooleanOperationFilterInput
  isLockedOut: BooleanOperationFilterInput
  createDate: DateTimeOperationFilterInput
  lastLoginDate: DateTimeOperationFilterInput
  lastPasswordChangedDate: DateTimeOperationFilterInput
  lastLockoutDate: DateTimeOperationFilterInput
  failedPasswordAttemptCount: IntOperationFilterInput
  failedPasswordAttemptWindowStart: DateTimeOperationFilterInput
  failedPasswordAnswerAttemptCount: IntOperationFilterInput
  failedPasswordAnswerAttemptWindowStart: DateTimeOperationFilterInput
  comment: StringOperationFilterInput
  password: StringOperationFilterInput
  application: AspnetApplicationFilterInput
  user: AspnetUserFilterInput
}

input AspnetMembershipInput {
  applicationId: UUID!
  userId: UUID!
  passwordFormat: Int!
  passwordSalt: String!
  mobilePin: String
  email: String
  loweredEmail: String
  passwordQuestion: String
  passwordAnswer: String
  isApproved: Boolean!
  isLockedOut: Boolean!
  createDate: DateTime!
  lastLoginDate: DateTime!
  lastPasswordChangedDate: DateTime!
  lastLockoutDate: DateTime!
  failedPasswordAttemptCount: Int!
  failedPasswordAttemptWindowStart: DateTime!
  failedPasswordAnswerAttemptCount: Int!
  failedPasswordAnswerAttemptWindowStart: DateTime!
  comment: String
  password: String!
  application: AspnetApplicationInput!
  user: AspnetUserInput!
}

type AspnetPath {
  applicationId: UUID!
  pathId: UUID!
  path: String!
  loweredPath: String!
  application: AspnetApplication!
  aspnetPersonalizationAllUser: AspnetPersonalizationAllUser
  aspnetPersonalizationPerUsers: [AspnetPersonalizationPerUser!]!
}

input AspnetPathFilterInput {
  and: [AspnetPathFilterInput!]
  or: [AspnetPathFilterInput!]
  applicationId: UuidOperationFilterInput
  pathId: UuidOperationFilterInput
  path: StringOperationFilterInput
  loweredPath: StringOperationFilterInput
  application: AspnetApplicationFilterInput
  aspnetPersonalizationAllUser: AspnetPersonalizationAllUserFilterInput
  aspnetPersonalizationPerUsers: ListFilterInputTypeOfAspnetPersonalizationPerUserFilterInput
}

input AspnetPathInput {
  applicationId: UUID!
  pathId: UUID!
  path: String!
  loweredPath: String!
  application: AspnetApplicationInput!
  aspnetPersonalizationAllUser: AspnetPersonalizationAllUserInput
  aspnetPersonalizationPerUsers: [AspnetPersonalizationPerUserInput!]!
}

type AspnetPersonalizationAllUser {
  pathId: UUID!
  pageSettings: [Byte!]!
  lastUpdatedDate: DateTime!
  path: AspnetPath!
}

input AspnetPersonalizationAllUserFilterInput {
  and: [AspnetPersonalizationAllUserFilterInput!]
  or: [AspnetPersonalizationAllUserFilterInput!]
  pathId: UuidOperationFilterInput
  pageSettings: ListByteOperationFilterInput
  lastUpdatedDate: DateTimeOperationFilterInput
  path: AspnetPathFilterInput
}

input AspnetPersonalizationAllUserInput {
  pathId: UUID!
  pageSettings: [Byte!]!
  lastUpdatedDate: DateTime!
  path: AspnetPathInput!
}

type AspnetPersonalizationPerUser {
  id: UUID!
  pathId: UUID
  userId: UUID
  pageSettings: [Byte!]!
  lastUpdatedDate: DateTime!
  path: AspnetPath
  user: AspnetUser
}

input AspnetPersonalizationPerUserFilterInput {
  and: [AspnetPersonalizationPerUserFilterInput!]
  or: [AspnetPersonalizationPerUserFilterInput!]
  id: UuidOperationFilterInput
  pathId: UuidOperationFilterInput
  userId: UuidOperationFilterInput
  pageSettings: ListByteOperationFilterInput
  lastUpdatedDate: DateTimeOperationFilterInput
  path: AspnetPathFilterInput
  user: AspnetUserFilterInput
}

input AspnetPersonalizationPerUserInput {
  id: UUID!
  pathId: UUID
  userId: UUID
  pageSettings: [Byte!]!
  lastUpdatedDate: DateTime!
  path: AspnetPathInput
  user: AspnetUserInput
}

type AspnetProfile {
  userId: UUID!
  propertyNames: String!
  propertyValuesString: String!
  propertyValuesBinary: [Byte!]!
  lastUpdatedDate: DateTime!
  user: AspnetUser!
}

input AspnetProfileFilterInput {
  and: [AspnetProfileFilterInput!]
  or: [AspnetProfileFilterInput!]
  userId: UuidOperationFilterInput
  propertyNames: StringOperationFilterInput
  propertyValuesString: StringOperationFilterInput
  propertyValuesBinary: ListByteOperationFilterInput
  lastUpdatedDate: DateTimeOperationFilterInput
  user: AspnetUserFilterInput
}

input AspnetProfileInput {
  userId: UUID!
  propertyNames: String!
  propertyValuesString: String!
  propertyValuesBinary: [Byte!]!
  lastUpdatedDate: DateTime!
  user: AspnetUserInput!
}

type AspnetRole {
  applicationId: UUID!
  roleId: UUID!
  roleName: String!
  loweredRoleName: String!
  description: String
  application: AspnetApplication!
  roleGroups: [RoleGroup!]!
  users: [AspnetUser!]!
}

input AspnetRoleFilterInput {
  and: [AspnetRoleFilterInput!]
  or: [AspnetRoleFilterInput!]
  applicationId: UuidOperationFilterInput
  roleId: UuidOperationFilterInput
  roleName: StringOperationFilterInput
  loweredRoleName: StringOperationFilterInput
  description: StringOperationFilterInput
  application: AspnetApplicationFilterInput
  roleGroups: ListFilterInputTypeOfRoleGroupFilterInput
  users: ListFilterInputTypeOfAspnetUserFilterInput
}

input AspnetRoleInput {
  applicationId: UUID!
  roleId: UUID!
  roleName: String!
  loweredRoleName: String!
  description: String
  application: AspnetApplicationInput!
  roleGroups: [RoleGroupInput!]!
  users: [AspnetUserInput!]!
}

type AspnetUser {
  applicationId: UUID!
  userId: UUID!
  userName: String!
  loweredUserName: String!
  mobileAlias: String
  isAnonymous: Boolean!
  lastActivityDate: DateTime!
  application: AspnetApplication!
  aspnetMembership: AspnetMembership
  aspnetPersonalizationPerUsers: [AspnetPersonalizationPerUser!]!
  aspnetProfile: AspnetProfile
  epruser: Epruser
  kbaanswers: [Kbaanswer!]!
  roleGroups: [RoleGroup!]!
  roles: [AspnetRole!]!
}

input AspnetUserFilterInput {
  and: [AspnetUserFilterInput!]
  or: [AspnetUserFilterInput!]
  applicationId: UuidOperationFilterInput
  userId: UuidOperationFilterInput
  userName: StringOperationFilterInput
  loweredUserName: StringOperationFilterInput
  mobileAlias: StringOperationFilterInput
  isAnonymous: BooleanOperationFilterInput
  lastActivityDate: DateTimeOperationFilterInput
  application: AspnetApplicationFilterInput
  aspnetMembership: AspnetMembershipFilterInput
  aspnetPersonalizationPerUsers: ListFilterInputTypeOfAspnetPersonalizationPerUserFilterInput
  aspnetProfile: AspnetProfileFilterInput
  epruser: EpruserFilterInput
  kbaanswers: ListFilterInputTypeOfKbaanswerFilterInput
  roleGroups: ListFilterInputTypeOfRoleGroupFilterInput
  roles: ListFilterInputTypeOfAspnetRoleFilterInput
}

input AspnetUserInput {
  applicationId: UUID!
  userId: UUID!
  userName: String!
  loweredUserName: String!
  mobileAlias: String
  isAnonymous: Boolean!
  lastActivityDate: DateTime!
  application: AspnetApplicationInput!
  aspnetMembership: AspnetMembershipInput
  aspnetPersonalizationPerUsers: [AspnetPersonalizationPerUserInput!]!
  aspnetProfile: AspnetProfileInput
  epruser: EpruserInput
  kbaanswers: [KbaanswerInput!]!
  roleGroups: [RoleGroupInput!]!
  roles: [AspnetRoleInput!]!
}

type Benefit {
  id: Int!
  chapterId: Int
  selectable: Boolean
  name: String!
  dstatusId: Int!
  description: String
  chapterAdministratorOnly: Boolean!
  employeeElectionOverridable: Boolean!
  employerElectionOverridable: Boolean!
  employerRateOverridable: Boolean!
  displayOnFundingOnly: Boolean!
  informationalOnly: Boolean!
  taxable: Boolean!
  employeeRateOverridable: Boolean!
  hourComponent: Boolean!
  applyToAssociationReport: Boolean!
  defaultOn: Boolean!
  employeeDeduction: Boolean!
  variantType: String
  oncePerEe: Boolean!
  doNotTotal: Boolean
  agreementsToBenefits: [AgreementsToBenefit!]!
  chapter: Chapter
  costCodes: [CostCode!]!
  electronicPaymentConfigurations: [ElectronicPaymentConfiguration!]!
  electronicPayments: [ElectronicPayment!]!
  fundingComments: [FundingComment!]!
  idNavigation: Root!
  paymentDetails: [PaymentDetail!]!
  reportLineItemDetails: [ReportLineItemDetail!]!
  reportedBenefitReleaseAuthorizations: [ReportedBenefitReleaseAuthorization!]!
}

type BenefitElectionsRosterDto {
  id: ID!
  employerName: String!
  fein: String
  dba: String
  benefitName: String
  override: Decimal
  effectiveStartDate: DateTime
  effectiveEndDate: DateTime
  elected: Boolean
  employerGUID: UUID
  associationID: String
  discount: Decimal
  lastReport: DateTime
}

"""A connection to a list of items."""
type BenefitElectionsRosterDtoConnection {
  """Information to aid in pagination."""
  pageInfo: PageInfo!

  """A list of edges."""
  edges: [BenefitElectionsRosterDtoEdge!]

  """A flattened list of the nodes."""
  nodes: [BenefitElectionsRosterDto!]

  """Identifies the total count of items in the connection."""
  totalCount: Int!
}

"""An edge in a connection."""
type BenefitElectionsRosterDtoEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of the edge."""
  node: BenefitElectionsRosterDto!
}

input BenefitElectionsRosterDtoFilterInput {
  and: [BenefitElectionsRosterDtoFilterInput!]
  or: [BenefitElectionsRosterDtoFilterInput!]
  id: IdOperationFilterInput
  employerName: StringOperationFilterInput
  fein: StringOperationFilterInput
  dba: StringOperationFilterInput
  benefitName: StringOperationFilterInput
  override: DecimalOperationFilterInput
  effectiveStartDate: DateTimeOperationFilterInput
  effectiveEndDate: DateTimeOperationFilterInput
  elected: BooleanOperationFilterInput
  employerGUID: UuidOperationFilterInput
  associationID: StringOperationFilterInput
  discount: DecimalOperationFilterInput
  lastReport: DateTimeOperationFilterInput
}

input BenefitElectionsRosterDtoSortInput {
  id: SortEnumType
  employerName: SortEnumType
  fein: SortEnumType
  dba: SortEnumType
  benefitName: SortEnumType
  override: SortEnumType
  effectiveStartDate: SortEnumType
  effectiveEndDate: SortEnumType
  elected: SortEnumType
  employerGUID: SortEnumType
  associationID: SortEnumType
  discount: SortEnumType
  lastReport: SortEnumType
}

input BenefitFilterInput {
  and: [BenefitFilterInput!]
  or: [BenefitFilterInput!]
  id: IntOperationFilterInput
  chapterId: IntOperationFilterInput
  selectable: BooleanOperationFilterInput
  name: StringOperationFilterInput
  dstatusId: IntOperationFilterInput
  description: StringOperationFilterInput
  chapterAdministratorOnly: BooleanOperationFilterInput
  employeeElectionOverridable: BooleanOperationFilterInput
  employerElectionOverridable: BooleanOperationFilterInput
  employerRateOverridable: BooleanOperationFilterInput
  displayOnFundingOnly: BooleanOperationFilterInput
  informationalOnly: BooleanOperationFilterInput
  taxable: BooleanOperationFilterInput
  employeeRateOverridable: BooleanOperationFilterInput
  hourComponent: BooleanOperationFilterInput
  applyToAssociationReport: BooleanOperationFilterInput
  defaultOn: BooleanOperationFilterInput
  employeeDeduction: BooleanOperationFilterInput
  variantType: StringOperationFilterInput
  oncePerEe: BooleanOperationFilterInput
  doNotTotal: BooleanOperationFilterInput
  agreementsToBenefits: ListFilterInputTypeOfAgreementsToBenefitFilterInput
  chapter: ChapterFilterInput
  costCodes: ListFilterInputTypeOfCostCodeFilterInput
  electronicPaymentConfigurations: ListFilterInputTypeOfElectronicPaymentConfigurationFilterInput
  electronicPayments: ListFilterInputTypeOfElectronicPaymentFilterInput
  fundingComments: ListFilterInputTypeOfFundingCommentFilterInput
  idNavigation: RootFilterInput
  paymentDetails: ListFilterInputTypeOfPaymentDetailFilterInput
  reportLineItemDetails: ListFilterInputTypeOfReportLineItemDetailFilterInput
  reportedBenefitReleaseAuthorizations: ListFilterInputTypeOfReportedBenefitReleaseAuthorizationFilterInput
}

input BenefitInput {
  id: Int!
  chapterId: Int
  selectable: Boolean
  name: String!
  dstatusId: Int!
  description: String
  chapterAdministratorOnly: Boolean!
  employeeElectionOverridable: Boolean!
  employerElectionOverridable: Boolean!
  employerRateOverridable: Boolean!
  displayOnFundingOnly: Boolean!
  informationalOnly: Boolean!
  taxable: Boolean!
  employeeRateOverridable: Boolean!
  hourComponent: Boolean!
  applyToAssociationReport: Boolean!
  defaultOn: Boolean!
  employeeDeduction: Boolean!
  variantType: String
  oncePerEe: Boolean!
  doNotTotal: Boolean
  agreementsToBenefits: [AgreementsToBenefitInput!]!
  chapter: ChapterInput
  costCodes: [CostCodeInput!]!
  electronicPaymentConfigurations: [ElectronicPaymentConfigurationInput!]!
  electronicPayments: [ElectronicPaymentInput!]!
  fundingComments: [FundingCommentInput!]!
  idNavigation: RootInput!
  paymentDetails: [PaymentDetailInput!]!
  reportLineItemDetails: [ReportLineItemDetailInput!]!
  reportedBenefitReleaseAuthorizations: [ReportedBenefitReleaseAuthorizationInput!]!
}

type BenefitOverridesOrig {
  id: Int!
  partyId: Int!
  benefitId: Int!
  election: Boolean
  value: Decimal
  chapterId: Int
  idNavigation: Root!
  party: Party!
}

input BenefitOverridesOrigFilterInput {
  and: [BenefitOverridesOrigFilterInput!]
  or: [BenefitOverridesOrigFilterInput!]
  id: IntOperationFilterInput
  partyId: IntOperationFilterInput
  benefitId: IntOperationFilterInput
  election: BooleanOperationFilterInput
  value: DecimalOperationFilterInput
  chapterId: IntOperationFilterInput
  idNavigation: RootFilterInput
  party: PartyFilterInput
}

input BenefitOverridesOrigInput {
  id: Int!
  partyId: Int!
  benefitId: Int!
  election: Boolean
  value: Decimal
  chapterId: Int
  idNavigation: RootInput!
  party: PartyInput!
}

input BenefitOverridesOrigSortInput {
  id: SortEnumType
  partyId: SortEnumType
  benefitId: SortEnumType
  election: SortEnumType
  value: SortEnumType
  chapterId: SortEnumType
  idNavigation: RootSortInput
  party: PartySortInput
}

input BenefitSortInput {
  id: SortEnumType
  chapterId: SortEnumType
  selectable: SortEnumType
  name: SortEnumType
  dstatusId: SortEnumType
  description: SortEnumType
  chapterAdministratorOnly: SortEnumType
  employeeElectionOverridable: SortEnumType
  employerElectionOverridable: SortEnumType
  employerRateOverridable: SortEnumType
  displayOnFundingOnly: SortEnumType
  informationalOnly: SortEnumType
  taxable: SortEnumType
  employeeRateOverridable: SortEnumType
  hourComponent: SortEnumType
  applyToAssociationReport: SortEnumType
  defaultOn: SortEnumType
  employeeDeduction: SortEnumType
  variantType: SortEnumType
  oncePerEe: SortEnumType
  doNotTotal: SortEnumType
  chapter: ChapterSortInput
  idNavigation: RootSortInput
}

input BooleanOperationFilterInput {
  eq: Boolean
  neq: Boolean
}

"""
The `Byte` scalar type represents non-fractional whole numeric values. Byte can represent values between 0 and 255.
"""
scalar Byte

input ByteOperationFilterInput {
  eq: Byte
  neq: Byte
  in: [Byte]
  nin: [Byte]
  gt: Byte
  ngt: Byte
  gte: Byte
  ngte: Byte
  lt: Byte
  nlt: Byte
  lte: Byte
  nlte: Byte
}

type CanDeleteEmployerResponse {
  canDelete: Boolean!
  canDeleteRelationship: Boolean!
  message: String
}

input ChangePasswordInput {
  currentPassword: String!
  newPassword: String!
  confirmPassword: String!
}

type ChangePasswordPayload {
  string: String
}

type Chapter {
  id: Int!
  employerAssociationId: String
  employeeAssociationId: String
  limited: Boolean!
  agreements: [Agreement!]!
  benefits: [Benefit!]!
  classificationNames: [ClassificationName!]!
  idNavigation: Organization!
  subClassifications: [SubClassification!]!
}

input ChapterFilterInput {
  and: [ChapterFilterInput!]
  or: [ChapterFilterInput!]
  id: IntOperationFilterInput
  employerAssociationId: StringOperationFilterInput
  employeeAssociationId: StringOperationFilterInput
  limited: BooleanOperationFilterInput
  agreements: ListFilterInputTypeOfAgreementFilterInput
  benefits: ListFilterInputTypeOfBenefitFilterInput
  classificationNames: ListFilterInputTypeOfClassificationNameFilterInput
  idNavigation: OrganizationFilterInput
  subClassifications: ListFilterInputTypeOfSubClassificationFilterInput
}

input ChapterInput {
  id: Int!
  employerAssociationId: String
  employeeAssociationId: String
  limited: Boolean!
  agreements: [AgreementInput!]!
  benefits: [BenefitInput!]!
  classificationNames: [ClassificationNameInput!]!
  idNavigation: OrganizationInput!
  subClassifications: [SubClassificationInput!]!
}

type ChaptersInfoDto implements Node {
  id: ID!
  value: Int!
  label: String!
  guid: UUID
}

"""A connection to a list of items."""
type ChaptersInfoDtoConnection {
  """Information to aid in pagination."""
  pageInfo: PageInfo!

  """A list of edges."""
  edges: [ChaptersInfoDtoEdge!]

  """A flattened list of the nodes."""
  nodes: [ChaptersInfoDto!]

  """Identifies the total count of items in the connection."""
  totalCount: Int!
}

"""An edge in a connection."""
type ChaptersInfoDtoEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of the edge."""
  node: ChaptersInfoDto!
}

input ChaptersInfoDtoFilterInput {
  and: [ChaptersInfoDtoFilterInput!]
  or: [ChaptersInfoDtoFilterInput!]
  id: IdOperationFilterInput
  value: IntOperationFilterInput
  label: StringOperationFilterInput
  guid: UuidOperationFilterInput
}

input ChaptersInfoDtoSortInput {
  id: SortEnumType
  value: SortEnumType
  label: SortEnumType
  guid: SortEnumType
}

input ChapterSortInput {
  id: SortEnumType
  employerAssociationId: SortEnumType
  employeeAssociationId: SortEnumType
  limited: SortEnumType
  idNavigation: OrganizationSortInput
}

type ChapterToEmployerRelationship {
  id: Int!
  isAssociationMember: Boolean!
  associationId: String
  datePriviledgedAuthorized: DateTime
  priviledgedAuthorizedBy: String
  relationship: Relationship!
}

input ChapterToEmployerRelationshipFilterInput {
  and: [ChapterToEmployerRelationshipFilterInput!]
  or: [ChapterToEmployerRelationshipFilterInput!]
  id: IntOperationFilterInput
  isAssociationMember: BooleanOperationFilterInput
  associationId: StringOperationFilterInput
  datePriviledgedAuthorized: DateTimeOperationFilterInput
  priviledgedAuthorizedBy: StringOperationFilterInput
  relationship: RelationshipFilterInput
}

input ChapterToEmployerRelationshipInput {
  id: Int!
  isAssociationMember: Boolean!
  associationId: String
  datePriviledgedAuthorized: DateTime
  priviledgedAuthorizedBy: String
  relationship: RelationshipInput!
}

input ChapterToEmployerRelationshipSortInput {
  id: SortEnumType
  isAssociationMember: SortEnumType
  associationId: SortEnumType
  datePriviledgedAuthorized: SortEnumType
  priviledgedAuthorizedBy: SortEnumType
  relationship: RelationshipSortInput
}

type ClassificationName implements Node {
  id: ID!
  chapterId: Int!
  name: String!
  dclassificationCodeId: Int!
  dstatusId: Int!
  description: String
  chapter: Chapter!
  dclassificationCode: DclassificationCode!
  dstatus: Dstatus!
  idNavigation: Root!
  reportLineItems: [ReportLineItem!]!
}

"""A connection to a list of items."""
type ClassificationNameConnection {
  """Information to aid in pagination."""
  pageInfo: PageInfo!

  """A list of edges."""
  edges: [ClassificationNameEdge!]

  """A flattened list of the nodes."""
  nodes: [ClassificationName!]

  """Identifies the total count of items in the connection."""
  totalCount: Int!
}

"""An edge in a connection."""
type ClassificationNameEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of the edge."""
  node: ClassificationName!
}

input ClassificationNameFilterInput {
  and: [ClassificationNameFilterInput!]
  or: [ClassificationNameFilterInput!]
  id: IntOperationFilterInput
  chapterId: IntOperationFilterInput
  name: StringOperationFilterInput
  dclassificationCodeId: IntOperationFilterInput
  dstatusId: IntOperationFilterInput
  description: StringOperationFilterInput
  chapter: ChapterFilterInput
  dclassificationCode: DclassificationCodeFilterInput
  dstatus: DstatusFilterInput
  idNavigation: RootFilterInput
  reportLineItems: ListFilterInputTypeOfReportLineItemFilterInput
}

input ClassificationNameInput {
  id: Int!
  chapterId: Int!
  name: String!
  dclassificationCodeId: Int!
  dstatusId: Int!
  description: String
  chapter: ChapterInput!
  dclassificationCode: DclassificationCodeInput!
  dstatus: DstatusInput!
  idNavigation: RootInput!
  reportLineItems: [ReportLineItemInput!]!
}

input ClassificationNameSortInput {
  id: SortEnumType
  chapterId: SortEnumType
  name: SortEnumType
  dclassificationCodeId: SortEnumType
  dstatusId: SortEnumType
  description: SortEnumType
  chapter: ChapterSortInput
  dclassificationCode: DclassificationCodeSortInput
  dstatus: DstatusSortInput
  idNavigation: RootSortInput
}

input ClassificationsByAgreementsInput {
  agreementIds: [Int!]!
}

type ClassificationSimpleId {
  id: Int!
  name: String!
}

"""A connection to a list of items."""
type ClassificationSimpleIdConnection {
  """Information to aid in pagination."""
  pageInfo: PageInfo!

  """A list of edges."""
  edges: [ClassificationSimpleIdEdge!]

  """A flattened list of the nodes."""
  nodes: [ClassificationSimpleId!]

  """Identifies the total count of items in the connection."""
  totalCount: Int!
}

"""An edge in a connection."""
type ClassificationSimpleIdEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of the edge."""
  node: ClassificationSimpleId!
}

input ClassificationSimpleIdFilterInput {
  and: [ClassificationSimpleIdFilterInput!]
  or: [ClassificationSimpleIdFilterInput!]
  id: IntOperationFilterInput
  name: StringOperationFilterInput
}

input ClassificationSimpleIdSortInput {
  id: SortEnumType
  name: SortEnumType
}

type ContactMechanism {
  id: Int!
  dcontactMechanismTypeId: Int!
  address: Address
  dcontactMechanismType: DcontactMechanismType!
  emailAddress: EmailAddress
  idNavigation: Root!
  partiesToContactMechanisms: [PartiesToContactMechanism!]!
  phoneNumber: PhoneNumber
  website: Website
}

input ContactMechanismFilterInput {
  and: [ContactMechanismFilterInput!]
  or: [ContactMechanismFilterInput!]
  id: IntOperationFilterInput
  dcontactMechanismTypeId: IntOperationFilterInput
  address: AddressFilterInput
  dcontactMechanismType: DcontactMechanismTypeFilterInput
  emailAddress: EmailAddressFilterInput
  idNavigation: RootFilterInput
  partiesToContactMechanisms: ListFilterInputTypeOfPartiesToContactMechanismFilterInput
  phoneNumber: PhoneNumberFilterInput
  website: WebsiteFilterInput
}

input ContactMechanismInput {
  id: Int!
  dcontactMechanismTypeId: Int!
  address: AddressInput
  dcontactMechanismType: DcontactMechanismTypeInput!
  emailAddress: EmailAddressInput
  idNavigation: RootInput!
  partiesToContactMechanisms: [PartiesToContactMechanismInput!]!
  phoneNumber: PhoneNumberInput
  website: WebsiteInput
}

input ContactMechanismSortInput {
  id: SortEnumType
  dcontactMechanismTypeId: SortEnumType
  address: AddressSortInput
  dcontactMechanismType: DcontactMechanismTypeSortInput
  emailAddress: EmailAddressSortInput
  idNavigation: RootSortInput
  phoneNumber: PhoneNumberSortInput
  website: WebsiteSortInput
}

type CostCode {
  id: Int!
  employerId: Int
  agreementId: Int
  benefitId: Int
  code: String
  agreement: Agreement
  benefit: Benefit
  employer: Employer
}

input CostCodeFilterInput {
  and: [CostCodeFilterInput!]
  or: [CostCodeFilterInput!]
  id: IntOperationFilterInput
  employerId: IntOperationFilterInput
  agreementId: IntOperationFilterInput
  benefitId: IntOperationFilterInput
  code: StringOperationFilterInput
  agreement: AgreementFilterInput
  benefit: BenefitFilterInput
  employer: EmployerFilterInput
}

input CostCodeInput {
  id: Int!
  employerId: Int
  agreementId: Int
  benefitId: Int
  code: String
  agreement: AgreementInput
  benefit: BenefitInput
  employer: EmployerInput
}

input CreateCustomViewsInput {
  name: String!
  description: String
  defaultValue: JSON
  value: JSON
  ownerId: UUID
  ownerType: String
}

type CreateCustomViewsPayload {
  customViews: CustomViews
}

type CreditCardPaymentMethod {
  associatedGuid: UUID!
  accountNumber: String!
  expiration: DateTime!
  securityCode: String!
  name: String!
  associated: PaymentMethod!
}

input CreditCardPaymentMethodFilterInput {
  and: [CreditCardPaymentMethodFilterInput!]
  or: [CreditCardPaymentMethodFilterInput!]
  associatedGuid: UuidOperationFilterInput
  accountNumber: StringOperationFilterInput
  expiration: DateTimeOperationFilterInput
  securityCode: StringOperationFilterInput
  name: StringOperationFilterInput
  associated: PaymentMethodFilterInput
}

input CreditCardPaymentMethodInput {
  associatedGuid: UUID!
  accountNumber: String!
  expiration: DateTime!
  securityCode: String!
  name: String!
  associated: PaymentMethodInput!
}

type CustomReport {
  id: Int!
  name: String!
  suppressDuplicatesThroughColumnName: String
  notes: String
  reportPath: String
  reportTypeId: Int!
  usesGuidSubstitution: Boolean!
  optimisticLockField: Int
  newApp: Boolean
  customReportPivot: CustomReportPivot
  customReportProducts: [CustomReportProduct!]!
}

input CustomReportFilterInput {
  and: [CustomReportFilterInput!]
  or: [CustomReportFilterInput!]
  id: IntOperationFilterInput
  name: StringOperationFilterInput
  suppressDuplicatesThroughColumnName: StringOperationFilterInput
  notes: StringOperationFilterInput
  reportPath: StringOperationFilterInput
  reportTypeId: IntOperationFilterInput
  usesGuidSubstitution: BooleanOperationFilterInput
  optimisticLockField: IntOperationFilterInput
  newApp: BooleanOperationFilterInput
  customReportPivot: CustomReportPivotFilterInput
  customReportProducts: ListFilterInputTypeOfCustomReportProductFilterInput
}

input CustomReportInput {
  id: Int!
  name: String!
  suppressDuplicatesThroughColumnName: String
  notes: String
  reportPath: String
  reportTypeId: Int!
  usesGuidSubstitution: Boolean!
  optimisticLockField: Int
  newApp: Boolean
  customReportPivot: CustomReportPivotInput
  customReportProducts: [CustomReportProductInput!]!
}

type CustomReportPivot {
  reportId: Int!
  nameColumn: String!
  valueColumn: String!
  sort: Boolean!
  report: CustomReport!
}

input CustomReportPivotFilterInput {
  and: [CustomReportPivotFilterInput!]
  or: [CustomReportPivotFilterInput!]
  reportId: IntOperationFilterInput
  nameColumn: StringOperationFilterInput
  valueColumn: StringOperationFilterInput
  sort: BooleanOperationFilterInput
  report: CustomReportFilterInput
}

input CustomReportPivotInput {
  reportId: Int!
  nameColumn: String!
  valueColumn: String!
  sort: Boolean!
  report: CustomReportInput!
}

type CustomReportProduct {
  id: Int!
  customReportId: Int!
  productId: Int!
  creationDate: DateTime!
  createdBy: String!
  lastModifiedDate: DateTime!
  lastModifiedBy: String!
  customReport: CustomReport!
  product: Product1!
}

input CustomReportProductFilterInput {
  and: [CustomReportProductFilterInput!]
  or: [CustomReportProductFilterInput!]
  id: IntOperationFilterInput
  customReportId: IntOperationFilterInput
  productId: IntOperationFilterInput
  creationDate: DateTimeOperationFilterInput
  createdBy: StringOperationFilterInput
  lastModifiedDate: DateTimeOperationFilterInput
  lastModifiedBy: StringOperationFilterInput
  customReport: CustomReportFilterInput
  product: Product1FilterInput
}

input CustomReportProductInput {
  id: Int!
  customReportId: Int!
  productId: Int!
  creationDate: DateTime!
  createdBy: String!
  lastModifiedDate: DateTime!
  lastModifiedBy: String!
  customReport: CustomReportInput!
  product: Product1Input!
}

type CustomViews {
  id: ID
  name: String
  description: String
  viewTypeId: ID
  defaultValue: JSON
  value: JSON
  ownerId: ID
  ownerType: String!
  lastModifiedBy: UUID
  lastModificationDate: DateTime
}

type DaddressType {
  id: Int!
  name: String!
  addresses: [Address!]!
}

input DaddressTypeFilterInput {
  and: [DaddressTypeFilterInput!]
  or: [DaddressTypeFilterInput!]
  id: IntOperationFilterInput
  name: StringOperationFilterInput
  addresses: ListFilterInputTypeOfAddressFilterInput
}

input DaddressTypeInput {
  id: Int!
  name: String!
  addresses: [AddressInput!]!
}

input DaddressTypeSortInput {
  id: SortEnumType
  name: SortEnumType
}

type DagreementType {
  id: Int!
  name: String!
  description: String
  agreements: [Agreement!]!
}

input DagreementTypeFilterInput {
  and: [DagreementTypeFilterInput!]
  or: [DagreementTypeFilterInput!]
  id: IntOperationFilterInput
  name: StringOperationFilterInput
  description: StringOperationFilterInput
  agreements: ListFilterInputTypeOfAgreementFilterInput
}

input DagreementTypeInput {
  id: Int!
  name: String!
  description: String
  agreements: [AgreementInput!]!
}

input DagreementTypeSortInput {
  id: SortEnumType
  name: SortEnumType
  description: SortEnumType
}

type DamendmentAction {
  id: Int!
  name: String!
  description: String
  reportLineItems: [ReportLineItem!]!
}

input DamendmentActionFilterInput {
  and: [DamendmentActionFilterInput!]
  or: [DamendmentActionFilterInput!]
  id: IntOperationFilterInput
  name: StringOperationFilterInput
  description: StringOperationFilterInput
  reportLineItems: ListFilterInputTypeOfReportLineItemFilterInput
}

input DamendmentActionInput {
  id: Int!
  name: String!
  description: String
  reportLineItems: [ReportLineItemInput!]!
}

input DamendmentActionSortInput {
  id: SortEnumType
  name: SortEnumType
  description: SortEnumType
}

"""The `DateTime` scalar represents an ISO-8601 compliant date time type."""
scalar DateTime

input DateTimeOperationFilterInput {
  eq: DateTime
  neq: DateTime
  in: [DateTime]
  nin: [DateTime]
  gt: DateTime
  ngt: DateTime
  gte: DateTime
  ngte: DateTime
  lt: DateTime
  nlt: DateTime
  lte: DateTime
  nlte: DateTime
}

type Dcategory {
  id: Int!
  name: String!
  keywords: String
  description: String
  active: Boolean
  parentCategory: Int
  smallImagePath: String
  mediumImagePath: String
  largeImagePath: String
  creationDate: DateTime!
  createdBy: String!
  lastModifiedDate: DateTime!
  lastModifiedBy: String!
  productCategories: [ProductCategory!]!
}

input DcategoryFilterInput {
  and: [DcategoryFilterInput!]
  or: [DcategoryFilterInput!]
  id: IntOperationFilterInput
  name: StringOperationFilterInput
  keywords: StringOperationFilterInput
  description: StringOperationFilterInput
  active: BooleanOperationFilterInput
  parentCategory: IntOperationFilterInput
  smallImagePath: StringOperationFilterInput
  mediumImagePath: StringOperationFilterInput
  largeImagePath: StringOperationFilterInput
  creationDate: DateTimeOperationFilterInput
  createdBy: StringOperationFilterInput
  lastModifiedDate: DateTimeOperationFilterInput
  lastModifiedBy: StringOperationFilterInput
  productCategories: ListFilterInputTypeOfProductCategoryFilterInput
}

input DcategoryInput {
  id: Int!
  name: String!
  keywords: String
  description: String
  active: Boolean
  parentCategory: Int
  smallImagePath: String
  mediumImagePath: String
  largeImagePath: String
  creationDate: DateTime!
  createdBy: String!
  lastModifiedDate: DateTime!
  lastModifiedBy: String!
  productCategories: [ProductCategoryInput!]!
}

type DclassificationCode {
  id: Int!
  category: String!
  className: String!
  description: String
  exemptEmployee: Boolean!
  classificationNames: [ClassificationName!]!
}

input DclassificationCodeFilterInput {
  and: [DclassificationCodeFilterInput!]
  or: [DclassificationCodeFilterInput!]
  id: IntOperationFilterInput
  category: StringOperationFilterInput
  className: StringOperationFilterInput
  description: StringOperationFilterInput
  exemptEmployee: BooleanOperationFilterInput
  classificationNames: ListFilterInputTypeOfClassificationNameFilterInput
}

input DclassificationCodeInput {
  id: Int!
  category: String!
  className: String!
  description: String
  exemptEmployee: Boolean!
  classificationNames: [ClassificationNameInput!]!
}

input DclassificationCodeSortInput {
  id: SortEnumType
  category: SortEnumType
  className: SortEnumType
  description: SortEnumType
  exemptEmployee: SortEnumType
}

type DcontactMechanismType {
  id: Int!
  name: String!
  contactMechanisms: [ContactMechanism!]!
}

input DcontactMechanismTypeFilterInput {
  and: [DcontactMechanismTypeFilterInput!]
  or: [DcontactMechanismTypeFilterInput!]
  id: IntOperationFilterInput
  name: StringOperationFilterInput
  contactMechanisms: ListFilterInputTypeOfContactMechanismFilterInput
}

input DcontactMechanismTypeInput {
  id: Int!
  name: String!
  contactMechanisms: [ContactMechanismInput!]!
}

input DcontactMechanismTypeSortInput {
  id: SortEnumType
  name: SortEnumType
}

type Dcountry {
  id: UUID!
  name: String!
  twoLetterCode: String!
  threeLetterCode: String!
  numericCode: String!
  addresses: [Address!]!
}

input DcountryFilterInput {
  and: [DcountryFilterInput!]
  or: [DcountryFilterInput!]
  id: UuidOperationFilterInput
  name: StringOperationFilterInput
  twoLetterCode: StringOperationFilterInput
  threeLetterCode: StringOperationFilterInput
  numericCode: StringOperationFilterInput
  addresses: ListFilterInputTypeOfAddressFilterInput
}

input DcountryInput {
  id: UUID!
  name: String!
  twoLetterCode: String!
  threeLetterCode: String!
  numericCode: String!
  addresses: [AddressInput!]!
}

input DcountrySortInput {
  id: SortEnumType
  name: SortEnumType
  twoLetterCode: SortEnumType
  threeLetterCode: SortEnumType
  numericCode: SortEnumType
}

"""The `Decimal` scalar type represents a decimal floating-point number."""
scalar Decimal

input DecimalOperationFilterInput {
  eq: Decimal
  neq: Decimal
  in: [Decimal]
  nin: [Decimal]
  gt: Decimal
  ngt: Decimal
  gte: Decimal
  ngte: Decimal
  lt: Decimal
  nlt: Decimal
  lte: Decimal
  nlte: Decimal
}

type DelectronicPaymentOption {
  id: Int!
  name: String!
  electronicPaymentConfigurations: [ElectronicPaymentConfiguration!]!
}

input DelectronicPaymentOptionFilterInput {
  and: [DelectronicPaymentOptionFilterInput!]
  or: [DelectronicPaymentOptionFilterInput!]
  id: IntOperationFilterInput
  name: StringOperationFilterInput
  electronicPaymentConfigurations: ListFilterInputTypeOfElectronicPaymentConfigurationFilterInput
}

input DelectronicPaymentOptionInput {
  id: Int!
  name: String!
  electronicPaymentConfigurations: [ElectronicPaymentConfigurationInput!]!
}

input DelectronicPaymentOptionSortInput {
  id: SortEnumType
  name: SortEnumType
}

input DeleteEmployerInput {
  employerId: ID!
}

type DeleteEmployerPayload {
  operationResult: OperationResult
}

input DeleteRelationshipWithSiteSponsorInput {
  employerId: ID!
  chapterId: ID!
}

type DeleteRelationshipWithSiteSponsorPayload {
  employerRosterViewEdge: EdgeOfEmployerRosterView
}

input DeleteTimesheetInput {
  id: ID!
}

type DeleteTimesheetPayload {
  int: Int
}

type DemailAddressType {
  id: Int!
  name: String!
  emailAddresses: [EmailAddress!]!
}

input DemailAddressTypeFilterInput {
  and: [DemailAddressTypeFilterInput!]
  or: [DemailAddressTypeFilterInput!]
  id: IntOperationFilterInput
  name: StringOperationFilterInput
  emailAddresses: ListFilterInputTypeOfEmailAddressFilterInput
}

input DemailAddressTypeInput {
  id: Int!
  name: String!
  emailAddresses: [EmailAddressInput!]!
}

input DemailAddressTypeSortInput {
  id: SortEnumType
  name: SortEnumType
}

type Dgender {
  id: Int!
  name: String!
  people: [Person!]!
}

input DgenderFilterInput {
  and: [DgenderFilterInput!]
  or: [DgenderFilterInput!]
  id: IntOperationFilterInput
  name: StringOperationFilterInput
  people: ListFilterInputTypeOfPersonFilterInput
}

input DgenderInput {
  id: Int!
  name: String!
  people: [PersonInput!]!
}

input DgenderSortInput {
  id: SortEnumType
  name: SortEnumType
}

type DorderStatus {
  id: Int!
  name: String!
  orders: [Order!]!
}

input DorderStatusFilterInput {
  and: [DorderStatusFilterInput!]
  or: [DorderStatusFilterInput!]
  id: IntOperationFilterInput
  name: StringOperationFilterInput
  orders: ListFilterInputTypeOfOrderFilterInput
}

input DorderStatusInput {
  id: Int!
  name: String!
  orders: [OrderInput!]!
}

type DorganizationType {
  id: Int!
  name: String!
  organizations: [Organization!]!
  productsToOrganizationTypes: [ProductsToOrganizationType!]!
}

input DorganizationTypeFilterInput {
  and: [DorganizationTypeFilterInput!]
  or: [DorganizationTypeFilterInput!]
  id: IntOperationFilterInput
  name: StringOperationFilterInput
  organizations: ListFilterInputTypeOfOrganizationFilterInput
  productsToOrganizationTypes: ListFilterInputTypeOfProductsToOrganizationTypeFilterInput
}

input DorganizationTypeInput {
  id: Int!
  name: String!
  organizations: [OrganizationInput!]!
  productsToOrganizationTypes: [ProductsToOrganizationTypeInput!]!
}

input DorganizationTypeSortInput {
  id: SortEnumType
  name: SortEnumType
}

type DpartyType {
  id: Int!
  name: String!
  parties: [Party!]!
}

input DpartyTypeFilterInput {
  and: [DpartyTypeFilterInput!]
  or: [DpartyTypeFilterInput!]
  id: IntOperationFilterInput
  name: StringOperationFilterInput
  parties: ListFilterInputTypeOfPartyFilterInput
}

input DpartyTypeInput {
  id: Int!
  name: String!
  parties: [PartyInput!]!
}

input DpartyTypeSortInput {
  id: SortEnumType
  name: SortEnumType
}

type DpaymentMethodType {
  id: Int!
  name: String!
  electronicPayments: [ElectronicPayment!]!
  paymentMethods: [PaymentMethod!]!
}

input DpaymentMethodTypeFilterInput {
  and: [DpaymentMethodTypeFilterInput!]
  or: [DpaymentMethodTypeFilterInput!]
  id: IntOperationFilterInput
  name: StringOperationFilterInput
  electronicPayments: ListFilterInputTypeOfElectronicPaymentFilterInput
  paymentMethods: ListFilterInputTypeOfPaymentMethodFilterInput
}

input DpaymentMethodTypeInput {
  id: Int!
  name: String!
  electronicPayments: [ElectronicPaymentInput!]!
  paymentMethods: [PaymentMethodInput!]!
}

input DpaymentMethodTypeSortInput {
  id: SortEnumType
  name: SortEnumType
}

type DpaymentType {
  id: Int!
  name: String!
  orderDetails: [OrderDetail!]!
  product1s: [Product1!]!
}

input DpaymentTypeFilterInput {
  and: [DpaymentTypeFilterInput!]
  or: [DpaymentTypeFilterInput!]
  id: IntOperationFilterInput
  name: StringOperationFilterInput
  orderDetails: ListFilterInputTypeOfOrderDetailFilterInput
  product1s: ListFilterInputTypeOfProduct1FilterInput
}

input DpaymentTypeInput {
  id: Int!
  name: String!
  orderDetails: [OrderDetailInput!]!
  product1s: [Product1Input!]!
}

type DpersonType {
  id: Int!
  name: String!
  people: [Person!]!
}

input DpersonTypeFilterInput {
  and: [DpersonTypeFilterInput!]
  or: [DpersonTypeFilterInput!]
  id: IntOperationFilterInput
  name: StringOperationFilterInput
  people: ListFilterInputTypeOfPersonFilterInput
}

input DpersonTypeInput {
  id: Int!
  name: String!
  people: [PersonInput!]!
}

input DpersonTypeSortInput {
  id: SortEnumType
  name: SortEnumType
}

type DphoneNumberType {
  id: Int!
  name: String!
  phoneNumbers: [PhoneNumber!]!
}

input DphoneNumberTypeFilterInput {
  and: [DphoneNumberTypeFilterInput!]
  or: [DphoneNumberTypeFilterInput!]
  id: IntOperationFilterInput
  name: StringOperationFilterInput
  phoneNumbers: ListFilterInputTypeOfPhoneNumberFilterInput
}

input DphoneNumberTypeInput {
  id: Int!
  name: String!
  phoneNumbers: [PhoneNumberInput!]!
}

input DphoneNumberTypeSortInput {
  id: SortEnumType
  name: SortEnumType
}

type DrelationshipStatus {
  id: Int!
  name: String!
  notes: String
  relationshipStatuses: [RelationshipStatus!]!
}

input DrelationshipStatusFilterInput {
  and: [DrelationshipStatusFilterInput!]
  or: [DrelationshipStatusFilterInput!]
  id: IntOperationFilterInput
  name: StringOperationFilterInput
  notes: StringOperationFilterInput
  relationshipStatuses: ListFilterInputTypeOfRelationshipStatusFilterInput
}

input DrelationshipStatusInput {
  id: Int!
  name: String!
  notes: String
  relationshipStatuses: [RelationshipStatusInput!]!
}

input DrelationshipStatusSortInput {
  id: SortEnumType
  name: SortEnumType
  notes: SortEnumType
}

type DrelationshipSubType {
  id: Int!
  drelationshipTypeId: Int!
  name: String!
  description: String
  relationships: [Relationship!]!
}

input DrelationshipSubTypeFilterInput {
  and: [DrelationshipSubTypeFilterInput!]
  or: [DrelationshipSubTypeFilterInput!]
  id: IntOperationFilterInput
  drelationshipTypeId: IntOperationFilterInput
  name: StringOperationFilterInput
  description: StringOperationFilterInput
  relationships: ListFilterInputTypeOfRelationshipFilterInput
}

input DrelationshipSubTypeInput {
  id: Int!
  drelationshipTypeId: Int!
  name: String!
  description: String
  relationships: [RelationshipInput!]!
}

input DrelationshipSubTypeSortInput {
  id: SortEnumType
  drelationshipTypeId: SortEnumType
  name: SortEnumType
  description: SortEnumType
}

type DrelationshipType {
  id: Int!
  name: String!
  isOneToOne: Boolean
  leftDentityTypeId: Int!
  rightDentityTypeId: Int!
  notes: String
  relationships: [Relationship!]!
}

input DrelationshipTypeFilterInput {
  and: [DrelationshipTypeFilterInput!]
  or: [DrelationshipTypeFilterInput!]
  id: IntOperationFilterInput
  name: StringOperationFilterInput
  isOneToOne: BooleanOperationFilterInput
  leftDentityTypeId: IntOperationFilterInput
  rightDentityTypeId: IntOperationFilterInput
  notes: StringOperationFilterInput
  relationships: ListFilterInputTypeOfRelationshipFilterInput
}

input DrelationshipTypeInput {
  id: Int!
  name: String!
  isOneToOne: Boolean
  leftDentityTypeId: Int!
  rightDentityTypeId: Int!
  notes: String
  relationships: [RelationshipInput!]!
}

input DrelationshipTypeSortInput {
  id: SortEnumType
  name: SortEnumType
  isOneToOne: SortEnumType
  leftDentityTypeId: SortEnumType
  rightDentityTypeId: SortEnumType
  notes: SortEnumType
}

type DreportStatus {
  id: Int!
  name: String!
  description: String
  reports: [Report!]!
}

input DreportStatusFilterInput {
  and: [DreportStatusFilterInput!]
  or: [DreportStatusFilterInput!]
  id: IntOperationFilterInput
  name: StringOperationFilterInput
  description: StringOperationFilterInput
  reports: ListFilterInputTypeOfReportFilterInput
}

input DreportStatusInput {
  id: Int!
  name: String!
  description: String
  reports: [ReportInput!]!
}

input DreportStatusSortInput {
  id: SortEnumType
  name: SortEnumType
  description: SortEnumType
}

type DsettingsType {
  id: ID
  name: String!
  description: String
  allowOverride: Boolean!
  valueType: String!
  defaultValue: Any
  validValues: String
  settings: [Setting]!
}

"""A connection to a list of items."""
type DsettingsTypeConnection {
  """Information to aid in pagination."""
  pageInfo: PageInfo!

  """A list of edges."""
  edges: [DsettingsTypeEdge!]

  """A flattened list of the nodes."""
  nodes: [DsettingsType!]

  """Identifies the total count of items in the connection."""
  totalCount: Int!
}

"""An edge in a connection."""
type DsettingsTypeEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of the edge."""
  node: DsettingsType!
}

input DsettingsTypeFilterInput {
  and: [DsettingsTypeFilterInput!]
  or: [DsettingsTypeFilterInput!]
  id: IdOperationFilterInput
  name: StringOperationFilterInput
  description: StringOperationFilterInput
  allowOverride: BooleanOperationFilterInput
  valueType: StringOperationFilterInput
  defaultValue: StringOperationFilterInput
  validValues: StringOperationFilterInput
  settings: ListFilterInputTypeOfSettingFilterInput
}

input DsettingsTypeInput {
  id: ID
  name: String!
  description: String
  allowOverride: Boolean!
  valueType: String!
  defaultValue: Any
  validValues: String
  settings: [SettingInput]!
}

type Dstatus {
  id: Int!
  name: String!
  notes: String
  classificationNames: [ClassificationName!]!
  subClassifications: [SubClassification!]!
}

input DstatusFilterInput {
  and: [DstatusFilterInput!]
  or: [DstatusFilterInput!]
  id: IntOperationFilterInput
  name: StringOperationFilterInput
  notes: StringOperationFilterInput
  classificationNames: ListFilterInputTypeOfClassificationNameFilterInput
  subClassifications: ListFilterInputTypeOfSubClassificationFilterInput
}

input DstatusInput {
  id: Int!
  name: String!
  notes: String
  classificationNames: [ClassificationNameInput!]!
  subClassifications: [SubClassificationInput!]!
}

input DstatusSortInput {
  id: SortEnumType
  name: SortEnumType
  notes: SortEnumType
}

type DwebsiteType {
  id: Int!
  name: String!
  websites: [Website!]!
}

input DwebsiteTypeFilterInput {
  and: [DwebsiteTypeFilterInput!]
  or: [DwebsiteTypeFilterInput!]
  id: IntOperationFilterInput
  name: StringOperationFilterInput
  websites: ListFilterInputTypeOfWebsiteFilterInput
}

input DwebsiteTypeInput {
  id: Int!
  name: String!
  websites: [WebsiteInput!]!
}

input DwebsiteTypeSortInput {
  id: SortEnumType
  name: SortEnumType
}

type EdgeOfChaptersInfoDto {
  cursor: String!
  node: ChaptersInfoDto
}

type EdgeOfEmployerRosterView {
  cursor: String!
  node: EmployerRosterView
}

type EdgeOfPayStub {
  cursor: String!
  node: PayStub
}

type EdgeOfThirdPartyInfoDto {
  cursor: String!
  node: ThirdPartyInfoDto
}

type EdgeOfTimeSheet {
  cursor: String!
  node: TimeSheet
}

type Eftpayment {
  id: Int!
  routingNumber: String!
  accountNumber: String!
  idNavigation: ElectronicPayment!
}

input EftpaymentFilterInput {
  and: [EftpaymentFilterInput!]
  or: [EftpaymentFilterInput!]
  id: IntOperationFilterInput
  routingNumber: StringOperationFilterInput
  accountNumber: StringOperationFilterInput
  idNavigation: ElectronicPaymentFilterInput
}

input EftpaymentInput {
  id: Int!
  routingNumber: String!
  accountNumber: String!
  idNavigation: ElectronicPaymentInput!
}

type EftpaymentMethod {
  associatedGuid: UUID!
  routingNumber: String!
  accountNumber: String!
  associated: PaymentMethod!
}

input EftpaymentMethodFilterInput {
  and: [EftpaymentMethodFilterInput!]
  or: [EftpaymentMethodFilterInput!]
  associatedGuid: UuidOperationFilterInput
  routingNumber: StringOperationFilterInput
  accountNumber: StringOperationFilterInput
  associated: PaymentMethodFilterInput
}

input EftpaymentMethodInput {
  associatedGuid: UUID!
  routingNumber: String!
  accountNumber: String!
  associated: PaymentMethodInput!
}

input EftpaymentSortInput {
  id: SortEnumType
  routingNumber: SortEnumType
  accountNumber: SortEnumType
  idNavigation: ElectronicPaymentSortInput
}

type ElectronicBatch {
  id: Int!
  collectingAgentId: Int!
  batchDate: DateTime!
  lastDownloaded: DateTime!
  electronicPayments: [ElectronicPayment!]!
  idNavigation: Root!
}

input ElectronicBatchFilterInput {
  and: [ElectronicBatchFilterInput!]
  or: [ElectronicBatchFilterInput!]
  id: IntOperationFilterInput
  collectingAgentId: IntOperationFilterInput
  batchDate: DateTimeOperationFilterInput
  lastDownloaded: DateTimeOperationFilterInput
  electronicPayments: ListFilterInputTypeOfElectronicPaymentFilterInput
  idNavigation: RootFilterInput
}

input ElectronicBatchInput {
  id: Int!
  collectingAgentId: Int!
  batchDate: DateTime!
  lastDownloaded: DateTime!
  electronicPayments: [ElectronicPaymentInput!]!
  idNavigation: RootInput!
}

input ElectronicBatchSortInput {
  id: SortEnumType
  collectingAgentId: SortEnumType
  batchDate: SortEnumType
  lastDownloaded: SortEnumType
  idNavigation: RootSortInput
}

type ElectronicPayment {
  id: Int!
  reportId: Int!
  benefitId: Int!
  amount: Decimal!
  dpaymentMethodTypeId: Int!
  paymentsId: Int
  electronicBatchId: Int
  suppress: Boolean!
  benefit: Benefit!
  dpaymentMethodType: DpaymentMethodType!
  eftpayment: Eftpayment
  electronicBatch: ElectronicBatch
  idNavigation: Root!
  payments: Payment
  report: Report!
}

type ElectronicPaymentConfiguration {
  id: Int!
  organizationId: Int!
  unionId: Int!
  benefitId: Int!
  delectronicPaymentOptionId: Int!
  nachaconfigurationId: Int
  chapterId: Int
  mandatory: Boolean!
  creationDate: DateTime!
  createdBy: String!
  lastModifiedDate: DateTime!
  lastModifiedBy: String!
  benefit: Benefit!
  chapter: Organization
  delectronicPaymentOption: DelectronicPaymentOption!
  idNavigation: Root!
  nachaconfiguration: Nachaconfiguration
  organization: Organization!
}

input ElectronicPaymentConfigurationFilterInput {
  and: [ElectronicPaymentConfigurationFilterInput!]
  or: [ElectronicPaymentConfigurationFilterInput!]
  id: IntOperationFilterInput
  organizationId: IntOperationFilterInput
  unionId: IntOperationFilterInput
  benefitId: IntOperationFilterInput
  delectronicPaymentOptionId: IntOperationFilterInput
  nachaconfigurationId: IntOperationFilterInput
  chapterId: IntOperationFilterInput
  mandatory: BooleanOperationFilterInput
  creationDate: DateTimeOperationFilterInput
  createdBy: StringOperationFilterInput
  lastModifiedDate: DateTimeOperationFilterInput
  lastModifiedBy: StringOperationFilterInput
  benefit: BenefitFilterInput
  chapter: OrganizationFilterInput
  delectronicPaymentOption: DelectronicPaymentOptionFilterInput
  idNavigation: RootFilterInput
  nachaconfiguration: NachaconfigurationFilterInput
  organization: OrganizationFilterInput
}

input ElectronicPaymentConfigurationInput {
  id: Int!
  organizationId: Int!
  unionId: Int!
  benefitId: Int!
  delectronicPaymentOptionId: Int!
  nachaconfigurationId: Int
  chapterId: Int
  mandatory: Boolean!
  creationDate: DateTime!
  createdBy: String!
  lastModifiedDate: DateTime!
  lastModifiedBy: String!
  benefit: BenefitInput!
  chapter: OrganizationInput
  delectronicPaymentOption: DelectronicPaymentOptionInput!
  idNavigation: RootInput!
  nachaconfiguration: NachaconfigurationInput
  organization: OrganizationInput!
}

input ElectronicPaymentConfigurationSortInput {
  id: SortEnumType
  organizationId: SortEnumType
  unionId: SortEnumType
  benefitId: SortEnumType
  delectronicPaymentOptionId: SortEnumType
  nachaconfigurationId: SortEnumType
  chapterId: SortEnumType
  mandatory: SortEnumType
  creationDate: SortEnumType
  createdBy: SortEnumType
  lastModifiedDate: SortEnumType
  lastModifiedBy: SortEnumType
  benefit: BenefitSortInput
  chapter: OrganizationSortInput
  delectronicPaymentOption: DelectronicPaymentOptionSortInput
  idNavigation: RootSortInput
  nachaconfiguration: NachaconfigurationSortInput
  organization: OrganizationSortInput
}

input ElectronicPaymentFilterInput {
  and: [ElectronicPaymentFilterInput!]
  or: [ElectronicPaymentFilterInput!]
  id: IntOperationFilterInput
  reportId: IntOperationFilterInput
  benefitId: IntOperationFilterInput
  amount: DecimalOperationFilterInput
  dpaymentMethodTypeId: IntOperationFilterInput
  paymentsId: IntOperationFilterInput
  electronicBatchId: IntOperationFilterInput
  suppress: BooleanOperationFilterInput
  benefit: BenefitFilterInput
  dpaymentMethodType: DpaymentMethodTypeFilterInput
  eftpayment: EftpaymentFilterInput
  electronicBatch: ElectronicBatchFilterInput
  idNavigation: RootFilterInput
  payments: PaymentFilterInput
  report: ReportFilterInput
}

input ElectronicPaymentInput {
  id: Int!
  reportId: Int!
  benefitId: Int!
  amount: Decimal!
  dpaymentMethodTypeId: Int!
  paymentsId: Int
  electronicBatchId: Int
  suppress: Boolean!
  benefit: BenefitInput!
  dpaymentMethodType: DpaymentMethodTypeInput!
  eftpayment: EftpaymentInput
  electronicBatch: ElectronicBatchInput
  idNavigation: RootInput!
  payments: PaymentInput
  report: ReportInput!
}

input ElectronicPaymentSortInput {
  id: SortEnumType
  reportId: SortEnumType
  benefitId: SortEnumType
  amount: SortEnumType
  dpaymentMethodTypeId: SortEnumType
  paymentsId: SortEnumType
  electronicBatchId: SortEnumType
  suppress: SortEnumType
  benefit: BenefitSortInput
  dpaymentMethodType: DpaymentMethodTypeSortInput
  eftpayment: EftpaymentSortInput
  electronicBatch: ElectronicBatchSortInput
  idNavigation: RootSortInput
  payments: PaymentSortInput
  report: ReportSortInput
}

type EmailAddress {
  id: Int!
  emailAddress1: String
  demailAddressTypeId: Int!
  demailAddressType: DemailAddressType!
  idNavigation: ContactMechanism!
}

input EmailAddressFilterInput {
  and: [EmailAddressFilterInput!]
  or: [EmailAddressFilterInput!]
  id: IntOperationFilterInput
  emailAddress1: StringOperationFilterInput
  demailAddressTypeId: IntOperationFilterInput
  demailAddressType: DemailAddressTypeFilterInput
  idNavigation: ContactMechanismFilterInput
}

input EmailAddressInput {
  id: Int!
  emailAddress1: String
  demailAddressTypeId: Int!
  demailAddressType: DemailAddressTypeInput!
  idNavigation: ContactMechanismInput!
}

input EmailAddressSortInput {
  id: SortEnumType
  emailAddress1: SortEnumType
  demailAddressTypeId: SortEnumType
  demailAddressType: DemailAddressTypeSortInput
  idNavigation: ContactMechanismSortInput
}

type Employee implements Node {
  id: ID!
  birthDate: DateTime
  ssn: [Byte!]
  searchSsn: [Byte!]
  dateOfHire: DateTime
  dateOfTermination: DateTime
  homeLocalId: Int
  externalEmployeeId: String
  agreements: [Agreement!]!
  idNavigation: Person!
  reportLineItems: [ReportLineItem!]!
  firstName: String
  middleName: String
  lastName: String!
  suffix: String
  active: Boolean!
}

"""A connection to a list of items."""
type EmployeeConnection {
  """Information to aid in pagination."""
  pageInfo: PageInfo!

  """A list of edges."""
  edges: [EmployeeEdge!]

  """A flattened list of the nodes."""
  nodes: [Employee!]

  """Identifies the total count of items in the connection."""
  totalCount: Int!
}

type EmployeeDefaultSettings {
  employeeId: ID
  defaultAgreementId: Int
  defaultClassificationId: Int
  defaultHourlyRate: Decimal
}

"""A connection to a list of items."""
type EmployeeDefaultSettingsConnection {
  """Information to aid in pagination."""
  pageInfo: PageInfo!

  """A list of edges."""
  edges: [EmployeeDefaultSettingsEdge!]

  """A flattened list of the nodes."""
  nodes: [EmployeeDefaultSettings!]

  """Identifies the total count of items in the connection."""
  totalCount: Int!
}

"""An edge in a connection."""
type EmployeeDefaultSettingsEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of the edge."""
  node: EmployeeDefaultSettings!
}

"""An edge in a connection."""
type EmployeeEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of the edge."""
  node: Employee!
}

input EmployeeFilterInput {
  and: [EmployeeFilterInput!]
  or: [EmployeeFilterInput!]
  id: IdOperationFilterInput
  birthDate: DateTimeOperationFilterInput
  ssn: ListByteOperationFilterInput
  searchSsn: ListByteOperationFilterInput
  dateOfHire: DateTimeOperationFilterInput
  dateOfTermination: DateTimeOperationFilterInput
  homeLocalId: IntOperationFilterInput
  externalEmployeeId: StringOperationFilterInput
  agreements: ListFilterInputTypeOfAgreementFilterInput
  idNavigation: PersonFilterInput
  reportLineItems: ListFilterInputTypeOfReportLineItemFilterInput
  firstName: StringOperationFilterInput
  middleName: StringOperationFilterInput
  lastName: StringOperationFilterInput
  suffix: StringOperationFilterInput
  active: BooleanOperationFilterInput
}

input EmployeeInput {
  id: ID!
  birthDate: DateTime
  ssn: [Byte!]
  searchSsn: [Byte!]
  dateOfHire: DateTime
  dateOfTermination: DateTime
  homeLocalId: Int
  externalEmployeeId: String
  agreements: [AgreementInput!]!
  idNavigation: PersonInput!
  reportLineItems: [ReportLineItemInput!]!
}

input EmployeeSortInput {
  id: SortEnumType
  birthDate: SortEnumType
  dateOfHire: SortEnumType
  dateOfTermination: SortEnumType
  homeLocalId: SortEnumType
  externalEmployeeId: SortEnumType
  idNavigation: PersonSortInput
  firstName: SortEnumType
  middleName: SortEnumType
  lastName: SortEnumType
  suffix: SortEnumType
  active: SortEnumType
}

type Employer implements Node {
  id: ID!
  fein: String
  dba: String
  businessDescription: String
  associationId: String
  isAssociationMember: Boolean!
  organization: Organization
  root: Root
  costCodes: [CostCode!]!
  employersToAgreements: [EmployersToAgreement!]!
  fundingComments: [FundingComment!]!
  reports: [Report!]!
}

"""A connection to a list of items."""
type EmployerConnection {
  """Information to aid in pagination."""
  pageInfo: PageInfo!

  """A list of edges."""
  edges: [EmployerEdge!]

  """A flattened list of the nodes."""
  nodes: [Employer!]

  """Identifies the total count of items in the connection."""
  totalCount: Int!
}

"""An edge in a connection."""
type EmployerEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of the edge."""
  node: Employer!
}

input EmployerFilterInput {
  and: [EmployerFilterInput!]
  or: [EmployerFilterInput!]
  id: IdOperationFilterInput
  fein: StringOperationFilterInput
  dba: StringOperationFilterInput
  businessDescription: StringOperationFilterInput
  associationId: StringOperationFilterInput
  isAssociationMember: BooleanOperationFilterInput
  organization: OrganizationFilterInput
  root: RootFilterInput
  costCodes: ListFilterInputTypeOfCostCodeFilterInput
  employersToAgreements: ListFilterInputTypeOfEmployersToAgreementFilterInput
  fundingComments: ListFilterInputTypeOfFundingCommentFilterInput
  reports: ListFilterInputTypeOfReportFilterInput
}

input EmployerInput {
  id: ID!
  fein: String
  dba: String
  businessDescription: String
  associationId: String
  isAssociationMember: Boolean!
  organization: OrganizationInput
  root: RootInput
  costCodes: [CostCodeInput!]!
  employersToAgreements: [EmployersToAgreementInput!]!
  fundingComments: [FundingCommentInput!]!
  reports: [ReportInput!]!
}

type EmployerRosterView implements Node {
  id: ID!
  guid: UUID!
  name: String!
  fein: String
  dba: String
  associationId: String
  payrollContactFirstName: String
  payrollContactLastName: String
  payrollContactPhoneNumber: String
  payrollContactEmailAddress: String
  lastReportedDate: DateTime
  relationshipStatusId: Int
  chapterId: ID!
}

"""A connection to a list of items."""
type EmployerRosterViewConnection {
  """Information to aid in pagination."""
  pageInfo: PageInfo!

  """A list of edges."""
  edges: [EmployerRosterViewEdge!]

  """A flattened list of the nodes."""
  nodes: [EmployerRosterView!]

  """Identifies the total count of items in the connection."""
  totalCount: Int!
}

"""An edge in a connection."""
type EmployerRosterViewEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of the edge."""
  node: EmployerRosterView!
}

input EmployerRosterViewFilterInput {
  and: [EmployerRosterViewFilterInput!]
  or: [EmployerRosterViewFilterInput!]
  id: IdOperationFilterInput
  guid: UuidOperationFilterInput
  name: StringOperationFilterInput
  fein: StringOperationFilterInput
  dba: StringOperationFilterInput
  associationId: StringOperationFilterInput
  payrollContactFirstName: StringOperationFilterInput
  payrollContactLastName: StringOperationFilterInput
  payrollContactPhoneNumber: StringOperationFilterInput
  payrollContactEmailAddress: StringOperationFilterInput
  lastReportedDate: DateTimeOperationFilterInput
  relationshipStatusId: IntOperationFilterInput
  chapterId: IdOperationFilterInput
}

input EmployerRosterViewSortInput {
  id: SortEnumType
  guid: SortEnumType
  name: SortEnumType
  fein: SortEnumType
  dba: SortEnumType
  associationId: SortEnumType
  payrollContactFirstName: SortEnumType
  payrollContactLastName: SortEnumType
  payrollContactPhoneNumber: SortEnumType
  payrollContactEmailAddress: SortEnumType
  lastReportedDate: SortEnumType
  relationshipStatusId: SortEnumType
  chapterId: SortEnumType
}

input EmployerSortInput {
  id: SortEnumType
  fein: SortEnumType
  dba: SortEnumType
  businessDescription: SortEnumType
  associationId: SortEnumType
  isAssociationMember: SortEnumType
  organization: OrganizationSortInput
  root: RootSortInput
}

type EmployersToAgreement {
  id: Int!
  employerId: Int!
  agreementId: Int!
  agreement: Agreement!
  employer: Employer!
  idNavigation: Root!
}

input EmployersToAgreementFilterInput {
  and: [EmployersToAgreementFilterInput!]
  or: [EmployersToAgreementFilterInput!]
  id: IntOperationFilterInput
  employerId: IntOperationFilterInput
  agreementId: IntOperationFilterInput
  agreement: AgreementFilterInput
  employer: EmployerFilterInput
  idNavigation: RootFilterInput
}

input EmployersToAgreementInput {
  id: Int!
  employerId: Int!
  agreementId: Int!
  agreement: AgreementInput!
  employer: EmployerInput!
  idNavigation: RootInput!
}

input EmployersToAgreementSortInput {
  id: SortEnumType
  employerId: SortEnumType
  agreementId: SortEnumType
  agreement: AgreementSortInput
  employer: EmployerSortInput
  idNavigation: RootSortInput
}

type Epruser {
  aspnetUserId: UUID!
  organizationId: Int!
  firstName: String!
  lastName: String!
  bannerImageId: Int
  phoneNumber: String
  phoneTypeId: Int
  currentSessionId: String
  preferredMfamethod: String
  phoneNumberExtension: String
  aspnetUser: AspnetUser!
  bannerImage: Image
  organization: Organization!
}

input EpruserFilterInput {
  and: [EpruserFilterInput!]
  or: [EpruserFilterInput!]
  aspnetUserId: UuidOperationFilterInput
  organizationId: IntOperationFilterInput
  firstName: StringOperationFilterInput
  lastName: StringOperationFilterInput
  bannerImageId: IntOperationFilterInput
  phoneNumber: StringOperationFilterInput
  phoneTypeId: IntOperationFilterInput
  currentSessionId: StringOperationFilterInput
  preferredMfamethod: StringOperationFilterInput
  phoneNumberExtension: StringOperationFilterInput
  aspnetUser: AspnetUserFilterInput
  bannerImage: ImageFilterInput
  organization: OrganizationFilterInput
}

input EpruserInput {
  aspnetUserId: UUID!
  organizationId: Int!
  firstName: String!
  lastName: String!
  bannerImageId: Int
  phoneNumber: String
  phoneTypeId: Int
  currentSessionId: String
  preferredMfamethod: String
  phoneNumberExtension: String
  aspnetUser: AspnetUserInput!
  bannerImage: ImageInput
  organization: OrganizationInput!
}

input FloatOperationFilterInput {
  eq: Float
  neq: Float
  in: [Float]
  nin: [Float]
  gt: Float
  ngt: Float
  gte: Float
  ngte: Float
  lt: Float
  nlt: Float
  lte: Float
  nlte: Float
}

type FundAdministrator {
  id: Int!
  idNavigation: Organization!
}

input FundAdministratorFilterInput {
  and: [FundAdministratorFilterInput!]
  or: [FundAdministratorFilterInput!]
  id: IntOperationFilterInput
  idNavigation: OrganizationFilterInput
}

input FundAdministratorInput {
  id: Int!
  idNavigation: OrganizationInput!
}

input FundAdministratorSortInput {
  id: SortEnumType
  idNavigation: OrganizationSortInput
}

type FundingComment {
  id: Int!
  agreementId: Int!
  employerId: Int!
  reportId: Int!
  workMonth: DateTime!
  benefitId: Int!
  creationDate: DateTime!
  createdBy: String!
  lastModifiedDate: DateTime!
  lastModifiedBy: String!
  agreement: Agreement!
  benefit: Benefit!
  employer: Employer!
  report: Report!
}

type FundingCommentDetail {
  id: Int!
  fundingCommentsId: Int!
  organizationId: Int!
  comment: String
  creationDate: DateTime!
  createdBy: String!
  lastModifiedDate: DateTime!
  lastModifiedBy: String!
  organization: Organization!
}

input FundingCommentDetailFilterInput {
  and: [FundingCommentDetailFilterInput!]
  or: [FundingCommentDetailFilterInput!]
  id: IntOperationFilterInput
  fundingCommentsId: IntOperationFilterInput
  organizationId: IntOperationFilterInput
  comment: StringOperationFilterInput
  creationDate: DateTimeOperationFilterInput
  createdBy: StringOperationFilterInput
  lastModifiedDate: DateTimeOperationFilterInput
  lastModifiedBy: StringOperationFilterInput
  organization: OrganizationFilterInput
}

input FundingCommentDetailInput {
  id: Int!
  fundingCommentsId: Int!
  organizationId: Int!
  comment: String
  creationDate: DateTime!
  createdBy: String!
  lastModifiedDate: DateTime!
  lastModifiedBy: String!
  organization: OrganizationInput!
}

input FundingCommentFilterInput {
  and: [FundingCommentFilterInput!]
  or: [FundingCommentFilterInput!]
  id: IntOperationFilterInput
  agreementId: IntOperationFilterInput
  employerId: IntOperationFilterInput
  reportId: IntOperationFilterInput
  workMonth: DateTimeOperationFilterInput
  benefitId: IntOperationFilterInput
  creationDate: DateTimeOperationFilterInput
  createdBy: StringOperationFilterInput
  lastModifiedDate: DateTimeOperationFilterInput
  lastModifiedBy: StringOperationFilterInput
  agreement: AgreementFilterInput
  benefit: BenefitFilterInput
  employer: EmployerFilterInput
  report: ReportFilterInput
}

input FundingCommentInput {
  id: Int!
  agreementId: Int!
  employerId: Int!
  reportId: Int!
  workMonth: DateTime!
  benefitId: Int!
  creationDate: DateTime!
  createdBy: String!
  lastModifiedDate: DateTime!
  lastModifiedBy: String!
  agreement: AgreementInput!
  benefit: BenefitInput!
  employer: EmployerInput!
  report: ReportInput!
}

input IdOperationFilterInput {
  eq: ID
  neq: ID
  in: [ID]
  nin: [ID]
}

type Image {
  id: Int!
  fileName: String
  filePath: String
  active: Boolean!
  eprusers: [Epruser!]!
  idNavigation: Root!
}

input ImageFilterInput {
  and: [ImageFilterInput!]
  or: [ImageFilterInput!]
  id: IntOperationFilterInput
  fileName: StringOperationFilterInput
  filePath: StringOperationFilterInput
  active: BooleanOperationFilterInput
  eprusers: ListFilterInputTypeOfEpruserFilterInput
  idNavigation: RootFilterInput
}

input ImageInput {
  id: Int!
  fileName: String
  filePath: String
  active: Boolean!
  eprusers: [EpruserInput!]!
  idNavigation: RootInput!
}

input ImageSortInput {
  id: SortEnumType
  fileName: SortEnumType
  filePath: SortEnumType
  active: SortEnumType
  idNavigation: RootSortInput
}

input IntOperationFilterInput {
  eq: Int
  neq: Int
  in: [Int]
  nin: [Int]
  gt: Int
  ngt: Int
  gte: Int
  ngte: Int
  lt: Int
  nlt: Int
  lte: Int
  nlte: Int
}

scalar JSON

type Kbaanswer {
  id: Int!
  userGuid: UUID!
  category: Int!
  questionId: Int!
  answer: String!
  question: Kbaquestion!
  user: AspnetUser!
}

input KbaanswerFilterInput {
  and: [KbaanswerFilterInput!]
  or: [KbaanswerFilterInput!]
  id: IntOperationFilterInput
  userGuid: UuidOperationFilterInput
  category: IntOperationFilterInput
  questionId: IntOperationFilterInput
  answer: StringOperationFilterInput
  question: KbaquestionFilterInput
  user: AspnetUserFilterInput
}

input KbaanswerInput {
  id: Int!
  userGuid: UUID!
  category: Int!
  questionId: Int!
  answer: String!
  question: KbaquestionInput!
  user: AspnetUserInput!
}

type Kbaquestion {
  id: Int!
  question: String!
  category: Int!
  kbaanswers: [Kbaanswer!]!
}

input KbaquestionFilterInput {
  and: [KbaquestionFilterInput!]
  or: [KbaquestionFilterInput!]
  id: IntOperationFilterInput
  question: StringOperationFilterInput
  category: IntOperationFilterInput
  kbaanswers: ListFilterInputTypeOfKbaanswerFilterInput
}

input KbaquestionInput {
  id: Int!
  question: String!
  category: Int!
  kbaanswers: [KbaanswerInput!]!
}

type KeyValuePairOfStringAndMetadata {
  key: String!
  value: Metadata!
}

input LinkThirdPartyInput {
  name: String!
  chapterId: Int!
  relationshipTypeId: Int!
  username: String!
}

type LinkThirdPartyPayload {
  thirdPartyInfoDtoEdge: EdgeOfThirdPartyInfoDto
}

input ListByteOperationFilterInput {
  all: ByteOperationFilterInput
  none: ByteOperationFilterInput
  some: ByteOperationFilterInput
  any: Boolean
}

input ListFilterInputTypeOfAddressFilterInput {
  all: AddressFilterInput
  none: AddressFilterInput
  some: AddressFilterInput
  any: Boolean
}

input ListFilterInputTypeOfAgreementFilterInput {
  all: AgreementFilterInput
  none: AgreementFilterInput
  some: AgreementFilterInput
  any: Boolean
}

input ListFilterInputTypeOfAgreementsToBenefitFilterInput {
  all: AgreementsToBenefitFilterInput
  none: AgreementsToBenefitFilterInput
  some: AgreementsToBenefitFilterInput
  any: Boolean
}

input ListFilterInputTypeOfAspnetMembershipFilterInput {
  all: AspnetMembershipFilterInput
  none: AspnetMembershipFilterInput
  some: AspnetMembershipFilterInput
  any: Boolean
}

input ListFilterInputTypeOfAspnetPathFilterInput {
  all: AspnetPathFilterInput
  none: AspnetPathFilterInput
  some: AspnetPathFilterInput
  any: Boolean
}

input ListFilterInputTypeOfAspnetPersonalizationPerUserFilterInput {
  all: AspnetPersonalizationPerUserFilterInput
  none: AspnetPersonalizationPerUserFilterInput
  some: AspnetPersonalizationPerUserFilterInput
  any: Boolean
}

input ListFilterInputTypeOfAspnetRoleFilterInput {
  all: AspnetRoleFilterInput
  none: AspnetRoleFilterInput
  some: AspnetRoleFilterInput
  any: Boolean
}

input ListFilterInputTypeOfAspnetUserFilterInput {
  all: AspnetUserFilterInput
  none: AspnetUserFilterInput
  some: AspnetUserFilterInput
  any: Boolean
}

input ListFilterInputTypeOfBenefitFilterInput {
  all: BenefitFilterInput
  none: BenefitFilterInput
  some: BenefitFilterInput
  any: Boolean
}

input ListFilterInputTypeOfBenefitOverridesOrigFilterInput {
  all: BenefitOverridesOrigFilterInput
  none: BenefitOverridesOrigFilterInput
  some: BenefitOverridesOrigFilterInput
  any: Boolean
}

input ListFilterInputTypeOfClassificationNameFilterInput {
  all: ClassificationNameFilterInput
  none: ClassificationNameFilterInput
  some: ClassificationNameFilterInput
  any: Boolean
}

input ListFilterInputTypeOfContactMechanismFilterInput {
  all: ContactMechanismFilterInput
  none: ContactMechanismFilterInput
  some: ContactMechanismFilterInput
  any: Boolean
}

input ListFilterInputTypeOfCostCodeFilterInput {
  all: CostCodeFilterInput
  none: CostCodeFilterInput
  some: CostCodeFilterInput
  any: Boolean
}

input ListFilterInputTypeOfCustomReportProductFilterInput {
  all: CustomReportProductFilterInput
  none: CustomReportProductFilterInput
  some: CustomReportProductFilterInput
  any: Boolean
}

input ListFilterInputTypeOfElectronicPaymentConfigurationFilterInput {
  all: ElectronicPaymentConfigurationFilterInput
  none: ElectronicPaymentConfigurationFilterInput
  some: ElectronicPaymentConfigurationFilterInput
  any: Boolean
}

input ListFilterInputTypeOfElectronicPaymentFilterInput {
  all: ElectronicPaymentFilterInput
  none: ElectronicPaymentFilterInput
  some: ElectronicPaymentFilterInput
  any: Boolean
}

input ListFilterInputTypeOfEmailAddressFilterInput {
  all: EmailAddressFilterInput
  none: EmailAddressFilterInput
  some: EmailAddressFilterInput
  any: Boolean
}

input ListFilterInputTypeOfEmployersToAgreementFilterInput {
  all: EmployersToAgreementFilterInput
  none: EmployersToAgreementFilterInput
  some: EmployersToAgreementFilterInput
  any: Boolean
}

input ListFilterInputTypeOfEpruserFilterInput {
  all: EpruserFilterInput
  none: EpruserFilterInput
  some: EpruserFilterInput
  any: Boolean
}

input ListFilterInputTypeOfFundingCommentDetailFilterInput {
  all: FundingCommentDetailFilterInput
  none: FundingCommentDetailFilterInput
  some: FundingCommentDetailFilterInput
  any: Boolean
}

input ListFilterInputTypeOfFundingCommentFilterInput {
  all: FundingCommentFilterInput
  none: FundingCommentFilterInput
  some: FundingCommentFilterInput
  any: Boolean
}

input ListFilterInputTypeOfKbaanswerFilterInput {
  all: KbaanswerFilterInput
  none: KbaanswerFilterInput
  some: KbaanswerFilterInput
  any: Boolean
}

input ListFilterInputTypeOfNewsItemFilterInput {
  all: NewsItemFilterInput
  none: NewsItemFilterInput
  some: NewsItemFilterInput
  any: Boolean
}

input ListFilterInputTypeOfNoteFilterInput {
  all: NoteFilterInput
  none: NoteFilterInput
  some: NoteFilterInput
  any: Boolean
}

input ListFilterInputTypeOfOrderDetailFilterInput {
  all: OrderDetailFilterInput
  none: OrderDetailFilterInput
  some: OrderDetailFilterInput
  any: Boolean
}

input ListFilterInputTypeOfOrderFilterInput {
  all: OrderFilterInput
  none: OrderFilterInput
  some: OrderFilterInput
  any: Boolean
}

input ListFilterInputTypeOfOrganizationFilterInput {
  all: OrganizationFilterInput
  none: OrganizationFilterInput
  some: OrganizationFilterInput
  any: Boolean
}

input ListFilterInputTypeOfPartiesToContactMechanismFilterInput {
  all: PartiesToContactMechanismFilterInput
  none: PartiesToContactMechanismFilterInput
  some: PartiesToContactMechanismFilterInput
  any: Boolean
}

input ListFilterInputTypeOfPartyFilterInput {
  all: PartyFilterInput
  none: PartyFilterInput
  some: PartyFilterInput
  any: Boolean
}

input ListFilterInputTypeOfPaymentDetailFilterInput {
  all: PaymentDetailFilterInput
  none: PaymentDetailFilterInput
  some: PaymentDetailFilterInput
  any: Boolean
}

input ListFilterInputTypeOfPaymentFilterInput {
  all: PaymentFilterInput
  none: PaymentFilterInput
  some: PaymentFilterInput
  any: Boolean
}

input ListFilterInputTypeOfPaymentMethodFilterInput {
  all: PaymentMethodFilterInput
  none: PaymentMethodFilterInput
  some: PaymentMethodFilterInput
  any: Boolean
}

input ListFilterInputTypeOfPayStubDetailFilterInput {
  all: PayStubDetailFilterInput
  none: PayStubDetailFilterInput
  some: PayStubDetailFilterInput
  any: Boolean
}

input ListFilterInputTypeOfPayStubFilterInput {
  all: PayStubFilterInput
  none: PayStubFilterInput
  some: PayStubFilterInput
  any: Boolean
}

input ListFilterInputTypeOfPersonFilterInput {
  all: PersonFilterInput
  none: PersonFilterInput
  some: PersonFilterInput
  any: Boolean
}

input ListFilterInputTypeOfPhoneNumberFilterInput {
  all: PhoneNumberFilterInput
  none: PhoneNumberFilterInput
  some: PhoneNumberFilterInput
  any: Boolean
}

input ListFilterInputTypeOfProduct1FilterInput {
  all: Product1FilterInput
  none: Product1FilterInput
  some: Product1FilterInput
  any: Boolean
}

input ListFilterInputTypeOfProductCategoryFilterInput {
  all: ProductCategoryFilterInput
  none: ProductCategoryFilterInput
  some: ProductCategoryFilterInput
  any: Boolean
}

input ListFilterInputTypeOfProductsToOrganizationTypeFilterInput {
  all: ProductsToOrganizationTypeFilterInput
  none: ProductsToOrganizationTypeFilterInput
  some: ProductsToOrganizationTypeFilterInput
  any: Boolean
}

input ListFilterInputTypeOfRateScheduleFilterInput {
  all: RateScheduleFilterInput
  none: RateScheduleFilterInput
  some: RateScheduleFilterInput
  any: Boolean
}

input ListFilterInputTypeOfRelationshipFilterInput {
  all: RelationshipFilterInput
  none: RelationshipFilterInput
  some: RelationshipFilterInput
  any: Boolean
}

input ListFilterInputTypeOfRelationshipStatusFilterInput {
  all: RelationshipStatusFilterInput
  none: RelationshipStatusFilterInput
  some: RelationshipStatusFilterInput
  any: Boolean
}

input ListFilterInputTypeOfReportedBenefitReleaseAuthorizationFilterInput {
  all: ReportedBenefitReleaseAuthorizationFilterInput
  none: ReportedBenefitReleaseAuthorizationFilterInput
  some: ReportedBenefitReleaseAuthorizationFilterInput
  any: Boolean
}

input ListFilterInputTypeOfReportFilterInput {
  all: ReportFilterInput
  none: ReportFilterInput
  some: ReportFilterInput
  any: Boolean
}

input ListFilterInputTypeOfReportLineItemDetailFilterInput {
  all: ReportLineItemDetailFilterInput
  none: ReportLineItemDetailFilterInput
  some: ReportLineItemDetailFilterInput
  any: Boolean
}

input ListFilterInputTypeOfReportLineItemFilterInput {
  all: ReportLineItemFilterInput
  none: ReportLineItemFilterInput
  some: ReportLineItemFilterInput
  any: Boolean
}

input ListFilterInputTypeOfReportSuppressionFilterInput {
  all: ReportSuppressionFilterInput
  none: ReportSuppressionFilterInput
  some: ReportSuppressionFilterInput
  any: Boolean
}

input ListFilterInputTypeOfRoleGroupFilterInput {
  all: RoleGroupFilterInput
  none: RoleGroupFilterInput
  some: RoleGroupFilterInput
  any: Boolean
}

input ListFilterInputTypeOfServiceSubscriptionFilterInput {
  all: ServiceSubscriptionFilterInput
  none: ServiceSubscriptionFilterInput
  some: ServiceSubscriptionFilterInput
  any: Boolean
}

input ListFilterInputTypeOfSettingFilterInput {
  all: SettingFilterInput
  none: SettingFilterInput
  some: SettingFilterInput
  any: Boolean
}

input ListFilterInputTypeOfSubClassificationFilterInput {
  all: SubClassificationFilterInput
  none: SubClassificationFilterInput
  some: SubClassificationFilterInput
  any: Boolean
}

input ListFilterInputTypeOfWebsiteFilterInput {
  all: WebsiteFilterInput
  none: WebsiteFilterInput
  some: WebsiteFilterInput
  any: Boolean
}

"""
The `LocalDate` scalar type represents a ISO date string, represented as UTF-8
character sequences YYYY-MM-DD. The scalar follows the specification defined in RFC3339
"""
scalar LocalDate

input LocalDateOperationFilterInput {
  eq: LocalDate
  neq: LocalDate
  in: [LocalDate]
  nin: [LocalDate]
  gt: LocalDate
  ngt: LocalDate
  gte: LocalDate
  ngte: LocalDate
  lt: LocalDate
  nlt: LocalDate
  lte: LocalDate
  nlte: LocalDate
}

type Metadata {
  fieldName: String!
  displayName: String!
  displayFormat: String
}

input ModifyPayStubDetailInput {
  id: ID
  name: String
  workDate: LocalDate!
  otHours: Float
  stHours: Float
  dtHours: Float
  jobCode: String
  earningsCode: String
  agreementId: Int
  classificationId: Int
  subClassificationId: Int
  costCenter: String
  hourlyRate: Float
  bonus: Float
  expenses: Float
  payStubId: ID
  delete: Boolean
  reportLineItemId: Int
}

input ModifyPayStubInput {
  id: ID!
  employeeId: ID!
  name: String
  details: [ModifyPayStubDetailInput!]
  expanded: Boolean
  inEdit: Boolean
  employeeName: String
  stHours: Float
  otHours: Float
  dtHours: Float
  bonus: Float
  expenses: Float
}

input ModifyTimeSheetInput {
  id: ID!
  employerGuid: UUID!
  name: String
  status: String
  type: String
  modificationDate: DateTime
  showDTHoursColumn: Boolean
  showCostCenterColumn: Boolean
  showBonusColumn: Boolean
  showExpensesColumn: Boolean
  showEarningsCodesColumn: Boolean
  readOnly: Boolean
  addPayStubs: [AddPayStubInput!]
  modifyPayStubs: [ModifyPayStubInput!]
  deletePayStubIds: [ID!]
  timeSheetId: UUID
}

type ModifyTimeSheetPayload {
  timeSheet: TimeSheet
}

type Mutation {
  changePassword(input: ChangePasswordInput!): ChangePasswordPayload!
  createCustomViews(input: CreateCustomViewsInput!): CreateCustomViewsPayload!
  updateCustomViews(input: UpdateCustomViewsInput!): UpdateCustomViewsPayload!
  addEmployer(input: AddEmployerInput!): AddEmployerPayload!
  deleteEmployer(input: DeleteEmployerInput!): DeleteEmployerPayload!
  deleteRelationshipWithSiteSponsor(input: DeleteRelationshipWithSiteSponsorInput!): DeleteRelationshipWithSiteSponsorPayload!
  addChapter(chapterInput: AddChapterInput!): AddChapterPayload!
  addFundAdministrator(input: AddFundAdministratorInput!): AddFundAdministratorPayload!
  addUnion(input: AddUnionInput!): AddUnionPayload!
  linkThirdParty(input: LinkThirdPartyInput!): LinkThirdPartyPayload!
  saveSetting(input: SaveSettingInput!): SaveSettingPayload!
  deleteTimesheet(input: DeleteTimesheetInput!): DeleteTimesheetPayload!
  addTimesheet(input: AddTimesheetInput!): AddTimesheetPayload!
  modifyTimeSheet(input: ModifyTimeSheetInput!): ModifyTimeSheetPayload!
  addEmptyPayStub(input: AddEmptyPayStubInput!): AddEmptyPayStubPayload!
}

type Nachaconfiguration {
  id: Int!
  collectingAgentId: Int!
  name: String!
  routingNumber: String!
  offsetEnabled: Boolean!
  immediateDestinationName: String
  immediateOriginName: String
  referenceCode: String
  companyDiscretionaryData: String
  companyDescriptiveDate: String
  offsetRoutingNumber: String
  offsetAccountNumber: String
  offsetDescription: String
  immediateOrigin: String
  companyIdentification: String
  companyName: String
  electronicPaymentConfigurations: [ElectronicPaymentConfiguration!]!
}

input NachaconfigurationFilterInput {
  and: [NachaconfigurationFilterInput!]
  or: [NachaconfigurationFilterInput!]
  id: IntOperationFilterInput
  collectingAgentId: IntOperationFilterInput
  name: StringOperationFilterInput
  routingNumber: StringOperationFilterInput
  offsetEnabled: BooleanOperationFilterInput
  immediateDestinationName: StringOperationFilterInput
  immediateOriginName: StringOperationFilterInput
  referenceCode: StringOperationFilterInput
  companyDiscretionaryData: StringOperationFilterInput
  companyDescriptiveDate: StringOperationFilterInput
  offsetRoutingNumber: StringOperationFilterInput
  offsetAccountNumber: StringOperationFilterInput
  offsetDescription: StringOperationFilterInput
  immediateOrigin: StringOperationFilterInput
  companyIdentification: StringOperationFilterInput
  companyName: StringOperationFilterInput
  electronicPaymentConfigurations: ListFilterInputTypeOfElectronicPaymentConfigurationFilterInput
}

input NachaconfigurationInput {
  id: Int!
  collectingAgentId: Int!
  name: String!
  routingNumber: String!
  offsetEnabled: Boolean!
  immediateDestinationName: String
  immediateOriginName: String
  referenceCode: String
  companyDiscretionaryData: String
  companyDescriptiveDate: String
  offsetRoutingNumber: String
  offsetAccountNumber: String
  offsetDescription: String
  immediateOrigin: String
  companyIdentification: String
  companyName: String
  electronicPaymentConfigurations: [ElectronicPaymentConfigurationInput!]!
}

input NachaconfigurationSortInput {
  id: SortEnumType
  collectingAgentId: SortEnumType
  name: SortEnumType
  routingNumber: SortEnumType
  offsetEnabled: SortEnumType
  immediateDestinationName: SortEnumType
  immediateOriginName: SortEnumType
  referenceCode: SortEnumType
  companyDiscretionaryData: SortEnumType
  companyDescriptiveDate: SortEnumType
  offsetRoutingNumber: SortEnumType
  offsetAccountNumber: SortEnumType
  offsetDescription: SortEnumType
  immediateOrigin: SortEnumType
  companyIdentification: SortEnumType
  companyName: SortEnumType
}

type NewsItem {
  id: Int!
  title: String
  navigationUrl: String
  description: String
  publishDate: DateTime
  imageUrl: String
  roleGroup: Int!
  creationDate: DateTime!
  createdBy: String!
  lastModifiedDate: DateTime!
  lastModifiedBy: String!
  active: Boolean!
  roleGroupNavigation: RoleGroup!
}

input NewsItemFilterInput {
  and: [NewsItemFilterInput!]
  or: [NewsItemFilterInput!]
  id: IntOperationFilterInput
  title: StringOperationFilterInput
  navigationUrl: StringOperationFilterInput
  description: StringOperationFilterInput
  publishDate: DateTimeOperationFilterInput
  imageUrl: StringOperationFilterInput
  roleGroup: IntOperationFilterInput
  creationDate: DateTimeOperationFilterInput
  createdBy: StringOperationFilterInput
  lastModifiedDate: DateTimeOperationFilterInput
  lastModifiedBy: StringOperationFilterInput
  active: BooleanOperationFilterInput
  roleGroupNavigation: RoleGroupFilterInput
}

input NewsItemInput {
  id: Int!
  title: String
  navigationUrl: String
  description: String
  publishDate: DateTime
  imageUrl: String
  roleGroup: Int!
  creationDate: DateTime!
  createdBy: String!
  lastModifiedDate: DateTime!
  lastModifiedBy: String!
  active: Boolean!
  roleGroupNavigation: RoleGroupInput!
}

"""
The node interface is implemented by entities that have a global unique identifier.
"""
interface Node {
  id: ID!
}

type Note {
  id: Int!
  rootId: Int!
  subject: String
  body: String
  idNavigation: Root!
  root: Root!
}

input NoteFilterInput {
  and: [NoteFilterInput!]
  or: [NoteFilterInput!]
  id: IntOperationFilterInput
  rootId: IntOperationFilterInput
  subject: StringOperationFilterInput
  body: StringOperationFilterInput
  idNavigation: RootFilterInput
  root: RootFilterInput
}

input NoteInput {
  id: Int!
  rootId: Int!
  subject: String
  body: String
  idNavigation: RootInput!
  root: RootInput!
}

type OperationResult {
  succeeded: OperationResult!
  failed(message: String!): OperationResult!
  success: Boolean!
  message: String!
}

type OperationResultOfSetting {
  succeeded(message: String!, data: SettingInput!): OperationResultOfSetting!
  failed(message: String!): OperationResultOfSetting!
  data: Setting!
  success: Boolean!
  message: String!
}

type Order {
  id: Int!
  organizationId: Int!
  statusId: Int!
  confirmationNumber: String
  creationDate: DateTime!
  createdBy: String!
  lastModifiedDate: DateTime!
  lastModifiedBy: String!
  orderDetails: [OrderDetail!]!
  organization: Organization!
  status: DorderStatus!
}

type OrderDetail {
  id: Int!
  orderId: Int!
  productId: Int!
  price: Decimal!
  paymentTypeId: Int!
  creationDate: DateTime!
  createdBy: String!
  lastModifiedDate: DateTime!
  lastModifiedBy: String!
  order: Order!
  paymentType: DpaymentType!
  product: Product1!
}

input OrderDetailFilterInput {
  and: [OrderDetailFilterInput!]
  or: [OrderDetailFilterInput!]
  id: IntOperationFilterInput
  orderId: IntOperationFilterInput
  productId: IntOperationFilterInput
  price: DecimalOperationFilterInput
  paymentTypeId: IntOperationFilterInput
  creationDate: DateTimeOperationFilterInput
  createdBy: StringOperationFilterInput
  lastModifiedDate: DateTimeOperationFilterInput
  lastModifiedBy: StringOperationFilterInput
  order: OrderFilterInput
  paymentType: DpaymentTypeFilterInput
  product: Product1FilterInput
}

input OrderDetailInput {
  id: Int!
  orderId: Int!
  productId: Int!
  price: Decimal!
  paymentTypeId: Int!
  creationDate: DateTime!
  createdBy: String!
  lastModifiedDate: DateTime!
  lastModifiedBy: String!
  order: OrderInput!
  paymentType: DpaymentTypeInput!
  product: Product1Input!
}

input OrderFilterInput {
  and: [OrderFilterInput!]
  or: [OrderFilterInput!]
  id: IntOperationFilterInput
  organizationId: IntOperationFilterInput
  statusId: IntOperationFilterInput
  confirmationNumber: StringOperationFilterInput
  creationDate: DateTimeOperationFilterInput
  createdBy: StringOperationFilterInput
  lastModifiedDate: DateTimeOperationFilterInput
  lastModifiedBy: StringOperationFilterInput
  orderDetails: ListFilterInputTypeOfOrderDetailFilterInput
  organization: OrganizationFilterInput
  status: DorderStatusFilterInput
}

input OrderInput {
  id: Int!
  organizationId: Int!
  statusId: Int!
  confirmationNumber: String
  creationDate: DateTime!
  createdBy: String!
  lastModifiedDate: DateTime!
  lastModifiedBy: String!
  orderDetails: [OrderDetailInput!]!
  organization: OrganizationInput!
  status: DorderStatusInput!
}

type Organization {
  id: Int!
  name: String!
  dorganizationTypeId: Int!
  optimisticLockField: Int
  logoFilePath: String
  agreementsToBenefitCollectingAgents: [AgreementsToBenefit!]!
  agreementsToBenefitFundAdministrators: [AgreementsToBenefit!]!
  chapter: Chapter
  dorganizationType: DorganizationType!
  electronicPaymentConfigurationChapters: [ElectronicPaymentConfiguration!]!
  electronicPaymentConfigurationOrganizations: [ElectronicPaymentConfiguration!]!
  employer: Employer
  eprusers: [Epruser!]!
  fundAdministrator: FundAdministrator
  fundingCommentDetails: [FundingCommentDetail!]!
  idNavigation: Party!
  orders: [Order!]!
  serviceSubscriptions: [ServiceSubscription!]!
  trade: Trade
  union: Union
  agreements: [Agreement!]!
}

input OrganizationFilterInput {
  and: [OrganizationFilterInput!]
  or: [OrganizationFilterInput!]
  id: IntOperationFilterInput
  name: StringOperationFilterInput
  dorganizationTypeId: IntOperationFilterInput
  optimisticLockField: IntOperationFilterInput
  logoFilePath: StringOperationFilterInput
  agreementsToBenefitCollectingAgents: ListFilterInputTypeOfAgreementsToBenefitFilterInput
  agreementsToBenefitFundAdministrators: ListFilterInputTypeOfAgreementsToBenefitFilterInput
  chapter: ChapterFilterInput
  dorganizationType: DorganizationTypeFilterInput
  electronicPaymentConfigurationChapters: ListFilterInputTypeOfElectronicPaymentConfigurationFilterInput
  electronicPaymentConfigurationOrganizations: ListFilterInputTypeOfElectronicPaymentConfigurationFilterInput
  employer: EmployerFilterInput
  eprusers: ListFilterInputTypeOfEpruserFilterInput
  fundAdministrator: FundAdministratorFilterInput
  fundingCommentDetails: ListFilterInputTypeOfFundingCommentDetailFilterInput
  idNavigation: PartyFilterInput
  orders: ListFilterInputTypeOfOrderFilterInput
  serviceSubscriptions: ListFilterInputTypeOfServiceSubscriptionFilterInput
  trade: TradeFilterInput
  union: UnionFilterInput
  agreements: ListFilterInputTypeOfAgreementFilterInput
}

input OrganizationInput {
  id: Int!
  name: String!
  dorganizationTypeId: Int!
  optimisticLockField: Int
  logoFilePath: String
  agreementsToBenefitCollectingAgents: [AgreementsToBenefitInput!]!
  agreementsToBenefitFundAdministrators: [AgreementsToBenefitInput!]!
  chapter: ChapterInput
  dorganizationType: DorganizationTypeInput!
  electronicPaymentConfigurationChapters: [ElectronicPaymentConfigurationInput!]!
  electronicPaymentConfigurationOrganizations: [ElectronicPaymentConfigurationInput!]!
  employer: EmployerInput
  eprusers: [EpruserInput!]!
  fundAdministrator: FundAdministratorInput
  fundingCommentDetails: [FundingCommentDetailInput!]!
  idNavigation: PartyInput!
  orders: [OrderInput!]!
  serviceSubscriptions: [ServiceSubscriptionInput!]!
  trade: TradeInput
  union: UnionInput
  agreements: [AgreementInput!]!
}

input OrganizationSortInput {
  id: SortEnumType
  name: SortEnumType
  dorganizationTypeId: SortEnumType
  optimisticLockField: SortEnumType
  logoFilePath: SortEnumType
  chapter: ChapterSortInput
  dorganizationType: DorganizationTypeSortInput
  employer: EmployerSortInput
  fundAdministrator: FundAdministratorSortInput
  idNavigation: PartySortInput
  trade: TradeSortInput
  union: UnionSortInput
}

"""Information about pagination in a connection."""
type PageInfo {
  """
  Indicates whether more edges exist following the set defined by the clients arguments.
  """
  hasNextPage: Boolean!

  """
  Indicates whether more edges exist prior the set defined by the clients arguments.
  """
  hasPreviousPage: Boolean!

  """When paginating backwards, the cursor to continue."""
  startCursor: String

  """When paginating forwards, the cursor to continue."""
  endCursor: String
}

type PartiesToContactMechanism {
  id: Int!
  partyId: Int!
  contactMechanismId: Int!
  contactMechanism: ContactMechanism!
  idNavigation: Root!
  party: Party!
}

input PartiesToContactMechanismFilterInput {
  and: [PartiesToContactMechanismFilterInput!]
  or: [PartiesToContactMechanismFilterInput!]
  id: IntOperationFilterInput
  partyId: IntOperationFilterInput
  contactMechanismId: IntOperationFilterInput
  contactMechanism: ContactMechanismFilterInput
  idNavigation: RootFilterInput
  party: PartyFilterInput
}

input PartiesToContactMechanismInput {
  id: Int!
  partyId: Int!
  contactMechanismId: Int!
  contactMechanism: ContactMechanismInput!
  idNavigation: RootInput!
  party: PartyInput!
}

input PartiesToContactMechanismSortInput {
  id: SortEnumType
  partyId: SortEnumType
  contactMechanismId: SortEnumType
  contactMechanism: ContactMechanismSortInput
  idNavigation: RootSortInput
  party: PartySortInput
}

type Party {
  id: Int!
  dpartyTypeId: Int!
  benefitOverridesOrigs: [BenefitOverridesOrig!]!
  dpartyType: DpartyType!
  idNavigation: Root!
  organization: Organization
  partiesToContactMechanisms: [PartiesToContactMechanism!]!
  person: Person
  relationshipLeftParties: [Relationship!]!
  relationshipRightParties: [Relationship!]!
}

input PartyFilterInput {
  and: [PartyFilterInput!]
  or: [PartyFilterInput!]
  id: IntOperationFilterInput
  dpartyTypeId: IntOperationFilterInput
  benefitOverridesOrigs: ListFilterInputTypeOfBenefitOverridesOrigFilterInput
  dpartyType: DpartyTypeFilterInput
  idNavigation: RootFilterInput
  organization: OrganizationFilterInput
  partiesToContactMechanisms: ListFilterInputTypeOfPartiesToContactMechanismFilterInput
  person: PersonFilterInput
  relationshipLeftParties: ListFilterInputTypeOfRelationshipFilterInput
  relationshipRightParties: ListFilterInputTypeOfRelationshipFilterInput
}

input PartyInput {
  id: Int!
  dpartyTypeId: Int!
  benefitOverridesOrigs: [BenefitOverridesOrigInput!]!
  dpartyType: DpartyTypeInput!
  idNavigation: RootInput!
  organization: OrganizationInput
  partiesToContactMechanisms: [PartiesToContactMechanismInput!]!
  person: PersonInput
  relationshipLeftParties: [RelationshipInput!]!
  relationshipRightParties: [RelationshipInput!]!
}

input PartySortInput {
  id: SortEnumType
  dpartyTypeId: SortEnumType
  dpartyType: DpartyTypeSortInput
  idNavigation: RootSortInput
  organization: OrganizationSortInput
  person: PersonSortInput
}

type Payment {
  id: Int!
  reportId: Int!
  amount: Decimal!
  collectingAgentId: Int!
  paymentDate: DateTime!
  paymentNumber: String
  electronicPayments: [ElectronicPayment!]!
  idNavigation: Root!
  paymentDetails: [PaymentDetail!]!
  report: Report!
}

type PaymentDetail {
  paymentId: Int!
  benefitId: Int!
  amount: Decimal!
  benefit: Benefit!
  payment: Payment!
}

input PaymentDetailFilterInput {
  and: [PaymentDetailFilterInput!]
  or: [PaymentDetailFilterInput!]
  paymentId: IntOperationFilterInput
  benefitId: IntOperationFilterInput
  amount: DecimalOperationFilterInput
  benefit: BenefitFilterInput
  payment: PaymentFilterInput
}

input PaymentDetailInput {
  paymentId: Int!
  benefitId: Int!
  amount: Decimal!
  benefit: BenefitInput!
  payment: PaymentInput!
}

input PaymentFilterInput {
  and: [PaymentFilterInput!]
  or: [PaymentFilterInput!]
  id: IntOperationFilterInput
  reportId: IntOperationFilterInput
  amount: DecimalOperationFilterInput
  collectingAgentId: IntOperationFilterInput
  paymentDate: DateTimeOperationFilterInput
  paymentNumber: StringOperationFilterInput
  electronicPayments: ListFilterInputTypeOfElectronicPaymentFilterInput
  idNavigation: RootFilterInput
  paymentDetails: ListFilterInputTypeOfPaymentDetailFilterInput
  report: ReportFilterInput
}

input PaymentInput {
  id: Int!
  reportId: Int!
  amount: Decimal!
  collectingAgentId: Int!
  paymentDate: DateTime!
  paymentNumber: String
  electronicPayments: [ElectronicPaymentInput!]!
  idNavigation: RootInput!
  paymentDetails: [PaymentDetailInput!]!
  report: ReportInput!
}

type PaymentMethod {
  id: Int!
  associatedGuid: UUID!
  dpaymentMethodTypeId: Int!
  creditCardPaymentMethod: CreditCardPaymentMethod
  dpaymentMethodType: DpaymentMethodType!
  eftpaymentMethod: EftpaymentMethod
  idNavigation: Root!
}

input PaymentMethodFilterInput {
  and: [PaymentMethodFilterInput!]
  or: [PaymentMethodFilterInput!]
  id: IntOperationFilterInput
  associatedGuid: UuidOperationFilterInput
  dpaymentMethodTypeId: IntOperationFilterInput
  creditCardPaymentMethod: CreditCardPaymentMethodFilterInput
  dpaymentMethodType: DpaymentMethodTypeFilterInput
  eftpaymentMethod: EftpaymentMethodFilterInput
  idNavigation: RootFilterInput
}

input PaymentMethodInput {
  id: Int!
  associatedGuid: UUID!
  dpaymentMethodTypeId: Int!
  creditCardPaymentMethod: CreditCardPaymentMethodInput
  dpaymentMethodType: DpaymentMethodTypeInput!
  eftpaymentMethod: EftpaymentMethodInput
  idNavigation: RootInput!
}

input PaymentSortInput {
  id: SortEnumType
  reportId: SortEnumType
  amount: SortEnumType
  collectingAgentId: SortEnumType
  paymentDate: SortEnumType
  paymentNumber: SortEnumType
  idNavigation: RootSortInput
  report: ReportSortInput
}

type PayStub implements Node {
  id: ID!
  name: String
  employeeId: ID!
  timeSheetId: Int!
  totalHours: Float
  details: [PayStubDetail!]!
  timeSheet: TimeSheet!
  employee: Employee!
}

"""A connection to a list of items."""
type PayStubConnection {
  """Information to aid in pagination."""
  pageInfo: PageInfo!

  """A list of edges."""
  edges: [PayStubEdge!]

  """A flattened list of the nodes."""
  nodes: [PayStub!]

  """Identifies the total count of items in the connection."""
  totalCount: Int!
}

type PayStubDetail implements Node {
  id: ID!
  name: String
  workDate: LocalDate!
  otHours: Float
  stHours: Float
  dtHours: Float
  totalHours: Float
  jobCode: String
  agreementId: Int
  classificationId: Int
  costCenter: String
  hourlyRate: Float
  bonus: Float
  expenses: Float
  payStubId: ID!
  reportLineItemId: Int
  subClassificationId: Int
  earningsCode: String
  payStub: PayStub!
  reportLineItem: ReportLineItem
}

input PayStubDetailFilterInput {
  and: [PayStubDetailFilterInput!]
  or: [PayStubDetailFilterInput!]
  id: IdOperationFilterInput
  name: StringOperationFilterInput
  workDate: LocalDateOperationFilterInput
  otHours: FloatOperationFilterInput
  stHours: FloatOperationFilterInput
  dtHours: FloatOperationFilterInput
  totalHours: FloatOperationFilterInput
  jobCode: StringOperationFilterInput
  agreementId: IntOperationFilterInput
  classificationId: IntOperationFilterInput
  costCenter: StringOperationFilterInput
  hourlyRate: FloatOperationFilterInput
  bonus: FloatOperationFilterInput
  expenses: FloatOperationFilterInput
  payStubId: IdOperationFilterInput
  reportLineItemId: IntOperationFilterInput
  subClassificationId: IntOperationFilterInput
  earningsCode: StringOperationFilterInput
  payStub: PayStubFilterInput
  reportLineItem: ReportLineItemFilterInput
}

input PayStubDetailInput {
  id: ID!
  name: String
  workDate: LocalDate!
  otHours: Float
  stHours: Float
  dtHours: Float
  jobCode: String
  agreementId: Int
  classificationId: Int
  costCenter: String
  hourlyRate: Float
  bonus: Float
  expenses: Float
  payStubId: ID!
  reportLineItemId: Int
  subClassificationId: Int
  earningsCode: String
  payStub: PayStubInput!
  reportLineItem: ReportLineItemInput
}

"""An edge in a connection."""
type PayStubEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of the edge."""
  node: PayStub!
}

input PayStubFilterInput {
  and: [PayStubFilterInput!]
  or: [PayStubFilterInput!]
  id: IdOperationFilterInput
  name: StringOperationFilterInput
  employeeId: IdOperationFilterInput
  timeSheetId: IntOperationFilterInput
  totalHours: FloatOperationFilterInput
  details: ListFilterInputTypeOfPayStubDetailFilterInput
  timeSheet: TimeSheetFilterInput
  employee: EmployeeFilterInput
}

input PayStubInput {
  id: ID!
  name: String
  employeeId: ID!
  timeSheetId: Int!
  details: [PayStubDetailInput!]!
  timeSheet: TimeSheetInput!
  employee: EmployeeInput!
}

type Person {
  id: Int!
  title: String
  suffix: String
  dgenderId: Int!
  dpersonTypeId: Int!
  firstName: String
  middleName: String
  lastName: String!
  dgender: Dgender!
  dpersonType: DpersonType!
  employee: Employee
  idNavigation: Party!
}

input PersonFilterInput {
  and: [PersonFilterInput!]
  or: [PersonFilterInput!]
  id: IntOperationFilterInput
  title: StringOperationFilterInput
  suffix: StringOperationFilterInput
  dgenderId: IntOperationFilterInput
  dpersonTypeId: IntOperationFilterInput
  firstName: StringOperationFilterInput
  middleName: StringOperationFilterInput
  lastName: StringOperationFilterInput
  dgender: DgenderFilterInput
  dpersonType: DpersonTypeFilterInput
  employee: EmployeeFilterInput
  idNavigation: PartyFilterInput
}

input PersonInput {
  id: Int!
  title: String
  suffix: String
  dgenderId: Int!
  dpersonTypeId: Int!
  firstName: String
  middleName: String
  lastName: String!
  dgender: DgenderInput!
  dpersonType: DpersonTypeInput!
  employee: EmployeeInput
  idNavigation: PartyInput!
}

input PersonSortInput {
  id: SortEnumType
  title: SortEnumType
  suffix: SortEnumType
  dgenderId: SortEnumType
  dpersonTypeId: SortEnumType
  firstName: SortEnumType
  middleName: SortEnumType
  lastName: SortEnumType
  dgender: DgenderSortInput
  dpersonType: DpersonTypeSortInput
  employee: EmployeeSortInput
  idNavigation: PartySortInput
}

type PhoneNumber {
  id: Int!
  dphoneNumberTypeId: Int!
  phoneNumber1: String
  extension: String
  dphoneNumberType: DphoneNumberType!
  idNavigation: ContactMechanism!
}

input PhoneNumberFilterInput {
  and: [PhoneNumberFilterInput!]
  or: [PhoneNumberFilterInput!]
  id: IntOperationFilterInput
  dphoneNumberTypeId: IntOperationFilterInput
  phoneNumber1: StringOperationFilterInput
  extension: StringOperationFilterInput
  dphoneNumberType: DphoneNumberTypeFilterInput
  idNavigation: ContactMechanismFilterInput
}

input PhoneNumberInput {
  id: Int!
  dphoneNumberTypeId: Int!
  phoneNumber1: String
  extension: String
  dphoneNumberType: DphoneNumberTypeInput!
  idNavigation: ContactMechanismInput!
}

input PhoneNumberSortInput {
  id: SortEnumType
  dphoneNumberTypeId: SortEnumType
  phoneNumber1: SortEnumType
  extension: SortEnumType
  dphoneNumberType: DphoneNumberTypeSortInput
  idNavigation: ContactMechanismSortInput
}

type Product1 {
  id: Int!
  name: String!
  keywords: String
  shortDescription: String
  description: String
  active: Boolean
  price: Decimal!
  smallImagePath: String
  mediumImagePath: String
  largeImagePath: String
  paymentTypeId: Int!
  creationDate: DateTime!
  createdBy: String!
  lastModifiedDate: DateTime!
  lastModifiedBy: String!
  customReportProducts: [CustomReportProduct!]!
  orderDetails: [OrderDetail!]!
  paymentType: DpaymentType!
  productCategories: [ProductCategory!]!
  productsToOrganizationTypes: [ProductsToOrganizationType!]!
}

input Product1FilterInput {
  and: [Product1FilterInput!]
  or: [Product1FilterInput!]
  id: IntOperationFilterInput
  name: StringOperationFilterInput
  keywords: StringOperationFilterInput
  shortDescription: StringOperationFilterInput
  description: StringOperationFilterInput
  active: BooleanOperationFilterInput
  price: DecimalOperationFilterInput
  smallImagePath: StringOperationFilterInput
  mediumImagePath: StringOperationFilterInput
  largeImagePath: StringOperationFilterInput
  paymentTypeId: IntOperationFilterInput
  creationDate: DateTimeOperationFilterInput
  createdBy: StringOperationFilterInput
  lastModifiedDate: DateTimeOperationFilterInput
  lastModifiedBy: StringOperationFilterInput
  customReportProducts: ListFilterInputTypeOfCustomReportProductFilterInput
  orderDetails: ListFilterInputTypeOfOrderDetailFilterInput
  paymentType: DpaymentTypeFilterInput
  productCategories: ListFilterInputTypeOfProductCategoryFilterInput
  productsToOrganizationTypes: ListFilterInputTypeOfProductsToOrganizationTypeFilterInput
}

input Product1Input {
  id: Int!
  name: String!
  keywords: String
  shortDescription: String
  description: String
  active: Boolean
  price: Decimal!
  smallImagePath: String
  mediumImagePath: String
  largeImagePath: String
  paymentTypeId: Int!
  creationDate: DateTime!
  createdBy: String!
  lastModifiedDate: DateTime!
  lastModifiedBy: String!
  customReportProducts: [CustomReportProductInput!]!
  orderDetails: [OrderDetailInput!]!
  paymentType: DpaymentTypeInput!
  productCategories: [ProductCategoryInput!]!
  productsToOrganizationTypes: [ProductsToOrganizationTypeInput!]!
}

type ProductCategory {
  id: Int!
  dcategoryId: Int!
  productId: Int!
  creationDate: DateTime!
  createdBy: String!
  lastModifiedDate: DateTime!
  lastModifiedBy: String!
  dcategory: Dcategory!
  product: Product1!
}

input ProductCategoryFilterInput {
  and: [ProductCategoryFilterInput!]
  or: [ProductCategoryFilterInput!]
  id: IntOperationFilterInput
  dcategoryId: IntOperationFilterInput
  productId: IntOperationFilterInput
  creationDate: DateTimeOperationFilterInput
  createdBy: StringOperationFilterInput
  lastModifiedDate: DateTimeOperationFilterInput
  lastModifiedBy: StringOperationFilterInput
  dcategory: DcategoryFilterInput
  product: Product1FilterInput
}

input ProductCategoryInput {
  id: Int!
  dcategoryId: Int!
  productId: Int!
  creationDate: DateTime!
  createdBy: String!
  lastModifiedDate: DateTime!
  lastModifiedBy: String!
  dcategory: DcategoryInput!
  product: Product1Input!
}

type ProductsToOrganizationType {
  id: Int!
  organizationTypeId: Int!
  productId: Int!
  creationDate: DateTime!
  createdBy: String!
  lastModifiedDate: DateTime!
  lastModifiedBy: String!
  organizationType: DorganizationType!
  product: Product1!
}

input ProductsToOrganizationTypeFilterInput {
  and: [ProductsToOrganizationTypeFilterInput!]
  or: [ProductsToOrganizationTypeFilterInput!]
  id: IntOperationFilterInput
  organizationTypeId: IntOperationFilterInput
  productId: IntOperationFilterInput
  creationDate: DateTimeOperationFilterInput
  createdBy: StringOperationFilterInput
  lastModifiedDate: DateTimeOperationFilterInput
  lastModifiedBy: StringOperationFilterInput
  organizationType: DorganizationTypeFilterInput
  product: Product1FilterInput
}

input ProductsToOrganizationTypeInput {
  id: Int!
  organizationTypeId: Int!
  productId: Int!
  creationDate: DateTime!
  createdBy: String!
  lastModifiedDate: DateTime!
  lastModifiedBy: String!
  organizationType: DorganizationTypeInput!
  product: Product1Input!
}

type Query {
  """Fetches an object given its ID."""
  node(
    """ID of the object."""
    id: ID!
  ): Node

  """Lookup nodes by a list of IDs."""
  nodes(
    """The list of node IDs."""
    ids: [ID!]!
  ): [Node]!
  signatoryAgreements(
    input: SignatoryAgreementInput!

    """Returns the first _n_ elements from the list."""
    first: Int

    """Returns the elements in the list that come after the specified cursor."""
    after: String

    """Returns the last _n_ elements from the list."""
    last: Int

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String
    where: AgreementFilterInput
    order: [AgreementSortInput!]
  ): AgreementConnection
  agreementsByEmployerIds(
    input: AgreementsByEmployerInput!

    """Returns the first _n_ elements from the list."""
    first: Int

    """Returns the elements in the list that come after the specified cursor."""
    after: String

    """Returns the last _n_ elements from the list."""
    last: Int

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String
    where: AgreementSimpleIdFilterInput
    order: [AgreementSimpleIdSortInput!]
  ): AgreementSimpleIdConnection
  agreementNodeById(id: ID!): Agreement
  benefitElectionRosterByChapterId(
    chapterId: ID!

    """Returns the first _n_ elements from the list."""
    first: Int

    """Returns the elements in the list that come after the specified cursor."""
    after: String

    """Returns the last _n_ elements from the list."""
    last: Int

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String
    where: BenefitElectionsRosterDtoFilterInput
    order: [BenefitElectionsRosterDtoSortInput!]
  ): BenefitElectionsRosterDtoConnection
  chapters(
    """Returns the first _n_ elements from the list."""
    first: Int

    """Returns the elements in the list that come after the specified cursor."""
    after: String

    """Returns the last _n_ elements from the list."""
    last: Int

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String
    where: ChaptersInfoDtoFilterInput
    order: [ChaptersInfoDtoSortInput!]
  ): ChaptersInfoDtoConnection
  chapterNodeById(id: ID!): ChaptersInfoDto
  classificationsByAgreementId(
    agreementId: ID!

    """Returns the first _n_ elements from the list."""
    first: Int

    """Returns the elements in the list that come after the specified cursor."""
    after: String

    """Returns the last _n_ elements from the list."""
    last: Int

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String
    where: ClassificationNameFilterInput
    order: [ClassificationNameSortInput!]
  ): ClassificationNameConnection
  classificationsByAgreementIds(
    input: ClassificationsByAgreementsInput!

    """Returns the first _n_ elements from the list."""
    first: Int

    """Returns the elements in the list that come after the specified cursor."""
    after: String

    """Returns the last _n_ elements from the list."""
    last: Int

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String
    where: ClassificationSimpleIdFilterInput
    order: [ClassificationSimpleIdSortInput!]
  ): ClassificationSimpleIdConnection
  subClassificationsByAgreementAndClassification(
    agreementId: ID!
    classificationId: ID!

    """Returns the first _n_ elements from the list."""
    first: Int

    """Returns the elements in the list that come after the specified cursor."""
    after: String

    """Returns the last _n_ elements from the list."""
    last: Int

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String
    where: SubClassificationFilterInput
    order: [SubClassificationSortInput!]
  ): SubClassificationConnection
  classificationNodeById(id: ID!): ClassificationName
  subClassificationNodeById(id: ID!): SubClassification
  customViewsByType(name: String!, ownerId: UUID, ownerType: String): CustomViews
  employeeDefaultSettings(employeeId: ID!): EmployeeDefaultSettings
  employeeDefaultSettingsByEmployerId(
    employerId: ID!

    """Returns the first _n_ elements from the list."""
    first: Int

    """Returns the elements in the list that come after the specified cursor."""
    after: String

    """Returns the last _n_ elements from the list."""
    last: Int

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String
  ): EmployeeDefaultSettingsConnection
  employeeById(employeeId: ID!): Employee
  employeesByEmployerIdAsync(
    employerId: ID!

    """Returns the first _n_ elements from the list."""
    first: Int

    """Returns the elements in the list that come after the specified cursor."""
    after: String

    """Returns the last _n_ elements from the list."""
    last: Int

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String
  ): EmployeeConnection
  employeesByEmployerGuidAsync(
    employerGuid: UUID!

    """Returns the first _n_ elements from the list."""
    first: Int

    """Returns the elements in the list that come after the specified cursor."""
    after: String

    """Returns the last _n_ elements from the list."""
    last: Int

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String
  ): EmployeeConnection
  employeeNodeById(id: ID!): Employee
  employerRosterByChapterId(
    chapterId: ID!

    """Returns the first _n_ elements from the list."""
    first: Int

    """Returns the elements in the list that come after the specified cursor."""
    after: String

    """Returns the last _n_ elements from the list."""
    last: Int

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String
    where: EmployerRosterViewFilterInput
    order: [EmployerRosterViewSortInput!]
  ): EmployerRosterViewConnection
  employerRosterNodeById(id: ID!): EmployerRosterView
  canDeleteEmployer(employerId: ID!, chapterId: ID!): CanDeleteEmployerResponse!
  employersByChapterId(
    chapterId: ID!

    """Returns the first _n_ elements from the list."""
    first: Int

    """Returns the elements in the list that come after the specified cursor."""
    after: String

    """Returns the last _n_ elements from the list."""
    last: Int

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String
    where: EmployerFilterInput
    order: [EmployerSortInput!]
  ): EmployerConnection
  employerNodeById(id: ID!): Employer
  fieldDefinitions: [KeyValuePairOfStringAndMetadata!]!
  settings(
    employerGuid: UUID!
    settingTypeId: Int

    """Returns the first _n_ elements from the list."""
    first: Int

    """Returns the elements in the list that come after the specified cursor."""
    after: String

    """Returns the last _n_ elements from the list."""
    last: Int

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String
    where: SettingFilterInput
  ): SettingConnection
  settingTypes(
    """Returns the first _n_ elements from the list."""
    first: Int

    """Returns the elements in the list that come after the specified cursor."""
    after: String

    """Returns the last _n_ elements from the list."""
    last: Int

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String
    where: DsettingsTypeFilterInput
  ): DsettingsTypeConnection
  settingNodeById(id: ID!): Setting
  settingBySettingTypeName(name: String!, ownerId: UUID!, ownerType: String!): DsettingsType
  thirdParties(
    chapterId: Int!

    """Returns the first _n_ elements from the list."""
    first: Int

    """Returns the elements in the list that come after the specified cursor."""
    after: String

    """Returns the last _n_ elements from the list."""
    last: Int

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String
    where: ThirdPartyInfoDtoFilterInput
    order: [ThirdPartyInfoDtoSortInput!]
  ): ThirdPartyInfoDtoConnection
  thirdPartyNodeById(id: ID!): ThirdPartyInfoDto!
  organizations(
    chapterId: Int!

    """Returns the first _n_ elements from the list."""
    first: Int

    """Returns the elements in the list that come after the specified cursor."""
    after: String

    """Returns the last _n_ elements from the list."""
    last: Int

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String
    where: ThirdPartyInfoDtoFilterInput
    order: [ThirdPartyInfoDtoSortInput!]
  ): ThirdPartyInfoDtoConnection
  timesheetsByEmployerGuid(
    employerGuid: UUID!

    """Returns the first _n_ elements from the list."""
    first: Int

    """Returns the elements in the list that come after the specified cursor."""
    after: String

    """Returns the last _n_ elements from the list."""
    last: Int

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String
    where: TimeSheetFilterInput
    order: [TimeSheetSortInput!]
  ): TimeSheetConnection
  payStubCount: Int!
  timeSheetById(id: ID!): TimeSheet
  timesheetUsersByEmployerGuid(
    employerGuid: UUID!

    """Returns the first _n_ elements from the list."""
    first: Int

    """Returns the elements in the list that come after the specified cursor."""
    after: String

    """Returns the last _n_ elements from the list."""
    last: Int

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String
    order: [UserSortInput!]
  ): UserConnection
  unionsByChapterId(
    chapterId: Int!

    """Returns the first _n_ elements from the list."""
    first: Int

    """Returns the elements in the list that come after the specified cursor."""
    after: String

    """Returns the last _n_ elements from the list."""
    last: Int

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String
    where: UnionRosterDtoFilterInput
    order: [UnionRosterDtoSortInput!]
  ): UnionRosterDtoConnection
  unionRosterNodeById(id: ID!): UnionRosterDto
  userInfo(username: String): UserInfoOutput!
}

type RateSchedule {
  id: Int!
  agreementId: Int!
  effectiveStartDate: DateTime!
  effectiveEndDate: DateTime
  agreement: Agreement!
  idNavigation: Root!
  reports: [Report!]!
}

input RateScheduleFilterInput {
  and: [RateScheduleFilterInput!]
  or: [RateScheduleFilterInput!]
  id: IntOperationFilterInput
  agreementId: IntOperationFilterInput
  effectiveStartDate: DateTimeOperationFilterInput
  effectiveEndDate: DateTimeOperationFilterInput
  agreement: AgreementFilterInput
  idNavigation: RootFilterInput
  reports: ListFilterInputTypeOfReportFilterInput
}

input RateScheduleInput {
  id: Int!
  agreementId: Int!
  effectiveStartDate: DateTime!
  effectiveEndDate: DateTime
  agreement: AgreementInput!
  idNavigation: RootInput!
  reports: [ReportInput!]!
}

input RateScheduleSortInput {
  id: SortEnumType
  agreementId: SortEnumType
  effectiveStartDate: SortEnumType
  effectiveEndDate: SortEnumType
  agreement: AgreementSortInput
  idNavigation: RootSortInput
}

type Relationship {
  id: Int!
  leftPartyId: Int!
  rightPartyId: Int
  drelationshipTypeId: Int!
  drelationshipSubTypeId: Int
  chapterToEmployerRelationship: ChapterToEmployerRelationship
  drelationship: DrelationshipSubType
  drelationshipType: DrelationshipType!
  idNavigation: Root!
  leftParty: Party!
  relationshipStatuses: [RelationshipStatus!]!
  rightParty: Party
}

input RelationshipFilterInput {
  and: [RelationshipFilterInput!]
  or: [RelationshipFilterInput!]
  id: IntOperationFilterInput
  leftPartyId: IntOperationFilterInput
  rightPartyId: IntOperationFilterInput
  drelationshipTypeId: IntOperationFilterInput
  drelationshipSubTypeId: IntOperationFilterInput
  chapterToEmployerRelationship: ChapterToEmployerRelationshipFilterInput
  drelationship: DrelationshipSubTypeFilterInput
  drelationshipType: DrelationshipTypeFilterInput
  idNavigation: RootFilterInput
  leftParty: PartyFilterInput
  relationshipStatuses: ListFilterInputTypeOfRelationshipStatusFilterInput
  rightParty: PartyFilterInput
}

input RelationshipInput {
  id: Int!
  leftPartyId: Int!
  rightPartyId: Int
  drelationshipTypeId: Int!
  drelationshipSubTypeId: Int
  chapterToEmployerRelationship: ChapterToEmployerRelationshipInput
  drelationship: DrelationshipSubTypeInput
  drelationshipType: DrelationshipTypeInput!
  idNavigation: RootInput!
  leftParty: PartyInput!
  relationshipStatuses: [RelationshipStatusInput!]!
  rightParty: PartyInput
}

input RelationshipSortInput {
  id: SortEnumType
  leftPartyId: SortEnumType
  rightPartyId: SortEnumType
  drelationshipTypeId: SortEnumType
  drelationshipSubTypeId: SortEnumType
  chapterToEmployerRelationship: ChapterToEmployerRelationshipSortInput
  drelationship: DrelationshipSubTypeSortInput
  drelationshipType: DrelationshipTypeSortInput
  idNavigation: RootSortInput
  leftParty: PartySortInput
  rightParty: PartySortInput
}

type RelationshipStatus {
  id: Int!
  relationshipId: Int!
  drelationshipStatusId: Int!
  drelationshipStatus: DrelationshipStatus!
  idNavigation: Root!
  relationship: Relationship!
}

input RelationshipStatusFilterInput {
  and: [RelationshipStatusFilterInput!]
  or: [RelationshipStatusFilterInput!]
  id: IntOperationFilterInput
  relationshipId: IntOperationFilterInput
  drelationshipStatusId: IntOperationFilterInput
  drelationshipStatus: DrelationshipStatusFilterInput
  idNavigation: RootFilterInput
  relationship: RelationshipFilterInput
}

input RelationshipStatusInput {
  id: Int!
  relationshipId: Int!
  drelationshipStatusId: Int!
  drelationshipStatus: DrelationshipStatusInput!
  idNavigation: RootInput!
  relationship: RelationshipInput!
}

input RelationshipStatusSortInput {
  id: SortEnumType
  relationshipId: SortEnumType
  drelationshipStatusId: SortEnumType
  drelationshipStatus: DrelationshipStatusSortInput
  idNavigation: RootSortInput
  relationship: RelationshipSortInput
}

type Report {
  id: Int!
  agreementId: Int!
  rateScheduleId: Int
  employerId: Int!
  periodStartDate: DateTime!
  periodEndDate: DateTime!
  workMonth: DateTime!
  dreportStatusId: Int!
  amendedReportId: Int
  aggregatesOnly: Boolean!
  notes: String
  aggregateEmployeeCount: Int
  zeroHour: Boolean!
  fullyPaid: Boolean
  submissionDate: DateTime
  agreement: Agreement!
  amendedReport: Report
  dreportStatus: DreportStatus!
  electronicPayments: [ElectronicPayment!]!
  employer: Employer!
  fundingComments: [FundingComment!]!
  idNavigation: Root!
  inverseAmendedReport: [Report!]!
  payments: [Payment!]!
  rateSchedule: RateSchedule
  reportLineItems: [ReportLineItem!]!
  reportSuppressions: [ReportSuppression!]!
  reportedBenefitReleaseAuthorizations: [ReportedBenefitReleaseAuthorization!]!
}

type ReportedBenefitReleaseAuthorization {
  id: Int!
  reportId: Int!
  benefitId: Int!
  benefit: Benefit!
  idNavigation: Root!
  report: Report!
}

input ReportedBenefitReleaseAuthorizationFilterInput {
  and: [ReportedBenefitReleaseAuthorizationFilterInput!]
  or: [ReportedBenefitReleaseAuthorizationFilterInput!]
  id: IntOperationFilterInput
  reportId: IntOperationFilterInput
  benefitId: IntOperationFilterInput
  benefit: BenefitFilterInput
  idNavigation: RootFilterInput
  report: ReportFilterInput
}

input ReportedBenefitReleaseAuthorizationInput {
  id: Int!
  reportId: Int!
  benefitId: Int!
  benefit: BenefitInput!
  idNavigation: RootInput!
  report: ReportInput!
}

input ReportedBenefitReleaseAuthorizationSortInput {
  id: SortEnumType
  reportId: SortEnumType
  benefitId: SortEnumType
  benefit: BenefitSortInput
  idNavigation: RootSortInput
  report: ReportSortInput
}

input ReportFilterInput {
  and: [ReportFilterInput!]
  or: [ReportFilterInput!]
  id: IntOperationFilterInput
  agreementId: IntOperationFilterInput
  rateScheduleId: IntOperationFilterInput
  employerId: IntOperationFilterInput
  periodStartDate: DateTimeOperationFilterInput
  periodEndDate: DateTimeOperationFilterInput
  workMonth: DateTimeOperationFilterInput
  dreportStatusId: IntOperationFilterInput
  amendedReportId: IntOperationFilterInput
  aggregatesOnly: BooleanOperationFilterInput
  notes: StringOperationFilterInput
  aggregateEmployeeCount: IntOperationFilterInput
  zeroHour: BooleanOperationFilterInput
  fullyPaid: BooleanOperationFilterInput
  submissionDate: DateTimeOperationFilterInput
  agreement: AgreementFilterInput
  amendedReport: ReportFilterInput
  dreportStatus: DreportStatusFilterInput
  electronicPayments: ListFilterInputTypeOfElectronicPaymentFilterInput
  employer: EmployerFilterInput
  fundingComments: ListFilterInputTypeOfFundingCommentFilterInput
  idNavigation: RootFilterInput
  inverseAmendedReport: ListFilterInputTypeOfReportFilterInput
  payments: ListFilterInputTypeOfPaymentFilterInput
  rateSchedule: RateScheduleFilterInput
  reportLineItems: ListFilterInputTypeOfReportLineItemFilterInput
  reportSuppressions: ListFilterInputTypeOfReportSuppressionFilterInput
  reportedBenefitReleaseAuthorizations: ListFilterInputTypeOfReportedBenefitReleaseAuthorizationFilterInput
}

input ReportInput {
  id: Int!
  agreementId: Int!
  rateScheduleId: Int
  employerId: Int!
  periodStartDate: DateTime!
  periodEndDate: DateTime!
  workMonth: DateTime!
  dreportStatusId: Int!
  amendedReportId: Int
  aggregatesOnly: Boolean!
  notes: String
  aggregateEmployeeCount: Int
  zeroHour: Boolean!
  fullyPaid: Boolean
  submissionDate: DateTime
  agreement: AgreementInput!
  amendedReport: ReportInput
  dreportStatus: DreportStatusInput!
  electronicPayments: [ElectronicPaymentInput!]!
  employer: EmployerInput!
  fundingComments: [FundingCommentInput!]!
  idNavigation: RootInput!
  inverseAmendedReport: [ReportInput!]!
  payments: [PaymentInput!]!
  rateSchedule: RateScheduleInput
  reportLineItems: [ReportLineItemInput!]!
  reportSuppressions: [ReportSuppressionInput!]!
  reportedBenefitReleaseAuthorizations: [ReportedBenefitReleaseAuthorizationInput!]!
}

type ReportLineItem {
  id: Int!
  reportId: Int!
  employeeId: Int
  classificationNameId: Int
  subClassificationId: Int
  damendmentActionId: Int
  amendedLineItemId: Int
  classificationName: ClassificationName
  damendmentAction: DamendmentAction
  employee: Employee
  idNavigation: Root!
  payStubDetails: [PayStubDetail!]!
  report: Report!
  reportLineItemDetails: [ReportLineItemDetail!]!
  subClassification: SubClassification
}

type ReportLineItemDetail {
  reportLineItemId: Int!
  benefitId: Int!
  amount: Decimal
  benefit: Benefit!
  reportLineItem: ReportLineItem!
}

input ReportLineItemDetailFilterInput {
  and: [ReportLineItemDetailFilterInput!]
  or: [ReportLineItemDetailFilterInput!]
  reportLineItemId: IntOperationFilterInput
  benefitId: IntOperationFilterInput
  amount: DecimalOperationFilterInput
  benefit: BenefitFilterInput
  reportLineItem: ReportLineItemFilterInput
}

input ReportLineItemDetailInput {
  reportLineItemId: Int!
  benefitId: Int!
  amount: Decimal
  benefit: BenefitInput!
  reportLineItem: ReportLineItemInput!
}

input ReportLineItemFilterInput {
  and: [ReportLineItemFilterInput!]
  or: [ReportLineItemFilterInput!]
  id: IntOperationFilterInput
  reportId: IntOperationFilterInput
  employeeId: IntOperationFilterInput
  classificationNameId: IntOperationFilterInput
  subClassificationId: IntOperationFilterInput
  damendmentActionId: IntOperationFilterInput
  amendedLineItemId: IntOperationFilterInput
  classificationName: ClassificationNameFilterInput
  damendmentAction: DamendmentActionFilterInput
  employee: EmployeeFilterInput
  idNavigation: RootFilterInput
  payStubDetails: ListFilterInputTypeOfPayStubDetailFilterInput
  report: ReportFilterInput
  reportLineItemDetails: ListFilterInputTypeOfReportLineItemDetailFilterInput
  subClassification: SubClassificationFilterInput
}

input ReportLineItemInput {
  id: Int!
  reportId: Int!
  employeeId: Int
  classificationNameId: Int
  subClassificationId: Int
  damendmentActionId: Int
  amendedLineItemId: Int
  classificationName: ClassificationNameInput
  damendmentAction: DamendmentActionInput
  employee: EmployeeInput
  idNavigation: RootInput!
  payStubDetails: [PayStubDetailInput!]!
  report: ReportInput!
  reportLineItemDetails: [ReportLineItemDetailInput!]!
  subClassification: SubClassificationInput
}

input ReportLineItemSortInput {
  id: SortEnumType
  reportId: SortEnumType
  employeeId: SortEnumType
  classificationNameId: SortEnumType
  subClassificationId: SortEnumType
  damendmentActionId: SortEnumType
  amendedLineItemId: SortEnumType
  classificationName: ClassificationNameSortInput
  damendmentAction: DamendmentActionSortInput
  employee: EmployeeSortInput
  idNavigation: RootSortInput
  report: ReportSortInput
  subClassification: SubClassificationSortInput
}

input ReportSortInput {
  id: SortEnumType
  agreementId: SortEnumType
  rateScheduleId: SortEnumType
  employerId: SortEnumType
  periodStartDate: SortEnumType
  periodEndDate: SortEnumType
  workMonth: SortEnumType
  dreportStatusId: SortEnumType
  amendedReportId: SortEnumType
  aggregatesOnly: SortEnumType
  notes: SortEnumType
  aggregateEmployeeCount: SortEnumType
  zeroHour: SortEnumType
  fullyPaid: SortEnumType
  submissionDate: SortEnumType
  agreement: AgreementSortInput
  amendedReport: ReportSortInput
  dreportStatus: DreportStatusSortInput
  employer: EmployerSortInput
  idNavigation: RootSortInput
  rateSchedule: RateScheduleSortInput
}

type ReportSuppression {
  id: Int!
  collectingAgentId: Int!
  reportId: Int!
  suppress: Boolean!
  idNavigation: Root!
  report: Report!
}

input ReportSuppressionFilterInput {
  and: [ReportSuppressionFilterInput!]
  or: [ReportSuppressionFilterInput!]
  id: IntOperationFilterInput
  collectingAgentId: IntOperationFilterInput
  reportId: IntOperationFilterInput
  suppress: BooleanOperationFilterInput
  idNavigation: RootFilterInput
  report: ReportFilterInput
}

input ReportSuppressionInput {
  id: Int!
  collectingAgentId: Int!
  reportId: Int!
  suppress: Boolean!
  idNavigation: RootInput!
  report: ReportInput!
}

input ReportSuppressionSortInput {
  id: SortEnumType
  collectingAgentId: SortEnumType
  reportId: SortEnumType
  suppress: SortEnumType
  idNavigation: RootSortInput
  report: ReportSortInput
}

type RoleGroup {
  id: Int!
  name: String!
  description: String
  newsItems: [NewsItem!]!
  aspnetRoles: [AspnetRole!]!
  aspnetUsers: [AspnetUser!]!
}

input RoleGroupFilterInput {
  and: [RoleGroupFilterInput!]
  or: [RoleGroupFilterInput!]
  id: IntOperationFilterInput
  name: StringOperationFilterInput
  description: StringOperationFilterInput
  newsItems: ListFilterInputTypeOfNewsItemFilterInput
  aspnetRoles: ListFilterInputTypeOfAspnetRoleFilterInput
  aspnetUsers: ListFilterInputTypeOfAspnetUserFilterInput
}

input RoleGroupInput {
  id: Int!
  name: String!
  description: String
  newsItems: [NewsItemInput!]!
  aspnetRoles: [AspnetRoleInput!]!
  aspnetUsers: [AspnetUserInput!]!
}

type Root {
  id: Int!
  guid: UUID
  creationDate: DateTime!
  createdBy: String!
  lastModificationDate: DateTime!
  lastModifiedBy: String!
  agreement: Agreement
  benefit: Benefit
  benefitOverridesOrig: BenefitOverridesOrig
  classificationName: ClassificationName
  contactMechanism: ContactMechanism
  electronicBatch: ElectronicBatch
  electronicPayment: ElectronicPayment
  electronicPaymentConfiguration: ElectronicPaymentConfiguration
  employersToAgreement: EmployersToAgreement
  image: Image
  noteIdNavigations: [Note!]!
  noteRoots: [Note!]!
  partiesToContactMechanism: PartiesToContactMechanism
  party: Party
  payment: Payment
  paymentMethods: [PaymentMethod!]!
  rateSchedule: RateSchedule
  relationship: Relationship
  relationshipStatus: RelationshipStatus
  report: Report
  reportLineItem: ReportLineItem
  reportSuppression: ReportSuppression
  reportedBenefitReleaseAuthorization: ReportedBenefitReleaseAuthorization
  subClassification: SubClassification
  timelinesOrig: TimelinesOrig
}

input RootFilterInput {
  and: [RootFilterInput!]
  or: [RootFilterInput!]
  id: IntOperationFilterInput
  guid: UuidOperationFilterInput
  creationDate: DateTimeOperationFilterInput
  createdBy: StringOperationFilterInput
  lastModificationDate: DateTimeOperationFilterInput
  lastModifiedBy: StringOperationFilterInput
  agreement: AgreementFilterInput
  benefit: BenefitFilterInput
  benefitOverridesOrig: BenefitOverridesOrigFilterInput
  classificationName: ClassificationNameFilterInput
  contactMechanism: ContactMechanismFilterInput
  electronicBatch: ElectronicBatchFilterInput
  electronicPayment: ElectronicPaymentFilterInput
  electronicPaymentConfiguration: ElectronicPaymentConfigurationFilterInput
  employersToAgreement: EmployersToAgreementFilterInput
  image: ImageFilterInput
  noteIdNavigations: ListFilterInputTypeOfNoteFilterInput
  noteRoots: ListFilterInputTypeOfNoteFilterInput
  partiesToContactMechanism: PartiesToContactMechanismFilterInput
  party: PartyFilterInput
  payment: PaymentFilterInput
  paymentMethods: ListFilterInputTypeOfPaymentMethodFilterInput
  rateSchedule: RateScheduleFilterInput
  relationship: RelationshipFilterInput
  relationshipStatus: RelationshipStatusFilterInput
  report: ReportFilterInput
  reportLineItem: ReportLineItemFilterInput
  reportSuppression: ReportSuppressionFilterInput
  reportedBenefitReleaseAuthorization: ReportedBenefitReleaseAuthorizationFilterInput
  subClassification: SubClassificationFilterInput
  timelinesOrig: TimelinesOrigFilterInput
}

input RootInput {
  id: Int!
  guid: UUID
  creationDate: DateTime!
  createdBy: String!
  lastModificationDate: DateTime!
  lastModifiedBy: String!
  agreement: AgreementInput
  benefit: BenefitInput
  benefitOverridesOrig: BenefitOverridesOrigInput
  classificationName: ClassificationNameInput
  contactMechanism: ContactMechanismInput
  electronicBatch: ElectronicBatchInput
  electronicPayment: ElectronicPaymentInput
  electronicPaymentConfiguration: ElectronicPaymentConfigurationInput
  employersToAgreement: EmployersToAgreementInput
  image: ImageInput
  noteIdNavigations: [NoteInput!]!
  noteRoots: [NoteInput!]!
  partiesToContactMechanism: PartiesToContactMechanismInput
  party: PartyInput
  payment: PaymentInput
  paymentMethods: [PaymentMethodInput!]!
  rateSchedule: RateScheduleInput
  relationship: RelationshipInput
  relationshipStatus: RelationshipStatusInput
  report: ReportInput
  reportLineItem: ReportLineItemInput
  reportSuppression: ReportSuppressionInput
  reportedBenefitReleaseAuthorization: ReportedBenefitReleaseAuthorizationInput
  subClassification: SubClassificationInput
  timelinesOrig: TimelinesOrigInput
}

input RootSortInput {
  id: SortEnumType
  guid: SortEnumType
  creationDate: SortEnumType
  createdBy: SortEnumType
  lastModificationDate: SortEnumType
  lastModifiedBy: SortEnumType
  agreement: AgreementSortInput
  benefit: BenefitSortInput
  benefitOverridesOrig: BenefitOverridesOrigSortInput
  classificationName: ClassificationNameSortInput
  contactMechanism: ContactMechanismSortInput
  electronicBatch: ElectronicBatchSortInput
  electronicPayment: ElectronicPaymentSortInput
  electronicPaymentConfiguration: ElectronicPaymentConfigurationSortInput
  employersToAgreement: EmployersToAgreementSortInput
  image: ImageSortInput
  partiesToContactMechanism: PartiesToContactMechanismSortInput
  party: PartySortInput
  payment: PaymentSortInput
  rateSchedule: RateScheduleSortInput
  relationship: RelationshipSortInput
  relationshipStatus: RelationshipStatusSortInput
  report: ReportSortInput
  reportLineItem: ReportLineItemSortInput
  reportSuppression: ReportSuppressionSortInput
  reportedBenefitReleaseAuthorization: ReportedBenefitReleaseAuthorizationSortInput
  subClassification: SubClassificationSortInput
  timelinesOrig: TimelinesOrigSortInput
}

input SaveSettingInput {
  id: ID
  settingId: ID
  value: Any
  ownerId: ID
  ownerType: String!
  lastModifiedBy: UUID
  lastModificationDate: DateTime
}

type SaveSettingPayload {
  operationResultOfSetting: OperationResultOfSetting
}

type ServiceSubscription {
  partyId: Int!
  subscriptionServiceId: Int!
  id: Int!
  party: Organization!
  subscriptionService: SubscriptionService!
}

input ServiceSubscriptionFilterInput {
  and: [ServiceSubscriptionFilterInput!]
  or: [ServiceSubscriptionFilterInput!]
  partyId: IntOperationFilterInput
  subscriptionServiceId: IntOperationFilterInput
  id: IntOperationFilterInput
  party: OrganizationFilterInput
  subscriptionService: SubscriptionServiceFilterInput
}

input ServiceSubscriptionInput {
  partyId: Int!
  subscriptionServiceId: Int!
  id: Int!
  party: OrganizationInput!
  subscriptionService: SubscriptionServiceInput!
}

type Setting implements Node {
  id: ID!
  dSettingsTypeId: Int!
  value: String
  ownerId: UUID!
  ownerType: String!
  lastModifiedBy: UUID
  lastModificationDate: DateTime
  settingsType: DsettingsType
}

"""A connection to a list of items."""
type SettingConnection {
  """Information to aid in pagination."""
  pageInfo: PageInfo!

  """A list of edges."""
  edges: [SettingEdge!]

  """A flattened list of the nodes."""
  nodes: [Setting!]

  """Identifies the total count of items in the connection."""
  totalCount: Int!
}

"""An edge in a connection."""
type SettingEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of the edge."""
  node: Setting!
}

input SettingFilterInput {
  and: [SettingFilterInput!]
  or: [SettingFilterInput!]
  id: IdOperationFilterInput
  dSettingsTypeId: IntOperationFilterInput
  value: StringOperationFilterInput
  ownerId: UuidOperationFilterInput
  ownerType: StringOperationFilterInput
  lastModifiedBy: UuidOperationFilterInput
  lastModificationDate: DateTimeOperationFilterInput
  settingsType: DsettingsTypeFilterInput
}

input SettingInput {
  id: ID!
  dSettingsTypeId: Int!
  value: String
  ownerId: UUID!
  ownerType: String!
  lastModifiedBy: UUID
  lastModificationDate: DateTime
  settingsType: DsettingsTypeInput
}

input SignatoryAgreementInput {
  employerGuid: UUID
  includeInactiveAgreements: Boolean
}

enum SortEnumType {
  ASC
  DESC
}

input StringOperationFilterInput {
  and: [StringOperationFilterInput!]
  or: [StringOperationFilterInput!]
  eq: String
  neq: String
  contains: String
  ncontains: String
  in: [String]
  nin: [String]
  startsWith: String
  nstartsWith: String
  endsWith: String
  nendsWith: String
}

type SubClassification implements Node {
  id: ID!
  chapterId: Int
  name: String!
  dstatusId: Int!
  description: String
  chapter: Chapter
  dstatus: Dstatus!
  idNavigation: Root!
  reportLineItems: [ReportLineItem!]!
}

"""A connection to a list of items."""
type SubClassificationConnection {
  """Information to aid in pagination."""
  pageInfo: PageInfo!

  """A list of edges."""
  edges: [SubClassificationEdge!]

  """A flattened list of the nodes."""
  nodes: [SubClassification!]

  """Identifies the total count of items in the connection."""
  totalCount: Int!
}

"""An edge in a connection."""
type SubClassificationEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of the edge."""
  node: SubClassification!
}

input SubClassificationFilterInput {
  and: [SubClassificationFilterInput!]
  or: [SubClassificationFilterInput!]
  id: IntOperationFilterInput
  chapterId: IntOperationFilterInput
  name: StringOperationFilterInput
  dstatusId: IntOperationFilterInput
  description: StringOperationFilterInput
  chapter: ChapterFilterInput
  dstatus: DstatusFilterInput
  idNavigation: RootFilterInput
  reportLineItems: ListFilterInputTypeOfReportLineItemFilterInput
}

input SubClassificationInput {
  id: Int!
  chapterId: Int
  name: String!
  dstatusId: Int!
  description: String
  chapter: ChapterInput
  dstatus: DstatusInput!
  idNavigation: RootInput!
  reportLineItems: [ReportLineItemInput!]!
}

input SubClassificationSortInput {
  id: SortEnumType
  chapterId: SortEnumType
  name: SortEnumType
  dstatusId: SortEnumType
  description: SortEnumType
  chapter: ChapterSortInput
  dstatus: DstatusSortInput
  idNavigation: RootSortInput
}

type SubscriptionService {
  id: Int!
  name: String
  serviceSubscriptions: [ServiceSubscription!]!
}

input SubscriptionServiceFilterInput {
  and: [SubscriptionServiceFilterInput!]
  or: [SubscriptionServiceFilterInput!]
  id: IntOperationFilterInput
  name: StringOperationFilterInput
  serviceSubscriptions: ListFilterInputTypeOfServiceSubscriptionFilterInput
}

input SubscriptionServiceInput {
  id: Int!
  name: String
  serviceSubscriptions: [ServiceSubscriptionInput!]!
}

type ThirdPartyInfoDto implements Node {
  id: ID!
  isLinked: Boolean!
  value: Int!
  label: String!
  guid: UUID
}

"""A connection to a list of items."""
type ThirdPartyInfoDtoConnection {
  """Information to aid in pagination."""
  pageInfo: PageInfo!

  """A list of edges."""
  edges: [ThirdPartyInfoDtoEdge!]

  """A flattened list of the nodes."""
  nodes: [ThirdPartyInfoDto!]

  """Identifies the total count of items in the connection."""
  totalCount: Int!
}

"""An edge in a connection."""
type ThirdPartyInfoDtoEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of the edge."""
  node: ThirdPartyInfoDto!
}

input ThirdPartyInfoDtoFilterInput {
  and: [ThirdPartyInfoDtoFilterInput!]
  or: [ThirdPartyInfoDtoFilterInput!]
  id: IdOperationFilterInput
  isLinked: BooleanOperationFilterInput
  value: IntOperationFilterInput
  label: StringOperationFilterInput
  guid: UuidOperationFilterInput
}

input ThirdPartyInfoDtoSortInput {
  id: SortEnumType
  isLinked: SortEnumType
  value: SortEnumType
  label: SortEnumType
  guid: SortEnumType
}

type TimelinesOrig {
  id: Int!
  effectiveStartDate: DateTime!
  effectiveEndDate: DateTime
  isOverridden: Boolean!
  idNavigation: Root!
}

input TimelinesOrigFilterInput {
  and: [TimelinesOrigFilterInput!]
  or: [TimelinesOrigFilterInput!]
  id: IntOperationFilterInput
  effectiveStartDate: DateTimeOperationFilterInput
  effectiveEndDate: DateTimeOperationFilterInput
  isOverridden: BooleanOperationFilterInput
  idNavigation: RootFilterInput
}

input TimelinesOrigInput {
  id: Int!
  effectiveStartDate: DateTime!
  effectiveEndDate: DateTime
  isOverridden: Boolean!
  idNavigation: RootInput!
}

input TimelinesOrigSortInput {
  id: SortEnumType
  effectiveStartDate: SortEnumType
  effectiveEndDate: SortEnumType
  isOverridden: SortEnumType
  idNavigation: RootSortInput
}

type TimeSheet implements Node {
  payStubCount: Int!
  id: ID!
  oldId: UUID
  numericId: Int!
  name: String
  payPeriodEndDate: LocalDate
  status: String!
  type: String!
  createdByUserId: String
  modifiedByUserId: String
  creationDate: DateTime
  modificationDate: DateTime
  employerGuid: UUID!
  showBonusColumn: Boolean
  showCostCenterColumn: Boolean
  showDTHoursColumn: Boolean
  showEarningsCodesColumn: Boolean
  showExpensesColumn: Boolean
  payStubs(
    """Returns the first _n_ elements from the list."""
    first: Int

    """Returns the elements in the list that come after the specified cursor."""
    after: String

    """Returns the last _n_ elements from the list."""
    last: Int

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String
  ): PayStubConnection
  hoursWorked: Float!
}

"""A connection to a list of items."""
type TimeSheetConnection {
  """Information to aid in pagination."""
  pageInfo: PageInfo!

  """A list of edges."""
  edges: [TimeSheetEdge!]

  """A flattened list of the nodes."""
  nodes: [TimeSheet!]

  """Identifies the total count of items in the connection."""
  totalCount: Int!
}

"""An edge in a connection."""
type TimeSheetEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of the edge."""
  node: TimeSheet!
}

input TimeSheetFilterInput {
  and: [TimeSheetFilterInput!]
  or: [TimeSheetFilterInput!]
  id: IdOperationFilterInput
  oldId: UuidOperationFilterInput
  numericId: IntOperationFilterInput
  name: StringOperationFilterInput
  payPeriodEndDate: LocalDateOperationFilterInput
  status: StringOperationFilterInput
  type: StringOperationFilterInput
  createdByUserId: StringOperationFilterInput
  modifiedByUserId: StringOperationFilterInput
  creationDate: DateTimeOperationFilterInput
  modificationDate: DateTimeOperationFilterInput
  employerGuid: UuidOperationFilterInput
  showBonusColumn: BooleanOperationFilterInput
  showCostCenterColumn: BooleanOperationFilterInput
  showDTHoursColumn: BooleanOperationFilterInput
  showEarningsCodesColumn: BooleanOperationFilterInput
  showExpensesColumn: BooleanOperationFilterInput
  payStubs: ListFilterInputTypeOfPayStubFilterInput
}

input TimeSheetInput {
  id: ID!
  oldId: UUID
  name: String
  payPeriodEndDate: LocalDate
  status: String!
  type: String!
  createdByUserId: String
  modifiedByUserId: String
  creationDate: DateTime
  modificationDate: DateTime
  employerGuid: UUID!
  showBonusColumn: Boolean
  showCostCenterColumn: Boolean
  showDTHoursColumn: Boolean
  showEarningsCodesColumn: Boolean
  showExpensesColumn: Boolean
  payStubs: [PayStubInput!]!
}

input TimeSheetSortInput {
  id: SortEnumType
  oldId: SortEnumType
  numericId: SortEnumType
  name: SortEnumType
  payPeriodEndDate: SortEnumType
  status: SortEnumType
  type: SortEnumType
  createdByUserId: SortEnumType
  modifiedByUserId: SortEnumType
  creationDate: SortEnumType
  modificationDate: SortEnumType
  employerGuid: SortEnumType
  showBonusColumn: SortEnumType
  showCostCenterColumn: SortEnumType
  showDTHoursColumn: SortEnumType
  showEarningsCodesColumn: SortEnumType
  showExpensesColumn: SortEnumType
}

type Trade {
  id: Int!
  idNavigation: Organization!
}

input TradeFilterInput {
  and: [TradeFilterInput!]
  or: [TradeFilterInput!]
  id: IntOperationFilterInput
  idNavigation: OrganizationFilterInput
}

input TradeInput {
  id: Int!
  idNavigation: OrganizationInput!
}

input TradeSortInput {
  id: SortEnumType
  idNavigation: OrganizationSortInput
}

type Union {
  id: Int!
  defaultDelinquentDay: Int!
  agreements: [Agreement!]!
  idNavigation: Organization!
}

input UnionFilterInput {
  and: [UnionFilterInput!]
  or: [UnionFilterInput!]
  id: IntOperationFilterInput
  defaultDelinquentDay: IntOperationFilterInput
  agreements: ListFilterInputTypeOfAgreementFilterInput
  idNavigation: OrganizationFilterInput
}

input UnionInput {
  id: Int!
  defaultDelinquentDay: Int!
  agreements: [AgreementInput!]!
  idNavigation: OrganizationInput!
}

type UnionRosterDto implements Node {
  id: ID!
  unionName: String
  employerCount: Int!
  agreementCount: Int!
}

"""A connection to a list of items."""
type UnionRosterDtoConnection {
  """Information to aid in pagination."""
  pageInfo: PageInfo!

  """A list of edges."""
  edges: [UnionRosterDtoEdge!]

  """A flattened list of the nodes."""
  nodes: [UnionRosterDto!]

  """Identifies the total count of items in the connection."""
  totalCount: Int!
}

"""An edge in a connection."""
type UnionRosterDtoEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of the edge."""
  node: UnionRosterDto!
}

input UnionRosterDtoFilterInput {
  and: [UnionRosterDtoFilterInput!]
  or: [UnionRosterDtoFilterInput!]
  id: IdOperationFilterInput
  unionName: StringOperationFilterInput
  employerCount: IntOperationFilterInput
  agreementCount: IntOperationFilterInput
}

input UnionRosterDtoSortInput {
  id: SortEnumType
  unionName: SortEnumType
  employerCount: SortEnumType
  agreementCount: SortEnumType
}

input UnionSortInput {
  id: SortEnumType
  defaultDelinquentDay: SortEnumType
  idNavigation: OrganizationSortInput
}

input UpdateCustomViewsInput {
  id: ID
  name: String
  value: JSON
}

type UpdateCustomViewsPayload {
  customViews: CustomViews
}

type User {
  id: UUID
  userName: String
  fullName: String
}

"""A connection to a list of items."""
type UserConnection {
  """Information to aid in pagination."""
  pageInfo: PageInfo!

  """A list of edges."""
  edges: [UserEdge!]

  """A flattened list of the nodes."""
  nodes: [User!]

  """Identifies the total count of items in the connection."""
  totalCount: Int!
}

"""An edge in a connection."""
type UserEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of the edge."""
  node: User!
}

type UserInfoOutput {
  username: String!
  roleGroups: [String!]!
  showMyReportsMenu: Boolean!
  showTimesheetsMenu: Boolean!
  firstName: String!
  lastName: String!
  email: String!
  orgGUID: UUID!
  orgName: String
  chapterId: Int!
  phoneNumber: String
  phoneTypeId: Int
  preferredMfamethod: String
  phoneNumberExtension: String
}

input UserSortInput {
  id: SortEnumType
  userName: SortEnumType
  fullName: SortEnumType
}

scalar UUID

input UuidOperationFilterInput {
  eq: UUID
  neq: UUID
  in: [UUID]
  nin: [UUID]
  gt: UUID
  ngt: UUID
  gte: UUID
  ngte: UUID
  lt: UUID
  nlt: UUID
  lte: UUID
  nlte: UUID
}

type Website {
  id: Int!
  dwebsiteTypeId: Int!
  url: String
  dwebsiteType: DwebsiteType!
  idNavigation: ContactMechanism!
}

input WebsiteFilterInput {
  and: [WebsiteFilterInput!]
  or: [WebsiteFilterInput!]
  id: IntOperationFilterInput
  dwebsiteTypeId: IntOperationFilterInput
  url: StringOperationFilterInput
  dwebsiteType: DwebsiteTypeFilterInput
  idNavigation: ContactMechanismFilterInput
}

input WebsiteInput {
  id: Int!
  dwebsiteTypeId: Int!
  url: String
  dwebsiteType: DwebsiteTypeInput!
  idNavigation: ContactMechanismInput!
}

input WebsiteSortInput {
  id: SortEnumType
  dwebsiteTypeId: SortEnumType
  url: SortEnumType
  dwebsiteType: DwebsiteTypeSortInput
  idNavigation: ContactMechanismSortInput
}

