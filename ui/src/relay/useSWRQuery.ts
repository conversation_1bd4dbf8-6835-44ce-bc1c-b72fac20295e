import { useLazyLoadQuery, GraphQLTaggedNode } from 'react-relay';
import type { OperationType } from 'relay-runtime';
import { relayObservability } from './observability';
import { useEffect, useRef } from 'react';

export function useSWRQuery<T extends OperationType>(
  query: GraphQLTaggedNode,
  variables: T['variables'],
) {
  const trackingRef = useRef(false);
  
  const result = useLazyLoadQuery<T>(query, variables, {
    fetchPolicy: 'store-and-network',    // SWR ✨
  });

  // Track cache hit/miss for observability
  useEffect(() => {
    if (!trackingRef.current) {
      trackingRef.current = true;
      
      // If data exists immediately, it was a cache hit
      if (result) {
        relayObservability.trackCacheHit();
      } else {
        relayObservability.trackCacheMiss();
      }
    }
  }, [result]);

  return result;
}