// TypeScript declarations for Relay dev tools namespace

import type { CachedBlob, FormattedCacheMetrics, FormattedQueryMetrics } from './types';

declare global {
  interface Window {
    __RELAY_DEV__?: {
      clearRelayCache: () => Promise<void>;
      inspectRelayCache: () => Promise<CachedBlob | null>;
      getCacheMetrics: () => Promise<FormattedCacheMetrics | null>;
      getCacheMetricsByQuery: (queryName?: string) => FormattedQueryMetrics | FormattedQueryMetrics[] | null;
      trackCacheLoad: (startTime: number, fromCache?: boolean, queryName?: string) => void;
    };
  }
}

export {};