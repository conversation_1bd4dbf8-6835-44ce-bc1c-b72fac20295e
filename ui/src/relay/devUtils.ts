import { openDB } from 'idb';
import type { CacheMetrics, FormattedCacheMetrics, FormattedQueryMetrics, CachedBlob } from './types';

// Performance tracking for cache behavior with query-specific metrics
const cacheMetrics: CacheMetrics = {
  loads: 0,
  cacheHits: 0,
  cacheMisses: 0,
  startTime: Date.now(),
  loadTimes: [],
  byQuery: {}
};

// Development utilities for cache management and debugging
export const setupDevUtils = () => {
  if (process.env.NODE_ENV === 'development') {
    // Create dedicated namespace for Relay dev tools
    const relayDevTools = {
      clearRelayCache: async () => {
        try {
          const db = await openDB('relay-cache-v1', 1);
          await db.clear('kv');
          console.log('✅ Relay cache cleared – reload the page to see fresh data.');
          // Reset metrics
          cacheMetrics.loads = 0;
          cacheMetrics.cacheHits = 0;
          cacheMetrics.cacheMisses = 0;
          cacheMetrics.startTime = Date.now();
          cacheMetrics.loadTimes = [];
          cacheMetrics.byQuery = {};
        } catch (error) {
          console.error('❌ Failed to clear Relay cache:', error);
        }
      },

      inspectRelayCache: async (): Promise<CachedBlob | null> => {
        try {
          const db = await openDB('relay-cache-v1', 1);
          const cached: CachedBlob | undefined = await db.get('kv', 'records');
          console.log('📦 Relay cache contents:', cached);
          return cached || null;
        } catch (error) {
          console.error('❌ Failed to inspect Relay cache:', error);
          return null;
        }
      },

      getCacheMetrics: async (): Promise<FormattedCacheMetrics | null> => {
        try {
          const db = await openDB('relay-cache-v1', 1);
          const cached: CachedBlob | undefined = await db.get('kv', 'records');
          const cacheSize = cached ? JSON.stringify(cached.records).length : 0;
          const avgLoadTime = cacheMetrics.loadTimes.length > 0 
            ? cacheMetrics.loadTimes.reduce((a, b) => a + b, 0) / cacheMetrics.loadTimes.length 
            : 0;

          const metrics: FormattedCacheMetrics = {
            cacheSize: `${(cacheSize / 1024).toFixed(2)} KB`,
            totalLoads: cacheMetrics.loads,
            cacheHits: cacheMetrics.cacheHits,
            cacheMisses: cacheMetrics.cacheMisses,
            hitRate: cacheMetrics.loads > 0 ? `${((cacheMetrics.cacheHits / cacheMetrics.loads) * 100).toFixed(1)}%` : '0%',
            avgLoadTime: `${avgLoadTime.toFixed(0)}ms`,
            lastUpdated: cached?.version || 'Unknown',
            sessionDuration: `${((Date.now() - cacheMetrics.startTime) / 1000 / 60).toFixed(1)} minutes`
          };

          console.table(metrics);
          return metrics;
        } catch (error) {
          console.error('❌ Failed to get cache metrics:', error);
          return null;
        }
      },

      trackCacheLoad: (startTime: number, fromCache: boolean = false, queryName?: string) => {
        const loadTime = Date.now() - startTime;
        cacheMetrics.loads++;
        cacheMetrics.loadTimes.push(loadTime);
        
        if (fromCache) {
          cacheMetrics.cacheHits++;
        } else {
          cacheMetrics.cacheMisses++;
        }

        // Track by query type if provided
        if (queryName) {
          if (!cacheMetrics.byQuery[queryName]) {
            cacheMetrics.byQuery[queryName] = {
              loads: 0,
              cacheHits: 0,
              cacheMisses: 0,
              loadTimes: []
            };
          }
          
          const queryMetrics = cacheMetrics.byQuery[queryName];
          queryMetrics.loads++;
          queryMetrics.loadTimes.push(loadTime);
          
          if (fromCache) {
            queryMetrics.cacheHits++;
          } else {
            queryMetrics.cacheMisses++;
          }
        }

        console.log(`⚡ Load completed in ${loadTime}ms ${fromCache ? '(from cache)' : '(from network)'}${queryName ? ` [${queryName}]` : ''}`);
      },

      getCacheMetricsByQuery: (queryName?: string): FormattedQueryMetrics | FormattedQueryMetrics[] | null => {
        try {
          if (queryName && cacheMetrics.byQuery[queryName]) {
            const queryMetrics = cacheMetrics.byQuery[queryName];
            const avgLoadTime = queryMetrics.loadTimes.length > 0 
              ? queryMetrics.loadTimes.reduce((a, b) => a + b, 0) / queryMetrics.loadTimes.length 
              : 0;

            const metrics: FormattedQueryMetrics = {
              queryName,
              totalLoads: queryMetrics.loads,
              cacheHits: queryMetrics.cacheHits,
              cacheMisses: queryMetrics.cacheMisses,
              hitRate: queryMetrics.loads > 0 ? `${((queryMetrics.cacheHits / queryMetrics.loads) * 100).toFixed(1)}%` : '0%',
              avgLoadTime: `${avgLoadTime.toFixed(0)}ms`
            };

            console.table(metrics);
            return metrics;
          } else {
            // Show all query metrics
            const allMetrics: FormattedQueryMetrics[] = Object.entries(cacheMetrics.byQuery).map(([name, metrics]) => {
              const avgLoadTime = metrics.loadTimes.length > 0 
                ? metrics.loadTimes.reduce((a, b) => a + b, 0) / metrics.loadTimes.length 
                : 0;

              return {
                queryName: name,
                totalLoads: metrics.loads,
                cacheHits: metrics.cacheHits,
                cacheMisses: metrics.cacheMisses,
                hitRate: metrics.loads > 0 ? `${((metrics.cacheHits / metrics.loads) * 100).toFixed(1)}%` : '0%',
                avgLoadTime: `${avgLoadTime.toFixed(0)}ms`
              };
            });

            console.table(allMetrics);
            return allMetrics;
          }
        } catch (error) {
          console.error('❌ Failed to get query-specific cache metrics:', error);
          return null;
        }
      }
    };

    // Assign to window under dedicated namespace
    (window as any).__RELAY_DEV__ = relayDevTools;
  }
};

// Auto-setup in development
if (process.env.NODE_ENV === 'development') {
  setupDevUtils();
}