import { Store, RecordSource, DataID } from 'relay-runtime';
import { openDB } from 'idb';
import type { CachedBlob } from './types';
import { relayObservability } from './observability';

const DB_NAME   = 'relay-cache-v1';
const STORE_KEY = 'records';
const VERSION   = '2025-06-22';
const DELAY_MS  = 1500; // debounce

// Global persistence status tracking
let persistenceDisabled = false;
let persistenceErrorLogged = false;

/* ---------- low-level helpers ---------- */
async function db() {
  return openDB(DB_NAME, 1, { upgrade: db => db.createObjectStore('kv') });
}

async function loadRecordMap(): Promise<Record<string, any>> {
  const cached: CachedBlob | undefined = await (await db()).get('kv', STORE_KEY);
  return cached?.version === VERSION ? (cached.records) : {};
}

async function persist(recordMap: Record<string, any>): Promise<void> {
  // Skip persistence if it has been disabled due to previous errors
  if (persistenceDisabled) {
    return;
  }

  try {
    const blob: CachedBlob = { version: VERSION, records: recordMap };
    await (await db()).put('kv', blob, STORE_KEY);
  } catch (error) {
    // Handle specific IndexedDB errors
    if (error instanceof Error) {
      const isQuotaError = error.name === 'QuotaExceededError' || 
                          error.name === 'NS_ERROR_DOM_QUOTA_REACHED';
      const isCorruptionError = error.name === 'VersionError' || 
                               error.name === 'InvalidStateError';
      
      if (isQuotaError || isCorruptionError) {
        persistenceDisabled = true;
        relayObservability.setPersistenceDisabled();
        
        if (isQuotaError) {
          relayObservability.trackQuotaExceeded();
        }
        
        // Log error only once to avoid console spam
        if (!persistenceErrorLogged) {
          persistenceErrorLogged = true;
          const errorType = isQuotaError ? 'quota exceeded' : 'database corruption';
          console.warn(`[createPersistedStore] Persistence disabled due to ${errorType}:`, error.message);
          
          // Emit toast in development mode for QA visibility
          if (process.env.NODE_ENV === 'development') {
            // Dynamic import to avoid bundling toast in production
            import('@react-spectrum/toast').then(({ ToastQueue }) => {
              ToastQueue.negative(`Cache persistence disabled: ${errorType}`, { timeout: 5000 });
            }).catch(() => {
              // Fallback if toast library not available
              console.error(`🚨 Cache persistence disabled: ${errorType}`);
            });
          }
        }
        return;
      }
    }
    
    // Re-throw other errors for normal handling
    throw error;
  }
}

/* ---------- public factory ---------- */
export async function createPersistedStore(): Promise<Store> {
  let initial: Record<string, any> = {};
  try {
    initial = await loadRecordMap();
  } catch (e) {
    // IndexedDB unavailable (private browsing, quota errors, etc.) – fall back gracefully
    console.warn('[createPersistedStore] IndexedDB unavailable, using in-memory cache only.', e);
  }
  const source  = new RecordSource(initial);
  const store   = new Store(source, {
    gcReleaseBufferSize: 10
  });

  // debounce write-back
  let t: number | undefined;
  const schedule = () => {
    clearTimeout(t);
    t = window.setTimeout(async () => {
      try {
        await persist(source.toJSON());
      } catch (e) {
        console.warn('[createPersistedStore] Failed to persist to IndexedDB', e);
      }
    }, DELAY_MS);
  };

  // Monitor store changes by overriding the publish method
  // The publish method is called whenever data is written to the store
  // (from network, optimistic updates, local updates, etc.)
  const originalPublish = store.publish.bind(store);
  store.publish = (source: RecordSource, idsMarkedForInvalidation?: Set<DataID>) => {
    originalPublish(source, idsMarkedForInvalidation);
    schedule(); // Trigger persistence after any publish
  };

  // Add beforeunload handler to flush data before tab close/reload
  // This prevents data loss from the debounce window
  const beforeUnloadHandler = () => {
    if (t) {
      clearTimeout(t);
      try {
        // Note: beforeunload must be synchronous, so we can't await
        // The persist function will handle errors gracefully
        persist(source.toJSON()).catch((e) => {
          // Ignore errors during unload - data will be recovered on next visit
          console.warn('[createPersistedStore] Failed to persist on beforeunload', e);
        });
      } catch (e) {
        console.warn('[createPersistedStore] Failed to initiate persist on beforeunload', e);
      }
    }
  };

  window.addEventListener('beforeunload', beforeUnloadHandler);

  return store;
}