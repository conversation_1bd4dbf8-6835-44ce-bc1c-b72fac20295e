import RelayEnvironment from '@/src/relay/RelayEnvironment';
import { Environment } from 'relay-runtime';
import { createPersistedStore } from './createPersistedStore';

// Dynamically load dev utilities only in development to prevent production bundling
if (process.env.NODE_ENV === 'development') {
  import('./devUtils').catch((error) => {
    console.warn('Failed to load Relay dev utilities:', error);
  });
}

let cached: Promise<Environment>;

export function getRelayEnvironment(): Promise<Environment> {

  if (!cached) {
    cached = (async () => {
      const store = await createPersistedStore();
      return new Environment({
        network: RelayEnvironment.getNetwork(), // keeps existing auth + toast logic
        store,
        UNSTABLE_defaultRenderPolicy: 'partial'
      });
    })();
  }
  return cached;
}