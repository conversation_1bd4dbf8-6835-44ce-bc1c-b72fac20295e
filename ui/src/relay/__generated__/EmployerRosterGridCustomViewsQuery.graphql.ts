/**
 * @generated SignedSource<<caaefb57b4a0cf7390c6e9a84603c37a>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ConcreteRequest } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type EmployerRosterGridCustomViewsQuery$variables = {
  customViewsName: string;
};
export type EmployerRosterGridCustomViewsQuery$data = {
  readonly customViewsByType: {
    readonly $updatableFragmentSpreads: FragmentRefs<"EmployerRosterGridCustomViewsFragment_updatable">;
    readonly " $fragmentSpreads": FragmentRefs<"EmployerRosterGridCustomViewsFragment">;
  } | null | undefined;
};
export type EmployerRosterGridCustomViewsQuery = {
  response: EmployerRosterGridCustomViewsQuery$data;
  variables: EmployerRosterGridCustomViewsQuery$variables;
};

const node: ConcreteRequest = (function(){
var v0 = [
  {
    "defaultValue": null,
    "kind": "LocalArgument",
    "name": "customViewsName"
  }
],
v1 = [
  {
    "kind": "Variable",
    "name": "name",
    "variableName": "customViewsName"
  }
];
return {
  "fragment": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Fragment",
    "metadata": null,
    "name": "EmployerRosterGridCustomViewsQuery",
    "selections": [
      {
        "alias": null,
        "args": (v1/*: any*/),
        "concreteType": "CustomViews",
        "kind": "LinkedField",
        "name": "customViewsByType",
        "plural": false,
        "selections": [
          {
            "args": null,
            "kind": "FragmentSpread",
            "name": "EmployerRosterGridCustomViewsFragment"
          },
          {
            "args": null,
            "kind": "FragmentSpread",
            "name": "EmployerRosterGridCustomViewsFragment_updatable"
          }
        ],
        "storageKey": null
      }
    ],
    "type": "Query",
    "abstractKey": null
  },
  "kind": "Request",
  "operation": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Operation",
    "name": "EmployerRosterGridCustomViewsQuery",
    "selections": [
      {
        "alias": null,
        "args": (v1/*: any*/),
        "concreteType": "CustomViews",
        "kind": "LinkedField",
        "name": "customViewsByType",
        "plural": false,
        "selections": [
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "id",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "value",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "__typename",
            "storageKey": null
          }
        ],
        "storageKey": null
      }
    ]
  },
  "params": {
    "cacheID": "ec0f36920091a8fcd9dc4ce3847b357f",
    "id": null,
    "metadata": {},
    "name": "EmployerRosterGridCustomViewsQuery",
    "operationKind": "query",
    "text": "query EmployerRosterGridCustomViewsQuery(\n  $customViewsName: String!\n) {\n  customViewsByType(name: $customViewsName) {\n    ...EmployerRosterGridCustomViewsFragment\n    __typename\n    id\n  }\n}\n\nfragment EmployerRosterGridCustomViewsFragment on CustomViews {\n  id\n  value\n}\n"
  }
};
})();

(node as any).hash = "631719d77c449de426ae22b990f4319c";

export default node;
