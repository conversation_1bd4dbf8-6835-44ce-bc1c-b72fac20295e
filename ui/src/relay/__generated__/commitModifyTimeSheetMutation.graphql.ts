/**
 * @generated SignedSource<<c21c804a2da0b72d59b3de48f82c8547>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ConcreteRequest } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type ModifyTimeSheetInput = {
  addPayStubs?: ReadonlyArray<AddPayStubInput> | null | undefined;
  deletePayStubIds?: ReadonlyArray<string> | null | undefined;
  employerGuid: any;
  id: string;
  modificationDate?: any | null | undefined;
  modifyPayStubs?: ReadonlyArray<ModifyPayStubInput> | null | undefined;
  name?: string | null | undefined;
  readOnly?: boolean | null | undefined;
  showBonusColumn?: boolean | null | undefined;
  showCostCenterColumn?: boolean | null | undefined;
  showDTHoursColumn?: boolean | null | undefined;
  showEarningsCodesColumn?: boolean | null | undefined;
  showExpensesColumn?: boolean | null | undefined;
  status?: string | null | undefined;
  timeSheetId?: any | null | undefined;
  type?: string | null | undefined;
};
export type AddPayStubInput = {
  bonus?: number | null | undefined;
  delete?: boolean | null | undefined;
  details?: ReadonlyArray<AddPayStubDetailInput> | null | undefined;
  dtHours?: number | null | undefined;
  employeeId: string;
  employeeName?: string | null | undefined;
  expanded?: boolean | null | undefined;
  expenses?: number | null | undefined;
  id?: string | null | undefined;
  inEdit?: boolean | null | undefined;
  name?: string | null | undefined;
  otHours?: number | null | undefined;
  stHours?: number | null | undefined;
};
export type AddPayStubDetailInput = {
  agreementId?: number | null | undefined;
  bonus?: number | null | undefined;
  classificationId?: number | null | undefined;
  costCenter?: string | null | undefined;
  delete?: boolean | null | undefined;
  dtHours?: number | null | undefined;
  earningsCode?: string | null | undefined;
  expenses?: number | null | undefined;
  hourlyRate?: number | null | undefined;
  id?: string | null | undefined;
  jobCode?: string | null | undefined;
  name?: string | null | undefined;
  otHours?: number | null | undefined;
  payStubId?: string | null | undefined;
  reportLineItemId?: number | null | undefined;
  stHours?: number | null | undefined;
  subClassificationId?: number | null | undefined;
  workDate: any;
};
export type ModifyPayStubInput = {
  bonus?: number | null | undefined;
  details?: ReadonlyArray<ModifyPayStubDetailInput> | null | undefined;
  dtHours?: number | null | undefined;
  employeeId: string;
  employeeName?: string | null | undefined;
  expanded?: boolean | null | undefined;
  expenses?: number | null | undefined;
  id: string;
  inEdit?: boolean | null | undefined;
  name?: string | null | undefined;
  otHours?: number | null | undefined;
  stHours?: number | null | undefined;
};
export type ModifyPayStubDetailInput = {
  agreementId?: number | null | undefined;
  bonus?: number | null | undefined;
  classificationId?: number | null | undefined;
  costCenter?: string | null | undefined;
  delete?: boolean | null | undefined;
  dtHours?: number | null | undefined;
  earningsCode?: string | null | undefined;
  expenses?: number | null | undefined;
  hourlyRate?: number | null | undefined;
  id?: string | null | undefined;
  jobCode?: string | null | undefined;
  name?: string | null | undefined;
  otHours?: number | null | undefined;
  payStubId?: string | null | undefined;
  reportLineItemId?: number | null | undefined;
  stHours?: number | null | undefined;
  subClassificationId?: number | null | undefined;
  workDate: any;
};
export type commitModifyTimeSheetMutation$variables = {
  input: ModifyTimeSheetInput;
};
export type commitModifyTimeSheetMutation$data = {
  readonly modifyTimeSheet: {
    readonly timeSheet: {
      readonly id: string;
      readonly " $fragmentSpreads": FragmentRefs<"TimeSheetSettings_timeSheet">;
    } | null | undefined;
  };
};
export type commitModifyTimeSheetMutation = {
  response: commitModifyTimeSheetMutation$data;
  variables: commitModifyTimeSheetMutation$variables;
};

const node: ConcreteRequest = (function(){
var v0 = [
  {
    "defaultValue": null,
    "kind": "LocalArgument",
    "name": "input"
  }
],
v1 = [
  {
    "kind": "Variable",
    "name": "input",
    "variableName": "input"
  }
],
v2 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "id",
  "storageKey": null
};
return {
  "fragment": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Fragment",
    "metadata": null,
    "name": "commitModifyTimeSheetMutation",
    "selections": [
      {
        "alias": null,
        "args": (v1/*: any*/),
        "concreteType": "ModifyTimeSheetPayload",
        "kind": "LinkedField",
        "name": "modifyTimeSheet",
        "plural": false,
        "selections": [
          {
            "alias": null,
            "args": null,
            "concreteType": "TimeSheet",
            "kind": "LinkedField",
            "name": "timeSheet",
            "plural": false,
            "selections": [
              (v2/*: any*/),
              {
                "args": null,
                "kind": "FragmentSpread",
                "name": "TimeSheetSettings_timeSheet"
              }
            ],
            "storageKey": null
          }
        ],
        "storageKey": null
      }
    ],
    "type": "Mutation",
    "abstractKey": null
  },
  "kind": "Request",
  "operation": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Operation",
    "name": "commitModifyTimeSheetMutation",
    "selections": [
      {
        "alias": null,
        "args": (v1/*: any*/),
        "concreteType": "ModifyTimeSheetPayload",
        "kind": "LinkedField",
        "name": "modifyTimeSheet",
        "plural": false,
        "selections": [
          {
            "alias": null,
            "args": null,
            "concreteType": "TimeSheet",
            "kind": "LinkedField",
            "name": "timeSheet",
            "plural": false,
            "selections": [
              (v2/*: any*/),
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "showBonusColumn",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "showCostCenterColumn",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "showDTHoursColumn",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "showEarningsCodesColumn",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "showExpensesColumn",
                "storageKey": null
              }
            ],
            "storageKey": null
          }
        ],
        "storageKey": null
      }
    ]
  },
  "params": {
    "cacheID": "5ecb510a39f345c7ffa87e31880a88b8",
    "id": null,
    "metadata": {},
    "name": "commitModifyTimeSheetMutation",
    "operationKind": "mutation",
    "text": "mutation commitModifyTimeSheetMutation(\n  $input: ModifyTimeSheetInput!\n) {\n  modifyTimeSheet(input: $input) {\n    timeSheet {\n      id\n      ...TimeSheetSettings_timeSheet\n    }\n  }\n}\n\nfragment TimeSheetSettings_timeSheet on TimeSheet {\n  id\n  showBonusColumn\n  showCostCenterColumn\n  showDTHoursColumn\n  showEarningsCodesColumn\n  showExpensesColumn\n}\n"
  }
};
})();

(node as any).hash = "6ba85dcdecb5606916fe627f4ae9718e";

export default node;
