/**
 * @generated SignedSource<<c90520706150d00ca29d5492add6a335>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ReaderFragment } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type fragments_TimesheetComboBoxData$data = {
  readonly id: string;
  readonly " $fragmentType": "fragments_TimesheetComboBoxData";
};
export type fragments_TimesheetComboBoxData$key = {
  readonly " $data"?: fragments_TimesheetComboBoxData$data;
  readonly " $fragmentSpreads": FragmentRefs<"fragments_TimesheetComboBoxData">;
};

const node: ReaderFragment = {
  "argumentDefinitions": [],
  "kind": "Fragment",
  "metadata": null,
  "name": "fragments_TimesheetComboBoxData",
  "selections": [
    {
      "alias": null,
      "args": null,
      "kind": "<PERSON>alar<PERSON>ield",
      "name": "id",
      "storageKey": null
    }
  ],
  "type": "TimeSheet",
  "abstractKey": null
};

(node as any).hash = "e3e8b4a1e0542dd3ed6c2c439e76e5c6";

export default node;
