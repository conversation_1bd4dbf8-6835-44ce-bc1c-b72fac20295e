/**
 * @generated SignedSource<<61c8b3428d0be47ddea2ec527015cf13>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ReaderFragment } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type fragments_AgreementDetailedInfo$data = {
  readonly id: string;
  readonly name: string;
  readonly " $fragmentType": "fragments_AgreementDetailedInfo";
};
export type fragments_AgreementDetailedInfo$key = {
  readonly " $data"?: fragments_AgreementDetailedInfo$data;
  readonly " $fragmentSpreads": FragmentRefs<"fragments_AgreementDetailedInfo">;
};

const node: ReaderFragment = {
  "argumentDefinitions": [],
  "kind": "Fragment",
  "metadata": null,
  "name": "fragments_AgreementDetailedInfo",
  "selections": [
    {
      "alias": null,
      "args": null,
      "kind": "<PERSON>alar<PERSON><PERSON>",
      "name": "id",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "Scalar<PERSON>ield",
      "name": "name",
      "storageKey": null
    }
  ],
  "type": "Agreement",
  "abstractKey": null
};

(node as any).hash = "83d62caeeaacd71e55a9f4e01669d5a0";

export default node;
