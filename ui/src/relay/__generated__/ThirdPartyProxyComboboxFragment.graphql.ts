/**
 * @generated SignedSource<<b8d0d4b269c69edcbb0cf8c95f8c85d5>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ReaderFragment } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type ThirdPartyProxyComboboxFragment$data = {
  readonly __id: string;
  readonly thirdParties: {
    readonly edges: ReadonlyArray<{
      readonly node: {
        readonly guid: any | null | undefined;
        readonly id: string;
        readonly label: string;
        readonly value: number;
      };
    }> | null | undefined;
  } | null | undefined;
  readonly " $fragmentType": "ThirdPartyProxyComboboxFragment";
};
export type ThirdPartyProxyComboboxFragment$key = {
  readonly " $data"?: ThirdPartyProxyComboboxFragment$data;
  readonly " $fragmentSpreads": FragmentRefs<"ThirdPartyProxyComboboxFragment">;
};

const node: ReaderFragment = {
  "argumentDefinitions": [
    {
      "defaultValue": null,
      "kind": "LocalArgument",
      "name": "chapterId"
    },
    {
      "defaultValue": 1000,
      "kind": "LocalArgument",
      "name": "first"
    }
  ],
  "kind": "Fragment",
  "metadata": {
    "connection": [
      {
        "count": "first",
        "cursor": null,
        "direction": "forward",
        "path": [
          "thirdParties"
        ]
      }
    ]
  },
  "name": "ThirdPartyProxyComboboxFragment",
  "selections": [
    {
      "alias": "thirdParties",
      "args": [
        {
          "kind": "Variable",
          "name": "chapterId",
          "variableName": "chapterId"
        }
      ],
      "concreteType": "ThirdPartyInfoDtoConnection",
      "kind": "LinkedField",
      "name": "__ThirdPartyProxyComboboxFragment_thirdParties_connection",
      "plural": false,
      "selections": [
        {
          "alias": null,
          "args": null,
          "concreteType": "ThirdPartyInfoDtoEdge",
          "kind": "LinkedField",
          "name": "edges",
          "plural": true,
          "selections": [
            {
              "alias": null,
              "args": null,
              "concreteType": "ThirdPartyInfoDto",
              "kind": "LinkedField",
              "name": "node",
              "plural": false,
              "selections": [
                {
                  "alias": null,
                  "args": null,
                  "kind": "ScalarField",
                  "name": "label",
                  "storageKey": null
                },
                {
                  "alias": null,
                  "args": null,
                  "kind": "ScalarField",
                  "name": "value",
                  "storageKey": null
                },
                {
                  "alias": null,
                  "args": null,
                  "kind": "ScalarField",
                  "name": "guid",
                  "storageKey": null
                },
                {
                  "alias": null,
                  "args": null,
                  "kind": "ScalarField",
                  "name": "id",
                  "storageKey": null
                },
                {
                  "alias": null,
                  "args": null,
                  "kind": "ScalarField",
                  "name": "__typename",
                  "storageKey": null
                }
              ],
              "storageKey": null
            },
            {
              "alias": null,
              "args": null,
              "kind": "ScalarField",
              "name": "cursor",
              "storageKey": null
            }
          ],
          "storageKey": null
        },
        {
          "alias": null,
          "args": null,
          "concreteType": "PageInfo",
          "kind": "LinkedField",
          "name": "pageInfo",
          "plural": false,
          "selections": [
            {
              "alias": null,
              "args": null,
              "kind": "ScalarField",
              "name": "endCursor",
              "storageKey": null
            },
            {
              "alias": null,
              "args": null,
              "kind": "ScalarField",
              "name": "hasNextPage",
              "storageKey": null
            }
          ],
          "storageKey": null
        }
      ],
      "storageKey": null
    },
    {
      "kind": "ClientExtension",
      "selections": [
        {
          "alias": null,
          "args": null,
          "kind": "ScalarField",
          "name": "__id",
          "storageKey": null
        }
      ]
    }
  ],
  "type": "Query",
  "abstractKey": null
};

(node as any).hash = "0abf03e380cb0ed278777140513db6c1";

export default node;
