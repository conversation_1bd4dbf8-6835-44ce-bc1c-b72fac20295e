/**
 * @generated SignedSource<<8ab412a15633ff393bade1deedad52ef>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ConcreteRequest } from 'relay-runtime';
export type AddTimesheetInput = {
  employerGuid: any;
  includeHours?: boolean | null | undefined;
  includeJobCodes?: boolean | null | undefined;
  isCopy?: boolean | null | undefined;
  name?: string | null | undefined;
  payPeriodEndDate: any;
  payStubs?: ReadonlyArray<AddPayStubInput> | null | undefined;
  readOnly?: boolean | null | undefined;
  showBonusColumn?: boolean | null | undefined;
  showCostCenterColumn?: boolean | null | undefined;
  showDTHoursColumn?: boolean | null | undefined;
  showEarningsCodesColumn?: boolean | null | undefined;
  showExpensesColumn?: boolean | null | undefined;
  sourceTimesheetId?: number | null | undefined;
  status: string;
  type: string;
};
export type AddPayStubInput = {
  bonus?: number | null | undefined;
  delete?: boolean | null | undefined;
  details?: ReadonlyArray<AddPayStubDetailInput> | null | undefined;
  dtHours?: number | null | undefined;
  employeeId: string;
  employeeName?: string | null | undefined;
  expanded?: boolean | null | undefined;
  expenses?: number | null | undefined;
  id?: string | null | undefined;
  inEdit?: boolean | null | undefined;
  name?: string | null | undefined;
  otHours?: number | null | undefined;
  stHours?: number | null | undefined;
};
export type AddPayStubDetailInput = {
  agreementId?: number | null | undefined;
  bonus?: number | null | undefined;
  classificationId?: number | null | undefined;
  costCenter?: string | null | undefined;
  delete?: boolean | null | undefined;
  dtHours?: number | null | undefined;
  earningsCode?: string | null | undefined;
  expenses?: number | null | undefined;
  hourlyRate?: number | null | undefined;
  id?: string | null | undefined;
  jobCode?: string | null | undefined;
  name?: string | null | undefined;
  otHours?: number | null | undefined;
  payStubId?: string | null | undefined;
  reportLineItemId?: number | null | undefined;
  stHours?: number | null | undefined;
  subClassificationId?: number | null | undefined;
  workDate: any;
};
export type useTimesheetSaverAddMutation$variables = {
  first?: number | null | undefined;
  input: AddTimesheetInput;
};
export type useTimesheetSaverAddMutation$data = {
  readonly addTimesheet: {
    readonly timeSheetEdge: {
      readonly node: {
        readonly creationDate: any | null | undefined;
        readonly employerGuid: any;
        readonly hoursWorked: number;
        readonly id: string;
        readonly modificationDate: any | null | undefined;
        readonly modifiedByUserId: string | null | undefined;
        readonly name: string | null | undefined;
        readonly numericId: number;
        readonly oldId: any | null | undefined;
        readonly payPeriodEndDate: any | null | undefined;
        readonly payStubCount: number;
        readonly payStubs: {
          readonly edges: ReadonlyArray<{
            readonly node: {
              readonly details: ReadonlyArray<{
                readonly agreementId: number | null | undefined;
                readonly bonus: number | null | undefined;
                readonly classificationId: number | null | undefined;
                readonly costCenter: string | null | undefined;
                readonly dtHours: number | null | undefined;
                readonly earningsCode: string | null | undefined;
                readonly expenses: number | null | undefined;
                readonly hourlyRate: number | null | undefined;
                readonly id: string;
                readonly jobCode: string | null | undefined;
                readonly name: string | null | undefined;
                readonly otHours: number | null | undefined;
                readonly payStubId: string;
                readonly reportLineItemId: number | null | undefined;
                readonly stHours: number | null | undefined;
                readonly subClassificationId: number | null | undefined;
                readonly workDate: any;
              }>;
              readonly employeeId: string;
              readonly id: string;
              readonly name: string | null | undefined;
            };
          }> | null | undefined;
        } | null | undefined;
        readonly showBonusColumn: boolean | null | undefined;
        readonly showCostCenterColumn: boolean | null | undefined;
        readonly showDTHoursColumn: boolean | null | undefined;
        readonly showEarningsCodesColumn: boolean | null | undefined;
        readonly showExpensesColumn: boolean | null | undefined;
        readonly status: string;
        readonly type: string;
      } | null | undefined;
    } | null | undefined;
  };
};
export type useTimesheetSaverAddMutation = {
  response: useTimesheetSaverAddMutation$data;
  variables: useTimesheetSaverAddMutation$variables;
};

const node: ConcreteRequest = (function(){
var v0 = {
  "defaultValue": 500,
  "kind": "LocalArgument",
  "name": "first"
},
v1 = {
  "defaultValue": null,
  "kind": "LocalArgument",
  "name": "input"
},
v2 = [
  {
    "kind": "Variable",
    "name": "input",
    "variableName": "input"
  }
],
v3 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "id",
  "storageKey": null
},
v4 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "numericId",
  "storageKey": null
},
v5 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "employerGuid",
  "storageKey": null
},
v6 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "name",
  "storageKey": null
},
v7 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "payPeriodEndDate",
  "storageKey": null
},
v8 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "status",
  "storageKey": null
},
v9 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "type",
  "storageKey": null
},
v10 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "creationDate",
  "storageKey": null
},
v11 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "modificationDate",
  "storageKey": null
},
v12 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "showBonusColumn",
  "storageKey": null
},
v13 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "showCostCenterColumn",
  "storageKey": null
},
v14 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "showDTHoursColumn",
  "storageKey": null
},
v15 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "showEarningsCodesColumn",
  "storageKey": null
},
v16 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "showExpensesColumn",
  "storageKey": null
},
v17 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "oldId",
  "storageKey": null
},
v18 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "hoursWorked",
  "storageKey": null
},
v19 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "payStubCount",
  "storageKey": null
},
v20 = [
  {
    "alias": null,
    "args": null,
    "concreteType": "PayStubEdge",
    "kind": "LinkedField",
    "name": "edges",
    "plural": true,
    "selections": [
      {
        "alias": null,
        "args": null,
        "concreteType": "PayStub",
        "kind": "LinkedField",
        "name": "node",
        "plural": false,
        "selections": [
          (v3/*: any*/),
          (v6/*: any*/),
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "employeeId",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "concreteType": "PayStubDetail",
            "kind": "LinkedField",
            "name": "details",
            "plural": true,
            "selections": [
              (v3/*: any*/),
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "payStubId",
                "storageKey": null
              },
              (v6/*: any*/),
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "workDate",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "otHours",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "stHours",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "dtHours",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "jobCode",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "earningsCode",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "agreementId",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "classificationId",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "subClassificationId",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "costCenter",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "hourlyRate",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "bonus",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "expenses",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "reportLineItemId",
                "storageKey": null
              }
            ],
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "__typename",
            "storageKey": null
          }
        ],
        "storageKey": null
      },
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "cursor",
        "storageKey": null
      }
    ],
    "storageKey": null
  },
  {
    "alias": null,
    "args": null,
    "concreteType": "PageInfo",
    "kind": "LinkedField",
    "name": "pageInfo",
    "plural": false,
    "selections": [
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "endCursor",
        "storageKey": null
      },
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "hasNextPage",
        "storageKey": null
      }
    ],
    "storageKey": null
  }
],
v21 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "modifiedByUserId",
  "storageKey": null
},
v22 = [
  {
    "kind": "Variable",
    "name": "first",
    "variableName": "first"
  }
];
return {
  "fragment": {
    "argumentDefinitions": [
      (v0/*: any*/),
      (v1/*: any*/)
    ],
    "kind": "Fragment",
    "metadata": null,
    "name": "useTimesheetSaverAddMutation",
    "selections": [
      {
        "alias": null,
        "args": (v2/*: any*/),
        "concreteType": "AddTimesheetPayload",
        "kind": "LinkedField",
        "name": "addTimesheet",
        "plural": false,
        "selections": [
          {
            "alias": null,
            "args": null,
            "concreteType": "EdgeOfTimeSheet",
            "kind": "LinkedField",
            "name": "timeSheetEdge",
            "plural": false,
            "selections": [
              {
                "alias": null,
                "args": null,
                "concreteType": "TimeSheet",
                "kind": "LinkedField",
                "name": "node",
                "plural": false,
                "selections": [
                  (v3/*: any*/),
                  (v4/*: any*/),
                  (v5/*: any*/),
                  (v6/*: any*/),
                  (v7/*: any*/),
                  (v8/*: any*/),
                  (v9/*: any*/),
                  (v10/*: any*/),
                  (v11/*: any*/),
                  (v12/*: any*/),
                  (v13/*: any*/),
                  (v14/*: any*/),
                  (v15/*: any*/),
                  (v16/*: any*/),
                  (v17/*: any*/),
                  (v18/*: any*/),
                  (v19/*: any*/),
                  {
                    "alias": "payStubs",
                    "args": null,
                    "concreteType": "PayStubConnection",
                    "kind": "LinkedField",
                    "name": "__useTimesheetSaverAdd_payStubs_connection",
                    "plural": false,
                    "selections": (v20/*: any*/),
                    "storageKey": null
                  },
                  (v21/*: any*/)
                ],
                "storageKey": null
              }
            ],
            "storageKey": null
          }
        ],
        "storageKey": null
      }
    ],
    "type": "Mutation",
    "abstractKey": null
  },
  "kind": "Request",
  "operation": {
    "argumentDefinitions": [
      (v1/*: any*/),
      (v0/*: any*/)
    ],
    "kind": "Operation",
    "name": "useTimesheetSaverAddMutation",
    "selections": [
      {
        "alias": null,
        "args": (v2/*: any*/),
        "concreteType": "AddTimesheetPayload",
        "kind": "LinkedField",
        "name": "addTimesheet",
        "plural": false,
        "selections": [
          {
            "alias": null,
            "args": null,
            "concreteType": "EdgeOfTimeSheet",
            "kind": "LinkedField",
            "name": "timeSheetEdge",
            "plural": false,
            "selections": [
              {
                "alias": null,
                "args": null,
                "concreteType": "TimeSheet",
                "kind": "LinkedField",
                "name": "node",
                "plural": false,
                "selections": [
                  (v3/*: any*/),
                  (v4/*: any*/),
                  (v5/*: any*/),
                  (v6/*: any*/),
                  (v7/*: any*/),
                  (v8/*: any*/),
                  (v9/*: any*/),
                  (v10/*: any*/),
                  (v11/*: any*/),
                  (v12/*: any*/),
                  (v13/*: any*/),
                  (v14/*: any*/),
                  (v15/*: any*/),
                  (v16/*: any*/),
                  (v17/*: any*/),
                  (v18/*: any*/),
                  (v19/*: any*/),
                  {
                    "alias": null,
                    "args": (v22/*: any*/),
                    "concreteType": "PayStubConnection",
                    "kind": "LinkedField",
                    "name": "payStubs",
                    "plural": false,
                    "selections": (v20/*: any*/),
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": (v22/*: any*/),
                    "filters": null,
                    "handle": "connection",
                    "key": "useTimesheetSaverAdd_payStubs",
                    "kind": "LinkedHandle",
                    "name": "payStubs"
                  },
                  (v21/*: any*/)
                ],
                "storageKey": null
              }
            ],
            "storageKey": null
          }
        ],
        "storageKey": null
      }
    ]
  },
  "params": {
    "cacheID": "de862dd7a522ffde862a50134aa4f2ff",
    "id": null,
    "metadata": {
      "connection": [
        {
          "count": "first",
          "cursor": null,
          "direction": "forward",
          "path": [
            "addTimesheet",
            "timeSheetEdge",
            "node",
            "payStubs"
          ]
        }
      ]
    },
    "name": "useTimesheetSaverAddMutation",
    "operationKind": "mutation",
    "text": "mutation useTimesheetSaverAddMutation(\n  $input: AddTimesheetInput!\n  $first: Int = 500\n) {\n  addTimesheet(input: $input) {\n    timeSheetEdge {\n      node {\n        id\n        numericId\n        employerGuid\n        name\n        payPeriodEndDate\n        status\n        type\n        creationDate\n        modificationDate\n        showBonusColumn\n        showCostCenterColumn\n        showDTHoursColumn\n        showEarningsCodesColumn\n        showExpensesColumn\n        oldId\n        hoursWorked\n        payStubCount\n        payStubs(first: $first) {\n          edges {\n            node {\n              id\n              name\n              employeeId\n              details {\n                id\n                payStubId\n                name\n                workDate\n                otHours\n                stHours\n                dtHours\n                jobCode\n                earningsCode\n                agreementId\n                classificationId\n                subClassificationId\n                costCenter\n                hourlyRate\n                bonus\n                expenses\n                reportLineItemId\n              }\n              __typename\n            }\n            cursor\n          }\n          pageInfo {\n            endCursor\n            hasNextPage\n          }\n        }\n        modifiedByUserId\n      }\n    }\n  }\n}\n"
  }
};
})();

(node as any).hash = "879006f3a6cb13f246cff9fb6408dbe3";

export default node;
