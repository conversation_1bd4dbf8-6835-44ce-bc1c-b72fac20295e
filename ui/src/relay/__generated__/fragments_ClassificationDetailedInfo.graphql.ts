/**
 * @generated SignedSource<<909c7524e87be7f6104709962bdfd928>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ReaderFragment } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type fragments_ClassificationDetailedInfo$data = {
  readonly id: string;
  readonly name: string;
  readonly " $fragmentType": "fragments_ClassificationDetailedInfo";
};
export type fragments_ClassificationDetailedInfo$key = {
  readonly " $data"?: fragments_ClassificationDetailedInfo$data;
  readonly " $fragmentSpreads": FragmentRefs<"fragments_ClassificationDetailedInfo">;
};

const node: ReaderFragment = {
  "argumentDefinitions": [],
  "kind": "Fragment",
  "metadata": null,
  "name": "fragments_ClassificationDetailedInfo",
  "selections": [
    {
      "alias": null,
      "args": null,
      "kind": "Scalar<PERSON>ield",
      "name": "id",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "Scalar<PERSON>ield",
      "name": "name",
      "storageKey": null
    }
  ],
  "type": "ClassificationName",
  "abstractKey": null
};

(node as any).hash = "645d2592344059d3ce042de44bc67ed0";

export default node;
