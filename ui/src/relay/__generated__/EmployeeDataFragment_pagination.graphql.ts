/**
 * @generated SignedSource<<ea89f5414ec7c4b66c068901aece023c>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ReaderFragment } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type EmployeeDataFragment_pagination$data = {
  readonly employeesByEmployerGuidAsync: {
    readonly edges: ReadonlyArray<{
      readonly node: {
        readonly active: boolean;
        readonly externalEmployeeId: string | null | undefined;
        readonly firstName: string | null | undefined;
        readonly id: string;
        readonly lastName: string;
        readonly ssn: ReadonlyArray<any> | null | undefined;
      };
    }> | null | undefined;
    readonly pageInfo: {
      readonly endCursor: string | null | undefined;
      readonly hasNextPage: boolean;
    };
    readonly totalCount: number;
  } | null | undefined;
  readonly signatoryAgreements: {
    readonly nodes: ReadonlyArray<{
      readonly id: string;
      readonly name: string;
    }> | null | undefined;
  } | null | undefined;
  readonly " $fragmentType": "EmployeeDataFragment_pagination";
};
export type EmployeeDataFragment_pagination$key = {
  readonly " $data"?: EmployeeDataFragment_pagination$data;
  readonly " $fragmentSpreads": FragmentRefs<"EmployeeDataFragment_pagination">;
};

import EmployeeDataPaginationQuery_graphql from './EmployeeDataPaginationQuery.graphql';

const node: ReaderFragment = (function(){
var v0 = [
  "employeesByEmployerGuidAsync"
],
v1 = {
  "kind": "Variable",
  "name": "employerGuid",
  "variableName": "employerGuid"
},
v2 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "id",
  "storageKey": null
};
return {
  "argumentDefinitions": [
    {
      "defaultValue": null,
      "kind": "LocalArgument",
      "name": "after"
    },
    {
      "defaultValue": null,
      "kind": "LocalArgument",
      "name": "employerGuid"
    },
    {
      "defaultValue": 50,
      "kind": "LocalArgument",
      "name": "first"
    }
  ],
  "kind": "Fragment",
  "metadata": {
    "connection": [
      {
        "count": "first",
        "cursor": "after",
        "direction": "forward",
        "path": (v0/*: any*/)
      }
    ],
    "refetch": {
      "connection": {
        "forward": {
          "count": "first",
          "cursor": "after"
        },
        "backward": null,
        "path": (v0/*: any*/)
      },
      "fragmentPathInResult": [],
      "operation": EmployeeDataPaginationQuery_graphql
    }
  },
  "name": "EmployeeDataFragment_pagination",
  "selections": [
    {
      "alias": "employeesByEmployerGuidAsync",
      "args": [
        (v1/*: any*/)
      ],
      "concreteType": "EmployeeConnection",
      "kind": "LinkedField",
      "name": "__EmployeeDataFragment_pagination__employeesByEmployerGuidAsync_connection",
      "plural": false,
      "selections": [
        {
          "alias": null,
          "args": null,
          "concreteType": "EmployeeEdge",
          "kind": "LinkedField",
          "name": "edges",
          "plural": true,
          "selections": [
            {
              "alias": null,
              "args": null,
              "concreteType": "Employee",
              "kind": "LinkedField",
              "name": "node",
              "plural": false,
              "selections": [
                (v2/*: any*/),
                {
                  "alias": null,
                  "args": null,
                  "kind": "ScalarField",
                  "name": "firstName",
                  "storageKey": null
                },
                {
                  "alias": null,
                  "args": null,
                  "kind": "ScalarField",
                  "name": "lastName",
                  "storageKey": null
                },
                {
                  "alias": null,
                  "args": null,
                  "kind": "ScalarField",
                  "name": "active",
                  "storageKey": null
                },
                {
                  "alias": null,
                  "args": null,
                  "kind": "ScalarField",
                  "name": "externalEmployeeId",
                  "storageKey": null
                },
                {
                  "alias": null,
                  "args": null,
                  "kind": "ScalarField",
                  "name": "ssn",
                  "storageKey": null
                },
                {
                  "alias": null,
                  "args": null,
                  "kind": "ScalarField",
                  "name": "__typename",
                  "storageKey": null
                }
              ],
              "storageKey": null
            },
            {
              "alias": null,
              "args": null,
              "kind": "ScalarField",
              "name": "cursor",
              "storageKey": null
            }
          ],
          "storageKey": null
        },
        {
          "alias": null,
          "args": null,
          "concreteType": "PageInfo",
          "kind": "LinkedField",
          "name": "pageInfo",
          "plural": false,
          "selections": [
            {
              "alias": null,
              "args": null,
              "kind": "ScalarField",
              "name": "hasNextPage",
              "storageKey": null
            },
            {
              "alias": null,
              "args": null,
              "kind": "ScalarField",
              "name": "endCursor",
              "storageKey": null
            }
          ],
          "storageKey": null
        },
        {
          "alias": null,
          "args": null,
          "kind": "ScalarField",
          "name": "totalCount",
          "storageKey": null
        }
      ],
      "storageKey": null
    },
    {
      "alias": null,
      "args": [
        {
          "fields": [
            (v1/*: any*/),
            {
              "kind": "Literal",
              "name": "includeInactiveAgreements",
              "value": false
            }
          ],
          "kind": "ObjectValue",
          "name": "input"
        }
      ],
      "concreteType": "AgreementConnection",
      "kind": "LinkedField",
      "name": "signatoryAgreements",
      "plural": false,
      "selections": [
        {
          "alias": null,
          "args": null,
          "concreteType": "Agreement",
          "kind": "LinkedField",
          "name": "nodes",
          "plural": true,
          "selections": [
            (v2/*: any*/),
            {
              "alias": null,
              "args": null,
              "kind": "ScalarField",
              "name": "name",
              "storageKey": null
            }
          ],
          "storageKey": null
        }
      ],
      "storageKey": null
    }
  ],
  "type": "Query",
  "abstractKey": null
};
})();

(node as any).hash = "a23e536b634826b3e8f1cfa025b3eb2b";

export default node;
