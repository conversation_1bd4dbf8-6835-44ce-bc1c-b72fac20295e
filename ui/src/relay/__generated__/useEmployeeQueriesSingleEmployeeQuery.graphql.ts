/**
 * @generated SignedSource<<8d85b3acd6db5644cfd2006d6151d4ca>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ConcreteRequest } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type useEmployeeQueriesSingleEmployeeQuery$variables = {
  employeeId: string;
};
export type useEmployeeQueriesSingleEmployeeQuery$data = {
  readonly employeeById: {
    readonly " $fragmentSpreads": FragmentRefs<"useEmployeeQueries_employee">;
  } | null | undefined;
};
export type useEmployeeQueriesSingleEmployeeQuery = {
  response: useEmployeeQueriesSingleEmployeeQuery$data;
  variables: useEmployeeQueriesSingleEmployeeQuery$variables;
};

const node: ConcreteRequest = (function(){
var v0 = [
  {
    "defaultValue": null,
    "kind": "LocalArgument",
    "name": "employeeId"
  }
],
v1 = [
  {
    "kind": "Variable",
    "name": "employeeId",
    "variableName": "employeeId"
  }
];
return {
  "fragment": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Fragment",
    "metadata": null,
    "name": "useEmployeeQueriesSingleEmployeeQuery",
    "selections": [
      {
        "alias": null,
        "args": (v1/*: any*/),
        "concreteType": "Employee",
        "kind": "LinkedField",
        "name": "employeeById",
        "plural": false,
        "selections": [
          {
            "args": null,
            "kind": "FragmentSpread",
            "name": "useEmployeeQueries_employee"
          }
        ],
        "storageKey": null
      }
    ],
    "type": "Query",
    "abstractKey": null
  },
  "kind": "Request",
  "operation": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Operation",
    "name": "useEmployeeQueriesSingleEmployeeQuery",
    "selections": [
      {
        "alias": null,
        "args": (v1/*: any*/),
        "concreteType": "Employee",
        "kind": "LinkedField",
        "name": "employeeById",
        "plural": false,
        "selections": [
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "id",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "externalEmployeeId",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "firstName",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "middleName",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "lastName",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "suffix",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "dateOfHire",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "dateOfTermination",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "active",
            "storageKey": null
          }
        ],
        "storageKey": null
      }
    ]
  },
  "params": {
    "cacheID": "61ee5179cbef2a2d19f3a3f14a32606a",
    "id": null,
    "metadata": {},
    "name": "useEmployeeQueriesSingleEmployeeQuery",
    "operationKind": "query",
    "text": "query useEmployeeQueriesSingleEmployeeQuery(\n  $employeeId: ID!\n) {\n  employeeById(employeeId: $employeeId) {\n    ...useEmployeeQueries_employee\n    id\n  }\n}\n\nfragment useEmployeeQueries_employee on Employee {\n  id\n  externalEmployeeId\n  firstName\n  middleName\n  lastName\n  suffix\n  dateOfHire\n  dateOfTermination\n  active\n}\n"
  }
};
})();

(node as any).hash = "c99dffac8cdbe5bac470e1ecc9240832";

export default node;
