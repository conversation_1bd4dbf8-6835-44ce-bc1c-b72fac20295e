/**
 * @generated SignedSource<<e274576ca7ef9ca188a87cc569ea4e64>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ConcreteRequest } from 'relay-runtime';
export type ClassificationComboBoxCellQuery$variables = {
  agreementId: string;
};
export type ClassificationComboBoxCellQuery$data = {
  readonly classificationsByAgreementId: {
    readonly nodes: ReadonlyArray<{
      readonly id: string;
      readonly name: string;
    }> | null | undefined;
  } | null | undefined;
};
export type ClassificationComboBoxCellQuery = {
  response: ClassificationComboBoxCellQuery$data;
  variables: ClassificationComboBoxCellQuery$variables;
};

const node: ConcreteRequest = (function(){
var v0 = [
  {
    "defaultValue": null,
    "kind": "LocalArgument",
    "name": "agreementId"
  }
],
v1 = [
  {
    "alias": null,
    "args": [
      {
        "kind": "Variable",
        "name": "agreementId",
        "variableName": "agreementId"
      }
    ],
    "concreteType": "ClassificationNameConnection",
    "kind": "LinkedField",
    "name": "classificationsByAgreementId",
    "plural": false,
    "selections": [
      {
        "alias": null,
        "args": null,
        "concreteType": "ClassificationName",
        "kind": "LinkedField",
        "name": "nodes",
        "plural": true,
        "selections": [
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "id",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "name",
            "storageKey": null
          }
        ],
        "storageKey": null
      }
    ],
    "storageKey": null
  }
];
return {
  "fragment": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Fragment",
    "metadata": null,
    "name": "ClassificationComboBoxCellQuery",
    "selections": (v1/*: any*/),
    "type": "Query",
    "abstractKey": null
  },
  "kind": "Request",
  "operation": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Operation",
    "name": "ClassificationComboBoxCellQuery",
    "selections": (v1/*: any*/)
  },
  "params": {
    "cacheID": "4ed64ac258026ac5c9ede5cea2c1abbc",
    "id": null,
    "metadata": {},
    "name": "ClassificationComboBoxCellQuery",
    "operationKind": "query",
    "text": "query ClassificationComboBoxCellQuery(\n  $agreementId: ID!\n) {\n  classificationsByAgreementId(agreementId: $agreementId) {\n    nodes {\n      id\n      name\n    }\n  }\n}\n"
  }
};
})();

(node as any).hash = "30b5515083384435b961597eb58ec0d8";

export default node;
