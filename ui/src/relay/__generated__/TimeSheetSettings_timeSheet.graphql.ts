/**
 * @generated SignedSource<<c1406eb616442547c57ce82ecdbf474c>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ReaderFragment } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type TimeSheetSettings_timeSheet$data = {
  readonly id: string;
  readonly showBonusColumn: boolean | null | undefined;
  readonly showCostCenterColumn: boolean | null | undefined;
  readonly showDTHoursColumn: boolean | null | undefined;
  readonly showEarningsCodesColumn: boolean | null | undefined;
  readonly showExpensesColumn: boolean | null | undefined;
  readonly " $fragmentType": "TimeSheetSettings_timeSheet";
};
export type TimeSheetSettings_timeSheet$key = {
  readonly " $data"?: TimeSheetSettings_timeSheet$data;
  readonly " $fragmentSpreads": FragmentRefs<"TimeSheetSettings_timeSheet">;
};

const node: ReaderFragment = {
  "argumentDefinitions": [],
  "kind": "Fragment",
  "metadata": null,
  "name": "TimeSheetSettings_timeSheet",
  "selections": [
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "id",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "showBonusColumn",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "showCostCenterColumn",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "showDTHoursColumn",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "showEarningsCodesColumn",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "showExpensesColumn",
      "storageKey": null
    }
  ],
  "type": "TimeSheet",
  "abstractKey": null
};

(node as any).hash = "4850836b5d0eda0efcd615e4e9c35ddf";

export default node;
