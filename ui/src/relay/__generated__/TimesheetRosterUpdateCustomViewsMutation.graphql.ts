/**
 * @generated SignedSource<<6afda91c4d2295636e2dbf7cdf2d5bbf>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ConcreteRequest } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type UpdateCustomViewsInput = {
  id?: string | null | undefined;
  name?: string | null | undefined;
  value?: any | null | undefined;
};
export type TimesheetRosterUpdateCustomViewsMutation$variables = {
  input: UpdateCustomViewsInput;
};
export type TimesheetRosterUpdateCustomViewsMutation$data = {
  readonly updateCustomViews: {
    readonly customViews: {
      readonly " $fragmentSpreads": FragmentRefs<"TimesheetRosterCustomViewsFragment">;
    } | null | undefined;
  };
};
export type TimesheetRosterUpdateCustomViewsMutation = {
  response: TimesheetRosterUpdateCustomViewsMutation$data;
  variables: TimesheetRosterUpdateCustomViewsMutation$variables;
};

const node: ConcreteRequest = (function(){
var v0 = [
  {
    "defaultValue": null,
    "kind": "LocalArgument",
    "name": "input"
  }
],
v1 = [
  {
    "kind": "Variable",
    "name": "input",
    "variableName": "input"
  }
];
return {
  "fragment": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Fragment",
    "metadata": null,
    "name": "TimesheetRosterUpdateCustomViewsMutation",
    "selections": [
      {
        "alias": null,
        "args": (v1/*: any*/),
        "concreteType": "UpdateCustomViewsPayload",
        "kind": "LinkedField",
        "name": "updateCustomViews",
        "plural": false,
        "selections": [
          {
            "alias": null,
            "args": null,
            "concreteType": "CustomViews",
            "kind": "LinkedField",
            "name": "customViews",
            "plural": false,
            "selections": [
              {
                "args": null,
                "kind": "FragmentSpread",
                "name": "TimesheetRosterCustomViewsFragment"
              }
            ],
            "storageKey": null
          }
        ],
        "storageKey": null
      }
    ],
    "type": "Mutation",
    "abstractKey": null
  },
  "kind": "Request",
  "operation": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Operation",
    "name": "TimesheetRosterUpdateCustomViewsMutation",
    "selections": [
      {
        "alias": null,
        "args": (v1/*: any*/),
        "concreteType": "UpdateCustomViewsPayload",
        "kind": "LinkedField",
        "name": "updateCustomViews",
        "plural": false,
        "selections": [
          {
            "alias": null,
            "args": null,
            "concreteType": "CustomViews",
            "kind": "LinkedField",
            "name": "customViews",
            "plural": false,
            "selections": [
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "id",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "value",
                "storageKey": null
              }
            ],
            "storageKey": null
          }
        ],
        "storageKey": null
      }
    ]
  },
  "params": {
    "cacheID": "b77749cff1a85b78d5ac673dd29a3b1b",
    "id": null,
    "metadata": {},
    "name": "TimesheetRosterUpdateCustomViewsMutation",
    "operationKind": "mutation",
    "text": "mutation TimesheetRosterUpdateCustomViewsMutation(\n  $input: UpdateCustomViewsInput!\n) {\n  updateCustomViews(input: $input) {\n    customViews {\n      ...TimesheetRosterCustomViewsFragment\n      id\n    }\n  }\n}\n\nfragment TimesheetRosterCustomViewsFragment on CustomViews {\n  id\n  value\n}\n"
  }
};
})();

(node as any).hash = "d6ab88d7f8c09d766e62d0172e67aa3b";

export default node;
