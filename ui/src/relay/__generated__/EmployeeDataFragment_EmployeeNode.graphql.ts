/**
 * @generated SignedSource<<e5d05b6727aaf83a4aa4f9f2caea3f7d>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ReaderFragment } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type EmployeeDataFragment_EmployeeNode$data = {
  readonly active: boolean;
  readonly dateOfHire: any | null | undefined;
  readonly dateOfTermination: any | null | undefined;
  readonly externalEmployeeId: string | null | undefined;
  readonly firstName: string | null | undefined;
  readonly id: string;
  readonly lastName: string;
  readonly middleName: string | null | undefined;
  readonly suffix: string | null | undefined;
  readonly " $fragmentType": "EmployeeDataFragment_EmployeeNode";
};
export type EmployeeDataFragment_EmployeeNode$key = {
  readonly " $data"?: EmployeeDataFragment_EmployeeNode$data;
  readonly " $fragmentSpreads": FragmentRefs<"EmployeeDataFragment_EmployeeNode">;
};

const node: ReaderFragment = {
  "argumentDefinitions": [],
  "kind": "Fragment",
  "metadata": null,
  "name": "EmployeeDataFragment_EmployeeNode",
  "selections": [
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "id",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "externalEmployeeId",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "firstName",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "middleName",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "lastName",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "suffix",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "dateOfHire",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "dateOfTermination",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "active",
      "storageKey": null
    }
  ],
  "type": "Employee",
  "abstractKey": null
};

(node as any).hash = "100b28bbc6356a36c584ea0b8444568e";

export default node;
