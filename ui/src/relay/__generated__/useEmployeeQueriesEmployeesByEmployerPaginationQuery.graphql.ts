/**
 * @generated SignedSource<<b83eb862484c247cf7edb35a733bc315>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ConcreteRequest } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type useEmployeeQueriesEmployeesByEmployerPaginationQuery$variables = {
  after?: string | null | undefined;
  employerGuid: any;
  first?: number | null | undefined;
};
export type useEmployeeQueriesEmployeesByEmployerPaginationQuery$data = {
  readonly " $fragmentSpreads": FragmentRefs<"useEmployeeQueries_employeesPagination">;
};
export type useEmployeeQueriesEmployeesByEmployerPaginationQuery = {
  response: useEmployeeQueriesEmployeesByEmployerPaginationQuery$data;
  variables: useEmployeeQueriesEmployeesByEmployerPaginationQuery$variables;
};

const node: ConcreteRequest = (function(){
var v0 = [
  {
    "defaultValue": null,
    "kind": "LocalArgument",
    "name": "after"
  },
  {
    "defaultValue": null,
    "kind": "LocalArgument",
    "name": "employerGuid"
  },
  {
    "defaultValue": null,
    "kind": "LocalArgument",
    "name": "first"
  }
],
v1 = [
  {
    "kind": "Variable",
    "name": "after",
    "variableName": "after"
  },
  {
    "kind": "Variable",
    "name": "employerGuid",
    "variableName": "employerGuid"
  },
  {
    "kind": "Variable",
    "name": "first",
    "variableName": "first"
  }
];
return {
  "fragment": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Fragment",
    "metadata": null,
    "name": "useEmployeeQueriesEmployeesByEmployerPaginationQuery",
    "selections": [
      {
        "args": null,
        "kind": "FragmentSpread",
        "name": "useEmployeeQueries_employeesPagination"
      }
    ],
    "type": "Query",
    "abstractKey": null
  },
  "kind": "Request",
  "operation": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Operation",
    "name": "useEmployeeQueriesEmployeesByEmployerPaginationQuery",
    "selections": [
      {
        "alias": null,
        "args": (v1/*: any*/),
        "concreteType": "EmployeeConnection",
        "kind": "LinkedField",
        "name": "employeesByEmployerGuidAsync",
        "plural": false,
        "selections": [
          {
            "alias": null,
            "args": null,
            "concreteType": "EmployeeEdge",
            "kind": "LinkedField",
            "name": "edges",
            "plural": true,
            "selections": [
              {
                "alias": null,
                "args": null,
                "concreteType": "Employee",
                "kind": "LinkedField",
                "name": "node",
                "plural": false,
                "selections": [
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "id",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "externalEmployeeId",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "firstName",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "middleName",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "lastName",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "suffix",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "dateOfHire",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "dateOfTermination",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "active",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "__typename",
                    "storageKey": null
                  }
                ],
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "cursor",
                "storageKey": null
              }
            ],
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "concreteType": "PageInfo",
            "kind": "LinkedField",
            "name": "pageInfo",
            "plural": false,
            "selections": [
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "hasNextPage",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "endCursor",
                "storageKey": null
              }
            ],
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "totalCount",
            "storageKey": null
          }
        ],
        "storageKey": null
      },
      {
        "alias": null,
        "args": (v1/*: any*/),
        "filters": [
          "employerGuid"
        ],
        "handle": "connection",
        "key": "useEmployeeQueries_employeesByEmployerGuidAsync",
        "kind": "LinkedHandle",
        "name": "employeesByEmployerGuidAsync"
      }
    ]
  },
  "params": {
    "cacheID": "1d8fdbaab06b302f104fbc13d3a7f14f",
    "id": null,
    "metadata": {},
    "name": "useEmployeeQueriesEmployeesByEmployerPaginationQuery",
    "operationKind": "query",
    "text": "query useEmployeeQueriesEmployeesByEmployerPaginationQuery(\n  $after: String\n  $employerGuid: UUID!\n  $first: Int\n) {\n  ...useEmployeeQueries_employeesPagination\n}\n\nfragment useEmployeeQueries_employee on Employee {\n  id\n  externalEmployeeId\n  firstName\n  middleName\n  lastName\n  suffix\n  dateOfHire\n  dateOfTermination\n  active\n}\n\nfragment useEmployeeQueries_employeesPagination on Query {\n  employeesByEmployerGuidAsync(employerGuid: $employerGuid, first: $first, after: $after) {\n    edges {\n      node {\n        ...useEmployeeQueries_employee\n        id\n        __typename\n      }\n      cursor\n    }\n    pageInfo {\n      hasNextPage\n      endCursor\n    }\n    totalCount\n  }\n}\n"
  }
};
})();

(node as any).hash = "f1d8145e2023fbcbf035f3ffcd1b1fe9";

export default node;
