/**
 * @generated SignedSource<<613b753e65f1f37c8de4f46bef154644>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ReaderFragment } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type UploadTimeSheetFragments_timeSheetConsolidated$data = {
  readonly employerGuid: any;
  readonly id: string;
  readonly numericId: number;
  readonly payStubs: {
    readonly edges: ReadonlyArray<{
      readonly node: {
        readonly employeeId: string;
        readonly id: string;
        readonly name: string | null | undefined;
      };
    }> | null | undefined;
  } | null | undefined;
  readonly " $fragmentType": "UploadTimeSheetFragments_timeSheetConsolidated";
};
export type UploadTimeSheetFragments_timeSheetConsolidated$key = {
  readonly " $data"?: UploadTimeSheetFragments_timeSheetConsolidated$data;
  readonly " $fragmentSpreads": FragmentRefs<"UploadTimeSheetFragments_timeSheetConsolidated">;
};

const node: ReaderFragment = (function(){
var v0 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "id",
  "storageKey": null
};
return {
  "argumentDefinitions": [
    {
      "defaultValue": null,
      "kind": "LocalArgument",
      "name": "after"
    },
    {
      "defaultValue": 500,
      "kind": "LocalArgument",
      "name": "first"
    }
  ],
  "kind": "Fragment",
  "metadata": {
    "connection": [
      {
        "count": "first",
        "cursor": "after",
        "direction": "forward",
        "path": [
          "payStubs"
        ]
      }
    ]
  },
  "name": "UploadTimeSheetFragments_timeSheetConsolidated",
  "selections": [
    (v0/*: any*/),
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "numericId",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "employerGuid",
      "storageKey": null
    },
    {
      "alias": "payStubs",
      "args": null,
      "concreteType": "PayStubConnection",
      "kind": "LinkedField",
      "name": "__UploadTimeSheetFragments_timeSheetConsolidated_payStubs_connection",
      "plural": false,
      "selections": [
        {
          "alias": null,
          "args": null,
          "concreteType": "PayStubEdge",
          "kind": "LinkedField",
          "name": "edges",
          "plural": true,
          "selections": [
            {
              "alias": null,
              "args": null,
              "concreteType": "PayStub",
              "kind": "LinkedField",
              "name": "node",
              "plural": false,
              "selections": [
                (v0/*: any*/),
                {
                  "alias": null,
                  "args": null,
                  "kind": "ScalarField",
                  "name": "employeeId",
                  "storageKey": null
                },
                {
                  "alias": null,
                  "args": null,
                  "kind": "ScalarField",
                  "name": "name",
                  "storageKey": null
                },
                {
                  "alias": null,
                  "args": null,
                  "kind": "ScalarField",
                  "name": "__typename",
                  "storageKey": null
                }
              ],
              "storageKey": null
            },
            {
              "alias": null,
              "args": null,
              "kind": "ScalarField",
              "name": "cursor",
              "storageKey": null
            }
          ],
          "storageKey": null
        },
        {
          "alias": null,
          "args": null,
          "concreteType": "PageInfo",
          "kind": "LinkedField",
          "name": "pageInfo",
          "plural": false,
          "selections": [
            {
              "alias": null,
              "args": null,
              "kind": "ScalarField",
              "name": "endCursor",
              "storageKey": null
            },
            {
              "alias": null,
              "args": null,
              "kind": "ScalarField",
              "name": "hasNextPage",
              "storageKey": null
            }
          ],
          "storageKey": null
        }
      ],
      "storageKey": null
    }
  ],
  "type": "TimeSheet",
  "abstractKey": null
};
})();

(node as any).hash = "0cf08f76f37a7a624c8f5b0484d07792";

export default node;
