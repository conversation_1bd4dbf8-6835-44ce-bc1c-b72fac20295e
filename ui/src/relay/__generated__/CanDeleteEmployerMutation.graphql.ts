/**
 * @generated SignedSource<<1c7997f6ac040c3024be6c41bf1b93b2>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ConcreteRequest } from 'relay-runtime';
export type DeleteEmployerInput = {
  employerId: string;
};
export type CanDeleteEmployerMutation$variables = {
  input: DeleteEmployerInput;
};
export type CanDeleteEmployerMutation$data = {
  readonly deleteEmployer: {
    readonly operationResult: {
      readonly message: string;
      readonly success: boolean;
    } | null | undefined;
  };
};
export type CanDeleteEmployerMutation = {
  response: CanDeleteEmployerMutation$data;
  variables: CanDeleteEmployerMutation$variables;
};

const node: ConcreteRequest = (function(){
var v0 = [
  {
    "defaultValue": null,
    "kind": "LocalArgument",
    "name": "input"
  }
],
v1 = [
  {
    "alias": null,
    "args": [
      {
        "kind": "Variable",
        "name": "input",
        "variableName": "input"
      }
    ],
    "concreteType": "DeleteEmployerPayload",
    "kind": "LinkedField",
    "name": "deleteEmployer",
    "plural": false,
    "selections": [
      {
        "alias": null,
        "args": null,
        "concreteType": "OperationResult",
        "kind": "LinkedField",
        "name": "operationResult",
        "plural": false,
        "selections": [
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "success",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "message",
            "storageKey": null
          }
        ],
        "storageKey": null
      }
    ],
    "storageKey": null
  }
];
return {
  "fragment": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Fragment",
    "metadata": null,
    "name": "CanDeleteEmployerMutation",
    "selections": (v1/*: any*/),
    "type": "Mutation",
    "abstractKey": null
  },
  "kind": "Request",
  "operation": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Operation",
    "name": "CanDeleteEmployerMutation",
    "selections": (v1/*: any*/)
  },
  "params": {
    "cacheID": "2e33ba9533f7c2a4d384632330a9d25a",
    "id": null,
    "metadata": {},
    "name": "CanDeleteEmployerMutation",
    "operationKind": "mutation",
    "text": "mutation CanDeleteEmployerMutation(\n  $input: DeleteEmployerInput!\n) {\n  deleteEmployer(input: $input) {\n    operationResult {\n      success\n      message\n    }\n  }\n}\n"
  }
};
})();

(node as any).hash = "fd8c9d5cee3074205cec9a53948e7685";

export default node;
