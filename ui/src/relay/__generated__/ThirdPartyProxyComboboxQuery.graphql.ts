/**
 * @generated SignedSource<<cbcff3119137be05b1b5ecd919b36f80>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ConcreteRequest } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type ThirdPartyProxyComboboxQuery$variables = {
  chapterId: number;
};
export type ThirdPartyProxyComboboxQuery$data = {
  readonly " $fragmentSpreads": FragmentRefs<"ThirdPartyProxyComboboxFragment">;
};
export type ThirdPartyProxyComboboxQuery = {
  response: ThirdPartyProxyComboboxQuery$data;
  variables: ThirdPartyProxyComboboxQuery$variables;
};

const node: ConcreteRequest = (function(){
var v0 = [
  {
    "defaultValue": null,
    "kind": "LocalArgument",
    "name": "chapterId"
  }
],
v1 = {
  "kind": "Variable",
  "name": "chapterId",
  "variableName": "chapterId"
},
v2 = [
  (v1/*: any*/),
  {
    "kind": "Literal",
    "name": "first",
    "value": 1000
  }
];
return {
  "fragment": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Fragment",
    "metadata": null,
    "name": "ThirdPartyProxyComboboxQuery",
    "selections": [
      {
        "args": [
          (v1/*: any*/)
        ],
        "kind": "FragmentSpread",
        "name": "ThirdPartyProxyComboboxFragment"
      }
    ],
    "type": "Query",
    "abstractKey": null
  },
  "kind": "Request",
  "operation": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Operation",
    "name": "ThirdPartyProxyComboboxQuery",
    "selections": [
      {
        "alias": null,
        "args": (v2/*: any*/),
        "concreteType": "ThirdPartyInfoDtoConnection",
        "kind": "LinkedField",
        "name": "thirdParties",
        "plural": false,
        "selections": [
          {
            "alias": null,
            "args": null,
            "concreteType": "ThirdPartyInfoDtoEdge",
            "kind": "LinkedField",
            "name": "edges",
            "plural": true,
            "selections": [
              {
                "alias": null,
                "args": null,
                "concreteType": "ThirdPartyInfoDto",
                "kind": "LinkedField",
                "name": "node",
                "plural": false,
                "selections": [
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "label",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "value",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "guid",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "id",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "__typename",
                    "storageKey": null
                  }
                ],
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "cursor",
                "storageKey": null
              }
            ],
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "concreteType": "PageInfo",
            "kind": "LinkedField",
            "name": "pageInfo",
            "plural": false,
            "selections": [
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "endCursor",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "hasNextPage",
                "storageKey": null
              }
            ],
            "storageKey": null
          }
        ],
        "storageKey": null
      },
      {
        "alias": null,
        "args": (v2/*: any*/),
        "filters": [
          "chapterId"
        ],
        "handle": "connection",
        "key": "ThirdPartyProxyComboboxFragment_thirdParties",
        "kind": "LinkedHandle",
        "name": "thirdParties"
      },
      {
        "kind": "ClientExtension",
        "selections": [
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "__id",
            "storageKey": null
          }
        ]
      }
    ]
  },
  "params": {
    "cacheID": "c3bbf2506900b5147b90b0028f99a635",
    "id": null,
    "metadata": {},
    "name": "ThirdPartyProxyComboboxQuery",
    "operationKind": "query",
    "text": "query ThirdPartyProxyComboboxQuery(\n  $chapterId: Int!\n) {\n  ...ThirdPartyProxyComboboxFragment_2anDGG\n}\n\nfragment ThirdPartyProxyComboboxFragment_2anDGG on Query {\n  thirdParties(chapterId: $chapterId, first: 1000) {\n    edges {\n      node {\n        label\n        value\n        guid\n        id\n        __typename\n      }\n      cursor\n    }\n    pageInfo {\n      endCursor\n      hasNextPage\n    }\n  }\n}\n"
  }
};
})();

(node as any).hash = "8fdd93f22205f3a26bf62735e713cfa0";

export default node;
