/**
 * @generated SignedSource<<96b5be520c4caa77c67f6532c3516409>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ConcreteRequest } from 'relay-runtime';
export type SignatoryAgreementInput = {
  employerGuid?: any | null | undefined;
  includeInactiveAgreements?: boolean | null | undefined;
};
export type useAgreementsByEmployerQuery$variables = {
  input: SignatoryAgreementInput;
};
export type useAgreementsByEmployerQuery$data = {
  readonly signatoryAgreements: {
    readonly nodes: ReadonlyArray<{
      readonly id: string;
      readonly name: string;
    }> | null | undefined;
  } | null | undefined;
};
export type useAgreementsByEmployerQuery = {
  response: useAgreementsByEmployerQuery$data;
  variables: useAgreementsByEmployerQuery$variables;
};

const node: ConcreteRequest = (function(){
var v0 = [
  {
    "defaultValue": null,
    "kind": "LocalArgument",
    "name": "input"
  }
],
v1 = [
  {
    "alias": null,
    "args": [
      {
        "kind": "Variable",
        "name": "input",
        "variableName": "input"
      }
    ],
    "concreteType": "AgreementConnection",
    "kind": "LinkedField",
    "name": "signatoryAgreements",
    "plural": false,
    "selections": [
      {
        "alias": null,
        "args": null,
        "concreteType": "Agreement",
        "kind": "LinkedField",
        "name": "nodes",
        "plural": true,
        "selections": [
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "id",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "name",
            "storageKey": null
          }
        ],
        "storageKey": null
      }
    ],
    "storageKey": null
  }
];
return {
  "fragment": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Fragment",
    "metadata": null,
    "name": "useAgreementsByEmployerQuery",
    "selections": (v1/*: any*/),
    "type": "Query",
    "abstractKey": null
  },
  "kind": "Request",
  "operation": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Operation",
    "name": "useAgreementsByEmployerQuery",
    "selections": (v1/*: any*/)
  },
  "params": {
    "cacheID": "9254a97e832ee732b618c98392cc1d26",
    "id": null,
    "metadata": {},
    "name": "useAgreementsByEmployerQuery",
    "operationKind": "query",
    "text": "query useAgreementsByEmployerQuery(\n  $input: SignatoryAgreementInput!\n) {\n  signatoryAgreements(input: $input) {\n    nodes {\n      id\n      name\n    }\n  }\n}\n"
  }
};
})();

(node as any).hash = "5a834120654e017d6b56654fc70d627b";

export default node;
