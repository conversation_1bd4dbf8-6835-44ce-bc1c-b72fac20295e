/**
 * @generated SignedSource<<96cb5316cce3c5f1bdcbc6fd480471c8>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ReaderFragment } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type EmployeeDefaultsFragment_employeeDefaults$data = {
  readonly defaultAgreementId: number | null | undefined;
  readonly defaultClassificationId: number | null | undefined;
  readonly defaultHourlyRate: any | null | undefined;
  readonly employeeId: string | null | undefined;
  readonly " $fragmentType": "EmployeeDefaultsFragment_employeeDefaults";
};
export type EmployeeDefaultsFragment_employeeDefaults$key = {
  readonly " $data"?: EmployeeDefaultsFragment_employeeDefaults$data;
  readonly " $fragmentSpreads": FragmentRefs<"EmployeeDefaultsFragment_employeeDefaults">;
};

const node: ReaderFragment = {
  "argumentDefinitions": [],
  "kind": "Fragment",
  "metadata": null,
  "name": "EmployeeDefaultsFragment_employeeDefaults",
  "selections": [
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "employeeId",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "defaultAgreementId",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "defaultClassificationId",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "defaultHourlyRate",
      "storageKey": null
    }
  ],
  "type": "EmployeeDefaultSettings",
  "abstractKey": null
};

(node as any).hash = "24e711755aeb1b8477452292e0edfc7c";

export default node;
