/**
 * @generated SignedSource<<500c41031a759a0e2f0f9d28613c988c>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ReaderFragment } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type EmployeeSelector_employee$data = {
  readonly active: boolean;
  readonly firstName: string | null | undefined;
  readonly id: string;
  readonly lastName: string;
  readonly middleName: string | null | undefined;
  readonly " $fragmentType": "EmployeeSelector_employee";
};
export type EmployeeSelector_employee$key = {
  readonly " $data"?: EmployeeSelector_employee$data;
  readonly " $fragmentSpreads": FragmentRefs<"EmployeeSelector_employee">;
};

const node: ReaderFragment = {
  "argumentDefinitions": [],
  "kind": "Fragment",
  "metadata": null,
  "name": "Employee<PERSON>elector_employee",
  "selections": [
    {
      "alias": null,
      "args": null,
      "kind": "Scala<PERSON><PERSON>ield",
      "name": "id",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "firstName",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "lastName",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "middleName",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "active",
      "storageKey": null
    }
  ],
  "type": "Employee",
  "abstractKey": null
};

(node as any).hash = "3c784a8aefa6fdd65453e67eac8450fe";

export default node;
