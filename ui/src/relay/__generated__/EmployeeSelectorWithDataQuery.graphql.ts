/**
 * @generated SignedSource<<e72ea90fb68f29818e4dbd465ffef40d>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ConcreteRequest } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type EmployeeSelectorWithDataQuery$variables = {
  employerGuid: any;
};
export type EmployeeSelectorWithDataQuery$data = {
  readonly " $fragmentSpreads": FragmentRefs<"EmployeeDataFragment">;
};
export type EmployeeSelectorWithDataQuery = {
  response: EmployeeSelectorWithDataQuery$data;
  variables: EmployeeSelectorWithDataQuery$variables;
};

const node: ConcreteRequest = (function(){
var v0 = [
  {
    "defaultValue": null,
    "kind": "LocalArgument",
    "name": "employerGuid"
  }
],
v1 = {
  "kind": "Variable",
  "name": "employerGuid",
  "variableName": "employerGuid"
},
v2 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "id",
  "storageKey": null
},
v3 = [
  (v2/*: any*/),
  {
    "alias": null,
    "args": null,
    "kind": "ScalarField",
    "name": "name",
    "storageKey": null
  }
];
return {
  "fragment": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Fragment",
    "metadata": null,
    "name": "EmployeeSelectorWithDataQuery",
    "selections": [
      {
        "args": [
          (v1/*: any*/)
        ],
        "kind": "FragmentSpread",
        "name": "EmployeeDataFragment"
      }
    ],
    "type": "Query",
    "abstractKey": null
  },
  "kind": "Request",
  "operation": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Operation",
    "name": "EmployeeSelectorWithDataQuery",
    "selections": [
      {
        "alias": null,
        "args": [
          (v1/*: any*/),
          {
            "kind": "Literal",
            "name": "first",
            "value": 50
          }
        ],
        "concreteType": "EmployeeConnection",
        "kind": "LinkedField",
        "name": "employeesByEmployerGuidAsync",
        "plural": false,
        "selections": [
          {
            "alias": null,
            "args": null,
            "concreteType": "EmployeeEdge",
            "kind": "LinkedField",
            "name": "edges",
            "plural": true,
            "selections": [
              {
                "alias": null,
                "args": null,
                "concreteType": "Employee",
                "kind": "LinkedField",
                "name": "node",
                "plural": false,
                "selections": [
                  (v2/*: any*/),
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "firstName",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "lastName",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "active",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "externalEmployeeId",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "ssn",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "dateOfHire",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "dateOfTermination",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "middleName",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "suffix",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "concreteType": "Agreement",
                    "kind": "LinkedField",
                    "name": "agreements",
                    "plural": true,
                    "selections": (v3/*: any*/),
                    "storageKey": null
                  }
                ],
                "storageKey": null
              }
            ],
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "totalCount",
            "storageKey": null
          }
        ],
        "storageKey": null
      },
      {
        "alias": null,
        "args": [
          {
            "fields": [
              (v1/*: any*/),
              {
                "kind": "Literal",
                "name": "includeInactiveAgreements",
                "value": false
              }
            ],
            "kind": "ObjectValue",
            "name": "input"
          }
        ],
        "concreteType": "AgreementConnection",
        "kind": "LinkedField",
        "name": "signatoryAgreements",
        "plural": false,
        "selections": [
          {
            "alias": null,
            "args": null,
            "concreteType": "Agreement",
            "kind": "LinkedField",
            "name": "nodes",
            "plural": true,
            "selections": (v3/*: any*/),
            "storageKey": null
          }
        ],
        "storageKey": null
      }
    ]
  },
  "params": {
    "cacheID": "2efdc35d4605b52f652f4ea9d0733e01",
    "id": null,
    "metadata": {},
    "name": "EmployeeSelectorWithDataQuery",
    "operationKind": "query",
    "text": "query EmployeeSelectorWithDataQuery(\n  $employerGuid: UUID!\n) {\n  ...EmployeeDataFragment_DcV1b\n}\n\nfragment EmployeeDataFragment_DcV1b on Query {\n  employeesByEmployerGuidAsync(employerGuid: $employerGuid, first: 50) {\n    edges {\n      node {\n        id\n        firstName\n        lastName\n        active\n        externalEmployeeId\n        ssn\n        dateOfHire\n        dateOfTermination\n        middleName\n        suffix\n        agreements {\n          id\n          name\n        }\n      }\n    }\n    totalCount\n  }\n  signatoryAgreements(input: {employerGuid: $employerGuid, includeInactiveAgreements: false}) {\n    nodes {\n      id\n      name\n    }\n  }\n}\n"
  }
};
})();

(node as any).hash = "d7dd27b94e08fed92039f64d8ecabf43";

export default node;
