/**
 * @generated SignedSource<<65d1bfbbcf2a726a4beaa1981f86ecbc>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ConcreteRequest } from 'relay-runtime';
export type RelayEnvironmentLayoutQuery$variables = Record<PropertyKey, never>;
export type RelayEnvironmentLayoutQuery$data = {
  readonly fieldDefinitions: ReadonlyArray<{
    readonly key: string;
    readonly value: {
      readonly displayFormat: string | null | undefined;
      readonly displayName: string;
      readonly fieldName: string;
    };
  }>;
  readonly userInfo: {
    readonly chapterId: number;
    readonly email: string;
    readonly firstName: string;
    readonly lastName: string;
    readonly orgGUID: any;
    readonly orgName: string | null | undefined;
    readonly phoneNumber: string | null | undefined;
    readonly phoneNumberExtension: string | null | undefined;
    readonly phoneTypeId: number | null | undefined;
    readonly preferredMfamethod: string | null | undefined;
    readonly roleGroups: ReadonlyArray<string>;
    readonly showMyReportsMenu: boolean;
    readonly showTimesheetsMenu: boolean;
    readonly username: string;
  };
};
export type RelayEnvironmentLayoutQuery = {
  response: RelayEnvironmentLayoutQuery$data;
  variables: RelayEnvironmentLayoutQuery$variables;
};

const node: ConcreteRequest = (function(){
var v0 = [
  {
    "alias": null,
    "args": null,
    "concreteType": "KeyValuePairOfStringAndMetadata",
    "kind": "LinkedField",
    "name": "fieldDefinitions",
    "plural": true,
    "selections": [
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "key",
        "storageKey": null
      },
      {
        "alias": null,
        "args": null,
        "concreteType": "Metadata",
        "kind": "LinkedField",
        "name": "value",
        "plural": false,
        "selections": [
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "fieldName",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "displayName",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "displayFormat",
            "storageKey": null
          }
        ],
        "storageKey": null
      }
    ],
    "storageKey": null
  },
  {
    "alias": null,
    "args": null,
    "concreteType": "UserInfoOutput",
    "kind": "LinkedField",
    "name": "userInfo",
    "plural": false,
    "selections": [
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "username",
        "storageKey": null
      },
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "roleGroups",
        "storageKey": null
      },
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "showMyReportsMenu",
        "storageKey": null
      },
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "showTimesheetsMenu",
        "storageKey": null
      },
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "firstName",
        "storageKey": null
      },
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "lastName",
        "storageKey": null
      },
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "email",
        "storageKey": null
      },
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "orgGUID",
        "storageKey": null
      },
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "orgName",
        "storageKey": null
      },
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "chapterId",
        "storageKey": null
      },
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "phoneNumber",
        "storageKey": null
      },
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "phoneTypeId",
        "storageKey": null
      },
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "preferredMfamethod",
        "storageKey": null
      },
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "phoneNumberExtension",
        "storageKey": null
      }
    ],
    "storageKey": null
  }
];
return {
  "fragment": {
    "argumentDefinitions": [],
    "kind": "Fragment",
    "metadata": null,
    "name": "RelayEnvironmentLayoutQuery",
    "selections": (v0/*: any*/),
    "type": "Query",
    "abstractKey": null
  },
  "kind": "Request",
  "operation": {
    "argumentDefinitions": [],
    "kind": "Operation",
    "name": "RelayEnvironmentLayoutQuery",
    "selections": (v0/*: any*/)
  },
  "params": {
    "cacheID": "3476ac527dcc8c0883dd3bb40274c5bf",
    "id": null,
    "metadata": {},
    "name": "RelayEnvironmentLayoutQuery",
    "operationKind": "query",
    "text": "query RelayEnvironmentLayoutQuery {\n  fieldDefinitions {\n    key\n    value {\n      fieldName\n      displayName\n      displayFormat\n    }\n  }\n  userInfo {\n    username\n    roleGroups\n    showMyReportsMenu\n    showTimesheetsMenu\n    firstName\n    lastName\n    email\n    orgGUID\n    orgName\n    chapterId\n    phoneNumber\n    phoneTypeId\n    preferredMfamethod\n    phoneNumberExtension\n  }\n}\n"
  }
};
})();

(node as any).hash = "abf3d171db2ae1c8007d827a0288a86f";

export default node;
