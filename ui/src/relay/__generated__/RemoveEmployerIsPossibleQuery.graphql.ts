/**
 * @generated SignedSource<<1be886c4b9d00493bbd72669536544b2>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ConcreteRequest } from 'relay-runtime';
export type RemoveEmployerIsPossibleQuery$variables = {
  chapterId: string;
  employerId: string;
};
export type RemoveEmployerIsPossibleQuery$data = {
  readonly canDeleteEmployer: {
    readonly canDelete: boolean;
    readonly canDeleteRelationship: boolean;
    readonly message: string | null | undefined;
  };
};
export type RemoveEmployerIsPossibleQuery = {
  response: RemoveEmployerIsPossibleQuery$data;
  variables: RemoveEmployerIsPossibleQuery$variables;
};

const node: ConcreteRequest = (function(){
var v0 = {
  "defaultValue": null,
  "kind": "LocalArgument",
  "name": "chapterId"
},
v1 = {
  "defaultValue": null,
  "kind": "LocalArgument",
  "name": "employerId"
},
v2 = [
  {
    "alias": null,
    "args": [
      {
        "kind": "Variable",
        "name": "chapterId",
        "variableName": "chapterId"
      },
      {
        "kind": "Variable",
        "name": "employerId",
        "variableName": "employerId"
      }
    ],
    "concreteType": "CanDeleteEmployerResponse",
    "kind": "LinkedField",
    "name": "canDeleteEmployer",
    "plural": false,
    "selections": [
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "canDelete",
        "storageKey": null
      },
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "canDeleteRelationship",
        "storageKey": null
      },
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "message",
        "storageKey": null
      }
    ],
    "storageKey": null
  }
];
return {
  "fragment": {
    "argumentDefinitions": [
      (v0/*: any*/),
      (v1/*: any*/)
    ],
    "kind": "Fragment",
    "metadata": null,
    "name": "RemoveEmployerIsPossibleQuery",
    "selections": (v2/*: any*/),
    "type": "Query",
    "abstractKey": null
  },
  "kind": "Request",
  "operation": {
    "argumentDefinitions": [
      (v1/*: any*/),
      (v0/*: any*/)
    ],
    "kind": "Operation",
    "name": "RemoveEmployerIsPossibleQuery",
    "selections": (v2/*: any*/)
  },
  "params": {
    "cacheID": "944df0e8104cf0fe2d386bf86fb3f546",
    "id": null,
    "metadata": {},
    "name": "RemoveEmployerIsPossibleQuery",
    "operationKind": "query",
    "text": "query RemoveEmployerIsPossibleQuery(\n  $employerId: ID!\n  $chapterId: ID!\n) {\n  canDeleteEmployer(employerId: $employerId, chapterId: $chapterId) {\n    canDelete\n    canDeleteRelationship\n    message\n  }\n}\n"
  }
};
})();

(node as any).hash = "d46ae10f403021ae48b8792446ab2386";

export default node;
