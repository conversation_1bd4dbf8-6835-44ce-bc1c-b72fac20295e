/**
 * @generated SignedSource<<61a9939004644f6ba2ca4a116f51f74e>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ReaderFragment } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type fragments_SubClassificationBasicInfo$data = {
  readonly id: string;
  readonly name: string;
  readonly " $fragmentType": "fragments_SubClassificationBasicInfo";
};
export type fragments_SubClassificationBasicInfo$key = {
  readonly " $data"?: fragments_SubClassificationBasicInfo$data;
  readonly " $fragmentSpreads": FragmentRefs<"fragments_SubClassificationBasicInfo">;
};

const node: ReaderFragment = {
  "argumentDefinitions": [],
  "kind": "Fragment",
  "metadata": null,
  "name": "fragments_SubClassificationBasicInfo",
  "selections": [
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "id",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "name",
      "storageKey": null
    }
  ],
  "type": "SubClassification",
  "abstractKey": null
};

(node as any).hash = "f7bda7366cf82e2b532ee002352d7697";

export default node;
