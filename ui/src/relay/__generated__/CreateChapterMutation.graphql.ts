/**
 * @generated SignedSource<<4502a1790f9e87a710f4a54d39c32397>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ConcreteRequest } from 'relay-runtime';
export type AddChapterInput = {
  limited: boolean;
  name: string;
  username: string;
};
export type CreateChapterMutation$variables = {
  connections: ReadonlyArray<string>;
  input: AddChapterInput;
};
export type CreateChapterMutation$data = {
  readonly addChapter: {
    readonly chaptersInfoDtoEdge: {
      readonly cursor: string;
      readonly node: {
        readonly guid: any | null | undefined;
        readonly label: string;
        readonly value: number;
      } | null | undefined;
    } | null | undefined;
  };
};
export type CreateChapterMutation = {
  response: CreateChapterMutation$data;
  variables: CreateChapterMutation$variables;
};

const node: ConcreteRequest = (function(){
var v0 = {
  "defaultValue": null,
  "kind": "LocalArgument",
  "name": "connections"
},
v1 = {
  "defaultValue": null,
  "kind": "LocalArgument",
  "name": "input"
},
v2 = [
  {
    "kind": "Variable",
    "name": "chapterInput",
    "variableName": "input"
  }
],
v3 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "cursor",
  "storageKey": null
},
v4 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "value",
  "storageKey": null
},
v5 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "label",
  "storageKey": null
},
v6 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "guid",
  "storageKey": null
};
return {
  "fragment": {
    "argumentDefinitions": [
      (v0/*: any*/),
      (v1/*: any*/)
    ],
    "kind": "Fragment",
    "metadata": null,
    "name": "CreateChapterMutation",
    "selections": [
      {
        "alias": null,
        "args": (v2/*: any*/),
        "concreteType": "AddChapterPayload",
        "kind": "LinkedField",
        "name": "addChapter",
        "plural": false,
        "selections": [
          {
            "alias": null,
            "args": null,
            "concreteType": "EdgeOfChaptersInfoDto",
            "kind": "LinkedField",
            "name": "chaptersInfoDtoEdge",
            "plural": false,
            "selections": [
              (v3/*: any*/),
              {
                "alias": null,
                "args": null,
                "concreteType": "ChaptersInfoDto",
                "kind": "LinkedField",
                "name": "node",
                "plural": false,
                "selections": [
                  (v4/*: any*/),
                  (v5/*: any*/),
                  (v6/*: any*/)
                ],
                "storageKey": null
              }
            ],
            "storageKey": null
          }
        ],
        "storageKey": null
      }
    ],
    "type": "Mutation",
    "abstractKey": null
  },
  "kind": "Request",
  "operation": {
    "argumentDefinitions": [
      (v1/*: any*/),
      (v0/*: any*/)
    ],
    "kind": "Operation",
    "name": "CreateChapterMutation",
    "selections": [
      {
        "alias": null,
        "args": (v2/*: any*/),
        "concreteType": "AddChapterPayload",
        "kind": "LinkedField",
        "name": "addChapter",
        "plural": false,
        "selections": [
          {
            "alias": null,
            "args": null,
            "concreteType": "EdgeOfChaptersInfoDto",
            "kind": "LinkedField",
            "name": "chaptersInfoDtoEdge",
            "plural": false,
            "selections": [
              (v3/*: any*/),
              {
                "alias": null,
                "args": null,
                "concreteType": "ChaptersInfoDto",
                "kind": "LinkedField",
                "name": "node",
                "plural": false,
                "selections": [
                  (v4/*: any*/),
                  (v5/*: any*/),
                  (v6/*: any*/),
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "id",
                    "storageKey": null
                  }
                ],
                "storageKey": null
              }
            ],
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "filters": null,
            "handle": "appendEdge",
            "key": "",
            "kind": "LinkedHandle",
            "name": "chaptersInfoDtoEdge",
            "handleArgs": [
              {
                "kind": "Variable",
                "name": "connections",
                "variableName": "connections"
              }
            ]
          }
        ],
        "storageKey": null
      }
    ]
  },
  "params": {
    "cacheID": "3595c27a316c49cdde157c4fa9a19ff8",
    "id": null,
    "metadata": {},
    "name": "CreateChapterMutation",
    "operationKind": "mutation",
    "text": "mutation CreateChapterMutation(\n  $input: AddChapterInput!\n) {\n  addChapter(chapterInput: $input) {\n    chaptersInfoDtoEdge {\n      cursor\n      node {\n        value\n        label\n        guid\n        id\n      }\n    }\n  }\n}\n"
  }
};
})();

(node as any).hash = "f642bacb7ab16766e72f028709eb54ea";

export default node;
