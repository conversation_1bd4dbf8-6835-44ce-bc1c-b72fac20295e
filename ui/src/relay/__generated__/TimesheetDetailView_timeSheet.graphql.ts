/**
 * @generated SignedSource<<5a49d693858bf7c7ad88d5b398622c72>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ReaderFragment } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type TimesheetDetailView_timeSheet$data = {
  readonly name: string | null | undefined;
  readonly numericId: number;
  readonly payPeriodEndDate: any | null | undefined;
  readonly status: string;
  readonly " $fragmentSpreads": FragmentRefs<"TimeSheetGrid_timeSheet">;
  readonly " $fragmentType": "TimesheetDetailView_timeSheet";
};
export type TimesheetDetailView_timeSheet$key = {
  readonly " $data"?: TimesheetDetailView_timeSheet$data;
  readonly " $fragmentSpreads": FragmentRefs<"TimesheetDetailView_timeSheet">;
};

const node: ReaderFragment = {
  "argumentDefinitions": [],
  "kind": "Fragment",
  "metadata": null,
  "name": "TimesheetDetailView_timeSheet",
  "selections": [
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "numericId",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "name",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "status",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "payPeriodEndDate",
      "storageKey": null
    },
    {
      "args": null,
      "kind": "FragmentSpread",
      "name": "TimeSheetGrid_timeSheet"
    }
  ],
  "type": "TimeSheet",
  "abstractKey": null
};

(node as any).hash = "3b53830eaf2079417f3206a0c1632253";

export default node;
