/**
 * @generated SignedSource<<0cfb681b090dc9491c5c38326c02371a>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ReaderFragment } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type TimeSheetGrid_timeSheet$data = {
  readonly payPeriodEndDate: any | null | undefined;
  readonly " $fragmentSpreads": FragmentRefs<"PayStubTable_connectionFragment" | "TimesheetToolbar_timeSheet">;
  readonly " $fragmentType": "TimeSheetGrid_timeSheet";
};
export type TimeSheetGrid_timeSheet$key = {
  readonly " $data"?: TimeSheetGrid_timeSheet$data;
  readonly " $fragmentSpreads": FragmentRefs<"TimeSheetGrid_timeSheet">;
};

const node: ReaderFragment = {
  "argumentDefinitions": [],
  "kind": "Fragment",
  "metadata": null,
  "name": "TimeSheetGrid_timeSheet",
  "selections": [
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "payPeriodEndDate",
      "storageKey": null
    },
    {
      "args": null,
      "kind": "FragmentSpread",
      "name": "PayStubTable_connectionFragment"
    },
    {
      "args": null,
      "kind": "FragmentSpread",
      "name": "TimesheetToolbar_timeSheet"
    }
  ],
  "type": "TimeSheet",
  "abstractKey": null
};

(node as any).hash = "03aed0af2509f1d271822208be39ac23";

export default node;
