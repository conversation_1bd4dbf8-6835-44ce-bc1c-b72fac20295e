/**
 * @generated SignedSource<<5f511b8b0b155f4ae906f89c5749af07>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ReaderFragment } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type fragments_SubClassificationDetailedInfo$data = {
  readonly id: string;
  readonly name: string;
  readonly " $fragmentType": "fragments_SubClassificationDetailedInfo";
};
export type fragments_SubClassificationDetailedInfo$key = {
  readonly " $data"?: fragments_SubClassificationDetailedInfo$data;
  readonly " $fragmentSpreads": FragmentRefs<"fragments_SubClassificationDetailedInfo">;
};

const node: ReaderFragment = {
  "argumentDefinitions": [],
  "kind": "Fragment",
  "metadata": null,
  "name": "fragments_SubClassificationDetailedInfo",
  "selections": [
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "id",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "name",
      "storageKey": null
    }
  ],
  "type": "SubClassification",
  "abstractKey": null
};

(node as any).hash = "3f35cb641d0b3e81c32ab653fc644336";

export default node;
