/**
 * @generated SignedSource<<2715e94024ebfa6ca4cd23dc6446cb48>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ReaderFragment } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type fragments_ClassificationBasicInfo$data = {
  readonly id: string;
  readonly name: string;
  readonly " $fragmentType": "fragments_ClassificationBasicInfo";
};
export type fragments_ClassificationBasicInfo$key = {
  readonly " $data"?: fragments_ClassificationBasicInfo$data;
  readonly " $fragmentSpreads": FragmentRefs<"fragments_ClassificationBasicInfo">;
};

const node: ReaderFragment = {
  "argumentDefinitions": [],
  "kind": "Fragment",
  "metadata": null,
  "name": "fragments_ClassificationBasicInfo",
  "selections": [
    {
      "alias": null,
      "args": null,
      "kind": "<PERSON>alar<PERSON><PERSON>",
      "name": "id",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "<PERSON>alar<PERSON>ield",
      "name": "name",
      "storageKey": null
    }
  ],
  "type": "ClassificationName",
  "abstractKey": null
};

(node as any).hash = "eca19bfeeac1672537c06008207efec0";

export default node;
