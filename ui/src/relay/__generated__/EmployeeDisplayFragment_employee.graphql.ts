/**
 * @generated SignedSource<<e36ef13587e5866dab71a146e40e526a>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ReaderFragment } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type EmployeeDisplayFragment_employee$data = {
  readonly active: boolean;
  readonly externalEmployeeId: string | null | undefined;
  readonly firstName: string | null | undefined;
  readonly id: string;
  readonly lastName: string;
  readonly " $fragmentType": "EmployeeDisplayFragment_employee";
};
export type EmployeeDisplayFragment_employee$key = {
  readonly " $data"?: EmployeeDisplayFragment_employee$data;
  readonly " $fragmentSpreads": FragmentRefs<"EmployeeDisplayFragment_employee">;
};

const node: ReaderFragment = {
  "argumentDefinitions": [],
  "kind": "Fragment",
  "metadata": null,
  "name": "EmployeeDisplayFragment_employee",
  "selections": [
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "id",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "firstName",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "lastName",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "active",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "externalEmployeeId",
      "storageKey": null
    }
  ],
  "type": "Employee",
  "abstractKey": null
};

(node as any).hash = "6c052468d637f27d15b83861a3896c74";

export default node;
