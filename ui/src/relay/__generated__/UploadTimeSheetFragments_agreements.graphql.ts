/**
 * @generated SignedSource<<bb305702486834e439f912435f326947>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ReaderFragment } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type UploadTimeSheetFragments_agreements$data = ReadonlyArray<{
  readonly id: string;
  readonly name: string;
  readonly " $fragmentType": "UploadTimeSheetFragments_agreements";
}>;
export type UploadTimeSheetFragments_agreements$key = ReadonlyArray<{
  readonly " $data"?: UploadTimeSheetFragments_agreements$data;
  readonly " $fragmentSpreads": FragmentRefs<"UploadTimeSheetFragments_agreements">;
}>;

const node: ReaderFragment = {
  "argumentDefinitions": [],
  "kind": "Fragment",
  "metadata": {
    "plural": true
  },
  "name": "UploadTimeSheetFragments_agreements",
  "selections": [
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "id",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "name",
      "storageKey": null
    }
  ],
  "type": "Agreement",
  "abstractKey": null
};

(node as any).hash = "662d071e9bdd4b306802edf27ab00984";

export default node;
