/**
 * @generated SignedSource<<92147bed9c3fff36dbe813c0a60c08b5>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ConcreteRequest } from 'relay-runtime';
export type TimesheetDuplicationQuery$variables = {
  timeSheetId: string;
};
export type TimesheetDuplicationQuery$data = {
  readonly timeSheetById: {
    readonly id: string;
    readonly name: string | null | undefined;
    readonly numericId: number;
    readonly payPeriodEndDate: any | null | undefined;
    readonly payStubs: {
      readonly edges: ReadonlyArray<{
        readonly node: {
          readonly details: ReadonlyArray<{
            readonly agreementId: number | null | undefined;
            readonly bonus: number | null | undefined;
            readonly classificationId: number | null | undefined;
            readonly costCenter: string | null | undefined;
            readonly dtHours: number | null | undefined;
            readonly earningsCode: string | null | undefined;
            readonly expenses: number | null | undefined;
            readonly hourlyRate: number | null | undefined;
            readonly id: string;
            readonly jobCode: string | null | undefined;
            readonly otHours: number | null | undefined;
            readonly reportLineItemId: number | null | undefined;
            readonly stHours: number | null | undefined;
            readonly subClassificationId: number | null | undefined;
            readonly workDate: any;
          }>;
          readonly employeeId: string;
          readonly id: string;
          readonly name: string | null | undefined;
          readonly totalHours: number | null | undefined;
        };
      }> | null | undefined;
    } | null | undefined;
    readonly showBonusColumn: boolean | null | undefined;
    readonly showCostCenterColumn: boolean | null | undefined;
    readonly showDTHoursColumn: boolean | null | undefined;
    readonly showEarningsCodesColumn: boolean | null | undefined;
    readonly showExpensesColumn: boolean | null | undefined;
    readonly status: string;
    readonly type: string;
  } | null | undefined;
};
export type TimesheetDuplicationQuery = {
  response: TimesheetDuplicationQuery$data;
  variables: TimesheetDuplicationQuery$variables;
};

const node: ConcreteRequest = (function(){
var v0 = [
  {
    "defaultValue": null,
    "kind": "LocalArgument",
    "name": "timeSheetId"
  }
],
v1 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "id",
  "storageKey": null
},
v2 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "name",
  "storageKey": null
},
v3 = [
  {
    "alias": null,
    "args": [
      {
        "kind": "Variable",
        "name": "id",
        "variableName": "timeSheetId"
      }
    ],
    "concreteType": "TimeSheet",
    "kind": "LinkedField",
    "name": "timeSheetById",
    "plural": false,
    "selections": [
      (v1/*: any*/),
      (v2/*: any*/),
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "numericId",
        "storageKey": null
      },
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "payPeriodEndDate",
        "storageKey": null
      },
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "status",
        "storageKey": null
      },
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "type",
        "storageKey": null
      },
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "showBonusColumn",
        "storageKey": null
      },
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "showCostCenterColumn",
        "storageKey": null
      },
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "showDTHoursColumn",
        "storageKey": null
      },
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "showEarningsCodesColumn",
        "storageKey": null
      },
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "showExpensesColumn",
        "storageKey": null
      },
      {
        "alias": null,
        "args": [
          {
            "kind": "Literal",
            "name": "first",
            "value": 10000
          }
        ],
        "concreteType": "PayStubConnection",
        "kind": "LinkedField",
        "name": "payStubs",
        "plural": false,
        "selections": [
          {
            "alias": null,
            "args": null,
            "concreteType": "PayStubEdge",
            "kind": "LinkedField",
            "name": "edges",
            "plural": true,
            "selections": [
              {
                "alias": null,
                "args": null,
                "concreteType": "PayStub",
                "kind": "LinkedField",
                "name": "node",
                "plural": false,
                "selections": [
                  (v1/*: any*/),
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "employeeId",
                    "storageKey": null
                  },
                  (v2/*: any*/),
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "totalHours",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "concreteType": "PayStubDetail",
                    "kind": "LinkedField",
                    "name": "details",
                    "plural": true,
                    "selections": [
                      (v1/*: any*/),
                      {
                        "alias": null,
                        "args": null,
                        "kind": "ScalarField",
                        "name": "workDate",
                        "storageKey": null
                      },
                      {
                        "alias": null,
                        "args": null,
                        "kind": "ScalarField",
                        "name": "jobCode",
                        "storageKey": null
                      },
                      {
                        "alias": null,
                        "args": null,
                        "kind": "ScalarField",
                        "name": "costCenter",
                        "storageKey": null
                      },
                      {
                        "alias": null,
                        "args": null,
                        "kind": "ScalarField",
                        "name": "stHours",
                        "storageKey": null
                      },
                      {
                        "alias": null,
                        "args": null,
                        "kind": "ScalarField",
                        "name": "otHours",
                        "storageKey": null
                      },
                      {
                        "alias": null,
                        "args": null,
                        "kind": "ScalarField",
                        "name": "dtHours",
                        "storageKey": null
                      },
                      {
                        "alias": null,
                        "args": null,
                        "kind": "ScalarField",
                        "name": "bonus",
                        "storageKey": null
                      },
                      {
                        "alias": null,
                        "args": null,
                        "kind": "ScalarField",
                        "name": "expenses",
                        "storageKey": null
                      },
                      {
                        "alias": null,
                        "args": null,
                        "kind": "ScalarField",
                        "name": "hourlyRate",
                        "storageKey": null
                      },
                      {
                        "alias": null,
                        "args": null,
                        "kind": "ScalarField",
                        "name": "earningsCode",
                        "storageKey": null
                      },
                      {
                        "alias": null,
                        "args": null,
                        "kind": "ScalarField",
                        "name": "agreementId",
                        "storageKey": null
                      },
                      {
                        "alias": null,
                        "args": null,
                        "kind": "ScalarField",
                        "name": "classificationId",
                        "storageKey": null
                      },
                      {
                        "alias": null,
                        "args": null,
                        "kind": "ScalarField",
                        "name": "subClassificationId",
                        "storageKey": null
                      },
                      {
                        "alias": null,
                        "args": null,
                        "kind": "ScalarField",
                        "name": "reportLineItemId",
                        "storageKey": null
                      }
                    ],
                    "storageKey": null
                  }
                ],
                "storageKey": null
              }
            ],
            "storageKey": null
          }
        ],
        "storageKey": "payStubs(first:10000)"
      }
    ],
    "storageKey": null
  }
];
return {
  "fragment": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Fragment",
    "metadata": null,
    "name": "TimesheetDuplicationQuery",
    "selections": (v3/*: any*/),
    "type": "Query",
    "abstractKey": null
  },
  "kind": "Request",
  "operation": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Operation",
    "name": "TimesheetDuplicationQuery",
    "selections": (v3/*: any*/)
  },
  "params": {
    "cacheID": "073ad50367642ba91646770c16fc72cd",
    "id": null,
    "metadata": {},
    "name": "TimesheetDuplicationQuery",
    "operationKind": "query",
    "text": "query TimesheetDuplicationQuery(\n  $timeSheetId: ID!\n) {\n  timeSheetById(id: $timeSheetId) {\n    id\n    name\n    numericId\n    payPeriodEndDate\n    status\n    type\n    showBonusColumn\n    showCostCenterColumn\n    showDTHoursColumn\n    showEarningsCodesColumn\n    showExpensesColumn\n    payStubs(first: 10000) {\n      edges {\n        node {\n          id\n          employeeId\n          name\n          totalHours\n          details {\n            id\n            workDate\n            jobCode\n            costCenter\n            stHours\n            otHours\n            dtHours\n            bonus\n            expenses\n            hourlyRate\n            earningsCode\n            agreementId\n            classificationId\n            subClassificationId\n            reportLineItemId\n          }\n        }\n      }\n    }\n  }\n}\n"
  }
};
})();

(node as any).hash = "5520b722583c75a4b7c26deb914dc73d";

export default node;
