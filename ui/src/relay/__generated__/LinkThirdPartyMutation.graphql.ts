/**
 * @generated SignedSource<<3dcce928d85416107210824bd3b692fd>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ConcreteRequest } from 'relay-runtime';
export type LinkThirdPartyInput = {
  chapterId: number;
  name: string;
  relationshipTypeId: number;
  username: string;
};
export type LinkThirdPartyMutation$variables = {
  connections: ReadonlyArray<string>;
  input: LinkThirdPartyInput;
};
export type LinkThirdPartyMutation$data = {
  readonly linkThirdParty: {
    readonly thirdPartyInfoDtoEdge: {
      readonly cursor: string;
      readonly node: {
        readonly guid: any | null | undefined;
        readonly label: string;
        readonly value: number;
      } | null | undefined;
    } | null | undefined;
  };
};
export type LinkThirdPartyMutation = {
  response: LinkThirdPartyMutation$data;
  variables: LinkThirdPartyMutation$variables;
};

const node: ConcreteRequest = (function(){
var v0 = {
  "defaultValue": null,
  "kind": "LocalArgument",
  "name": "connections"
},
v1 = {
  "defaultValue": null,
  "kind": "LocalArgument",
  "name": "input"
},
v2 = [
  {
    "kind": "Variable",
    "name": "input",
    "variableName": "input"
  }
],
v3 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "cursor",
  "storageKey": null
},
v4 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "value",
  "storageKey": null
},
v5 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "label",
  "storageKey": null
},
v6 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "guid",
  "storageKey": null
};
return {
  "fragment": {
    "argumentDefinitions": [
      (v0/*: any*/),
      (v1/*: any*/)
    ],
    "kind": "Fragment",
    "metadata": null,
    "name": "LinkThirdPartyMutation",
    "selections": [
      {
        "alias": null,
        "args": (v2/*: any*/),
        "concreteType": "LinkThirdPartyPayload",
        "kind": "LinkedField",
        "name": "linkThirdParty",
        "plural": false,
        "selections": [
          {
            "alias": null,
            "args": null,
            "concreteType": "EdgeOfThirdPartyInfoDto",
            "kind": "LinkedField",
            "name": "thirdPartyInfoDtoEdge",
            "plural": false,
            "selections": [
              (v3/*: any*/),
              {
                "alias": null,
                "args": null,
                "concreteType": "ThirdPartyInfoDto",
                "kind": "LinkedField",
                "name": "node",
                "plural": false,
                "selections": [
                  (v4/*: any*/),
                  (v5/*: any*/),
                  (v6/*: any*/)
                ],
                "storageKey": null
              }
            ],
            "storageKey": null
          }
        ],
        "storageKey": null
      }
    ],
    "type": "Mutation",
    "abstractKey": null
  },
  "kind": "Request",
  "operation": {
    "argumentDefinitions": [
      (v1/*: any*/),
      (v0/*: any*/)
    ],
    "kind": "Operation",
    "name": "LinkThirdPartyMutation",
    "selections": [
      {
        "alias": null,
        "args": (v2/*: any*/),
        "concreteType": "LinkThirdPartyPayload",
        "kind": "LinkedField",
        "name": "linkThirdParty",
        "plural": false,
        "selections": [
          {
            "alias": null,
            "args": null,
            "concreteType": "EdgeOfThirdPartyInfoDto",
            "kind": "LinkedField",
            "name": "thirdPartyInfoDtoEdge",
            "plural": false,
            "selections": [
              (v3/*: any*/),
              {
                "alias": null,
                "args": null,
                "concreteType": "ThirdPartyInfoDto",
                "kind": "LinkedField",
                "name": "node",
                "plural": false,
                "selections": [
                  (v4/*: any*/),
                  (v5/*: any*/),
                  (v6/*: any*/),
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "id",
                    "storageKey": null
                  }
                ],
                "storageKey": null
              }
            ],
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "filters": null,
            "handle": "appendEdge",
            "key": "",
            "kind": "LinkedHandle",
            "name": "thirdPartyInfoDtoEdge",
            "handleArgs": [
              {
                "kind": "Variable",
                "name": "connections",
                "variableName": "connections"
              }
            ]
          }
        ],
        "storageKey": null
      }
    ]
  },
  "params": {
    "cacheID": "ead400f06390364297b2a019cccd88f0",
    "id": null,
    "metadata": {},
    "name": "LinkThirdPartyMutation",
    "operationKind": "mutation",
    "text": "mutation LinkThirdPartyMutation(\n  $input: LinkThirdPartyInput!\n) {\n  linkThirdParty(input: $input) {\n    thirdPartyInfoDtoEdge {\n      cursor\n      node {\n        value\n        label\n        guid\n        id\n      }\n    }\n  }\n}\n"
  }
};
})();

(node as any).hash = "b47e2fa093b2c807905c001104450a73";

export default node;
