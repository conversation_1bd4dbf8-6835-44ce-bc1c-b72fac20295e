/**
 * @generated SignedSource<<973b77109e8f94d66780bda1ee29b274>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ConcreteRequest } from 'relay-runtime';
export type ChangePasswordInput = {
  confirmPassword: string;
  currentPassword: string;
  newPassword: string;
};
export type PersonalDetailsChangePasswordMutationQuery$variables = {
  input: ChangePasswordInput;
};
export type PersonalDetailsChangePasswordMutationQuery$data = {
  readonly changePassword: {
    readonly string: string | null | undefined;
  };
};
export type PersonalDetailsChangePasswordMutationQuery = {
  response: PersonalDetailsChangePasswordMutationQuery$data;
  variables: PersonalDetailsChangePasswordMutationQuery$variables;
};

const node: ConcreteRequest = (function(){
var v0 = [
  {
    "defaultValue": null,
    "kind": "LocalArgument",
    "name": "input"
  }
],
v1 = [
  {
    "alias": null,
    "args": [
      {
        "kind": "Variable",
        "name": "input",
        "variableName": "input"
      }
    ],
    "concreteType": "ChangePasswordPayload",
    "kind": "LinkedField",
    "name": "changePassword",
    "plural": false,
    "selections": [
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "string",
        "storageKey": null
      }
    ],
    "storageKey": null
  }
];
return {
  "fragment": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Fragment",
    "metadata": null,
    "name": "PersonalDetailsChangePasswordMutationQuery",
    "selections": (v1/*: any*/),
    "type": "Mutation",
    "abstractKey": null
  },
  "kind": "Request",
  "operation": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Operation",
    "name": "PersonalDetailsChangePasswordMutationQuery",
    "selections": (v1/*: any*/)
  },
  "params": {
    "cacheID": "7e801c9c3bd0b3c4bb0a327376ee7901",
    "id": null,
    "metadata": {},
    "name": "PersonalDetailsChangePasswordMutationQuery",
    "operationKind": "mutation",
    "text": "mutation PersonalDetailsChangePasswordMutationQuery(\n  $input: ChangePasswordInput!\n) {\n  changePassword(input: $input) {\n    string\n  }\n}\n"
  }
};
})();

(node as any).hash = "ae54b0c48c6776489822ab3f6ab648fb";

export default node;
