/**
 * @generated SignedSource<<f737bda925a0d5bc3ff46b2d135af4e1>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ConcreteRequest } from 'relay-runtime';
export type AddFundAdministratorInput = {
  input: AddThirdPartyInput;
};
export type AddThirdPartyInput = {
  chapterId: number;
  name: string;
  username: string;
};
export type CreateFundAdminMutation$variables = {
  connections: ReadonlyArray<string>;
  input: AddFundAdministratorInput;
};
export type CreateFundAdminMutation$data = {
  readonly addFundAdministrator: {
    readonly thirdPartyInfoDtoEdge: {
      readonly cursor: string;
      readonly node: {
        readonly guid: any | null | undefined;
        readonly label: string;
        readonly value: number;
      } | null | undefined;
    } | null | undefined;
  };
};
export type CreateFundAdminMutation = {
  response: CreateFundAdminMutation$data;
  variables: CreateFundAdminMutation$variables;
};

const node: ConcreteRequest = (function(){
var v0 = {
  "defaultValue": null,
  "kind": "LocalArgument",
  "name": "connections"
},
v1 = {
  "defaultValue": null,
  "kind": "LocalArgument",
  "name": "input"
},
v2 = [
  {
    "kind": "Variable",
    "name": "input",
    "variableName": "input"
  }
],
v3 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "cursor",
  "storageKey": null
},
v4 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "value",
  "storageKey": null
},
v5 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "label",
  "storageKey": null
},
v6 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "guid",
  "storageKey": null
};
return {
  "fragment": {
    "argumentDefinitions": [
      (v0/*: any*/),
      (v1/*: any*/)
    ],
    "kind": "Fragment",
    "metadata": null,
    "name": "CreateFundAdminMutation",
    "selections": [
      {
        "alias": null,
        "args": (v2/*: any*/),
        "concreteType": "AddFundAdministratorPayload",
        "kind": "LinkedField",
        "name": "addFundAdministrator",
        "plural": false,
        "selections": [
          {
            "alias": null,
            "args": null,
            "concreteType": "EdgeOfThirdPartyInfoDto",
            "kind": "LinkedField",
            "name": "thirdPartyInfoDtoEdge",
            "plural": false,
            "selections": [
              (v3/*: any*/),
              {
                "alias": null,
                "args": null,
                "concreteType": "ThirdPartyInfoDto",
                "kind": "LinkedField",
                "name": "node",
                "plural": false,
                "selections": [
                  (v4/*: any*/),
                  (v5/*: any*/),
                  (v6/*: any*/)
                ],
                "storageKey": null
              }
            ],
            "storageKey": null
          }
        ],
        "storageKey": null
      }
    ],
    "type": "Mutation",
    "abstractKey": null
  },
  "kind": "Request",
  "operation": {
    "argumentDefinitions": [
      (v1/*: any*/),
      (v0/*: any*/)
    ],
    "kind": "Operation",
    "name": "CreateFundAdminMutation",
    "selections": [
      {
        "alias": null,
        "args": (v2/*: any*/),
        "concreteType": "AddFundAdministratorPayload",
        "kind": "LinkedField",
        "name": "addFundAdministrator",
        "plural": false,
        "selections": [
          {
            "alias": null,
            "args": null,
            "concreteType": "EdgeOfThirdPartyInfoDto",
            "kind": "LinkedField",
            "name": "thirdPartyInfoDtoEdge",
            "plural": false,
            "selections": [
              (v3/*: any*/),
              {
                "alias": null,
                "args": null,
                "concreteType": "ThirdPartyInfoDto",
                "kind": "LinkedField",
                "name": "node",
                "plural": false,
                "selections": [
                  (v4/*: any*/),
                  (v5/*: any*/),
                  (v6/*: any*/),
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "id",
                    "storageKey": null
                  }
                ],
                "storageKey": null
              }
            ],
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "filters": null,
            "handle": "appendEdge",
            "key": "",
            "kind": "LinkedHandle",
            "name": "thirdPartyInfoDtoEdge",
            "handleArgs": [
              {
                "kind": "Variable",
                "name": "connections",
                "variableName": "connections"
              }
            ]
          }
        ],
        "storageKey": null
      }
    ]
  },
  "params": {
    "cacheID": "7130080c4d27d3cfcc722b8fa0878c25",
    "id": null,
    "metadata": {},
    "name": "CreateFundAdminMutation",
    "operationKind": "mutation",
    "text": "mutation CreateFundAdminMutation(\n  $input: AddFundAdministratorInput!\n) {\n  addFundAdministrator(input: $input) {\n    thirdPartyInfoDtoEdge {\n      cursor\n      node {\n        value\n        label\n        guid\n        id\n      }\n    }\n  }\n}\n"
  }
};
})();

(node as any).hash = "f5bd08fab075e980f2485643cdaf14c3";

export default node;
