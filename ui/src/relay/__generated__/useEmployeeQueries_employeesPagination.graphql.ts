/**
 * @generated SignedSource<<0ce215bd22387ff139afab8a015b7c18>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ReaderFragment } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type useEmployeeQueries_employeesPagination$data = {
  readonly employeesByEmployerGuidAsync: {
    readonly edges: ReadonlyArray<{
      readonly node: {
        readonly " $fragmentSpreads": FragmentRefs<"useEmployeeQueries_employee">;
      };
    }> | null | undefined;
    readonly pageInfo: {
      readonly endCursor: string | null | undefined;
      readonly hasNextPage: boolean;
    };
    readonly totalCount: number;
  } | null | undefined;
  readonly " $fragmentType": "useEmployeeQueries_employeesPagination";
};
export type useEmployeeQueries_employeesPagination$key = {
  readonly " $data"?: useEmployeeQueries_employeesPagination$data;
  readonly " $fragmentSpreads": FragmentRefs<"useEmployeeQueries_employeesPagination">;
};

import useEmployeeQueriesEmployeesByEmployerPaginationQuery_graphql from './useEmployeeQueriesEmployeesByEmployerPaginationQuery.graphql';

const node: ReaderFragment = (function(){
var v0 = [
  "employeesByEmployerGuidAsync"
];
return {
  "argumentDefinitions": [
    {
      "kind": "RootArgument",
      "name": "after"
    },
    {
      "kind": "RootArgument",
      "name": "employerGuid"
    },
    {
      "kind": "RootArgument",
      "name": "first"
    }
  ],
  "kind": "Fragment",
  "metadata": {
    "connection": [
      {
        "count": "first",
        "cursor": "after",
        "direction": "forward",
        "path": (v0/*: any*/)
      }
    ],
    "refetch": {
      "connection": {
        "forward": {
          "count": "first",
          "cursor": "after"
        },
        "backward": null,
        "path": (v0/*: any*/)
      },
      "fragmentPathInResult": [],
      "operation": useEmployeeQueriesEmployeesByEmployerPaginationQuery_graphql
    }
  },
  "name": "useEmployeeQueries_employeesPagination",
  "selections": [
    {
      "alias": "employeesByEmployerGuidAsync",
      "args": [
        {
          "kind": "Variable",
          "name": "employerGuid",
          "variableName": "employerGuid"
        }
      ],
      "concreteType": "EmployeeConnection",
      "kind": "LinkedField",
      "name": "__useEmployeeQueries_employeesByEmployerGuidAsync_connection",
      "plural": false,
      "selections": [
        {
          "alias": null,
          "args": null,
          "concreteType": "EmployeeEdge",
          "kind": "LinkedField",
          "name": "edges",
          "plural": true,
          "selections": [
            {
              "alias": null,
              "args": null,
              "concreteType": "Employee",
              "kind": "LinkedField",
              "name": "node",
              "plural": false,
              "selections": [
                {
                  "args": null,
                  "kind": "FragmentSpread",
                  "name": "useEmployeeQueries_employee"
                },
                {
                  "alias": null,
                  "args": null,
                  "kind": "ScalarField",
                  "name": "__typename",
                  "storageKey": null
                }
              ],
              "storageKey": null
            },
            {
              "alias": null,
              "args": null,
              "kind": "ScalarField",
              "name": "cursor",
              "storageKey": null
            }
          ],
          "storageKey": null
        },
        {
          "alias": null,
          "args": null,
          "concreteType": "PageInfo",
          "kind": "LinkedField",
          "name": "pageInfo",
          "plural": false,
          "selections": [
            {
              "alias": null,
              "args": null,
              "kind": "ScalarField",
              "name": "hasNextPage",
              "storageKey": null
            },
            {
              "alias": null,
              "args": null,
              "kind": "ScalarField",
              "name": "endCursor",
              "storageKey": null
            }
          ],
          "storageKey": null
        },
        {
          "alias": null,
          "args": null,
          "kind": "ScalarField",
          "name": "totalCount",
          "storageKey": null
        }
      ],
      "storageKey": null
    }
  ],
  "type": "Query",
  "abstractKey": null
};
})();

(node as any).hash = "f1d8145e2023fbcbf035f3ffcd1b1fe9";

export default node;
