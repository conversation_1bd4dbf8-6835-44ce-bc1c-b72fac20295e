/**
 * @generated SignedSource<<77954ce200947accb90b228c8091f81b>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ConcreteRequest } from 'relay-runtime';
export type DeleteRelationshipWithSiteSponsorInput = {
  chapterId: string;
  employerId: string;
};
export type CanDeleteRelationshipMutation$variables = {
  input: DeleteRelationshipWithSiteSponsorInput;
};
export type CanDeleteRelationshipMutation$data = {
  readonly deleteRelationshipWithSiteSponsor: {
    readonly employerRosterViewEdge: {
      readonly node: {
        readonly associationId: string | null | undefined;
        readonly chapterId: string;
        readonly dba: string | null | undefined;
        readonly fein: string | null | undefined;
        readonly id: string;
        readonly lastReportedDate: any | null | undefined;
        readonly name: string;
        readonly payrollContactEmailAddress: string | null | undefined;
        readonly payrollContactFirstName: string | null | undefined;
        readonly payrollContactLastName: string | null | undefined;
        readonly payrollContactPhoneNumber: string | null | undefined;
      } | null | undefined;
    } | null | undefined;
  };
};
export type CanDeleteRelationshipMutation = {
  response: CanDeleteRelationshipMutation$data;
  variables: CanDeleteRelationshipMutation$variables;
};

const node: ConcreteRequest = (function(){
var v0 = [
  {
    "defaultValue": null,
    "kind": "LocalArgument",
    "name": "input"
  }
],
v1 = [
  {
    "alias": null,
    "args": [
      {
        "kind": "Variable",
        "name": "input",
        "variableName": "input"
      }
    ],
    "concreteType": "DeleteRelationshipWithSiteSponsorPayload",
    "kind": "LinkedField",
    "name": "deleteRelationshipWithSiteSponsor",
    "plural": false,
    "selections": [
      {
        "alias": null,
        "args": null,
        "concreteType": "EdgeOfEmployerRosterView",
        "kind": "LinkedField",
        "name": "employerRosterViewEdge",
        "plural": false,
        "selections": [
          {
            "alias": null,
            "args": null,
            "concreteType": "EmployerRosterView",
            "kind": "LinkedField",
            "name": "node",
            "plural": false,
            "selections": [
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "associationId",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "chapterId",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "dba",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "fein",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "id",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "lastReportedDate",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "name",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "payrollContactEmailAddress",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "payrollContactFirstName",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "payrollContactLastName",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "payrollContactPhoneNumber",
                "storageKey": null
              }
            ],
            "storageKey": null
          }
        ],
        "storageKey": null
      }
    ],
    "storageKey": null
  }
];
return {
  "fragment": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Fragment",
    "metadata": null,
    "name": "CanDeleteRelationshipMutation",
    "selections": (v1/*: any*/),
    "type": "Mutation",
    "abstractKey": null
  },
  "kind": "Request",
  "operation": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Operation",
    "name": "CanDeleteRelationshipMutation",
    "selections": (v1/*: any*/)
  },
  "params": {
    "cacheID": "5fe018c2c6dba76ae5417d187ce9f950",
    "id": null,
    "metadata": {},
    "name": "CanDeleteRelationshipMutation",
    "operationKind": "mutation",
    "text": "mutation CanDeleteRelationshipMutation(\n  $input: DeleteRelationshipWithSiteSponsorInput!\n) {\n  deleteRelationshipWithSiteSponsor(input: $input) {\n    employerRosterViewEdge {\n      node {\n        associationId\n        chapterId\n        dba\n        fein\n        id\n        lastReportedDate\n        name\n        payrollContactEmailAddress\n        payrollContactFirstName\n        payrollContactLastName\n        payrollContactPhoneNumber\n      }\n    }\n  }\n}\n"
  }
};
})();

(node as any).hash = "0d531c16e807afeff98e105c20414652";

export default node;
