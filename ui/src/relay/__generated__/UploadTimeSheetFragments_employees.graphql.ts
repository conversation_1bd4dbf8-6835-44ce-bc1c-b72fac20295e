/**
 * @generated SignedSource<<fe4ae2f13f584ae5754b1a744e8e3059>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ReaderFragment } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type UploadTimeSheetFragments_employees$data = ReadonlyArray<{
  readonly externalEmployeeId: string | null | undefined;
  readonly firstName: string | null | undefined;
  readonly id: string;
  readonly lastName: string;
  readonly ssn: ReadonlyArray<any> | null | undefined;
  readonly " $fragmentType": "UploadTimeSheetFragments_employees";
}>;
export type UploadTimeSheetFragments_employees$key = ReadonlyArray<{
  readonly " $data"?: UploadTimeSheetFragments_employees$data;
  readonly " $fragmentSpreads": FragmentRefs<"UploadTimeSheetFragments_employees">;
}>;

const node: ReaderFragment = {
  "argumentDefinitions": [],
  "kind": "Fragment",
  "metadata": {
    "plural": true
  },
  "name": "UploadTimeSheetFragments_employees",
  "selections": [
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "id",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "firstName",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "lastName",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "ssn",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "externalEmployeeId",
      "storageKey": null
    }
  ],
  "type": "Employee",
  "abstractKey": null
};

(node as any).hash = "0c7e630bc7cd1122ee4a2ccd8809f24b";

export default node;
