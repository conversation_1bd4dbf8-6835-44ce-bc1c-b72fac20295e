/**
 * @generated SignedSource<<36d7bb35f15174d72b04a28d5dc91e6c>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ConcreteRequest } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type EmployerProxyComboboxQuery$variables = {
  chapterId: string;
};
export type EmployerProxyComboboxQuery$data = {
  readonly " $fragmentSpreads": FragmentRefs<"EmployerProxyComboboxFragment">;
};
export type EmployerProxyComboboxQuery = {
  response: EmployerProxyComboboxQuery$data;
  variables: EmployerProxyComboboxQuery$variables;
};

const node: ConcreteRequest = (function(){
var v0 = [
  {
    "defaultValue": null,
    "kind": "LocalArgument",
    "name": "chapterId"
  }
],
v1 = {
  "kind": "Variable",
  "name": "chapterId",
  "variableName": "chapterId"
};
return {
  "fragment": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Fragment",
    "metadata": null,
    "name": "EmployerProxyComboboxQuery",
    "selections": [
      {
        "args": [
          (v1/*: any*/)
        ],
        "kind": "FragmentSpread",
        "name": "EmployerProxyComboboxFragment"
      }
    ],
    "type": "Query",
    "abstractKey": null
  },
  "kind": "Request",
  "operation": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Operation",
    "name": "EmployerProxyComboboxQuery",
    "selections": [
      {
        "alias": null,
        "args": [
          (v1/*: any*/),
          {
            "kind": "Literal",
            "name": "first",
            "value": 10000
          }
        ],
        "concreteType": "EmployerConnection",
        "kind": "LinkedField",
        "name": "employersByChapterId",
        "plural": false,
        "selections": [
          {
            "alias": null,
            "args": null,
            "concreteType": "Employer",
            "kind": "LinkedField",
            "name": "nodes",
            "plural": true,
            "selections": [
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "id",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "concreteType": "Organization",
                "kind": "LinkedField",
                "name": "organization",
                "plural": false,
                "selections": [
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "name",
                    "storageKey": null
                  }
                ],
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "concreteType": "Root",
                "kind": "LinkedField",
                "name": "root",
                "plural": false,
                "selections": [
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "guid",
                    "storageKey": null
                  }
                ],
                "storageKey": null
              }
            ],
            "storageKey": null
          }
        ],
        "storageKey": null
      },
      {
        "kind": "ClientExtension",
        "selections": [
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "__id",
            "storageKey": null
          }
        ]
      }
    ]
  },
  "params": {
    "cacheID": "fd44cc36eb1f696fa0d2d7eebffa4225",
    "id": null,
    "metadata": {},
    "name": "EmployerProxyComboboxQuery",
    "operationKind": "query",
    "text": "query EmployerProxyComboboxQuery(\n  $chapterId: ID!\n) {\n  ...EmployerProxyComboboxFragment_2anDGG\n}\n\nfragment EmployerProxyComboboxFragment_2anDGG on Query {\n  employersByChapterId(chapterId: $chapterId, first: 10000) {\n    nodes {\n      id\n      organization {\n        name\n      }\n      root {\n        guid\n      }\n    }\n  }\n}\n"
  }
};
})();

(node as any).hash = "d580dc81a19723e5475478eca0e83fd4";

export default node;
