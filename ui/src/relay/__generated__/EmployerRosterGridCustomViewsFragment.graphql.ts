/**
 * @generated SignedSource<<7d6e0fd7dc1b2765523d7693b3895ad2>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ReaderFragment } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type EmployerRosterGridCustomViewsFragment$data = {
  readonly id: string | null | undefined;
  readonly value: any | null | undefined;
  readonly " $fragmentType": "EmployerRosterGridCustomViewsFragment";
};
export type EmployerRosterGridCustomViewsFragment$key = {
  readonly " $data"?: EmployerRosterGridCustomViewsFragment$data;
  readonly " $fragmentSpreads": FragmentRefs<"EmployerRosterGridCustomViewsFragment">;
};

const node: ReaderFragment = {
  "argumentDefinitions": [],
  "kind": "Fragment",
  "metadata": null,
  "name": "EmployerRosterGridCustomViewsFragment",
  "selections": [
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "id",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "value",
      "storageKey": null
    }
  ],
  "type": "CustomViews",
  "abstractKey": null
};

(node as any).hash = "3b0e6a1f1028d9c0ab2efea63810225c";

export default node;
