/**
 * @generated SignedSource<<ea88e09c86dd36c9d3141d7d711d0735>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ConcreteRequest } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type UpdateCustomViewsInput = {
  id?: string | null | undefined;
  name?: string | null | undefined;
  value?: any | null | undefined;
};
export type EmployerRosterGridUpdateCustomViewsMutation$variables = {
  input: UpdateCustomViewsInput;
};
export type EmployerRosterGridUpdateCustomViewsMutation$data = {
  readonly updateCustomViews: {
    readonly customViews: {
      readonly value: any | null | undefined;
      readonly " $fragmentSpreads": FragmentRefs<"EmployerRosterGridCustomViewsFragment">;
    } | null | undefined;
  };
};
export type EmployerRosterGridUpdateCustomViewsMutation = {
  response: EmployerRosterGridUpdateCustomViewsMutation$data;
  variables: EmployerRosterGridUpdateCustomViewsMutation$variables;
};

const node: ConcreteRequest = (function(){
var v0 = [
  {
    "defaultValue": null,
    "kind": "LocalArgument",
    "name": "input"
  }
],
v1 = [
  {
    "kind": "Variable",
    "name": "input",
    "variableName": "input"
  }
],
v2 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "value",
  "storageKey": null
};
return {
  "fragment": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Fragment",
    "metadata": null,
    "name": "EmployerRosterGridUpdateCustomViewsMutation",
    "selections": [
      {
        "alias": null,
        "args": (v1/*: any*/),
        "concreteType": "UpdateCustomViewsPayload",
        "kind": "LinkedField",
        "name": "updateCustomViews",
        "plural": false,
        "selections": [
          {
            "alias": null,
            "args": null,
            "concreteType": "CustomViews",
            "kind": "LinkedField",
            "name": "customViews",
            "plural": false,
            "selections": [
              {
                "args": null,
                "kind": "FragmentSpread",
                "name": "EmployerRosterGridCustomViewsFragment"
              },
              (v2/*: any*/)
            ],
            "storageKey": null
          }
        ],
        "storageKey": null
      }
    ],
    "type": "Mutation",
    "abstractKey": null
  },
  "kind": "Request",
  "operation": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Operation",
    "name": "EmployerRosterGridUpdateCustomViewsMutation",
    "selections": [
      {
        "alias": null,
        "args": (v1/*: any*/),
        "concreteType": "UpdateCustomViewsPayload",
        "kind": "LinkedField",
        "name": "updateCustomViews",
        "plural": false,
        "selections": [
          {
            "alias": null,
            "args": null,
            "concreteType": "CustomViews",
            "kind": "LinkedField",
            "name": "customViews",
            "plural": false,
            "selections": [
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "id",
                "storageKey": null
              },
              (v2/*: any*/)
            ],
            "storageKey": null
          }
        ],
        "storageKey": null
      }
    ]
  },
  "params": {
    "cacheID": "cabe954e1c14ecade273315e46ec1296",
    "id": null,
    "metadata": {},
    "name": "EmployerRosterGridUpdateCustomViewsMutation",
    "operationKind": "mutation",
    "text": "mutation EmployerRosterGridUpdateCustomViewsMutation(\n  $input: UpdateCustomViewsInput!\n) {\n  updateCustomViews(input: $input) {\n    customViews {\n      ...EmployerRosterGridCustomViewsFragment\n      value\n      id\n    }\n  }\n}\n\nfragment EmployerRosterGridCustomViewsFragment on CustomViews {\n  id\n  value\n}\n"
  }
};
})();

(node as any).hash = "a4789cafacf47065f91fb6837a32c6eb";

export default node;
