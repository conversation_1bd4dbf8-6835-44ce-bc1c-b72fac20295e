/**
 * @generated SignedSource<<c3f2db0694681d181df0d14407df9952>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ReaderFragment } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type EmployerProxyComboboxFragment$data = {
  readonly __id: string;
  readonly employersByChapterId: {
    readonly nodes: ReadonlyArray<{
      readonly id: string;
      readonly organization: {
        readonly name: string;
      } | null | undefined;
      readonly root: {
        readonly guid: any | null | undefined;
      } | null | undefined;
    }> | null | undefined;
  } | null | undefined;
  readonly " $fragmentType": "EmployerProxyComboboxFragment";
};
export type EmployerProxyComboboxFragment$key = {
  readonly " $data"?: EmployerProxyComboboxFragment$data;
  readonly " $fragmentSpreads": FragmentRefs<"EmployerProxyComboboxFragment">;
};

const node: ReaderFragment = {
  "argumentDefinitions": [
    {
      "defaultValue": null,
      "kind": "LocalArgument",
      "name": "chapterId"
    },
    {
      "defaultValue": 10000,
      "kind": "LocalArgument",
      "name": "first"
    }
  ],
  "kind": "Fragment",
  "metadata": null,
  "name": "EmployerProxyComboboxFragment",
  "selections": [
    {
      "alias": null,
      "args": [
        {
          "kind": "Variable",
          "name": "chapterId",
          "variableName": "chapterId"
        },
        {
          "kind": "Variable",
          "name": "first",
          "variableName": "first"
        }
      ],
      "concreteType": "EmployerConnection",
      "kind": "LinkedField",
      "name": "employersByChapterId",
      "plural": false,
      "selections": [
        {
          "alias": null,
          "args": null,
          "concreteType": "Employer",
          "kind": "LinkedField",
          "name": "nodes",
          "plural": true,
          "selections": [
            {
              "alias": null,
              "args": null,
              "kind": "ScalarField",
              "name": "id",
              "storageKey": null
            },
            {
              "alias": null,
              "args": null,
              "concreteType": "Organization",
              "kind": "LinkedField",
              "name": "organization",
              "plural": false,
              "selections": [
                {
                  "alias": null,
                  "args": null,
                  "kind": "ScalarField",
                  "name": "name",
                  "storageKey": null
                }
              ],
              "storageKey": null
            },
            {
              "alias": null,
              "args": null,
              "concreteType": "Root",
              "kind": "LinkedField",
              "name": "root",
              "plural": false,
              "selections": [
                {
                  "alias": null,
                  "args": null,
                  "kind": "ScalarField",
                  "name": "guid",
                  "storageKey": null
                }
              ],
              "storageKey": null
            }
          ],
          "storageKey": null
        }
      ],
      "storageKey": null
    },
    {
      "kind": "ClientExtension",
      "selections": [
        {
          "alias": null,
          "args": null,
          "kind": "ScalarField",
          "name": "__id",
          "storageKey": null
        }
      ]
    }
  ],
  "type": "Query",
  "abstractKey": null
};

(node as any).hash = "0ff1d5f1acec0f67b2b8d030985af5b0";

export default node;
