/**
 * @generated SignedSource<<66a4d47521c93cd43a463f2c77c2aff3>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ConcreteRequest } from 'relay-runtime';
export type useEmployeeDefaultSettingsQueriesSingleEmployeeQuery$variables = {
  employeeId: string;
};
export type useEmployeeDefaultSettingsQueriesSingleEmployeeQuery$data = {
  readonly employeeDefaultSettings: {
    readonly defaultAgreementId: number | null | undefined;
    readonly defaultClassificationId: number | null | undefined;
    readonly defaultHourlyRate: any | null | undefined;
    readonly employeeId: string | null | undefined;
  } | null | undefined;
};
export type useEmployeeDefaultSettingsQueriesSingleEmployeeQuery = {
  response: useEmployeeDefaultSettingsQueriesSingleEmployeeQuery$data;
  variables: useEmployeeDefaultSettingsQueriesSingleEmployeeQuery$variables;
};

const node: ConcreteRequest = (function(){
var v0 = [
  {
    "defaultValue": null,
    "kind": "LocalArgument",
    "name": "employeeId"
  }
],
v1 = [
  {
    "alias": null,
    "args": [
      {
        "kind": "Variable",
        "name": "employeeId",
        "variableName": "employeeId"
      }
    ],
    "concreteType": "EmployeeDefaultSettings",
    "kind": "LinkedField",
    "name": "employeeDefaultSettings",
    "plural": false,
    "selections": [
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "employeeId",
        "storageKey": null
      },
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "defaultAgreementId",
        "storageKey": null
      },
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "defaultClassificationId",
        "storageKey": null
      },
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "defaultHourlyRate",
        "storageKey": null
      }
    ],
    "storageKey": null
  }
];
return {
  "fragment": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Fragment",
    "metadata": null,
    "name": "useEmployeeDefaultSettingsQueriesSingleEmployeeQuery",
    "selections": (v1/*: any*/),
    "type": "Query",
    "abstractKey": null
  },
  "kind": "Request",
  "operation": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Operation",
    "name": "useEmployeeDefaultSettingsQueriesSingleEmployeeQuery",
    "selections": (v1/*: any*/)
  },
  "params": {
    "cacheID": "8d1b4b0bae16ddba5e5384540a041222",
    "id": null,
    "metadata": {},
    "name": "useEmployeeDefaultSettingsQueriesSingleEmployeeQuery",
    "operationKind": "query",
    "text": "query useEmployeeDefaultSettingsQueriesSingleEmployeeQuery(\n  $employeeId: ID!\n) {\n  employeeDefaultSettings(employeeId: $employeeId) {\n    employeeId\n    defaultAgreementId\n    defaultClassificationId\n    defaultHourlyRate\n  }\n}\n"
  }
};
})();

(node as any).hash = "2248b203ca2f46c46bc2df8c099d74b1";

export default node;
