/**
 * @generated SignedSource<<08721feb5736d967fabc335554cdf0fd>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ReaderFragment } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type EmployerRosterGridCustomViewsFragment_updatable$data = {
  readonly id: string | null | undefined;
  value: any | null | undefined;
  readonly " $fragmentType": "EmployerRosterGridCustomViewsFragment_updatable";
};
export type EmployerRosterGridCustomViewsFragment_updatable$key = {
  readonly " $data"?: EmployerRosterGridCustomViewsFragment_updatable$data;
  readonly $updatableFragmentSpreads: FragmentRefs<"EmployerRosterGridCustomViewsFragment_updatable">;
};

const node: ReaderFragment = {
  "argumentDefinitions": [],
  "kind": "Fragment",
  "metadata": null,
  "name": "EmployerRosterGridCustomViewsFragment_updatable",
  "selections": [
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "id",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "value",
      "storageKey": null
    }
  ],
  "type": "CustomViews",
  "abstractKey": null
};

(node as any).hash = "d32c4bf8460a6957ce06c6ee8f0aac4a";

export default node;
