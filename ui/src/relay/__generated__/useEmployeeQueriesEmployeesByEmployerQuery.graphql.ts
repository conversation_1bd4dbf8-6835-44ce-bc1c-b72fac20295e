/**
 * @generated SignedSource<<a2c1bfc737ae8055c5dd00b44bfa5d65>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ConcreteRequest } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type useEmployeeQueriesEmployeesByEmployerQuery$variables = {
  after?: string | null | undefined;
  employerGuid: any;
  first?: number | null | undefined;
};
export type useEmployeeQueriesEmployeesByEmployerQuery$data = {
  readonly " $fragmentSpreads": FragmentRefs<"useEmployeeQueries_employeesPagination">;
};
export type useEmployeeQueriesEmployeesByEmployerQuery = {
  response: useEmployeeQueriesEmployeesByEmployerQuery$data;
  variables: useEmployeeQueriesEmployeesByEmployerQuery$variables;
};

const node: ConcreteRequest = (function(){
var v0 = {
  "defaultValue": null,
  "kind": "LocalArgument",
  "name": "after"
},
v1 = {
  "defaultValue": null,
  "kind": "LocalArgument",
  "name": "employerGuid"
},
v2 = {
  "defaultValue": null,
  "kind": "LocalArgument",
  "name": "first"
},
v3 = [
  {
    "kind": "Variable",
    "name": "after",
    "variableName": "after"
  },
  {
    "kind": "Variable",
    "name": "employerGuid",
    "variableName": "employerGuid"
  },
  {
    "kind": "Variable",
    "name": "first",
    "variableName": "first"
  }
];
return {
  "fragment": {
    "argumentDefinitions": [
      (v0/*: any*/),
      (v1/*: any*/),
      (v2/*: any*/)
    ],
    "kind": "Fragment",
    "metadata": null,
    "name": "useEmployeeQueriesEmployeesByEmployerQuery",
    "selections": [
      {
        "args": null,
        "kind": "FragmentSpread",
        "name": "useEmployeeQueries_employeesPagination"
      }
    ],
    "type": "Query",
    "abstractKey": null
  },
  "kind": "Request",
  "operation": {
    "argumentDefinitions": [
      (v1/*: any*/),
      (v2/*: any*/),
      (v0/*: any*/)
    ],
    "kind": "Operation",
    "name": "useEmployeeQueriesEmployeesByEmployerQuery",
    "selections": [
      {
        "alias": null,
        "args": (v3/*: any*/),
        "concreteType": "EmployeeConnection",
        "kind": "LinkedField",
        "name": "employeesByEmployerGuidAsync",
        "plural": false,
        "selections": [
          {
            "alias": null,
            "args": null,
            "concreteType": "EmployeeEdge",
            "kind": "LinkedField",
            "name": "edges",
            "plural": true,
            "selections": [
              {
                "alias": null,
                "args": null,
                "concreteType": "Employee",
                "kind": "LinkedField",
                "name": "node",
                "plural": false,
                "selections": [
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "id",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "externalEmployeeId",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "firstName",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "middleName",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "lastName",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "suffix",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "dateOfHire",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "dateOfTermination",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "active",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "__typename",
                    "storageKey": null
                  }
                ],
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "cursor",
                "storageKey": null
              }
            ],
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "concreteType": "PageInfo",
            "kind": "LinkedField",
            "name": "pageInfo",
            "plural": false,
            "selections": [
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "hasNextPage",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "endCursor",
                "storageKey": null
              }
            ],
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "totalCount",
            "storageKey": null
          }
        ],
        "storageKey": null
      },
      {
        "alias": null,
        "args": (v3/*: any*/),
        "filters": [
          "employerGuid"
        ],
        "handle": "connection",
        "key": "useEmployeeQueries_employeesByEmployerGuidAsync",
        "kind": "LinkedHandle",
        "name": "employeesByEmployerGuidAsync"
      }
    ]
  },
  "params": {
    "cacheID": "94be004dfb3c6138230987bd7531d9bb",
    "id": null,
    "metadata": {},
    "name": "useEmployeeQueriesEmployeesByEmployerQuery",
    "operationKind": "query",
    "text": "query useEmployeeQueriesEmployeesByEmployerQuery(\n  $employerGuid: UUID!\n  $first: Int\n  $after: String\n) {\n  ...useEmployeeQueries_employeesPagination\n}\n\nfragment useEmployeeQueries_employee on Employee {\n  id\n  externalEmployeeId\n  firstName\n  middleName\n  lastName\n  suffix\n  dateOfHire\n  dateOfTermination\n  active\n}\n\nfragment useEmployeeQueries_employeesPagination on Query {\n  employeesByEmployerGuidAsync(employerGuid: $employerGuid, first: $first, after: $after) {\n    edges {\n      node {\n        ...useEmployeeQueries_employee\n        id\n        __typename\n      }\n      cursor\n    }\n    pageInfo {\n      hasNextPage\n      endCursor\n    }\n    totalCount\n  }\n}\n"
  }
};
})();

(node as any).hash = "2a3185e656ed8b3114f5b6123e49181c";

export default node;
