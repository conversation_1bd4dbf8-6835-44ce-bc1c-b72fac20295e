/**
 * @generated SignedSource<<6741f3a8248ba5ba42d773b58969f498>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ReaderFragment } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type useEmployeeDefaultSettingsQueries_EmployeeDefaultsNode$data = {
  readonly defaultAgreementId: number | null | undefined;
  readonly defaultClassificationId: number | null | undefined;
  readonly defaultHourlyRate: any | null | undefined;
  readonly employeeId: string | null | undefined;
  readonly " $fragmentType": "useEmployeeDefaultSettingsQueries_EmployeeDefaultsNode";
};
export type useEmployeeDefaultSettingsQueries_EmployeeDefaultsNode$key = {
  readonly " $data"?: useEmployeeDefaultSettingsQueries_EmployeeDefaultsNode$data;
  readonly " $fragmentSpreads": FragmentRefs<"useEmployeeDefaultSettingsQueries_EmployeeDefaultsNode">;
};

const node: ReaderFragment = {
  "argumentDefinitions": [],
  "kind": "Fragment",
  "metadata": null,
  "name": "useEmployeeDefaultSettingsQueries_EmployeeDefaultsNode",
  "selections": [
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "employeeId",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "defaultAgreementId",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "defaultClassificationId",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "defaultHourlyRate",
      "storageKey": null
    }
  ],
  "type": "EmployeeDefaultSettings",
  "abstractKey": null
};

(node as any).hash = "7b24d3b285a4e6d53367ac22f2252d04";

export default node;
