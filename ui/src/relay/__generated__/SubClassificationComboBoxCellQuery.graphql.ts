/**
 * @generated SignedSource<<8767627b2a0d479f38e850e6b57fd258>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ConcreteRequest } from 'relay-runtime';
export type SubClassificationComboBoxCellQuery$variables = {
  agreementId: string;
  classificationId: string;
};
export type SubClassificationComboBoxCellQuery$data = {
  readonly subClassificationsByAgreementAndClassification: {
    readonly nodes: ReadonlyArray<{
      readonly id: string;
      readonly name: string;
    }> | null | undefined;
  } | null | undefined;
};
export type SubClassificationComboBoxCellQuery = {
  response: SubClassificationComboBoxCellQuery$data;
  variables: SubClassificationComboBoxCellQuery$variables;
};

const node: ConcreteRequest = (function(){
var v0 = [
  {
    "defaultValue": null,
    "kind": "LocalArgument",
    "name": "agreementId"
  },
  {
    "defaultValue": null,
    "kind": "LocalArgument",
    "name": "classificationId"
  }
],
v1 = [
  {
    "alias": null,
    "args": [
      {
        "kind": "Variable",
        "name": "agreementId",
        "variableName": "agreementId"
      },
      {
        "kind": "Variable",
        "name": "classificationId",
        "variableName": "classificationId"
      }
    ],
    "concreteType": "SubClassificationConnection",
    "kind": "LinkedField",
    "name": "subClassificationsByAgreementAndClassification",
    "plural": false,
    "selections": [
      {
        "alias": null,
        "args": null,
        "concreteType": "SubClassification",
        "kind": "LinkedField",
        "name": "nodes",
        "plural": true,
        "selections": [
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "id",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "name",
            "storageKey": null
          }
        ],
        "storageKey": null
      }
    ],
    "storageKey": null
  }
];
return {
  "fragment": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Fragment",
    "metadata": null,
    "name": "SubClassificationComboBoxCellQuery",
    "selections": (v1/*: any*/),
    "type": "Query",
    "abstractKey": null
  },
  "kind": "Request",
  "operation": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Operation",
    "name": "SubClassificationComboBoxCellQuery",
    "selections": (v1/*: any*/)
  },
  "params": {
    "cacheID": "11cec34f447279eda477e58e3510999d",
    "id": null,
    "metadata": {},
    "name": "SubClassificationComboBoxCellQuery",
    "operationKind": "query",
    "text": "query SubClassificationComboBoxCellQuery(\n  $agreementId: ID!\n  $classificationId: ID!\n) {\n  subClassificationsByAgreementAndClassification(agreementId: $agreementId, classificationId: $classificationId) {\n    nodes {\n      id\n      name\n    }\n  }\n}\n"
  }
};
})();

(node as any).hash = "9924059021afc3ebf56d8adcf3ade441";

export default node;
