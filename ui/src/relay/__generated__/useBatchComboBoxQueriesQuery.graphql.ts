/**
 * @generated SignedSource<<9e67a409051376e235cdeff0834b6713>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ConcreteRequest } from 'relay-runtime';
export type useBatchComboBoxQueriesQuery$variables = {
  agreementId: string;
  classificationId: string;
};
export type useBatchComboBoxQueriesQuery$data = {
  readonly classificationsByAgreementId: {
    readonly nodes: ReadonlyArray<{
      readonly id: string;
      readonly name: string;
    }> | null | undefined;
  } | null | undefined;
  readonly subClassificationsByAgreementAndClassification: {
    readonly nodes: ReadonlyArray<{
      readonly id: string;
      readonly name: string;
    }> | null | undefined;
  } | null | undefined;
};
export type useBatchComboBoxQueriesQuery = {
  response: useBatchComboBoxQueriesQuery$data;
  variables: useBatchComboBoxQueriesQuery$variables;
};

const node: ConcreteRequest = (function(){
var v0 = [
  {
    "defaultValue": null,
    "kind": "LocalArgument",
    "name": "agreementId"
  },
  {
    "defaultValue": null,
    "kind": "LocalArgument",
    "name": "classificationId"
  }
],
v1 = {
  "kind": "Variable",
  "name": "agreementId",
  "variableName": "agreementId"
},
v2 = [
  {
    "alias": null,
    "args": null,
    "kind": "ScalarField",
    "name": "id",
    "storageKey": null
  },
  {
    "alias": null,
    "args": null,
    "kind": "ScalarField",
    "name": "name",
    "storageKey": null
  }
],
v3 = [
  {
    "alias": null,
    "args": [
      (v1/*: any*/)
    ],
    "concreteType": "ClassificationNameConnection",
    "kind": "LinkedField",
    "name": "classificationsByAgreementId",
    "plural": false,
    "selections": [
      {
        "alias": null,
        "args": null,
        "concreteType": "ClassificationName",
        "kind": "LinkedField",
        "name": "nodes",
        "plural": true,
        "selections": (v2/*: any*/),
        "storageKey": null
      }
    ],
    "storageKey": null
  },
  {
    "alias": null,
    "args": [
      (v1/*: any*/),
      {
        "kind": "Variable",
        "name": "classificationId",
        "variableName": "classificationId"
      }
    ],
    "concreteType": "SubClassificationConnection",
    "kind": "LinkedField",
    "name": "subClassificationsByAgreementAndClassification",
    "plural": false,
    "selections": [
      {
        "alias": null,
        "args": null,
        "concreteType": "SubClassification",
        "kind": "LinkedField",
        "name": "nodes",
        "plural": true,
        "selections": (v2/*: any*/),
        "storageKey": null
      }
    ],
    "storageKey": null
  }
];
return {
  "fragment": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Fragment",
    "metadata": null,
    "name": "useBatchComboBoxQueriesQuery",
    "selections": (v3/*: any*/),
    "type": "Query",
    "abstractKey": null
  },
  "kind": "Request",
  "operation": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Operation",
    "name": "useBatchComboBoxQueriesQuery",
    "selections": (v3/*: any*/)
  },
  "params": {
    "cacheID": "b1e355fccb746eb4298ce1f79d65d07f",
    "id": null,
    "metadata": {},
    "name": "useBatchComboBoxQueriesQuery",
    "operationKind": "query",
    "text": "query useBatchComboBoxQueriesQuery(\n  $agreementId: ID!\n  $classificationId: ID!\n) {\n  classificationsByAgreementId(agreementId: $agreementId) {\n    nodes {\n      id\n      name\n    }\n  }\n  subClassificationsByAgreementAndClassification(agreementId: $agreementId, classificationId: $classificationId) {\n    nodes {\n      id\n      name\n    }\n  }\n}\n"
  }
};
})();

(node as any).hash = "bf11052a6a87b4b6ed3fc229e67ad2e1";

export default node;
