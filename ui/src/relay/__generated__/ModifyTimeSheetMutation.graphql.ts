/**
 * @generated SignedSource<<1936c7155a0f84bcd5dbadc00395ddde>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ConcreteRequest } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type ModifyTimeSheetInput = {
  addPayStubs?: ReadonlyArray<AddPayStubInput> | null | undefined;
  deletePayStubIds?: ReadonlyArray<string> | null | undefined;
  employerGuid: any;
  id: string;
  modificationDate?: any | null | undefined;
  modifyPayStubs?: ReadonlyArray<ModifyPayStubInput> | null | undefined;
  name?: string | null | undefined;
  readOnly?: boolean | null | undefined;
  showBonusColumn?: boolean | null | undefined;
  showCostCenterColumn?: boolean | null | undefined;
  showDTHoursColumn?: boolean | null | undefined;
  showEarningsCodesColumn?: boolean | null | undefined;
  showExpensesColumn?: boolean | null | undefined;
  status?: string | null | undefined;
  timeSheetId?: any | null | undefined;
  type?: string | null | undefined;
};
export type AddPayStubInput = {
  bonus?: number | null | undefined;
  delete?: boolean | null | undefined;
  details?: ReadonlyArray<AddPayStubDetailInput> | null | undefined;
  dtHours?: number | null | undefined;
  employeeId: string;
  employeeName?: string | null | undefined;
  expanded?: boolean | null | undefined;
  expenses?: number | null | undefined;
  id?: string | null | undefined;
  inEdit?: boolean | null | undefined;
  name?: string | null | undefined;
  otHours?: number | null | undefined;
  stHours?: number | null | undefined;
};
export type AddPayStubDetailInput = {
  agreementId?: number | null | undefined;
  bonus?: number | null | undefined;
  classificationId?: number | null | undefined;
  costCenter?: string | null | undefined;
  delete?: boolean | null | undefined;
  dtHours?: number | null | undefined;
  earningsCode?: string | null | undefined;
  expenses?: number | null | undefined;
  hourlyRate?: number | null | undefined;
  id?: string | null | undefined;
  jobCode?: string | null | undefined;
  name?: string | null | undefined;
  otHours?: number | null | undefined;
  payStubId?: string | null | undefined;
  reportLineItemId?: number | null | undefined;
  stHours?: number | null | undefined;
  subClassificationId?: number | null | undefined;
  workDate: any;
};
export type ModifyPayStubInput = {
  bonus?: number | null | undefined;
  details?: ReadonlyArray<ModifyPayStubDetailInput> | null | undefined;
  dtHours?: number | null | undefined;
  employeeId: string;
  employeeName?: string | null | undefined;
  expanded?: boolean | null | undefined;
  expenses?: number | null | undefined;
  id: string;
  inEdit?: boolean | null | undefined;
  name?: string | null | undefined;
  otHours?: number | null | undefined;
  stHours?: number | null | undefined;
};
export type ModifyPayStubDetailInput = {
  agreementId?: number | null | undefined;
  bonus?: number | null | undefined;
  classificationId?: number | null | undefined;
  costCenter?: string | null | undefined;
  delete?: boolean | null | undefined;
  dtHours?: number | null | undefined;
  earningsCode?: string | null | undefined;
  expenses?: number | null | undefined;
  hourlyRate?: number | null | undefined;
  id?: string | null | undefined;
  jobCode?: string | null | undefined;
  name?: string | null | undefined;
  otHours?: number | null | undefined;
  payStubId?: string | null | undefined;
  reportLineItemId?: number | null | undefined;
  stHours?: number | null | undefined;
  subClassificationId?: number | null | undefined;
  workDate: any;
};
export type ModifyTimeSheetMutation$variables = {
  input: ModifyTimeSheetInput;
};
export type ModifyTimeSheetMutation$data = {
  readonly modifyTimeSheet: {
    readonly timeSheet: {
      readonly id: string;
      readonly " $fragmentSpreads": FragmentRefs<"TimesheetDetail_timeSheet">;
    } | null | undefined;
  };
};
export type ModifyTimeSheetMutation = {
  response: ModifyTimeSheetMutation$data;
  variables: ModifyTimeSheetMutation$variables;
};

const node: ConcreteRequest = (function(){
var v0 = [
  {
    "defaultValue": null,
    "kind": "LocalArgument",
    "name": "input"
  }
],
v1 = [
  {
    "kind": "Variable",
    "name": "input",
    "variableName": "input"
  }
],
v2 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "id",
  "storageKey": null
},
v3 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "name",
  "storageKey": null
},
v4 = [
  {
    "kind": "Literal",
    "name": "first",
    "value": 500
  }
],
v5 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "cursor",
  "storageKey": null
},
v6 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "employeeId",
  "storageKey": null
},
v7 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "firstName",
  "storageKey": null
},
v8 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "lastName",
  "storageKey": null
},
v9 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "active",
  "storageKey": null
},
v10 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "externalEmployeeId",
  "storageKey": null
},
v11 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "totalHours",
  "storageKey": null
},
v12 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "payStubId",
  "storageKey": null
},
v13 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "reportLineItemId",
  "storageKey": null
},
v14 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "workDate",
  "storageKey": null
},
v15 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "stHours",
  "storageKey": null
},
v16 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "otHours",
  "storageKey": null
},
v17 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "dtHours",
  "storageKey": null
},
v18 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "bonus",
  "storageKey": null
},
v19 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "expenses",
  "storageKey": null
},
v20 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "jobCode",
  "storageKey": null
},
v21 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "agreementId",
  "storageKey": null
},
v22 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "classificationId",
  "storageKey": null
},
v23 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "subClassificationId",
  "storageKey": null
},
v24 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "costCenter",
  "storageKey": null
},
v25 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "hourlyRate",
  "storageKey": null
},
v26 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "earningsCode",
  "storageKey": null
},
v27 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "__typename",
  "storageKey": null
},
v28 = {
  "alias": null,
  "args": null,
  "concreteType": "PageInfo",
  "kind": "LinkedField",
  "name": "pageInfo",
  "plural": false,
  "selections": [
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "hasNextPage",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "hasPreviousPage",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "startCursor",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "endCursor",
      "storageKey": null
    }
  ],
  "storageKey": null
},
v29 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "totalCount",
  "storageKey": null
};
return {
  "fragment": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Fragment",
    "metadata": null,
    "name": "ModifyTimeSheetMutation",
    "selections": [
      {
        "alias": null,
        "args": (v1/*: any*/),
        "concreteType": "ModifyTimeSheetPayload",
        "kind": "LinkedField",
        "name": "modifyTimeSheet",
        "plural": false,
        "selections": [
          {
            "alias": null,
            "args": null,
            "concreteType": "TimeSheet",
            "kind": "LinkedField",
            "name": "timeSheet",
            "plural": false,
            "selections": [
              (v2/*: any*/),
              {
                "args": null,
                "kind": "FragmentSpread",
                "name": "TimesheetDetail_timeSheet"
              }
            ],
            "storageKey": null
          }
        ],
        "storageKey": null
      }
    ],
    "type": "Mutation",
    "abstractKey": null
  },
  "kind": "Request",
  "operation": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Operation",
    "name": "ModifyTimeSheetMutation",
    "selections": [
      {
        "alias": null,
        "args": (v1/*: any*/),
        "concreteType": "ModifyTimeSheetPayload",
        "kind": "LinkedField",
        "name": "modifyTimeSheet",
        "plural": false,
        "selections": [
          {
            "alias": null,
            "args": null,
            "concreteType": "TimeSheet",
            "kind": "LinkedField",
            "name": "timeSheet",
            "plural": false,
            "selections": [
              (v2/*: any*/),
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "numericId",
                "storageKey": null
              },
              (v3/*: any*/),
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "status",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "payPeriodEndDate",
                "storageKey": null
              },
              {
                "alias": null,
                "args": (v4/*: any*/),
                "concreteType": "PayStubConnection",
                "kind": "LinkedField",
                "name": "payStubs",
                "plural": false,
                "selections": [
                  {
                    "alias": null,
                    "args": null,
                    "concreteType": "PayStubEdge",
                    "kind": "LinkedField",
                    "name": "edges",
                    "plural": true,
                    "selections": [
                      (v5/*: any*/),
                      {
                        "alias": null,
                        "args": null,
                        "concreteType": "PayStub",
                        "kind": "LinkedField",
                        "name": "node",
                        "plural": false,
                        "selections": [
                          (v2/*: any*/),
                          (v6/*: any*/),
                          {
                            "alias": null,
                            "args": null,
                            "concreteType": "Employee",
                            "kind": "LinkedField",
                            "name": "employee",
                            "plural": false,
                            "selections": [
                              (v2/*: any*/),
                              (v7/*: any*/),
                              (v8/*: any*/),
                              (v9/*: any*/),
                              (v10/*: any*/)
                            ],
                            "storageKey": null
                          },
                          (v3/*: any*/),
                          (v11/*: any*/),
                          {
                            "alias": null,
                            "args": null,
                            "concreteType": "PayStubDetail",
                            "kind": "LinkedField",
                            "name": "details",
                            "plural": true,
                            "selections": [
                              (v2/*: any*/),
                              (v12/*: any*/),
                              (v13/*: any*/),
                              (v14/*: any*/),
                              (v3/*: any*/),
                              (v15/*: any*/),
                              (v16/*: any*/),
                              (v17/*: any*/),
                              (v11/*: any*/),
                              (v18/*: any*/),
                              (v19/*: any*/),
                              (v20/*: any*/),
                              (v21/*: any*/),
                              (v22/*: any*/),
                              (v23/*: any*/),
                              (v24/*: any*/),
                              (v25/*: any*/),
                              (v26/*: any*/)
                            ],
                            "storageKey": null
                          },
                          (v27/*: any*/)
                        ],
                        "storageKey": null
                      }
                    ],
                    "storageKey": null
                  },
                  (v28/*: any*/),
                  (v29/*: any*/)
                ],
                "storageKey": "payStubs(first:500)"
              },
              {
                "alias": null,
                "args": (v4/*: any*/),
                "filters": null,
                "handle": "connection",
                "key": "PayStubTable_connectionFragment__payStubs",
                "kind": "LinkedHandle",
                "name": "payStubs"
              },
              {
                "alias": null,
                "args": (v4/*: any*/),
                "filters": null,
                "handle": "connection",
                "key": "TimesheetToolbar_timeSheet_payStubs",
                "kind": "LinkedHandle",
                "name": "payStubs"
              },
              {
                "alias": null,
                "args": (v4/*: any*/),
                "filters": null,
                "handle": "connection",
                "key": "UploadTimeSheetFragments_timeSheetConsolidated_payStubs",
                "kind": "LinkedHandle",
                "name": "payStubs"
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "employerGuid",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "showBonusColumn",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "showCostCenterColumn",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "showDTHoursColumn",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "showEarningsCodesColumn",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "showExpensesColumn",
                "storageKey": null
              },
              {
                "alias": "payStubsConnection",
                "args": (v4/*: any*/),
                "concreteType": "PayStubConnection",
                "kind": "LinkedField",
                "name": "payStubs",
                "plural": false,
                "selections": [
                  {
                    "alias": null,
                    "args": null,
                    "concreteType": "PayStubEdge",
                    "kind": "LinkedField",
                    "name": "edges",
                    "plural": true,
                    "selections": [
                      (v5/*: any*/),
                      {
                        "alias": null,
                        "args": null,
                        "concreteType": "PayStub",
                        "kind": "LinkedField",
                        "name": "node",
                        "plural": false,
                        "selections": [
                          (v2/*: any*/),
                          (v6/*: any*/),
                          {
                            "alias": null,
                            "args": null,
                            "concreteType": "Employee",
                            "kind": "LinkedField",
                            "name": "employee",
                            "plural": false,
                            "selections": [
                              (v2/*: any*/),
                              (v7/*: any*/),
                              (v8/*: any*/),
                              (v10/*: any*/),
                              (v9/*: any*/)
                            ],
                            "storageKey": null
                          },
                          {
                            "alias": null,
                            "args": null,
                            "concreteType": "PayStubDetail",
                            "kind": "LinkedField",
                            "name": "details",
                            "plural": true,
                            "selections": [
                              (v2/*: any*/),
                              (v12/*: any*/),
                              (v13/*: any*/),
                              (v14/*: any*/),
                              (v3/*: any*/),
                              (v15/*: any*/),
                              (v16/*: any*/),
                              (v17/*: any*/),
                              (v20/*: any*/),
                              (v26/*: any*/),
                              (v21/*: any*/),
                              (v22/*: any*/),
                              (v23/*: any*/),
                              (v24/*: any*/),
                              (v25/*: any*/),
                              (v18/*: any*/),
                              (v19/*: any*/)
                            ],
                            "storageKey": null
                          },
                          (v27/*: any*/)
                        ],
                        "storageKey": null
                      }
                    ],
                    "storageKey": null
                  },
                  (v28/*: any*/),
                  (v29/*: any*/)
                ],
                "storageKey": "payStubs(first:500)"
              },
              {
                "alias": "payStubsConnection",
                "args": (v4/*: any*/),
                "filters": null,
                "handle": "connection",
                "key": "TimeSheetPayStubsConnectionFragment_payStubsConnection",
                "kind": "LinkedHandle",
                "name": "payStubs"
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "payStubCount",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "creationDate",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "modificationDate",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "modifiedByUserId",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "type",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "hoursWorked",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "oldId",
                "storageKey": null
              }
            ],
            "storageKey": null
          }
        ],
        "storageKey": null
      }
    ]
  },
  "params": {
    "cacheID": "b7b75f0fc1ba32e3fc98aba2fda94212",
    "id": null,
    "metadata": {},
    "name": "ModifyTimeSheetMutation",
    "operationKind": "mutation",
    "text": "mutation ModifyTimeSheetMutation(\n  $input: ModifyTimeSheetInput!\n) {\n  modifyTimeSheet(input: $input) {\n    timeSheet {\n      id\n      ...TimesheetDetail_timeSheet\n    }\n  }\n}\n\nfragment EmployeeDisplayFragment_employee on Employee {\n  id\n  firstName\n  lastName\n  active\n  externalEmployeeId\n}\n\nfragment PayStubTable_connectionFragment on TimeSheet {\n  id\n  payStubs(first: 500) {\n    edges {\n      cursor\n      node {\n        ...PayStubTable_payStub\n        ...PayStubTable_payStubs\n        id\n        __typename\n      }\n    }\n    pageInfo {\n      hasNextPage\n      hasPreviousPage\n      startCursor\n      endCursor\n    }\n    totalCount\n  }\n}\n\nfragment PayStubTable_payStub on PayStub {\n  id\n  employeeId\n  employee {\n    id\n    ...EmployeeDisplayFragment_employee\n    ...TimeSheetDetailRow_employee\n  }\n  name\n  totalHours\n  details {\n    id\n    payStubId\n    reportLineItemId\n    workDate\n    name\n    stHours\n    otHours\n    dtHours\n    totalHours\n    bonus\n    expenses\n    jobCode\n    agreementId\n    classificationId\n    subClassificationId\n    costCenter\n    hourlyRate\n    earningsCode\n    ...TimeSheetDetailRow_payStubDetail\n  }\n  ...TimeSheetDetailTableView_payStub\n}\n\nfragment PayStubTable_payStubs on PayStub {\n  id\n  employeeId\n  name\n  totalHours\n  details {\n    id\n    payStubId\n    stHours\n    otHours\n    dtHours\n    totalHours\n    bonus\n    expenses\n  }\n}\n\nfragment TimeSheetDetailRow_employee on Employee {\n  id\n  externalEmployeeId\n  firstName\n  lastName\n  active\n}\n\nfragment TimeSheetDetailRow_payStubDetail on PayStubDetail {\n  id\n  payStubId\n  workDate\n  jobCode\n  agreementId\n  classificationId\n  subClassificationId\n  hourlyRate\n  stHours\n  otHours\n  dtHours\n  bonus\n  expenses\n  earningsCode\n  costCenter\n}\n\nfragment TimeSheetDetailTableView_payStub on PayStub {\n  id\n  employeeId\n  name\n  totalHours\n  employee {\n    ...TimeSheetDetailRow_employee\n    id\n  }\n  details {\n    id\n    workDate\n    jobCode\n    agreementId\n    classificationId\n    subClassificationId\n    hourlyRate\n    stHours\n    otHours\n    dtHours\n    bonus\n    expenses\n    earningsCode\n    costCenter\n    payStubId\n    reportLineItemId\n    totalHours\n    name\n    ...TimeSheetDetailRow_payStubDetail\n  }\n}\n\nfragment TimeSheetGrid_timeSheet on TimeSheet {\n  payPeriodEndDate\n  ...PayStubTable_connectionFragment\n  ...TimesheetToolbar_timeSheet\n}\n\nfragment TimeSheetPayStubsConnectionFragment_timeSheet on TimeSheet {\n  id\n  payStubsConnection: payStubs(first: 500) {\n    edges {\n      cursor\n      node {\n        id\n        employeeId\n        employee {\n          id\n          firstName\n          lastName\n          externalEmployeeId\n          active\n        }\n        details {\n          id\n          payStubId\n          reportLineItemId\n          workDate\n          name\n          stHours\n          otHours\n          dtHours\n          jobCode\n          earningsCode\n          agreementId\n          classificationId\n          subClassificationId\n          costCenter\n          hourlyRate\n          bonus\n          expenses\n        }\n        __typename\n      }\n    }\n    pageInfo {\n      hasNextPage\n      hasPreviousPage\n      startCursor\n      endCursor\n    }\n    totalCount\n  }\n  payStubCount\n}\n\nfragment TimeSheetSettings_timeSheet on TimeSheet {\n  id\n  showBonusColumn\n  showCostCenterColumn\n  showDTHoursColumn\n  showEarningsCodesColumn\n  showExpensesColumn\n}\n\nfragment TimesheetDetailView_timeSheet on TimeSheet {\n  numericId\n  name\n  status\n  payPeriodEndDate\n  ...TimeSheetGrid_timeSheet\n}\n\nfragment TimesheetDetail_timeSheet on TimeSheet {\n  ...TimesheetDetailView_timeSheet\n  ...TimeSheetGrid_timeSheet\n  ...TimeSheetSettings_timeSheet\n  ...PayStubTable_connectionFragment\n  ...TimeSheetPayStubsConnectionFragment_timeSheet\n  id\n  numericId\n  name\n  status\n  payPeriodEndDate\n  showBonusColumn\n  showCostCenterColumn\n  showDTHoursColumn\n  showEarningsCodesColumn\n  showExpensesColumn\n  employerGuid\n  creationDate\n  modificationDate\n  modifiedByUserId\n  type\n  hoursWorked\n  oldId\n  ...TimesheetToolbar_timeSheet\n}\n\nfragment TimesheetToolbar_timeSheet on TimeSheet {\n  id\n  numericId\n  employerGuid\n  payStubs(first: 500) {\n    edges {\n      node {\n        id\n        details {\n          id\n        }\n        __typename\n      }\n      cursor\n    }\n    pageInfo {\n      endCursor\n      hasNextPage\n    }\n  }\n  ...TimeSheetSettings_timeSheet\n  ...UploadTimeSheetFragments_timeSheetConsolidated\n}\n\nfragment UploadTimeSheetFragments_timeSheetConsolidated on TimeSheet {\n  id\n  numericId\n  employerGuid\n  payStubs(first: 500) {\n    edges {\n      node {\n        id\n        employeeId\n        name\n        __typename\n      }\n      cursor\n    }\n    pageInfo {\n      endCursor\n      hasNextPage\n    }\n  }\n}\n"
  }
};
})();

(node as any).hash = "31f12d36b62fe24ed75e2e453e3f0fa9";

export default node;
