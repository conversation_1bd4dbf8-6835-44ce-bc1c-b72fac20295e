/**
 * @generated SignedSource<<8007e1a624ec93fdee00e4bcd301603b>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ReaderFragment } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type fragments_DropdownOption$data = {
  readonly id: string;
  readonly name?: string;
  readonly " $fragmentType": "fragments_DropdownOption";
};
export type fragments_DropdownOption$key = {
  readonly " $data"?: fragments_DropdownOption$data;
  readonly " $fragmentSpreads": FragmentRefs<"fragments_DropdownOption">;
};

const node: ReaderFragment = (function(){
var v0 = [
  {
    "alias": null,
    "args": null,
    "kind": "ScalarField",
    "name": "name",
    "storageKey": null
  }
];
return {
  "argumentDefinitions": [],
  "kind": "Fragment",
  "metadata": null,
  "name": "fragments_DropdownOption",
  "selections": [
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "id",
      "storageKey": null
    },
    {
      "kind": "InlineFragment",
      "selections": (v0/*: any*/),
      "type": "Agreement",
      "abstractKey": null
    },
    {
      "kind": "InlineFragment",
      "selections": (v0/*: any*/),
      "type": "ClassificationName",
      "abstractKey": null
    },
    {
      "kind": "InlineFragment",
      "selections": (v0/*: any*/),
      "type": "SubClassification",
      "abstractKey": null
    }
  ],
  "type": "Node",
  "abstractKey": "__isNode"
};
})();

(node as any).hash = "fc098167777e1eb61153c23224f89ab2";

export default node;
