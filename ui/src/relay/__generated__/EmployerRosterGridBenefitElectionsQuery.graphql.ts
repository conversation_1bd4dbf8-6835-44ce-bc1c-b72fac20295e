/**
 * @generated SignedSource<<2a8a86c80b881dda9bee8035a3c97656>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ConcreteRequest } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type SortEnumType = "ASC" | "DESC" | "%future added value";
export type BenefitElectionsRosterDtoSortInput = {
  associationID?: SortEnumType | null | undefined;
  benefitName?: SortEnumType | null | undefined;
  dba?: SortEnumType | null | undefined;
  discount?: SortEnumType | null | undefined;
  effectiveEndDate?: SortEnumType | null | undefined;
  effectiveStartDate?: SortEnumType | null | undefined;
  elected?: SortEnumType | null | undefined;
  employerGUID?: SortEnumType | null | undefined;
  employerName?: SortEnumType | null | undefined;
  fein?: SortEnumType | null | undefined;
  id?: SortEnumType | null | undefined;
  lastReport?: SortEnumType | null | undefined;
  override?: SortEnumType | null | undefined;
};
export type BenefitElectionsRosterDtoFilterInput = {
  and?: ReadonlyArray<BenefitElectionsRosterDtoFilterInput> | null | undefined;
  associationID?: StringOperationFilterInput | null | undefined;
  benefitName?: StringOperationFilterInput | null | undefined;
  dba?: StringOperationFilterInput | null | undefined;
  discount?: DecimalOperationFilterInput | null | undefined;
  effectiveEndDate?: DateTimeOperationFilterInput | null | undefined;
  effectiveStartDate?: DateTimeOperationFilterInput | null | undefined;
  elected?: BooleanOperationFilterInput | null | undefined;
  employerGUID?: UuidOperationFilterInput | null | undefined;
  employerName?: StringOperationFilterInput | null | undefined;
  fein?: StringOperationFilterInput | null | undefined;
  id?: IdOperationFilterInput | null | undefined;
  lastReport?: DateTimeOperationFilterInput | null | undefined;
  or?: ReadonlyArray<BenefitElectionsRosterDtoFilterInput> | null | undefined;
  override?: DecimalOperationFilterInput | null | undefined;
};
export type IdOperationFilterInput = {
  eq?: string | null | undefined;
  in?: ReadonlyArray<string | null | undefined> | null | undefined;
  neq?: string | null | undefined;
  nin?: ReadonlyArray<string | null | undefined> | null | undefined;
};
export type StringOperationFilterInput = {
  and?: ReadonlyArray<StringOperationFilterInput> | null | undefined;
  contains?: string | null | undefined;
  endsWith?: string | null | undefined;
  eq?: string | null | undefined;
  in?: ReadonlyArray<string | null | undefined> | null | undefined;
  ncontains?: string | null | undefined;
  nendsWith?: string | null | undefined;
  neq?: string | null | undefined;
  nin?: ReadonlyArray<string | null | undefined> | null | undefined;
  nstartsWith?: string | null | undefined;
  or?: ReadonlyArray<StringOperationFilterInput> | null | undefined;
  startsWith?: string | null | undefined;
};
export type DecimalOperationFilterInput = {
  eq?: any | null | undefined;
  gt?: any | null | undefined;
  gte?: any | null | undefined;
  in?: ReadonlyArray<any | null | undefined> | null | undefined;
  lt?: any | null | undefined;
  lte?: any | null | undefined;
  neq?: any | null | undefined;
  ngt?: any | null | undefined;
  ngte?: any | null | undefined;
  nin?: ReadonlyArray<any | null | undefined> | null | undefined;
  nlt?: any | null | undefined;
  nlte?: any | null | undefined;
};
export type DateTimeOperationFilterInput = {
  eq?: any | null | undefined;
  gt?: any | null | undefined;
  gte?: any | null | undefined;
  in?: ReadonlyArray<any | null | undefined> | null | undefined;
  lt?: any | null | undefined;
  lte?: any | null | undefined;
  neq?: any | null | undefined;
  ngt?: any | null | undefined;
  ngte?: any | null | undefined;
  nin?: ReadonlyArray<any | null | undefined> | null | undefined;
  nlt?: any | null | undefined;
  nlte?: any | null | undefined;
};
export type BooleanOperationFilterInput = {
  eq?: boolean | null | undefined;
  neq?: boolean | null | undefined;
};
export type UuidOperationFilterInput = {
  eq?: any | null | undefined;
  gt?: any | null | undefined;
  gte?: any | null | undefined;
  in?: ReadonlyArray<any | null | undefined> | null | undefined;
  lt?: any | null | undefined;
  lte?: any | null | undefined;
  neq?: any | null | undefined;
  ngt?: any | null | undefined;
  ngte?: any | null | undefined;
  nin?: ReadonlyArray<any | null | undefined> | null | undefined;
  nlt?: any | null | undefined;
  nlte?: any | null | undefined;
};
export type EmployerRosterGridBenefitElectionsQuery$variables = {
  benefitElectionsRosterOrder?: ReadonlyArray<BenefitElectionsRosterDtoSortInput> | null | undefined;
  benefitElectionsRosterWhere: BenefitElectionsRosterDtoFilterInput;
  chapterId: string;
};
export type EmployerRosterGridBenefitElectionsQuery$data = {
  readonly " $fragmentSpreads": FragmentRefs<"BenefitElectionsTableFragment">;
};
export type EmployerRosterGridBenefitElectionsQuery = {
  response: EmployerRosterGridBenefitElectionsQuery$data;
  variables: EmployerRosterGridBenefitElectionsQuery$variables;
};

const node: ConcreteRequest = (function(){
var v0 = {
  "defaultValue": null,
  "kind": "LocalArgument",
  "name": "benefitElectionsRosterOrder"
},
v1 = {
  "defaultValue": null,
  "kind": "LocalArgument",
  "name": "benefitElectionsRosterWhere"
},
v2 = {
  "defaultValue": null,
  "kind": "LocalArgument",
  "name": "chapterId"
},
v3 = {
  "kind": "Variable",
  "name": "chapterId",
  "variableName": "chapterId"
},
v4 = {
  "kind": "Variable",
  "name": "order",
  "variableName": "benefitElectionsRosterOrder"
},
v5 = {
  "kind": "Variable",
  "name": "where",
  "variableName": "benefitElectionsRosterWhere"
},
v6 = [
  (v3/*: any*/),
  {
    "kind": "Literal",
    "name": "first",
    "value": 20
  },
  (v4/*: any*/),
  (v5/*: any*/)
];
return {
  "fragment": {
    "argumentDefinitions": [
      (v0/*: any*/),
      (v1/*: any*/),
      (v2/*: any*/)
    ],
    "kind": "Fragment",
    "metadata": null,
    "name": "EmployerRosterGridBenefitElectionsQuery",
    "selections": [
      {
        "args": [
          (v3/*: any*/),
          (v4/*: any*/),
          (v5/*: any*/)
        ],
        "kind": "FragmentSpread",
        "name": "BenefitElectionsTableFragment"
      }
    ],
    "type": "Query",
    "abstractKey": null
  },
  "kind": "Request",
  "operation": {
    "argumentDefinitions": [
      (v2/*: any*/),
      (v0/*: any*/),
      (v1/*: any*/)
    ],
    "kind": "Operation",
    "name": "EmployerRosterGridBenefitElectionsQuery",
    "selections": [
      {
        "alias": null,
        "args": (v6/*: any*/),
        "concreteType": "BenefitElectionsRosterDtoConnection",
        "kind": "LinkedField",
        "name": "benefitElectionRosterByChapterId",
        "plural": false,
        "selections": [
          {
            "alias": null,
            "args": null,
            "concreteType": "BenefitElectionsRosterDtoEdge",
            "kind": "LinkedField",
            "name": "edges",
            "plural": true,
            "selections": [
              {
                "alias": null,
                "args": null,
                "concreteType": "BenefitElectionsRosterDto",
                "kind": "LinkedField",
                "name": "node",
                "plural": false,
                "selections": [
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "id",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "employerName",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "fein",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "dba",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "benefitName",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "override",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "effectiveStartDate",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "effectiveEndDate",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "elected",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "employerGUID",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "associationID",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "discount",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "lastReport",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "__typename",
                    "storageKey": null
                  }
                ],
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "cursor",
                "storageKey": null
              }
            ],
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "concreteType": "PageInfo",
            "kind": "LinkedField",
            "name": "pageInfo",
            "plural": false,
            "selections": [
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "endCursor",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "hasNextPage",
                "storageKey": null
              }
            ],
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "totalCount",
            "storageKey": null
          }
        ],
        "storageKey": null
      },
      {
        "alias": null,
        "args": (v6/*: any*/),
        "filters": [
          "chapterId",
          "order",
          "where"
        ],
        "handle": "connection",
        "key": "BenefitElectionsTableFragment_benefitElectionRosterByChapterId",
        "kind": "LinkedHandle",
        "name": "benefitElectionRosterByChapterId"
      }
    ]
  },
  "params": {
    "cacheID": "99498f665b8be73fe7794788e62966b5",
    "id": null,
    "metadata": {},
    "name": "EmployerRosterGridBenefitElectionsQuery",
    "operationKind": "query",
    "text": "query EmployerRosterGridBenefitElectionsQuery(\n  $chapterId: ID!\n  $benefitElectionsRosterOrder: [BenefitElectionsRosterDtoSortInput!]\n  $benefitElectionsRosterWhere: BenefitElectionsRosterDtoFilterInput!\n) {\n  ...BenefitElectionsTableFragment_42Im1o\n}\n\nfragment BenefitElectionsTableFragment_42Im1o on Query {\n  benefitElectionRosterByChapterId(first: 20, chapterId: $chapterId, order: $benefitElectionsRosterOrder, where: $benefitElectionsRosterWhere) {\n    edges {\n      node {\n        id\n        employerName\n        fein\n        dba\n        benefitName\n        override\n        effectiveStartDate\n        effectiveEndDate\n        elected\n        employerGUID\n        associationID\n        discount\n        lastReport\n        __typename\n      }\n      cursor\n    }\n    pageInfo {\n      endCursor\n      hasNextPage\n    }\n    totalCount\n  }\n}\n"
  }
};
})();

(node as any).hash = "920cbeea2ac7328249fe3355e6fb9ef2";

export default node;
