/**
 * @generated SignedSource<<c9df218d84f41eb82336f99d9ee47806>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ConcreteRequest } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type useTimesheetDataQuery$variables = {
  timeSheetId: string;
};
export type useTimesheetDataQuery$data = {
  readonly timeSheetById: {
    readonly creationDate: any | null | undefined;
    readonly employerGuid: any;
    readonly hoursWorked: number;
    readonly modificationDate: any | null | undefined;
    readonly modifiedByUserId: string | null | undefined;
    readonly name: string | null | undefined;
    readonly numericId: number;
    readonly oldId: any | null | undefined;
    readonly payPeriodEndDate: any | null | undefined;
    readonly payStubs: {
      readonly edges: ReadonlyArray<{
        readonly node: {
          readonly details: ReadonlyArray<{
            readonly agreementId: number | null | undefined;
            readonly bonus: number | null | undefined;
            readonly classificationId: number | null | undefined;
            readonly costCenter: string | null | undefined;
            readonly dtHours: number | null | undefined;
            readonly earningsCode: string | null | undefined;
            readonly expenses: number | null | undefined;
            readonly hourlyRate: number | null | undefined;
            readonly id: string;
            readonly jobCode: string | null | undefined;
            readonly name: string | null | undefined;
            readonly otHours: number | null | undefined;
            readonly payStubId: string;
            readonly reportLineItemId: number | null | undefined;
            readonly stHours: number | null | undefined;
            readonly subClassificationId: number | null | undefined;
            readonly totalHours: number | null | undefined;
            readonly workDate: any;
          }>;
          readonly employee: {
            readonly active: boolean;
            readonly firstName: string | null | undefined;
            readonly id: string;
            readonly lastName: string;
          };
          readonly employeeId: string;
          readonly id: string;
          readonly name: string | null | undefined;
          readonly totalHours: number | null | undefined;
          readonly " $fragmentSpreads": FragmentRefs<"PayStubTable_payStub">;
        };
      }> | null | undefined;
    } | null | undefined;
    readonly showBonusColumn: boolean | null | undefined;
    readonly showCostCenterColumn: boolean | null | undefined;
    readonly showDTHoursColumn: boolean | null | undefined;
    readonly showEarningsCodesColumn: boolean | null | undefined;
    readonly showExpensesColumn: boolean | null | undefined;
    readonly status: string;
    readonly type: string;
    readonly " $fragmentSpreads": FragmentRefs<"TimesheetDetail_timeSheet">;
  } | null | undefined;
};
export type useTimesheetDataQuery = {
  response: useTimesheetDataQuery$data;
  variables: useTimesheetDataQuery$variables;
};

const node: ConcreteRequest = (function(){
var v0 = [
  {
    "defaultValue": null,
    "kind": "LocalArgument",
    "name": "timeSheetId"
  }
],
v1 = [
  {
    "kind": "Variable",
    "name": "id",
    "variableName": "timeSheetId"
  }
],
v2 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "numericId",
  "storageKey": null
},
v3 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "oldId",
  "storageKey": null
},
v4 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "employerGuid",
  "storageKey": null
},
v5 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "name",
  "storageKey": null
},
v6 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "hoursWorked",
  "storageKey": null
},
v7 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "payPeriodEndDate",
  "storageKey": null
},
v8 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "status",
  "storageKey": null
},
v9 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "type",
  "storageKey": null
},
v10 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "creationDate",
  "storageKey": null
},
v11 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "modificationDate",
  "storageKey": null
},
v12 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "showBonusColumn",
  "storageKey": null
},
v13 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "showCostCenterColumn",
  "storageKey": null
},
v14 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "showDTHoursColumn",
  "storageKey": null
},
v15 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "showEarningsCodesColumn",
  "storageKey": null
},
v16 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "showExpensesColumn",
  "storageKey": null
},
v17 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "modifiedByUserId",
  "storageKey": null
},
v18 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "id",
  "storageKey": null
},
v19 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "employeeId",
  "storageKey": null
},
v20 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "firstName",
  "storageKey": null
},
v21 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "lastName",
  "storageKey": null
},
v22 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "active",
  "storageKey": null
},
v23 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "totalHours",
  "storageKey": null
},
v24 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "payStubId",
  "storageKey": null
},
v25 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "reportLineItemId",
  "storageKey": null
},
v26 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "workDate",
  "storageKey": null
},
v27 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "stHours",
  "storageKey": null
},
v28 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "otHours",
  "storageKey": null
},
v29 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "dtHours",
  "storageKey": null
},
v30 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "jobCode",
  "storageKey": null
},
v31 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "agreementId",
  "storageKey": null
},
v32 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "classificationId",
  "storageKey": null
},
v33 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "subClassificationId",
  "storageKey": null
},
v34 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "costCenter",
  "storageKey": null
},
v35 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "earningsCode",
  "storageKey": null
},
v36 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "hourlyRate",
  "storageKey": null
},
v37 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "bonus",
  "storageKey": null
},
v38 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "expenses",
  "storageKey": null
},
v39 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "__typename",
  "storageKey": null
},
v40 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "cursor",
  "storageKey": null
},
v41 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "endCursor",
  "storageKey": null
},
v42 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "hasNextPage",
  "storageKey": null
},
v43 = [
  {
    "kind": "Literal",
    "name": "first",
    "value": 500
  }
],
v44 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "externalEmployeeId",
  "storageKey": null
},
v45 = {
  "alias": null,
  "args": null,
  "concreteType": "PageInfo",
  "kind": "LinkedField",
  "name": "pageInfo",
  "plural": false,
  "selections": [
    (v42/*: any*/),
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "hasPreviousPage",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "startCursor",
      "storageKey": null
    },
    (v41/*: any*/)
  ],
  "storageKey": null
},
v46 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "totalCount",
  "storageKey": null
};
return {
  "fragment": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Fragment",
    "metadata": null,
    "name": "useTimesheetDataQuery",
    "selections": [
      {
        "alias": null,
        "args": (v1/*: any*/),
        "concreteType": "TimeSheet",
        "kind": "LinkedField",
        "name": "timeSheetById",
        "plural": false,
        "selections": [
          {
            "args": null,
            "kind": "FragmentSpread",
            "name": "TimesheetDetail_timeSheet"
          },
          (v2/*: any*/),
          (v3/*: any*/),
          (v4/*: any*/),
          (v5/*: any*/),
          (v6/*: any*/),
          (v7/*: any*/),
          (v8/*: any*/),
          (v9/*: any*/),
          (v10/*: any*/),
          (v11/*: any*/),
          (v12/*: any*/),
          (v13/*: any*/),
          (v14/*: any*/),
          (v15/*: any*/),
          (v16/*: any*/),
          (v17/*: any*/),
          {
            "alias": "payStubs",
            "args": null,
            "concreteType": "PayStubConnection",
            "kind": "LinkedField",
            "name": "__useTimesheetData_payStubs_connection",
            "plural": false,
            "selections": [
              {
                "alias": null,
                "args": null,
                "concreteType": "PayStubEdge",
                "kind": "LinkedField",
                "name": "edges",
                "plural": true,
                "selections": [
                  {
                    "alias": null,
                    "args": null,
                    "concreteType": "PayStub",
                    "kind": "LinkedField",
                    "name": "node",
                    "plural": false,
                    "selections": [
                      {
                        "args": null,
                        "kind": "FragmentSpread",
                        "name": "PayStubTable_payStub"
                      },
                      (v18/*: any*/),
                      (v19/*: any*/),
                      {
                        "alias": null,
                        "args": null,
                        "concreteType": "Employee",
                        "kind": "LinkedField",
                        "name": "employee",
                        "plural": false,
                        "selections": [
                          (v18/*: any*/),
                          (v20/*: any*/),
                          (v21/*: any*/),
                          (v22/*: any*/)
                        ],
                        "storageKey": null
                      },
                      (v5/*: any*/),
                      (v23/*: any*/),
                      {
                        "alias": null,
                        "args": null,
                        "concreteType": "PayStubDetail",
                        "kind": "LinkedField",
                        "name": "details",
                        "plural": true,
                        "selections": [
                          (v18/*: any*/),
                          (v24/*: any*/),
                          (v25/*: any*/),
                          (v26/*: any*/),
                          (v5/*: any*/),
                          (v27/*: any*/),
                          (v28/*: any*/),
                          (v29/*: any*/),
                          (v23/*: any*/),
                          (v30/*: any*/),
                          (v31/*: any*/),
                          (v32/*: any*/),
                          (v33/*: any*/),
                          (v34/*: any*/),
                          (v35/*: any*/),
                          (v36/*: any*/),
                          (v37/*: any*/),
                          (v38/*: any*/)
                        ],
                        "storageKey": null
                      },
                      (v39/*: any*/)
                    ],
                    "storageKey": null
                  },
                  (v40/*: any*/)
                ],
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "concreteType": "PageInfo",
                "kind": "LinkedField",
                "name": "pageInfo",
                "plural": false,
                "selections": [
                  (v41/*: any*/),
                  (v42/*: any*/)
                ],
                "storageKey": null
              }
            ],
            "storageKey": null
          }
        ],
        "storageKey": null
      }
    ],
    "type": "Query",
    "abstractKey": null
  },
  "kind": "Request",
  "operation": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Operation",
    "name": "useTimesheetDataQuery",
    "selections": [
      {
        "alias": null,
        "args": (v1/*: any*/),
        "concreteType": "TimeSheet",
        "kind": "LinkedField",
        "name": "timeSheetById",
        "plural": false,
        "selections": [
          (v2/*: any*/),
          (v5/*: any*/),
          (v8/*: any*/),
          (v7/*: any*/),
          (v18/*: any*/),
          {
            "alias": null,
            "args": (v43/*: any*/),
            "concreteType": "PayStubConnection",
            "kind": "LinkedField",
            "name": "payStubs",
            "plural": false,
            "selections": [
              {
                "alias": null,
                "args": null,
                "concreteType": "PayStubEdge",
                "kind": "LinkedField",
                "name": "edges",
                "plural": true,
                "selections": [
                  (v40/*: any*/),
                  {
                    "alias": null,
                    "args": null,
                    "concreteType": "PayStub",
                    "kind": "LinkedField",
                    "name": "node",
                    "plural": false,
                    "selections": [
                      (v18/*: any*/),
                      (v19/*: any*/),
                      {
                        "alias": null,
                        "args": null,
                        "concreteType": "Employee",
                        "kind": "LinkedField",
                        "name": "employee",
                        "plural": false,
                        "selections": [
                          (v18/*: any*/),
                          (v20/*: any*/),
                          (v21/*: any*/),
                          (v22/*: any*/),
                          (v44/*: any*/)
                        ],
                        "storageKey": null
                      },
                      (v5/*: any*/),
                      (v23/*: any*/),
                      {
                        "alias": null,
                        "args": null,
                        "concreteType": "PayStubDetail",
                        "kind": "LinkedField",
                        "name": "details",
                        "plural": true,
                        "selections": [
                          (v18/*: any*/),
                          (v24/*: any*/),
                          (v25/*: any*/),
                          (v26/*: any*/),
                          (v5/*: any*/),
                          (v27/*: any*/),
                          (v28/*: any*/),
                          (v29/*: any*/),
                          (v23/*: any*/),
                          (v37/*: any*/),
                          (v38/*: any*/),
                          (v30/*: any*/),
                          (v31/*: any*/),
                          (v32/*: any*/),
                          (v33/*: any*/),
                          (v34/*: any*/),
                          (v36/*: any*/),
                          (v35/*: any*/)
                        ],
                        "storageKey": null
                      },
                      (v39/*: any*/)
                    ],
                    "storageKey": null
                  }
                ],
                "storageKey": null
              },
              (v45/*: any*/),
              (v46/*: any*/)
            ],
            "storageKey": "payStubs(first:500)"
          },
          {
            "alias": null,
            "args": (v43/*: any*/),
            "filters": null,
            "handle": "connection",
            "key": "PayStubTable_connectionFragment__payStubs",
            "kind": "LinkedHandle",
            "name": "payStubs"
          },
          {
            "alias": null,
            "args": (v43/*: any*/),
            "filters": null,
            "handle": "connection",
            "key": "TimesheetToolbar_timeSheet_payStubs",
            "kind": "LinkedHandle",
            "name": "payStubs"
          },
          {
            "alias": null,
            "args": (v43/*: any*/),
            "filters": null,
            "handle": "connection",
            "key": "UploadTimeSheetFragments_timeSheetConsolidated_payStubs",
            "kind": "LinkedHandle",
            "name": "payStubs"
          },
          {
            "alias": null,
            "args": (v43/*: any*/),
            "filters": null,
            "handle": "connection",
            "key": "useTimesheetData_payStubs",
            "kind": "LinkedHandle",
            "name": "payStubs"
          },
          (v4/*: any*/),
          (v12/*: any*/),
          (v13/*: any*/),
          (v14/*: any*/),
          (v15/*: any*/),
          (v16/*: any*/),
          {
            "alias": "payStubsConnection",
            "args": (v43/*: any*/),
            "concreteType": "PayStubConnection",
            "kind": "LinkedField",
            "name": "payStubs",
            "plural": false,
            "selections": [
              {
                "alias": null,
                "args": null,
                "concreteType": "PayStubEdge",
                "kind": "LinkedField",
                "name": "edges",
                "plural": true,
                "selections": [
                  (v40/*: any*/),
                  {
                    "alias": null,
                    "args": null,
                    "concreteType": "PayStub",
                    "kind": "LinkedField",
                    "name": "node",
                    "plural": false,
                    "selections": [
                      (v18/*: any*/),
                      (v19/*: any*/),
                      {
                        "alias": null,
                        "args": null,
                        "concreteType": "Employee",
                        "kind": "LinkedField",
                        "name": "employee",
                        "plural": false,
                        "selections": [
                          (v18/*: any*/),
                          (v20/*: any*/),
                          (v21/*: any*/),
                          (v44/*: any*/),
                          (v22/*: any*/)
                        ],
                        "storageKey": null
                      },
                      {
                        "alias": null,
                        "args": null,
                        "concreteType": "PayStubDetail",
                        "kind": "LinkedField",
                        "name": "details",
                        "plural": true,
                        "selections": [
                          (v18/*: any*/),
                          (v24/*: any*/),
                          (v25/*: any*/),
                          (v26/*: any*/),
                          (v5/*: any*/),
                          (v27/*: any*/),
                          (v28/*: any*/),
                          (v29/*: any*/),
                          (v30/*: any*/),
                          (v35/*: any*/),
                          (v31/*: any*/),
                          (v32/*: any*/),
                          (v33/*: any*/),
                          (v34/*: any*/),
                          (v36/*: any*/),
                          (v37/*: any*/),
                          (v38/*: any*/)
                        ],
                        "storageKey": null
                      },
                      (v39/*: any*/)
                    ],
                    "storageKey": null
                  }
                ],
                "storageKey": null
              },
              (v45/*: any*/),
              (v46/*: any*/)
            ],
            "storageKey": "payStubs(first:500)"
          },
          {
            "alias": "payStubsConnection",
            "args": (v43/*: any*/),
            "filters": null,
            "handle": "connection",
            "key": "TimeSheetPayStubsConnectionFragment_payStubsConnection",
            "kind": "LinkedHandle",
            "name": "payStubs"
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "payStubCount",
            "storageKey": null
          },
          (v10/*: any*/),
          (v11/*: any*/),
          (v17/*: any*/),
          (v9/*: any*/),
          (v6/*: any*/),
          (v3/*: any*/)
        ],
        "storageKey": null
      }
    ]
  },
  "params": {
    "cacheID": "0f8d9a8573a80ad970abfd063114aecf",
    "id": null,
    "metadata": {
      "connection": [
        {
          "count": null,
          "cursor": null,
          "direction": "forward",
          "path": [
            "timeSheetById",
            "payStubs"
          ]
        }
      ]
    },
    "name": "useTimesheetDataQuery",
    "operationKind": "query",
    "text": "query useTimesheetDataQuery(\n  $timeSheetId: ID!\n) {\n  timeSheetById(id: $timeSheetId) {\n    ...TimesheetDetail_timeSheet\n    numericId\n    oldId\n    employerGuid\n    name\n    hoursWorked\n    payPeriodEndDate\n    status\n    type\n    creationDate\n    modificationDate\n    showBonusColumn\n    showCostCenterColumn\n    showDTHoursColumn\n    showEarningsCodesColumn\n    showExpensesColumn\n    modifiedByUserId\n    payStubs(first: 500) {\n      edges {\n        node {\n          ...PayStubTable_payStub\n          id\n          employeeId\n          employee {\n            id\n            firstName\n            lastName\n            active\n          }\n          name\n          totalHours\n          details {\n            id\n            payStubId\n            reportLineItemId\n            workDate\n            name\n            stHours\n            otHours\n            dtHours\n            totalHours\n            jobCode\n            agreementId\n            classificationId\n            subClassificationId\n            costCenter\n            earningsCode\n            hourlyRate\n            bonus\n            expenses\n          }\n          __typename\n        }\n        cursor\n      }\n      pageInfo {\n        endCursor\n        hasNextPage\n      }\n    }\n    id\n  }\n}\n\nfragment EmployeeDisplayFragment_employee on Employee {\n  id\n  firstName\n  lastName\n  active\n  externalEmployeeId\n}\n\nfragment PayStubTable_connectionFragment on TimeSheet {\n  id\n  payStubs(first: 500) {\n    edges {\n      cursor\n      node {\n        ...PayStubTable_payStub\n        ...PayStubTable_payStubs\n        id\n        __typename\n      }\n    }\n    pageInfo {\n      hasNextPage\n      hasPreviousPage\n      startCursor\n      endCursor\n    }\n    totalCount\n  }\n}\n\nfragment PayStubTable_payStub on PayStub {\n  id\n  employeeId\n  employee {\n    id\n    ...EmployeeDisplayFragment_employee\n    ...TimeSheetDetailRow_employee\n  }\n  name\n  totalHours\n  details {\n    id\n    payStubId\n    reportLineItemId\n    workDate\n    name\n    stHours\n    otHours\n    dtHours\n    totalHours\n    bonus\n    expenses\n    jobCode\n    agreementId\n    classificationId\n    subClassificationId\n    costCenter\n    hourlyRate\n    earningsCode\n    ...TimeSheetDetailRow_payStubDetail\n  }\n  ...TimeSheetDetailTableView_payStub\n}\n\nfragment PayStubTable_payStubs on PayStub {\n  id\n  employeeId\n  name\n  totalHours\n  details {\n    id\n    payStubId\n    stHours\n    otHours\n    dtHours\n    totalHours\n    bonus\n    expenses\n  }\n}\n\nfragment TimeSheetDetailRow_employee on Employee {\n  id\n  externalEmployeeId\n  firstName\n  lastName\n  active\n}\n\nfragment TimeSheetDetailRow_payStubDetail on PayStubDetail {\n  id\n  payStubId\n  workDate\n  jobCode\n  agreementId\n  classificationId\n  subClassificationId\n  hourlyRate\n  stHours\n  otHours\n  dtHours\n  bonus\n  expenses\n  earningsCode\n  costCenter\n}\n\nfragment TimeSheetDetailTableView_payStub on PayStub {\n  id\n  employeeId\n  name\n  totalHours\n  employee {\n    ...TimeSheetDetailRow_employee\n    id\n  }\n  details {\n    id\n    workDate\n    jobCode\n    agreementId\n    classificationId\n    subClassificationId\n    hourlyRate\n    stHours\n    otHours\n    dtHours\n    bonus\n    expenses\n    earningsCode\n    costCenter\n    payStubId\n    reportLineItemId\n    totalHours\n    name\n    ...TimeSheetDetailRow_payStubDetail\n  }\n}\n\nfragment TimeSheetGrid_timeSheet on TimeSheet {\n  payPeriodEndDate\n  ...PayStubTable_connectionFragment\n  ...TimesheetToolbar_timeSheet\n}\n\nfragment TimeSheetPayStubsConnectionFragment_timeSheet on TimeSheet {\n  id\n  payStubsConnection: payStubs(first: 500) {\n    edges {\n      cursor\n      node {\n        id\n        employeeId\n        employee {\n          id\n          firstName\n          lastName\n          externalEmployeeId\n          active\n        }\n        details {\n          id\n          payStubId\n          reportLineItemId\n          workDate\n          name\n          stHours\n          otHours\n          dtHours\n          jobCode\n          earningsCode\n          agreementId\n          classificationId\n          subClassificationId\n          costCenter\n          hourlyRate\n          bonus\n          expenses\n        }\n        __typename\n      }\n    }\n    pageInfo {\n      hasNextPage\n      hasPreviousPage\n      startCursor\n      endCursor\n    }\n    totalCount\n  }\n  payStubCount\n}\n\nfragment TimeSheetSettings_timeSheet on TimeSheet {\n  id\n  showBonusColumn\n  showCostCenterColumn\n  showDTHoursColumn\n  showEarningsCodesColumn\n  showExpensesColumn\n}\n\nfragment TimesheetDetailView_timeSheet on TimeSheet {\n  numericId\n  name\n  status\n  payPeriodEndDate\n  ...TimeSheetGrid_timeSheet\n}\n\nfragment TimesheetDetail_timeSheet on TimeSheet {\n  ...TimesheetDetailView_timeSheet\n  ...TimeSheetGrid_timeSheet\n  ...TimeSheetSettings_timeSheet\n  ...PayStubTable_connectionFragment\n  ...TimeSheetPayStubsConnectionFragment_timeSheet\n  id\n  numericId\n  name\n  status\n  payPeriodEndDate\n  showBonusColumn\n  showCostCenterColumn\n  showDTHoursColumn\n  showEarningsCodesColumn\n  showExpensesColumn\n  employerGuid\n  creationDate\n  modificationDate\n  modifiedByUserId\n  type\n  hoursWorked\n  oldId\n  ...TimesheetToolbar_timeSheet\n}\n\nfragment TimesheetToolbar_timeSheet on TimeSheet {\n  id\n  numericId\n  employerGuid\n  payStubs(first: 500) {\n    edges {\n      node {\n        id\n        details {\n          id\n        }\n        __typename\n      }\n      cursor\n    }\n    pageInfo {\n      endCursor\n      hasNextPage\n    }\n  }\n  ...TimeSheetSettings_timeSheet\n  ...UploadTimeSheetFragments_timeSheetConsolidated\n}\n\nfragment UploadTimeSheetFragments_timeSheetConsolidated on TimeSheet {\n  id\n  numericId\n  employerGuid\n  payStubs(first: 500) {\n    edges {\n      node {\n        id\n        employeeId\n        name\n        __typename\n      }\n      cursor\n    }\n    pageInfo {\n      endCursor\n      hasNextPage\n    }\n  }\n}\n"
  }
};
})();

(node as any).hash = "e485e4476834d5c85dad319959f87866";

export default node;
