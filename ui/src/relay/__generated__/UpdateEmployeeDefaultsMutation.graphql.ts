/**
 * @generated SignedSource<<4237f08da254873da12707ca1cd87316>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ConcreteRequest } from 'relay-runtime';
export type SaveSettingInput = {
  id?: string | null | undefined;
  lastModificationDate?: any | null | undefined;
  lastModifiedBy?: any | null | undefined;
  ownerId?: string | null | undefined;
  ownerType: string;
  settingId?: string | null | undefined;
  value?: any | null | undefined;
};
export type UpdateEmployeeDefaultsMutation$variables = {
  input: SaveSettingInput;
};
export type UpdateEmployeeDefaultsMutation$data = {
  readonly saveSetting: {
    readonly operationResultOfSetting: {
      readonly data: {
        readonly id: string;
        readonly lastModificationDate: any | null | undefined;
        readonly lastModifiedBy: any | null | undefined;
        readonly ownerId: any;
        readonly ownerType: string;
        readonly value: string | null | undefined;
      };
      readonly message: string;
      readonly success: boolean;
    } | null | undefined;
  };
};
export type UpdateEmployeeDefaultsMutation = {
  response: UpdateEmployeeDefaultsMutation$data;
  variables: UpdateEmployeeDefaultsMutation$variables;
};

const node: ConcreteRequest = (function(){
var v0 = [
  {
    "defaultValue": null,
    "kind": "LocalArgument",
    "name": "input"
  }
],
v1 = [
  {
    "alias": null,
    "args": [
      {
        "kind": "Variable",
        "name": "input",
        "variableName": "input"
      }
    ],
    "concreteType": "SaveSettingPayload",
    "kind": "LinkedField",
    "name": "saveSetting",
    "plural": false,
    "selections": [
      {
        "alias": null,
        "args": null,
        "concreteType": "OperationResultOfSetting",
        "kind": "LinkedField",
        "name": "operationResultOfSetting",
        "plural": false,
        "selections": [
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "success",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "message",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "concreteType": "Setting",
            "kind": "LinkedField",
            "name": "data",
            "plural": false,
            "selections": [
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "id",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "value",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "ownerId",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "ownerType",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "lastModifiedBy",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "lastModificationDate",
                "storageKey": null
              }
            ],
            "storageKey": null
          }
        ],
        "storageKey": null
      }
    ],
    "storageKey": null
  }
];
return {
  "fragment": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Fragment",
    "metadata": null,
    "name": "UpdateEmployeeDefaultsMutation",
    "selections": (v1/*: any*/),
    "type": "Mutation",
    "abstractKey": null
  },
  "kind": "Request",
  "operation": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Operation",
    "name": "UpdateEmployeeDefaultsMutation",
    "selections": (v1/*: any*/)
  },
  "params": {
    "cacheID": "d9a6106f6308cc33ed061ee12288caa7",
    "id": null,
    "metadata": {},
    "name": "UpdateEmployeeDefaultsMutation",
    "operationKind": "mutation",
    "text": "mutation UpdateEmployeeDefaultsMutation(\n  $input: SaveSettingInput!\n) {\n  saveSetting(input: $input) {\n    operationResultOfSetting {\n      success\n      message\n      data {\n        id\n        value\n        ownerId\n        ownerType\n        lastModifiedBy\n        lastModificationDate\n      }\n    }\n  }\n}\n"
  }
};
})();

(node as any).hash = "e35c76eedbfea090b11ca7b29f6d2690";

export default node;
