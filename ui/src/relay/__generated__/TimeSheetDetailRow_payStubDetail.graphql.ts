/**
 * @generated SignedSource<<ef7ea631c8575f03bbd073fce4687e5b>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ReaderFragment } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type TimeSheetDetailRow_payStubDetail$data = {
  readonly agreementId: number | null | undefined;
  readonly bonus: number | null | undefined;
  readonly classificationId: number | null | undefined;
  readonly costCenter: string | null | undefined;
  readonly dtHours: number | null | undefined;
  readonly earningsCode: string | null | undefined;
  readonly expenses: number | null | undefined;
  readonly hourlyRate: number | null | undefined;
  readonly id: string;
  readonly jobCode: string | null | undefined;
  readonly otHours: number | null | undefined;
  readonly payStubId: string;
  readonly stHours: number | null | undefined;
  readonly subClassificationId: number | null | undefined;
  readonly workDate: any;
  readonly " $fragmentType": "TimeSheetDetailRow_payStubDetail";
};
export type TimeSheetDetailRow_payStubDetail$key = {
  readonly " $data"?: TimeSheetDetailRow_payStubDetail$data;
  readonly " $fragmentSpreads": FragmentRefs<"TimeSheetDetailRow_payStubDetail">;
};

const node: ReaderFragment = {
  "argumentDefinitions": [],
  "kind": "Fragment",
  "metadata": null,
  "name": "TimeSheetDetailRow_payStubDetail",
  "selections": [
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "id",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "payStubId",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "workDate",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "jobCode",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "agreementId",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "classificationId",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "subClassificationId",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "hourlyRate",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "stHours",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "otHours",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "dtHours",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "bonus",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "expenses",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "earningsCode",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "costCenter",
      "storageKey": null
    }
  ],
  "type": "PayStubDetail",
  "abstractKey": null
};

(node as any).hash = "fc23433246dfea908b4089fc95d4e636";

export default node;
