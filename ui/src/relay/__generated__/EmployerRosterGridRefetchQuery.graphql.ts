/**
 * @generated SignedSource<<981f5bcdd31a96ac409d1f636bd6b54f>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ConcreteRequest } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type SortEnumType = "ASC" | "DESC" | "%future added value";
export type EmployerRosterViewSortInput = {
  associationId?: SortEnumType | null | undefined;
  chapterId?: SortEnumType | null | undefined;
  dba?: SortEnumType | null | undefined;
  fein?: SortEnumType | null | undefined;
  guid?: SortEnumType | null | undefined;
  id?: SortEnumType | null | undefined;
  lastReportedDate?: SortEnumType | null | undefined;
  name?: SortEnumType | null | undefined;
  payrollContactEmailAddress?: SortEnumType | null | undefined;
  payrollContactFirstName?: SortEnumType | null | undefined;
  payrollContactLastName?: SortEnumType | null | undefined;
  payrollContactPhoneNumber?: SortEnumType | null | undefined;
  relationshipStatusId?: SortEnumType | null | undefined;
};
export type EmployerRosterViewFilterInput = {
  and?: ReadonlyArray<EmployerRosterViewFilterInput> | null | undefined;
  associationId?: StringOperationFilterInput | null | undefined;
  chapterId?: IdOperationFilterInput | null | undefined;
  dba?: StringOperationFilterInput | null | undefined;
  fein?: StringOperationFilterInput | null | undefined;
  guid?: UuidOperationFilterInput | null | undefined;
  id?: IdOperationFilterInput | null | undefined;
  lastReportedDate?: DateTimeOperationFilterInput | null | undefined;
  name?: StringOperationFilterInput | null | undefined;
  or?: ReadonlyArray<EmployerRosterViewFilterInput> | null | undefined;
  payrollContactEmailAddress?: StringOperationFilterInput | null | undefined;
  payrollContactFirstName?: StringOperationFilterInput | null | undefined;
  payrollContactLastName?: StringOperationFilterInput | null | undefined;
  payrollContactPhoneNumber?: StringOperationFilterInput | null | undefined;
  relationshipStatusId?: IntOperationFilterInput | null | undefined;
};
export type IdOperationFilterInput = {
  eq?: string | null | undefined;
  in?: ReadonlyArray<string | null | undefined> | null | undefined;
  neq?: string | null | undefined;
  nin?: ReadonlyArray<string | null | undefined> | null | undefined;
};
export type UuidOperationFilterInput = {
  eq?: any | null | undefined;
  gt?: any | null | undefined;
  gte?: any | null | undefined;
  in?: ReadonlyArray<any | null | undefined> | null | undefined;
  lt?: any | null | undefined;
  lte?: any | null | undefined;
  neq?: any | null | undefined;
  ngt?: any | null | undefined;
  ngte?: any | null | undefined;
  nin?: ReadonlyArray<any | null | undefined> | null | undefined;
  nlt?: any | null | undefined;
  nlte?: any | null | undefined;
};
export type StringOperationFilterInput = {
  and?: ReadonlyArray<StringOperationFilterInput> | null | undefined;
  contains?: string | null | undefined;
  endsWith?: string | null | undefined;
  eq?: string | null | undefined;
  in?: ReadonlyArray<string | null | undefined> | null | undefined;
  ncontains?: string | null | undefined;
  nendsWith?: string | null | undefined;
  neq?: string | null | undefined;
  nin?: ReadonlyArray<string | null | undefined> | null | undefined;
  nstartsWith?: string | null | undefined;
  or?: ReadonlyArray<StringOperationFilterInput> | null | undefined;
  startsWith?: string | null | undefined;
};
export type DateTimeOperationFilterInput = {
  eq?: any | null | undefined;
  gt?: any | null | undefined;
  gte?: any | null | undefined;
  in?: ReadonlyArray<any | null | undefined> | null | undefined;
  lt?: any | null | undefined;
  lte?: any | null | undefined;
  neq?: any | null | undefined;
  ngt?: any | null | undefined;
  ngte?: any | null | undefined;
  nin?: ReadonlyArray<any | null | undefined> | null | undefined;
  nlt?: any | null | undefined;
  nlte?: any | null | undefined;
};
export type IntOperationFilterInput = {
  eq?: number | null | undefined;
  gt?: number | null | undefined;
  gte?: number | null | undefined;
  in?: ReadonlyArray<number | null | undefined> | null | undefined;
  lt?: number | null | undefined;
  lte?: number | null | undefined;
  neq?: number | null | undefined;
  ngt?: number | null | undefined;
  ngte?: number | null | undefined;
  nin?: ReadonlyArray<number | null | undefined> | null | undefined;
  nlt?: number | null | undefined;
  nlte?: number | null | undefined;
};
export type EmployerRosterGridRefetchQuery$variables = {
  after?: string | null | undefined;
  chapterId: string;
  first?: number | null | undefined;
  order?: ReadonlyArray<EmployerRosterViewSortInput> | null | undefined;
  where?: EmployerRosterViewFilterInput | null | undefined;
};
export type EmployerRosterGridRefetchQuery$data = {
  readonly " $fragmentSpreads": FragmentRefs<"EmployerRosterTableFragment">;
};
export type EmployerRosterGridRefetchQuery = {
  response: EmployerRosterGridRefetchQuery$data;
  variables: EmployerRosterGridRefetchQuery$variables;
};

const node: ConcreteRequest = (function(){
var v0 = [
  {
    "defaultValue": null,
    "kind": "LocalArgument",
    "name": "after"
  },
  {
    "defaultValue": null,
    "kind": "LocalArgument",
    "name": "chapterId"
  },
  {
    "defaultValue": 20,
    "kind": "LocalArgument",
    "name": "first"
  },
  {
    "defaultValue": null,
    "kind": "LocalArgument",
    "name": "order"
  },
  {
    "defaultValue": null,
    "kind": "LocalArgument",
    "name": "where"
  }
],
v1 = [
  {
    "kind": "Variable",
    "name": "after",
    "variableName": "after"
  },
  {
    "kind": "Variable",
    "name": "chapterId",
    "variableName": "chapterId"
  },
  {
    "kind": "Variable",
    "name": "first",
    "variableName": "first"
  },
  {
    "kind": "Variable",
    "name": "order",
    "variableName": "order"
  },
  {
    "kind": "Variable",
    "name": "where",
    "variableName": "where"
  }
];
return {
  "fragment": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Fragment",
    "metadata": null,
    "name": "EmployerRosterGridRefetchQuery",
    "selections": [
      {
        "args": (v1/*: any*/),
        "kind": "FragmentSpread",
        "name": "EmployerRosterTableFragment"
      }
    ],
    "type": "Query",
    "abstractKey": null
  },
  "kind": "Request",
  "operation": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Operation",
    "name": "EmployerRosterGridRefetchQuery",
    "selections": [
      {
        "alias": null,
        "args": (v1/*: any*/),
        "concreteType": "EmployerRosterViewConnection",
        "kind": "LinkedField",
        "name": "employerRosterByChapterId",
        "plural": false,
        "selections": [
          {
            "alias": null,
            "args": null,
            "concreteType": "EmployerRosterViewEdge",
            "kind": "LinkedField",
            "name": "edges",
            "plural": true,
            "selections": [
              {
                "alias": null,
                "args": null,
                "concreteType": "EmployerRosterView",
                "kind": "LinkedField",
                "name": "node",
                "plural": false,
                "selections": [
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "id",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "guid",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "name",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "fein",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "dba",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "associationId",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "payrollContactFirstName",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "payrollContactLastName",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "payrollContactPhoneNumber",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "payrollContactEmailAddress",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "lastReportedDate",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "chapterId",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "relationshipStatusId",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "__typename",
                    "storageKey": null
                  }
                ],
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "cursor",
                "storageKey": null
              }
            ],
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "concreteType": "PageInfo",
            "kind": "LinkedField",
            "name": "pageInfo",
            "plural": false,
            "selections": [
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "endCursor",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "hasNextPage",
                "storageKey": null
              }
            ],
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "totalCount",
            "storageKey": null
          }
        ],
        "storageKey": null
      },
      {
        "alias": null,
        "args": (v1/*: any*/),
        "filters": [
          "chapterId",
          "order",
          "where"
        ],
        "handle": "connection",
        "key": "EmployerRosterTableFragment_employerRosterByChapterId",
        "kind": "LinkedHandle",
        "name": "employerRosterByChapterId"
      }
    ]
  },
  "params": {
    "cacheID": "adc413b2fcc397290e430d625565b60c",
    "id": null,
    "metadata": {},
    "name": "EmployerRosterGridRefetchQuery",
    "operationKind": "query",
    "text": "query EmployerRosterGridRefetchQuery(\n  $after: String\n  $chapterId: ID!\n  $first: Int = 20\n  $order: [EmployerRosterViewSortInput!] = null\n  $where: EmployerRosterViewFilterInput = null\n) {\n  ...EmployerRosterTableFragment_AvdXu\n}\n\nfragment EmployerRosterTableFragment_AvdXu on Query {\n  employerRosterByChapterId(first: $first, after: $after, chapterId: $chapterId, order: $order, where: $where) {\n    edges {\n      node {\n        id\n        guid\n        name\n        fein\n        dba\n        associationId\n        payrollContactFirstName\n        payrollContactLastName\n        payrollContactPhoneNumber\n        payrollContactEmailAddress\n        lastReportedDate\n        chapterId\n        relationshipStatusId\n        __typename\n      }\n      cursor\n    }\n    pageInfo {\n      endCursor\n      hasNextPage\n    }\n    totalCount\n  }\n}\n"
  }
};
})();

(node as any).hash = "c590baf881377c2e7bc5e58f7da3bf7d";

export default node;
