/**
 * @generated SignedSource<<6947a51d1e5b493c9d4657e7dd0a478c>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ReaderFragment } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type TimeSheetDetailRow_employee$data = {
  readonly active: boolean;
  readonly externalEmployeeId: string | null | undefined;
  readonly firstName: string | null | undefined;
  readonly id: string;
  readonly lastName: string;
  readonly " $fragmentType": "TimeSheetDetailRow_employee";
};
export type TimeSheetDetailRow_employee$key = {
  readonly " $data"?: TimeSheetDetailRow_employee$data;
  readonly " $fragmentSpreads": FragmentRefs<"TimeSheetDetailRow_employee">;
};

const node: ReaderFragment = {
  "argumentDefinitions": [],
  "kind": "Fragment",
  "metadata": null,
  "name": "TimeSheetDetailRow_employee",
  "selections": [
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "id",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "externalEmployeeId",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "firstName",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "lastName",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "active",
      "storageKey": null
    }
  ],
  "type": "Employee",
  "abstractKey": null
};

(node as any).hash = "182fd89f37a01e6256ecc3c27a07c231";

export default node;
