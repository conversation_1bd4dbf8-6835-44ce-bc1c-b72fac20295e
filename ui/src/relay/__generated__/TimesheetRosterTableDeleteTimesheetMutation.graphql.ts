/**
 * @generated SignedSource<<ef63cd77ccc68b54731d36aa2a742390>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ConcreteRequest } from 'relay-runtime';
export type DeleteTimesheetInput = {
  id: string;
};
export type TimesheetRosterTableDeleteTimesheetMutation$variables = {
  input: DeleteTimesheetInput;
};
export type TimesheetRosterTableDeleteTimesheetMutation$data = {
  readonly deleteTimesheet: {
    readonly int: number | null | undefined;
  };
};
export type TimesheetRosterTableDeleteTimesheetMutation = {
  response: TimesheetRosterTableDeleteTimesheetMutation$data;
  variables: TimesheetRosterTableDeleteTimesheetMutation$variables;
};

const node: ConcreteRequest = (function(){
var v0 = [
  {
    "defaultValue": null,
    "kind": "LocalArgument",
    "name": "input"
  }
],
v1 = [
  {
    "alias": null,
    "args": [
      {
        "kind": "Variable",
        "name": "input",
        "variableName": "input"
      }
    ],
    "concreteType": "DeleteTimesheetPayload",
    "kind": "LinkedField",
    "name": "deleteTimesheet",
    "plural": false,
    "selections": [
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "int",
        "storageKey": null
      }
    ],
    "storageKey": null
  }
];
return {
  "fragment": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Fragment",
    "metadata": null,
    "name": "TimesheetRosterTableDeleteTimesheetMutation",
    "selections": (v1/*: any*/),
    "type": "Mutation",
    "abstractKey": null
  },
  "kind": "Request",
  "operation": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Operation",
    "name": "TimesheetRosterTableDeleteTimesheetMutation",
    "selections": (v1/*: any*/)
  },
  "params": {
    "cacheID": "5a3e88589e4bff40a4d714c4db548ff5",
    "id": null,
    "metadata": {},
    "name": "TimesheetRosterTableDeleteTimesheetMutation",
    "operationKind": "mutation",
    "text": "mutation TimesheetRosterTableDeleteTimesheetMutation(\n  $input: DeleteTimesheetInput!\n) {\n  deleteTimesheet(input: $input) {\n    int\n  }\n}\n"
  }
};
})();

(node as any).hash = "653723b8566311427c508534244d8d5e";

export default node;
