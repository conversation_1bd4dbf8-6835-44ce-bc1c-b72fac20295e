/**
 * @generated SignedSource<<1cf293e796519cea13e66e0076f90745>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ReaderFragment } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type TimesheetRosterCustomViewsFragment$data = {
  readonly id: string | null | undefined;
  readonly value: any | null | undefined;
  readonly " $fragmentType": "TimesheetRosterCustomViewsFragment";
};
export type TimesheetRosterCustomViewsFragment$key = {
  readonly " $data"?: TimesheetRosterCustomViewsFragment$data;
  readonly " $fragmentSpreads": FragmentRefs<"TimesheetRosterCustomViewsFragment">;
};

const node: ReaderFragment = {
  "argumentDefinitions": [],
  "kind": "Fragment",
  "metadata": null,
  "name": "TimesheetRosterCustomViewsFragment",
  "selections": [
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "id",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "value",
      "storageKey": null
    }
  ],
  "type": "CustomViews",
  "abstractKey": null
};

(node as any).hash = "ad3c5b2484f1096ecc66d02a4a5dbb83";

export default node;
