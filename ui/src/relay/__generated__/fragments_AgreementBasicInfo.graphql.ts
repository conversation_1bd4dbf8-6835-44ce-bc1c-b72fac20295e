/**
 * @generated SignedSource<<bc1268783880ec418eb8baa3c59414b8>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ReaderFragment } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type fragments_AgreementBasicInfo$data = {
  readonly id: string;
  readonly name: string;
  readonly " $fragmentType": "fragments_AgreementBasicInfo";
};
export type fragments_AgreementBasicInfo$key = {
  readonly " $data"?: fragments_AgreementBasicInfo$data;
  readonly " $fragmentSpreads": FragmentRefs<"fragments_AgreementBasicInfo">;
};

const node: ReaderFragment = {
  "argumentDefinitions": [],
  "kind": "Fragment",
  "metadata": null,
  "name": "fragments_AgreementBasicInfo",
  "selections": [
    {
      "alias": null,
      "args": null,
      "kind": "Scalar<PERSON>ield",
      "name": "id",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "Scalar<PERSON>ield",
      "name": "name",
      "storageKey": null
    }
  ],
  "type": "Agreement",
  "abstractKey": null
};

(node as any).hash = "685cc35f8e82dc7fa415aaf95b6c673d";

export default node;
