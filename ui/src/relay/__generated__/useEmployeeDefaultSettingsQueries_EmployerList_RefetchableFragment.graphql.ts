/**
 * @generated SignedSource<<678ad3ac16a6541ca2e10523bc97fabf>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ReaderFragment } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type useEmployeeDefaultSettingsQueries_EmployerList_RefetchableFragment$data = {
  readonly employeeDefaultSettingsByEmployerId: {
    readonly edges: ReadonlyArray<{
      readonly node: {
        readonly defaultAgreementId: number | null | undefined;
        readonly defaultClassificationId: number | null | undefined;
        readonly defaultHourlyRate: any | null | undefined;
        readonly employeeId: string | null | undefined;
        readonly " $fragmentSpreads": FragmentRefs<"useEmployeeDefaultSettingsQueries_EmployeeDefaultsNode">;
      };
    }> | null | undefined;
    readonly pageInfo: {
      readonly endCursor: string | null | undefined;
      readonly hasNextPage: boolean;
    };
    readonly totalCount: number;
  } | null | undefined;
  readonly " $fragmentType": "useEmployeeDefaultSettingsQueries_EmployerList_RefetchableFragment";
};
export type useEmployeeDefaultSettingsQueries_EmployerList_RefetchableFragment$key = {
  readonly " $data"?: useEmployeeDefaultSettingsQueries_EmployerList_RefetchableFragment$data;
  readonly " $fragmentSpreads": FragmentRefs<"useEmployeeDefaultSettingsQueries_EmployerList_RefetchableFragment">;
};

import EmployeeDefaultSettingsByEmployerListPaginationQuery_graphql from './EmployeeDefaultSettingsByEmployerListPaginationQuery.graphql';

const node: ReaderFragment = (function(){
var v0 = [
  "employeeDefaultSettingsByEmployerId"
];
return {
  "argumentDefinitions": [
    {
      "defaultValue": null,
      "kind": "LocalArgument",
      "name": "after"
    },
    {
      "defaultValue": null,
      "kind": "LocalArgument",
      "name": "employerId"
    },
    {
      "defaultValue": 10,
      "kind": "LocalArgument",
      "name": "first"
    }
  ],
  "kind": "Fragment",
  "metadata": {
    "connection": [
      {
        "count": "first",
        "cursor": "after",
        "direction": "forward",
        "path": (v0/*: any*/)
      }
    ],
    "refetch": {
      "connection": {
        "forward": {
          "count": "first",
          "cursor": "after"
        },
        "backward": null,
        "path": (v0/*: any*/)
      },
      "fragmentPathInResult": [],
      "operation": EmployeeDefaultSettingsByEmployerListPaginationQuery_graphql
    }
  },
  "name": "useEmployeeDefaultSettingsQueries_EmployerList_RefetchableFragment",
  "selections": [
    {
      "alias": "employeeDefaultSettingsByEmployerId",
      "args": [
        {
          "kind": "Variable",
          "name": "employerId",
          "variableName": "employerId"
        }
      ],
      "concreteType": "EmployeeDefaultSettingsConnection",
      "kind": "LinkedField",
      "name": "__UseEmployeeDefaultSettingsQueries_employeeDefaultSettingsByEmployerId_connection",
      "plural": false,
      "selections": [
        {
          "alias": null,
          "args": null,
          "concreteType": "EmployeeDefaultSettingsEdge",
          "kind": "LinkedField",
          "name": "edges",
          "plural": true,
          "selections": [
            {
              "alias": null,
              "args": null,
              "concreteType": "EmployeeDefaultSettings",
              "kind": "LinkedField",
              "name": "node",
              "plural": false,
              "selections": [
                {
                  "alias": null,
                  "args": null,
                  "kind": "ScalarField",
                  "name": "employeeId",
                  "storageKey": null
                },
                {
                  "alias": null,
                  "args": null,
                  "kind": "ScalarField",
                  "name": "defaultAgreementId",
                  "storageKey": null
                },
                {
                  "alias": null,
                  "args": null,
                  "kind": "ScalarField",
                  "name": "defaultClassificationId",
                  "storageKey": null
                },
                {
                  "alias": null,
                  "args": null,
                  "kind": "ScalarField",
                  "name": "defaultHourlyRate",
                  "storageKey": null
                },
                {
                  "args": null,
                  "kind": "FragmentSpread",
                  "name": "useEmployeeDefaultSettingsQueries_EmployeeDefaultsNode"
                },
                {
                  "alias": null,
                  "args": null,
                  "kind": "ScalarField",
                  "name": "__typename",
                  "storageKey": null
                }
              ],
              "storageKey": null
            },
            {
              "alias": null,
              "args": null,
              "kind": "ScalarField",
              "name": "cursor",
              "storageKey": null
            }
          ],
          "storageKey": null
        },
        {
          "alias": null,
          "args": null,
          "concreteType": "PageInfo",
          "kind": "LinkedField",
          "name": "pageInfo",
          "plural": false,
          "selections": [
            {
              "alias": null,
              "args": null,
              "kind": "ScalarField",
              "name": "hasNextPage",
              "storageKey": null
            },
            {
              "alias": null,
              "args": null,
              "kind": "ScalarField",
              "name": "endCursor",
              "storageKey": null
            }
          ],
          "storageKey": null
        },
        {
          "alias": null,
          "args": null,
          "kind": "ScalarField",
          "name": "totalCount",
          "storageKey": null
        }
      ],
      "storageKey": null
    }
  ],
  "type": "Query",
  "abstractKey": null
};
})();

(node as any).hash = "19d0c60c6a24501949edc9c3e2a7435a";

export default node;
