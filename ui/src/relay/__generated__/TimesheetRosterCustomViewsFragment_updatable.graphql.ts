/**
 * @generated SignedSource<<01489d07d2f4ed944a5a4ddadc34502e>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ReaderFragment } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type TimesheetRosterCustomViewsFragment_updatable$data = {
  readonly id: string | null | undefined;
  value: any | null | undefined;
  readonly " $fragmentType": "TimesheetRosterCustomViewsFragment_updatable";
};
export type TimesheetRosterCustomViewsFragment_updatable$key = {
  readonly " $data"?: TimesheetRosterCustomViewsFragment_updatable$data;
  readonly $updatableFragmentSpreads: FragmentRefs<"TimesheetRosterCustomViewsFragment_updatable">;
};

const node: ReaderFragment = {
  "argumentDefinitions": [],
  "kind": "Fragment",
  "metadata": null,
  "name": "TimesheetRosterCustomViewsFragment_updatable",
  "selections": [
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "id",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "value",
      "storageKey": null
    }
  ],
  "type": "CustomViews",
  "abstractKey": null
};

(node as any).hash = "4fd3150d1ea7a0589f2961976fba12bd";

export default node;
