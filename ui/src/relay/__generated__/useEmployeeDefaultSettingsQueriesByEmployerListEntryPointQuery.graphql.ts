/**
 * @generated SignedSource<<efab2df625432760fbc50df877cd0440>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ConcreteRequest } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type useEmployeeDefaultSettingsQueriesByEmployerListEntryPointQuery$variables = {
  after?: string | null | undefined;
  employerId: string;
  first?: number | null | undefined;
};
export type useEmployeeDefaultSettingsQueriesByEmployerListEntryPointQuery$data = {
  readonly " $fragmentSpreads": FragmentRefs<"useEmployeeDefaultSettingsQueries_EmployerList_RefetchableFragment">;
};
export type useEmployeeDefaultSettingsQueriesByEmployerListEntryPointQuery = {
  response: useEmployeeDefaultSettingsQueriesByEmployerListEntryPointQuery$data;
  variables: useEmployeeDefaultSettingsQueriesByEmployerListEntryPointQuery$variables;
};

const node: ConcreteRequest = (function(){
var v0 = {
  "defaultValue": null,
  "kind": "LocalArgument",
  "name": "after"
},
v1 = {
  "defaultValue": null,
  "kind": "LocalArgument",
  "name": "employerId"
},
v2 = {
  "defaultValue": 10,
  "kind": "LocalArgument",
  "name": "first"
},
v3 = [
  {
    "kind": "Variable",
    "name": "after",
    "variableName": "after"
  },
  {
    "kind": "Variable",
    "name": "employerId",
    "variableName": "employerId"
  },
  {
    "kind": "Variable",
    "name": "first",
    "variableName": "first"
  }
];
return {
  "fragment": {
    "argumentDefinitions": [
      (v0/*: any*/),
      (v1/*: any*/),
      (v2/*: any*/)
    ],
    "kind": "Fragment",
    "metadata": null,
    "name": "useEmployeeDefaultSettingsQueriesByEmployerListEntryPointQuery",
    "selections": [
      {
        "args": (v3/*: any*/),
        "kind": "FragmentSpread",
        "name": "useEmployeeDefaultSettingsQueries_EmployerList_RefetchableFragment"
      }
    ],
    "type": "Query",
    "abstractKey": null
  },
  "kind": "Request",
  "operation": {
    "argumentDefinitions": [
      (v1/*: any*/),
      (v2/*: any*/),
      (v0/*: any*/)
    ],
    "kind": "Operation",
    "name": "useEmployeeDefaultSettingsQueriesByEmployerListEntryPointQuery",
    "selections": [
      {
        "alias": null,
        "args": (v3/*: any*/),
        "concreteType": "EmployeeDefaultSettingsConnection",
        "kind": "LinkedField",
        "name": "employeeDefaultSettingsByEmployerId",
        "plural": false,
        "selections": [
          {
            "alias": null,
            "args": null,
            "concreteType": "EmployeeDefaultSettingsEdge",
            "kind": "LinkedField",
            "name": "edges",
            "plural": true,
            "selections": [
              {
                "alias": null,
                "args": null,
                "concreteType": "EmployeeDefaultSettings",
                "kind": "LinkedField",
                "name": "node",
                "plural": false,
                "selections": [
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "employeeId",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "defaultAgreementId",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "defaultClassificationId",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "defaultHourlyRate",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "__typename",
                    "storageKey": null
                  }
                ],
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "cursor",
                "storageKey": null
              }
            ],
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "concreteType": "PageInfo",
            "kind": "LinkedField",
            "name": "pageInfo",
            "plural": false,
            "selections": [
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "hasNextPage",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "endCursor",
                "storageKey": null
              }
            ],
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "totalCount",
            "storageKey": null
          }
        ],
        "storageKey": null
      },
      {
        "alias": null,
        "args": (v3/*: any*/),
        "filters": [
          "employerId"
        ],
        "handle": "connection",
        "key": "UseEmployeeDefaultSettingsQueries_employeeDefaultSettingsByEmployerId",
        "kind": "LinkedHandle",
        "name": "employeeDefaultSettingsByEmployerId"
      }
    ]
  },
  "params": {
    "cacheID": "5af1232ac4f7d7c913209c142e20ab21",
    "id": null,
    "metadata": {},
    "name": "useEmployeeDefaultSettingsQueriesByEmployerListEntryPointQuery",
    "operationKind": "query",
    "text": "query useEmployeeDefaultSettingsQueriesByEmployerListEntryPointQuery(\n  $employerId: ID!\n  $first: Int = 10\n  $after: String\n) {\n  ...useEmployeeDefaultSettingsQueries_EmployerList_RefetchableFragment_2553ZF\n}\n\nfragment useEmployeeDefaultSettingsQueries_EmployeeDefaultsNode on EmployeeDefaultSettings {\n  employeeId\n  defaultAgreementId\n  defaultClassificationId\n  defaultHourlyRate\n}\n\nfragment useEmployeeDefaultSettingsQueries_EmployerList_RefetchableFragment_2553ZF on Query {\n  employeeDefaultSettingsByEmployerId(employerId: $employerId, first: $first, after: $after) {\n    edges {\n      node {\n        employeeId\n        defaultAgreementId\n        defaultClassificationId\n        defaultHourlyRate\n        ...useEmployeeDefaultSettingsQueries_EmployeeDefaultsNode\n        __typename\n      }\n      cursor\n    }\n    pageInfo {\n      hasNextPage\n      endCursor\n    }\n    totalCount\n  }\n}\n"
  }
};
})();

(node as any).hash = "c9acea120797e9b796963e86461bbbd2";

export default node;
