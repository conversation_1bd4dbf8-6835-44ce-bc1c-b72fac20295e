/**
 * @generated SignedSource<<2ad9e137a0e51d620ccb873f6f31ae24>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ConcreteRequest } from 'relay-runtime';
export type AddUnionInput = {
  input: AddThirdPartyInput;
};
export type AddThirdPartyInput = {
  chapterId: number;
  name: string;
  username: string;
};
export type CreateUnionMutation$variables = {
  connections: ReadonlyArray<string>;
  input: AddUnionInput;
};
export type CreateUnionMutation$data = {
  readonly addUnion: {
    readonly thirdPartyInfoDtoEdge: {
      readonly cursor: string;
      readonly node: {
        readonly guid: any | null | undefined;
        readonly label: string;
        readonly value: number;
      } | null | undefined;
    } | null | undefined;
  };
};
export type CreateUnionMutation = {
  response: CreateUnionMutation$data;
  variables: CreateUnionMutation$variables;
};

const node: ConcreteRequest = (function(){
var v0 = {
  "defaultValue": null,
  "kind": "LocalArgument",
  "name": "connections"
},
v1 = {
  "defaultValue": null,
  "kind": "LocalArgument",
  "name": "input"
},
v2 = [
  {
    "kind": "Variable",
    "name": "input",
    "variableName": "input"
  }
],
v3 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "cursor",
  "storageKey": null
},
v4 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "value",
  "storageKey": null
},
v5 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "label",
  "storageKey": null
},
v6 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "guid",
  "storageKey": null
};
return {
  "fragment": {
    "argumentDefinitions": [
      (v0/*: any*/),
      (v1/*: any*/)
    ],
    "kind": "Fragment",
    "metadata": null,
    "name": "CreateUnionMutation",
    "selections": [
      {
        "alias": null,
        "args": (v2/*: any*/),
        "concreteType": "AddUnionPayload",
        "kind": "LinkedField",
        "name": "addUnion",
        "plural": false,
        "selections": [
          {
            "alias": null,
            "args": null,
            "concreteType": "EdgeOfThirdPartyInfoDto",
            "kind": "LinkedField",
            "name": "thirdPartyInfoDtoEdge",
            "plural": false,
            "selections": [
              (v3/*: any*/),
              {
                "alias": null,
                "args": null,
                "concreteType": "ThirdPartyInfoDto",
                "kind": "LinkedField",
                "name": "node",
                "plural": false,
                "selections": [
                  (v4/*: any*/),
                  (v5/*: any*/),
                  (v6/*: any*/)
                ],
                "storageKey": null
              }
            ],
            "storageKey": null
          }
        ],
        "storageKey": null
      }
    ],
    "type": "Mutation",
    "abstractKey": null
  },
  "kind": "Request",
  "operation": {
    "argumentDefinitions": [
      (v1/*: any*/),
      (v0/*: any*/)
    ],
    "kind": "Operation",
    "name": "CreateUnionMutation",
    "selections": [
      {
        "alias": null,
        "args": (v2/*: any*/),
        "concreteType": "AddUnionPayload",
        "kind": "LinkedField",
        "name": "addUnion",
        "plural": false,
        "selections": [
          {
            "alias": null,
            "args": null,
            "concreteType": "EdgeOfThirdPartyInfoDto",
            "kind": "LinkedField",
            "name": "thirdPartyInfoDtoEdge",
            "plural": false,
            "selections": [
              (v3/*: any*/),
              {
                "alias": null,
                "args": null,
                "concreteType": "ThirdPartyInfoDto",
                "kind": "LinkedField",
                "name": "node",
                "plural": false,
                "selections": [
                  (v4/*: any*/),
                  (v5/*: any*/),
                  (v6/*: any*/),
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "id",
                    "storageKey": null
                  }
                ],
                "storageKey": null
              }
            ],
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "filters": null,
            "handle": "appendEdge",
            "key": "",
            "kind": "LinkedHandle",
            "name": "thirdPartyInfoDtoEdge",
            "handleArgs": [
              {
                "kind": "Variable",
                "name": "connections",
                "variableName": "connections"
              }
            ]
          }
        ],
        "storageKey": null
      }
    ]
  },
  "params": {
    "cacheID": "052f9793f19305630fa91528f3e1511c",
    "id": null,
    "metadata": {},
    "name": "CreateUnionMutation",
    "operationKind": "mutation",
    "text": "mutation CreateUnionMutation(\n  $input: AddUnionInput!\n) {\n  addUnion(input: $input) {\n    thirdPartyInfoDtoEdge {\n      cursor\n      node {\n        value\n        label\n        guid\n        id\n      }\n    }\n  }\n}\n"
  }
};
})();

(node as any).hash = "a5b4110023e67d64a140d7dfa319cfb7";

export default node;
