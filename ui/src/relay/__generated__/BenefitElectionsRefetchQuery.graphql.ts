/**
 * @generated SignedSource<<6c4a4297fd642cc2394f51c0789e838c>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ConcreteRequest } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type SortEnumType = "ASC" | "DESC" | "%future added value";
export type BenefitElectionsRosterDtoSortInput = {
  associationID?: SortEnumType | null | undefined;
  benefitName?: SortEnumType | null | undefined;
  dba?: SortEnumType | null | undefined;
  discount?: SortEnumType | null | undefined;
  effectiveEndDate?: SortEnumType | null | undefined;
  effectiveStartDate?: SortEnumType | null | undefined;
  elected?: SortEnumType | null | undefined;
  employerGUID?: SortEnumType | null | undefined;
  employerName?: SortEnumType | null | undefined;
  fein?: SortEnumType | null | undefined;
  id?: SortEnumType | null | undefined;
  lastReport?: SortEnumType | null | undefined;
  override?: SortEnumType | null | undefined;
};
export type BenefitElectionsRosterDtoFilterInput = {
  and?: ReadonlyArray<BenefitElectionsRosterDtoFilterInput> | null | undefined;
  associationID?: StringOperationFilterInput | null | undefined;
  benefitName?: StringOperationFilterInput | null | undefined;
  dba?: StringOperationFilterInput | null | undefined;
  discount?: DecimalOperationFilterInput | null | undefined;
  effectiveEndDate?: DateTimeOperationFilterInput | null | undefined;
  effectiveStartDate?: DateTimeOperationFilterInput | null | undefined;
  elected?: BooleanOperationFilterInput | null | undefined;
  employerGUID?: UuidOperationFilterInput | null | undefined;
  employerName?: StringOperationFilterInput | null | undefined;
  fein?: StringOperationFilterInput | null | undefined;
  id?: IdOperationFilterInput | null | undefined;
  lastReport?: DateTimeOperationFilterInput | null | undefined;
  or?: ReadonlyArray<BenefitElectionsRosterDtoFilterInput> | null | undefined;
  override?: DecimalOperationFilterInput | null | undefined;
};
export type IdOperationFilterInput = {
  eq?: string | null | undefined;
  in?: ReadonlyArray<string | null | undefined> | null | undefined;
  neq?: string | null | undefined;
  nin?: ReadonlyArray<string | null | undefined> | null | undefined;
};
export type StringOperationFilterInput = {
  and?: ReadonlyArray<StringOperationFilterInput> | null | undefined;
  contains?: string | null | undefined;
  endsWith?: string | null | undefined;
  eq?: string | null | undefined;
  in?: ReadonlyArray<string | null | undefined> | null | undefined;
  ncontains?: string | null | undefined;
  nendsWith?: string | null | undefined;
  neq?: string | null | undefined;
  nin?: ReadonlyArray<string | null | undefined> | null | undefined;
  nstartsWith?: string | null | undefined;
  or?: ReadonlyArray<StringOperationFilterInput> | null | undefined;
  startsWith?: string | null | undefined;
};
export type DecimalOperationFilterInput = {
  eq?: any | null | undefined;
  gt?: any | null | undefined;
  gte?: any | null | undefined;
  in?: ReadonlyArray<any | null | undefined> | null | undefined;
  lt?: any | null | undefined;
  lte?: any | null | undefined;
  neq?: any | null | undefined;
  ngt?: any | null | undefined;
  ngte?: any | null | undefined;
  nin?: ReadonlyArray<any | null | undefined> | null | undefined;
  nlt?: any | null | undefined;
  nlte?: any | null | undefined;
};
export type DateTimeOperationFilterInput = {
  eq?: any | null | undefined;
  gt?: any | null | undefined;
  gte?: any | null | undefined;
  in?: ReadonlyArray<any | null | undefined> | null | undefined;
  lt?: any | null | undefined;
  lte?: any | null | undefined;
  neq?: any | null | undefined;
  ngt?: any | null | undefined;
  ngte?: any | null | undefined;
  nin?: ReadonlyArray<any | null | undefined> | null | undefined;
  nlt?: any | null | undefined;
  nlte?: any | null | undefined;
};
export type BooleanOperationFilterInput = {
  eq?: boolean | null | undefined;
  neq?: boolean | null | undefined;
};
export type UuidOperationFilterInput = {
  eq?: any | null | undefined;
  gt?: any | null | undefined;
  gte?: any | null | undefined;
  in?: ReadonlyArray<any | null | undefined> | null | undefined;
  lt?: any | null | undefined;
  lte?: any | null | undefined;
  neq?: any | null | undefined;
  ngt?: any | null | undefined;
  ngte?: any | null | undefined;
  nin?: ReadonlyArray<any | null | undefined> | null | undefined;
  nlt?: any | null | undefined;
  nlte?: any | null | undefined;
};
export type BenefitElectionsRefetchQuery$variables = {
  after?: string | null | undefined;
  chapterId: string;
  first?: number | null | undefined;
  order?: ReadonlyArray<BenefitElectionsRosterDtoSortInput> | null | undefined;
  where?: BenefitElectionsRosterDtoFilterInput | null | undefined;
};
export type BenefitElectionsRefetchQuery$data = {
  readonly " $fragmentSpreads": FragmentRefs<"BenefitElectionsTableFragment">;
};
export type BenefitElectionsRefetchQuery = {
  response: BenefitElectionsRefetchQuery$data;
  variables: BenefitElectionsRefetchQuery$variables;
};

const node: ConcreteRequest = (function(){
var v0 = [
  {
    "defaultValue": null,
    "kind": "LocalArgument",
    "name": "after"
  },
  {
    "defaultValue": null,
    "kind": "LocalArgument",
    "name": "chapterId"
  },
  {
    "defaultValue": 20,
    "kind": "LocalArgument",
    "name": "first"
  },
  {
    "defaultValue": null,
    "kind": "LocalArgument",
    "name": "order"
  },
  {
    "defaultValue": null,
    "kind": "LocalArgument",
    "name": "where"
  }
],
v1 = [
  {
    "kind": "Variable",
    "name": "after",
    "variableName": "after"
  },
  {
    "kind": "Variable",
    "name": "chapterId",
    "variableName": "chapterId"
  },
  {
    "kind": "Variable",
    "name": "first",
    "variableName": "first"
  },
  {
    "kind": "Variable",
    "name": "order",
    "variableName": "order"
  },
  {
    "kind": "Variable",
    "name": "where",
    "variableName": "where"
  }
];
return {
  "fragment": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Fragment",
    "metadata": null,
    "name": "BenefitElectionsRefetchQuery",
    "selections": [
      {
        "args": (v1/*: any*/),
        "kind": "FragmentSpread",
        "name": "BenefitElectionsTableFragment"
      }
    ],
    "type": "Query",
    "abstractKey": null
  },
  "kind": "Request",
  "operation": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Operation",
    "name": "BenefitElectionsRefetchQuery",
    "selections": [
      {
        "alias": null,
        "args": (v1/*: any*/),
        "concreteType": "BenefitElectionsRosterDtoConnection",
        "kind": "LinkedField",
        "name": "benefitElectionRosterByChapterId",
        "plural": false,
        "selections": [
          {
            "alias": null,
            "args": null,
            "concreteType": "BenefitElectionsRosterDtoEdge",
            "kind": "LinkedField",
            "name": "edges",
            "plural": true,
            "selections": [
              {
                "alias": null,
                "args": null,
                "concreteType": "BenefitElectionsRosterDto",
                "kind": "LinkedField",
                "name": "node",
                "plural": false,
                "selections": [
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "id",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "employerName",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "fein",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "dba",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "benefitName",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "override",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "effectiveStartDate",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "effectiveEndDate",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "elected",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "employerGUID",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "associationID",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "discount",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "lastReport",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "__typename",
                    "storageKey": null
                  }
                ],
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "cursor",
                "storageKey": null
              }
            ],
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "concreteType": "PageInfo",
            "kind": "LinkedField",
            "name": "pageInfo",
            "plural": false,
            "selections": [
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "endCursor",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "hasNextPage",
                "storageKey": null
              }
            ],
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "totalCount",
            "storageKey": null
          }
        ],
        "storageKey": null
      },
      {
        "alias": null,
        "args": (v1/*: any*/),
        "filters": [
          "chapterId",
          "order",
          "where"
        ],
        "handle": "connection",
        "key": "BenefitElectionsTableFragment_benefitElectionRosterByChapterId",
        "kind": "LinkedHandle",
        "name": "benefitElectionRosterByChapterId"
      }
    ]
  },
  "params": {
    "cacheID": "218ae688e34e5e49e317ed904cd5cc3c",
    "id": null,
    "metadata": {},
    "name": "BenefitElectionsRefetchQuery",
    "operationKind": "query",
    "text": "query BenefitElectionsRefetchQuery(\n  $after: String\n  $chapterId: ID!\n  $first: Int = 20\n  $order: [BenefitElectionsRosterDtoSortInput!] = null\n  $where: BenefitElectionsRosterDtoFilterInput = null\n) {\n  ...BenefitElectionsTableFragment_AvdXu\n}\n\nfragment BenefitElectionsTableFragment_AvdXu on Query {\n  benefitElectionRosterByChapterId(first: $first, after: $after, chapterId: $chapterId, order: $order, where: $where) {\n    edges {\n      node {\n        id\n        employerName\n        fein\n        dba\n        benefitName\n        override\n        effectiveStartDate\n        effectiveEndDate\n        elected\n        employerGUID\n        associationID\n        discount\n        lastReport\n        __typename\n      }\n      cursor\n    }\n    pageInfo {\n      endCursor\n      hasNextPage\n    }\n    totalCount\n  }\n}\n"
  }
};
})();

(node as any).hash = "65b98db6e1b8dcf50c51e3b7617acddf";

export default node;
