/**
 * @generated SignedSource<<d7ecaade7fa4fddc1f4a62104a4adfd9>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ConcreteRequest } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type EmployeeDefaultSettingsByEmployerListPaginationQuery$variables = {
  after?: string | null | undefined;
  employerId: string;
  first?: number | null | undefined;
};
export type EmployeeDefaultSettingsByEmployerListPaginationQuery$data = {
  readonly " $fragmentSpreads": FragmentRefs<"useEmployeeDefaultSettingsQueries_EmployerList_RefetchableFragment">;
};
export type EmployeeDefaultSettingsByEmployerListPaginationQuery = {
  response: EmployeeDefaultSettingsByEmployerListPaginationQuery$data;
  variables: EmployeeDefaultSettingsByEmployerListPaginationQuery$variables;
};

const node: ConcreteRequest = (function(){
var v0 = [
  {
    "defaultValue": null,
    "kind": "LocalArgument",
    "name": "after"
  },
  {
    "defaultValue": null,
    "kind": "LocalArgument",
    "name": "employerId"
  },
  {
    "defaultValue": 10,
    "kind": "LocalArgument",
    "name": "first"
  }
],
v1 = [
  {
    "kind": "Variable",
    "name": "after",
    "variableName": "after"
  },
  {
    "kind": "Variable",
    "name": "employerId",
    "variableName": "employerId"
  },
  {
    "kind": "Variable",
    "name": "first",
    "variableName": "first"
  }
];
return {
  "fragment": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Fragment",
    "metadata": null,
    "name": "EmployeeDefaultSettingsByEmployerListPaginationQuery",
    "selections": [
      {
        "args": (v1/*: any*/),
        "kind": "FragmentSpread",
        "name": "useEmployeeDefaultSettingsQueries_EmployerList_RefetchableFragment"
      }
    ],
    "type": "Query",
    "abstractKey": null
  },
  "kind": "Request",
  "operation": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Operation",
    "name": "EmployeeDefaultSettingsByEmployerListPaginationQuery",
    "selections": [
      {
        "alias": null,
        "args": (v1/*: any*/),
        "concreteType": "EmployeeDefaultSettingsConnection",
        "kind": "LinkedField",
        "name": "employeeDefaultSettingsByEmployerId",
        "plural": false,
        "selections": [
          {
            "alias": null,
            "args": null,
            "concreteType": "EmployeeDefaultSettingsEdge",
            "kind": "LinkedField",
            "name": "edges",
            "plural": true,
            "selections": [
              {
                "alias": null,
                "args": null,
                "concreteType": "EmployeeDefaultSettings",
                "kind": "LinkedField",
                "name": "node",
                "plural": false,
                "selections": [
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "employeeId",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "defaultAgreementId",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "defaultClassificationId",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "defaultHourlyRate",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "__typename",
                    "storageKey": null
                  }
                ],
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "cursor",
                "storageKey": null
              }
            ],
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "concreteType": "PageInfo",
            "kind": "LinkedField",
            "name": "pageInfo",
            "plural": false,
            "selections": [
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "hasNextPage",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "endCursor",
                "storageKey": null
              }
            ],
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "totalCount",
            "storageKey": null
          }
        ],
        "storageKey": null
      },
      {
        "alias": null,
        "args": (v1/*: any*/),
        "filters": [
          "employerId"
        ],
        "handle": "connection",
        "key": "UseEmployeeDefaultSettingsQueries_employeeDefaultSettingsByEmployerId",
        "kind": "LinkedHandle",
        "name": "employeeDefaultSettingsByEmployerId"
      }
    ]
  },
  "params": {
    "cacheID": "a11b775e327befec69f15cc9478535ce",
    "id": null,
    "metadata": {},
    "name": "EmployeeDefaultSettingsByEmployerListPaginationQuery",
    "operationKind": "query",
    "text": "query EmployeeDefaultSettingsByEmployerListPaginationQuery(\n  $after: String\n  $employerId: ID!\n  $first: Int = 10\n) {\n  ...useEmployeeDefaultSettingsQueries_EmployerList_RefetchableFragment_2553ZF\n}\n\nfragment useEmployeeDefaultSettingsQueries_EmployeeDefaultsNode on EmployeeDefaultSettings {\n  employeeId\n  defaultAgreementId\n  defaultClassificationId\n  defaultHourlyRate\n}\n\nfragment useEmployeeDefaultSettingsQueries_EmployerList_RefetchableFragment_2553ZF on Query {\n  employeeDefaultSettingsByEmployerId(employerId: $employerId, first: $first, after: $after) {\n    edges {\n      node {\n        employeeId\n        defaultAgreementId\n        defaultClassificationId\n        defaultHourlyRate\n        ...useEmployeeDefaultSettingsQueries_EmployeeDefaultsNode\n        __typename\n      }\n      cursor\n    }\n    pageInfo {\n      hasNextPage\n      endCursor\n    }\n    totalCount\n  }\n}\n"
  }
};
})();

(node as any).hash = "19d0c60c6a24501949edc9c3e2a7435a";

export default node;
