// TypeScript type definitions for Relay cache persistence

export interface CachedBlob {
  version: string;
  records: Record<string, any>;
}

export interface CacheMetrics {
  loads: number;
  cacheHits: number;
  cacheMisses: number;
  startTime: number;
  loadTimes: number[];
  byQuery: Record<string, QueryMetrics>;
}

export interface QueryMetrics {
  loads: number;
  cacheHits: number;
  cacheMisses: number;
  loadTimes: number[];
}

export interface FormattedCacheMetrics {
  cacheSize: string;
  totalLoads: number;
  cacheHits: number;
  cacheMisses: number;
  hitRate: string;
  avgLoadTime: string;
  lastUpdated: string;
  sessionDuration: string;
}

export interface FormattedQueryMetrics {
  queryName: string;
  totalLoads: number;
  cacheHits: number;
  cacheMisses: number;
  hitRate: string;
  avgLoadTime: string;
}

export type PersistenceError = 
  | 'QuotaExceededError'
  | 'NS_ERROR_DOM_QUOTA_REACHED'
  | 'VersionError' 
  | 'InvalidStateError';