// Production observability for Relay cache persistence
// Lightweight metrics collection that can be enabled per environment

interface ProductionMetrics {
  cacheHits: number;
  cacheMisses: number;
  quotaExceeded: number;
  persistenceDisabled: boolean;
  sessionStart: number;
}

class RelayObservability {
  private metrics: ProductionMetrics = {
    cacheHits: 0,
    cacheMisses: 0,
    quotaExceeded: 0,
    persistenceDisabled: false,
    sessionStart: Date.now()
  };

  private enabled = false;

  constructor() {
    // Enable metrics collection based on environment variable
    this.enabled = process.env.NODE_ENV === 'production' && 
                   (process.env.VITE_ENABLE_RELAY_METRICS === 'true');
    
    if (this.enabled) {
      // Report metrics periodically
      setInterval(() => this.reportMetrics(), 5 * 60 * 1000); // Every 5 minutes
    }
  }

  trackCacheHit(): void {
    if (this.enabled) {
      this.metrics.cacheHits++;
    }
  }

  trackCacheMiss(): void {
    if (this.enabled) {
      this.metrics.cacheMisses++;
    }
  }

  trackQuotaExceeded(): void {
    if (this.enabled) {
      this.metrics.quotaExceeded++;
    }
  }

  setPersistenceDisabled(): void {
    if (this.enabled) {
      this.metrics.persistenceDisabled = true;
    }
  }

  private reportMetrics(): void {
    if (!this.enabled) return;

    const sessionDuration = Date.now() - this.metrics.sessionStart;
    const totalRequests = this.metrics.cacheHits + this.metrics.cacheMisses;
    const hitRate = totalRequests > 0 ? (this.metrics.cacheHits / totalRequests) * 100 : 0;

    // Use performance.mark for telemetry systems to scrape
    try {
      performance.mark('relay-cache-metrics', {
        detail: {
          cacheHits: this.metrics.cacheHits,
          cacheMisses: this.metrics.cacheMisses,
          hitRate: hitRate.toFixed(1),
          quotaExceeded: this.metrics.quotaExceeded,
          persistenceDisabled: this.metrics.persistenceDisabled,
          sessionDuration: Math.round(sessionDuration / 1000), // seconds
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      // Fallback to console.debug for log collection
      console.debug('[RelayCache]', {
        cacheHits: this.metrics.cacheHits,
        cacheMisses: this.metrics.cacheMisses,
        hitRate: `${hitRate.toFixed(1)}%`,
        quotaExceeded: this.metrics.quotaExceeded,
        persistenceDisabled: this.metrics.persistenceDisabled,
        sessionDurationSeconds: Math.round(sessionDuration / 1000)
      });
    }
  }

  // Expose metrics for external telemetry systems
  getMetrics(): ProductionMetrics & { hitRate: string; sessionDuration: number } {
    const sessionDuration = Date.now() - this.metrics.sessionStart;
    const totalRequests = this.metrics.cacheHits + this.metrics.cacheMisses;
    const hitRate = totalRequests > 0 ? (this.metrics.cacheHits / totalRequests) * 100 : 0;

    return {
      ...this.metrics,
      hitRate: `${hitRate.toFixed(1)}%`,
      sessionDuration: Math.round(sessionDuration / 1000)
    };
  }
}

// Export singleton instance
export const relayObservability = new RelayObservability();