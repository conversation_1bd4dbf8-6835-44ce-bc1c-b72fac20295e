/**
 * Phase 5: Validation & Testing - Validation Function Tests
 *
 * Comprehensive tests for the runtime validation functions,
 * ensuring they properly validate GraphQL types and provide
 * meaningful error reporting.
 */

import {
    validateModifyTimeSheetInput,
    validateModifyPayStubInput,
    validateModifyPayStubDetailInput,
    validateTimesheetDomainModel,
    validatePayStubDomainModel,
    validatePayStubDetailDomainModel,
    validateTimesheetInputComprehensive,
    validatePayStubInputComprehensive,
    validatePayStubDetailInputComprehensive,
    validateTimesheetBusinessRules,
    safeValidateForGraphQL,
    type ValidationResult
} from '../timesheet-validation';

import type { ModifyTimeSheetInput, ModifyPayStubInput, ModifyPayStubDetailInput } from '../../types/graphql-timesheet';

// =============================================================================
// TEST DATA FACTORIES
// =============================================================================

function createValidTimeSheetInput(): ModifyTimeSheetInput {
    return {
        id: '12345',
        employerGuid: 'test-employer-guid',
        name: 'Test Timesheet',
        status: 'draft',
        type: 'weekly',
        readOnly: false,
        showDTHoursColumn: true,
        showCostCenterColumn: false,
        showBonusColumn: true,
        showExpensesColumn: false,
        showEarningsCodesColumn: true,
        modificationDate: '2023-12-01T10:00:00.000Z',
        modifyPayStubs: [createValidPayStubInput()]
    };
}

function createValidPayStubInput(): ModifyPayStubInput {
    return {
        id: 'paystub-123',
        employeeId: 'RW1wbG95ZWU6NDU2', // Global ID for Employee:456
        employeeName: 'John Doe',
        name: 'Week 1',
        stHours: 40,
        otHours: 5,
        dtHours: 2,
        totalHours: 47,
        bonus: 100,
        expenses: 50,
        expanded: true,
        inEdit: false,
        delete: false,
        details: [createValidPayStubDetailInput()]
    } as any;
}

function createValidPayStubDetailInput(): ModifyPayStubDetailInput {
    return {
        id: 'detail-789',
        payStubId: 'paystub-123',
        workDate: '2023-12-01',
        name: 'Friday Work',
        stHours: 8,
        otHours: 1,
        dtHours: 0.5,
        totalHours: 9.5,
        jobCode: 'JOB001',
        costCenter: 'CC001',
        agreementId: 1,
        classificationId: 2,
        subClassificationId: 3,
        hourlyRate: 25.5,
        bonus: 25,
        expenses: 15,
        earningsCode: 'REG',
        delete: false
    } as any;
}

// =============================================================================
// BASIC VALIDATION TESTS
// =============================================================================

describe('Basic Validation Functions', () => {
    describe('validateModifyTimeSheetInput', () => {
        it('should validate correct input', () => {
            const validInput = createValidTimeSheetInput();
            expect(validateModifyTimeSheetInput(validInput)).toBe(true);
        });

        it('should reject null/undefined input', () => {
            expect(validateModifyTimeSheetInput(null)).toBe(false);
            expect(validateModifyTimeSheetInput(undefined)).toBe(false);
        });

        it('should reject non-object input', () => {
            expect(validateModifyTimeSheetInput('string')).toBe(false);
            expect(validateModifyTimeSheetInput(123)).toBe(false);
            expect(validateModifyTimeSheetInput([])).toBe(false);
            expect(validateModifyTimeSheetInput(true)).toBe(false);
        });

        it('should reject input missing required id field', () => {
            const invalidInput = {
                employerGuid: 'test-guid',
                name: 'Test'
            };
            expect(validateModifyTimeSheetInput(invalidInput)).toBe(false);
        });

        it('should reject input with numeric id', () => {
            const invalidInput = {
                id: 'string-id',
                employerGuid: 'test-guid'
            };
            expect(validateModifyTimeSheetInput(invalidInput)).toBe(false);
        });

        it('should reject input missing employerGuid', () => {
            const invalidInput = {
                id: '123',
                name: 'Test'
            };
            expect(validateModifyTimeSheetInput(invalidInput)).toBe(false);
        });

        it('should validate optional string fields', () => {
            const validInput = {
                id: '123',
                employerGuid: 'test-guid',
                name: 'Valid Name',
                status: 'draft',
                type: 'weekly'
            };
            expect(validateModifyTimeSheetInput(validInput)).toBe(true);

            const invalidInput = {
                id: '123',
                employerGuid: 'test-guid',
                name: 123 // Should be string
            };
            expect(validateModifyTimeSheetInput(invalidInput)).toBe(false);
        });

        it('should validate optional boolean fields', () => {
            const validInput = {
                id: '123',
                employerGuid: 'test-guid',
                readOnly: true,
                showDTHoursColumn: false
            };
            expect(validateModifyTimeSheetInput(validInput)).toBe(true);

            const invalidInput = {
                id: '123',
                employerGuid: 'test-guid',
                readOnly: 'true' // Should be boolean
            };
            expect(validateModifyTimeSheetInput(invalidInput)).toBe(false);
        });

        it('should validate payStubs array', () => {
            const validInput = {
                id: '123',
                employerGuid: 'test-guid',
                modifyPayStubs: [createValidPayStubInput()]
            };
            expect(validateModifyTimeSheetInput(validInput)).toBe(true);

            const invalidInput = {
                id: '123',
                employerGuid: 'test-guid',
                modifyPayStubs: 'not-an-array'
            };
            expect(validateModifyTimeSheetInput(invalidInput)).toBe(false);
        });

        it('should validate date fields', () => {
            const validInput = {
                id: '123',
                employerGuid: 'test-guid',
                modificationDate: '2023-12-01T10:00:00.000Z'
            };
            expect(validateModifyTimeSheetInput(validInput)).toBe(true);

            const invalidInput = {
                id: '123',
                employerGuid: 'test-guid',
                modificationDate: 'invalid-date'
            };
            expect(validateModifyTimeSheetInput(invalidInput)).toBe(false);
        });
    });

    describe('validateModifyPayStubInput', () => {
        it('should validate correct input', () => {
            const validInput = createValidPayStubInput();
            expect(validateModifyPayStubInput(validInput)).toBe(true);
        });

        it('should reject input missing employeeId', () => {
            const invalidInput = {
                employeeName: 'John Doe',
                stHours: 40
            };
            expect(validateModifyPayStubInput(invalidInput)).toBe(false);
        });

        it('should reject invalid Global ID employeeId', () => {
            const invalidInput = {
                employeeId: 'invalid-global-id',
                employeeName: 'John Doe'
            };
            expect(validateModifyPayStubInput(invalidInput)).toBe(false);
        });

        it('should reject negative hours', () => {
            const invalidInput = {
                employeeId: 'RW1wbG95ZWU6MTIz', // Global ID for Employee:123
                stHours: -5
            };
            expect(validateModifyPayStubInput(invalidInput)).toBe(false);
        });

        it('should allow zero hours', () => {
            const validInput = {
                employeeId: 'RW1wbG95ZWU6MTIz', // Global ID for Employee:123
                stHours: 0,
                otHours: 0,
                dtHours: 0
            };
            expect(validateModifyPayStubInput(validInput)).toBe(true);
        });

        it('should validate decimal hours', () => {
            const validInput = {
                employeeId: 'RW1wbG95ZWU6MTIz', // Global ID for Employee:123
                stHours: 7.5,
                otHours: 1.25,
                dtHours: 0.75
            };
            expect(validateModifyPayStubInput(validInput)).toBe(true);
        });

        it('should validate details array', () => {
            const validInput = {
                employeeId: 'RW1wbG95ZWU6MTIz', // Global ID for Employee:123
                details: [createValidPayStubDetailInput()]
            };
            expect(validateModifyPayStubInput(validInput)).toBe(true);

            const invalidInput = {
                employeeId: 'RW1wbG95ZWU6MTIz', // Global ID for Employee:123
                details: 'not-an-array'
            };
            expect(validateModifyPayStubInput(invalidInput)).toBe(false);
        });
    });

    describe('validateModifyPayStubDetailInput', () => {
        it('should validate correct input', () => {
            const validInput = createValidPayStubDetailInput();
            expect(validateModifyPayStubDetailInput(validInput)).toBe(true);
        });

        it('should validate minimal input', () => {
            const minimalInput = {};
            expect(validateModifyPayStubDetailInput(minimalInput)).toBe(true);
        });

        it('should validate workDate format', () => {
            const validInput = {
                workDate: '2023-12-01'
            };
            expect(validateModifyPayStubDetailInput(validInput)).toBe(true);

            const invalidInput = {
                workDate: '12/01/2023' // Wrong format
            };
            expect(validateModifyPayStubDetailInput(invalidInput)).toBe(false);
        });

        it('should reject negative hours', () => {
            const invalidInput = {
                stHours: -2
            };
            expect(validateModifyPayStubDetailInput(invalidInput)).toBe(false);
        });

        it('should validate string fields', () => {
            const validInput = {
                id: 'detail-123',
                payStubId: 'paystub-456',
                name: 'Work Detail',
                jobCode: 'JOB001',
                costCenter: 'CC001',
                earningsCode: 'REG'
            };
            expect(validateModifyPayStubDetailInput(validInput)).toBe(true);

            const invalidInput = {
                id: 123 // Should be string
            };
            expect(validateModifyPayStubDetailInput(invalidInput)).toBe(false);
        });

        it('should validate number fields', () => {
            const validInput = {
                stHours: 8,
                otHours: 1.5,
                agreementId: 123,
                hourlyRate: 25.5,
                bonus: 50,
                expenses: 25
            };
            expect(validateModifyPayStubDetailInput(validInput)).toBe(true);

            const invalidInput = {
                hourlyRate: 'twenty-five' // Should be number
            };
            expect(validateModifyPayStubDetailInput(invalidInput)).toBe(false);
        });
    });
});

// =============================================================================
// COMPREHENSIVE VALIDATION TESTS
// =============================================================================

describe('Comprehensive Validation Functions', () => {
    describe('validateTimesheetInputComprehensive', () => {
        it('should validate correct input with no errors', () => {
            const validInput = createValidTimeSheetInput();
            const result = validateTimesheetInputComprehensive(validInput);

            expect(result.isValid).toBe(true);
            expect(result.errors).toHaveLength(0);
            expect(result.warnings).toBeDefined();
        });

        it('should provide detailed error information', () => {
            const invalidInput = {
                id: 'string-id', // Wrong type
                employerGuid: null, // Missing required
                name: 123, // Wrong type
                modifyPayStubs: 'not-array' // Wrong type
            };

            const result = validateTimesheetInputComprehensive(invalidInput);

            expect(result.isValid).toBe(false);
            expect(result.errors.length).toBeGreaterThan(0);

            // Check for specific errors
            const errorFields = result.errors.map((e) => e.field);
            expect(errorFields).toContain('id');
            expect(errorFields).toContain('employerGuid');
            expect(errorFields).toContain('name');
            expect(errorFields).toContain('modifyPayStubs');

            // Check error structure
            result.errors.forEach((error) => {
                expect(error).toHaveProperty('field');
                expect(error).toHaveProperty('message');
                expect(error).toHaveProperty('code');
                expect(error).toHaveProperty('value');
            });
        });

        it('should validate nested PayStub errors', () => {
            const invalidInput = {
                id: '123',
                employerGuid: 'test-guid',
                modifyPayStubs: [
                    {
                        employeeId: 'string-id', // Wrong type
                        stHours: -5 // Negative
                    },
                    {
                        employeeId: '', // Invalid value - empty string
                        details: [
                            {
                                stHours: -2 // Negative in detail
                            }
                        ]
                    }
                ]
            };

            const result = validateTimesheetInputComprehensive(invalidInput);

            expect(result.isValid).toBe(false);
            expect(result.errors.length).toBeGreaterThan(0);

            // Check for nested field errors
            const errorFields = result.errors.map((e) => e.field);
            expect(errorFields.some((f) => f.includes('modifyPayStubs[0]'))).toBe(true);
            expect(errorFields.some((f) => f.includes('modifyPayStubs[1]'))).toBe(true);
        });

        it('should provide warnings for suspicious data', () => {
            const suspiciousInput = createValidTimeSheetInput();
            if (suspiciousInput.modifyPayStubs?.[0]) {
                suspiciousInput.modifyPayStubs[0].stHours = 25; // Excessive hours
                suspiciousInput.name = ''; // Empty name
            }

            const result = validateTimesheetInputComprehensive(suspiciousInput);

            expect(result.isValid).toBe(true); // Still valid
            expect(result.warnings.length).toBeGreaterThan(0);

            const warningCodes = result.warnings.map((w) => w.code);
            expect(warningCodes).toContain('EXCESSIVE_HOURS');
            expect(warningCodes).toContain('EMPTY_NAME');
        });

        it('should handle edge case values', () => {
            const edgeCaseInput = {
                id: '0', // Edge case: zero ID
                employerGuid: 'test-guid'
            };

            const result = validateTimesheetInputComprehensive(edgeCaseInput);

            expect(result.isValid).toBe(false);
            expect(result.errors.some((e) => e.code === 'INVALID_ID_VALUE')).toBe(true);
        });
    });

    describe('validatePayStubInputComprehensive', () => {
        it('should validate correct PayStub input', () => {
            const validInput = createValidPayStubInput();
            const result = validatePayStubInputComprehensive(validInput);

            expect(result.isValid).toBe(true);
            expect(result.errors).toHaveLength(0);
        });

        it('should detect excessive hours', () => {
            const inputWithExcessiveHours = {
                employeeId: 'RW1wbG95ZWU6MTIz', // Global ID for Employee:123
                stHours: 30, // Excessive for a single day
                otHours: 10
            };

            const result = validatePayStubInputComprehensive(inputWithExcessiveHours);

            expect(result.isValid).toBe(true); // Still valid
            expect(result.warnings.length).toBeGreaterThan(0);
            expect(result.warnings.some((w) => w.code === 'EXCESSIVE_HOURS')).toBe(true);
        });

        it('should validate nested details', () => {
            const inputWithInvalidDetails = {
                employeeId: 'RW1wbG95ZWU6MTIz', // Global ID for Employee:123
                details: [
                    {
                        workDate: 'invalid-date',
                        stHours: -5
                    }
                ]
            };

            const result = validatePayStubInputComprehensive(inputWithInvalidDetails);

            expect(result.isValid).toBe(false);
            expect(result.errors.some((e) => e.field.includes('details[0]'))).toBe(true);
        });
    });

    describe('validatePayStubDetailInputComprehensive', () => {
        it('should validate correct detail input', () => {
            const validInput = createValidPayStubDetailInput();
            const result = validatePayStubDetailInputComprehensive(validInput);

            expect(result.isValid).toBe(true);
            expect(result.errors).toHaveLength(0);
        });

        it('should warn about future work dates', () => {
            const futureDate = new Date();
            futureDate.setDate(futureDate.getDate() + 30);
            const futureDateString = futureDate.toISOString().split('T')[0];

            const inputWithFutureDate = {
                workDate: futureDateString
            };

            const result = validatePayStubDetailInputComprehensive(inputWithFutureDate);

            expect(result.isValid).toBe(true); // Still valid
            expect(result.warnings.some((w) => w.code === 'FUTURE_WORKDATE')).toBe(true);
        });

        it('should detect excessive daily hours', () => {
            const inputWithExcessiveHours = {
                stHours: 25 // More than 24 hours in a day
            };

            const result = validatePayStubDetailInputComprehensive(inputWithExcessiveHours);

            expect(result.isValid).toBe(true); // Still valid
            expect(result.warnings.some((w) => w.code === 'EXCESSIVE_DAILY_HOURS')).toBe(true);
        });

        it('should validate date formats strictly', () => {
            const invalidDates = [
                '2023-13-01', // Invalid month
                '2023-12-32', // Invalid day
                '12/01/2023', // Wrong format
                'December 1, 2023' // Wrong format
            ];

            invalidDates.forEach((invalidDate) => {
                const input = { workDate: invalidDate };
                const result = validatePayStubDetailInputComprehensive(input);

                expect(result.isValid).toBe(false);
                expect(result.errors.some((e) => e.code === 'INVALID_WORKDATE_FORMAT')).toBe(true);
            });
        });
    });
});

// =============================================================================
// BUSINESS RULES VALIDATION TESTS
// =============================================================================

describe('Business Rules Validation', () => {
    describe('validateTimesheetBusinessRules', () => {
        it('should pass validation for normal timesheet', () => {
            const validInput = createValidTimeSheetInput();
            const result = validateTimesheetBusinessRules(validInput);

            expect(result.isValid).toBe(true);
            expect(result.errors).toHaveLength(0);
        });

        it('should detect duplicate employee IDs', () => {
            const inputWithDuplicates: ModifyTimeSheetInput = {
                id: '123',
                employerGuid: 'test-guid',
                modifyPayStubs: [
                    { id: 'ps-1', employeeId: 'RW1wbG95ZWU6NDU2', employeeName: 'John Doe' },
                    { id: 'ps-2', employeeId: 'RW1wbG95ZWU6NDU2', employeeName: 'John Doe (duplicate)' },
                    { id: 'ps-3', employeeId: 'RW1wbG95ZWU6Nzg5', employeeName: 'Jane Smith' },
                    { id: 'ps-4', employeeId: 'RW1wbG95ZWU6NDU2', employeeName: 'John Doe (another duplicate)' }
                ]
            };

            const result = validateTimesheetBusinessRules(inputWithDuplicates);

            expect(result.isValid).toBe(true); // Not an error, just a warning
            expect(result.warnings.length).toBeGreaterThan(0);
            expect(result.warnings.some((w) => w.code === 'DUPLICATE_EMPLOYEES')).toBe(true);

            const duplicateWarning = result.warnings.find((w) => w.code === 'DUPLICATE_EMPLOYEES');
            expect(duplicateWarning?.value).toContain('RW1wbG95ZWU6NDU2'); // Global ID for Employee:456
        });

        it('should handle empty payStubs array', () => {
            const inputWithoutPayStubs: ModifyTimeSheetInput = {
                id: '123',
                employerGuid: 'test-guid',
                modifyPayStubs: []
            };

            const result = validateTimesheetBusinessRules(inputWithoutPayStubs);

            expect(result.isValid).toBe(true);
            expect(result.warnings).toHaveLength(0);
        });

        it('should handle undefined payStubs', () => {
            const inputWithUndefinedPayStubs: ModifyTimeSheetInput = {
                id: '123',
                employerGuid: 'test-guid'
                // payStubs: undefined
            };

            const result = validateTimesheetBusinessRules(inputWithUndefinedPayStubs);

            expect(result.isValid).toBe(true);
            expect(result.warnings).toHaveLength(0);
        });
    });
});

// =============================================================================
// UTILITY FUNCTION TESTS
// =============================================================================

describe('Utility Functions', () => {
    describe('safeValidateForGraphQL', () => {
        it('should return success for valid input', () => {
            const validInput = createValidPayStubInput();
            const result = safeValidateForGraphQL(validInput, validateModifyPayStubInput, 'Invalid PayStub');

            expect(result.success).toBe(true);
            if (result.success) {
                expect(result.data).toBe(validInput);
            }
        });

        it('should return error for invalid input', () => {
            const invalidInput = { invalid: 'data' };
            const result = safeValidateForGraphQL(invalidInput, validateModifyPayStubInput, 'Invalid PayStub');

            expect(result.success).toBe(false);
            if (!result.success) {
                expect(result.error).toBe('Invalid PayStub');
            }
        });

        it('should handle validation function exceptions', () => {
            const throwingValidator = (): boolean => {
                throw new Error('Validator error');
            };

            const result = safeValidateForGraphQL({}, throwingValidator as any, 'Validation failed');

            expect(result.success).toBe(false);
            if (!result.success) {
                expect(result.error).toBe('Validator error');
            }
        });
    });
});

// =============================================================================
// DOMAIN MODEL VALIDATION TESTS
// =============================================================================

describe('Domain Model Validation', () => {
    describe('validateTimesheetDomainModel', () => {
        it('should validate correct domain model', () => {
            const validDomain = {
                id: '123',
                employerGuid: 'test-guid',
                settings: { readOnly: false },
                meta: { canEdit: true },
                payStubs: [
                    {
                        id: 'paystub-1',
                        employeeId: '456',
                        hours: { standard: 40, overtime: 0, doubletime: 0, total: 40 },
                        amounts: { bonus: 0, expenses: 0 },
                        employee: { id: '456', fullName: 'John Doe' },
                        ui: { expanded: false },
                        details: [
                            {
                                id: 'detail-1',
                                workDate: '2023-12-01',
                                employeeId: '456',
                                hours: { standard: 8, overtime: 0, doubletime: 0, total: 8 },
                                job: { jobCode: 'JOB001' },
                                agreements: { agreementId: 1 },
                                amounts: { bonus: 0, expenses: 0 },
                                earnings: { earningsCode: 'REG' },
                                ui: { isEditing: false, validationErrors: [] }
                            }
                        ]
                    }
                ]
            };

            expect(validateTimesheetDomainModel(validDomain)).toBe(true);
        });

        it('should reject invalid domain model', () => {
            const invalidDomain = {
                id: '123', // Should be string
                employerGuid: 'test-guid',
                settings: 'invalid', // Should be object
                payStubs: [] // Valid but empty
            };

            expect(validateTimesheetDomainModel(invalidDomain)).toBe(false);
        });
    });

    describe('validatePayStubDomainModel', () => {
        it('should validate correct PayStub domain model', () => {
            const validPayStub = {
                id: 'paystub-1',
                employeeId: '456',
                hours: { standard: 40, overtime: 0, doubletime: 0, total: 40 },
                amounts: { bonus: 0, expenses: 0 },
                employee: { id: '456', fullName: 'John Doe' },
                ui: { expanded: false },
                details: []
            };

            expect(validatePayStubDomainModel(validPayStub)).toBe(true);
        });

        it('should reject invalid PayStub domain model', () => {
            const invalidPayStub = {
                id: '123', // Should be string
                employeeId: 'RW1wbG95ZWU6NDU2', // Global ID for Employee:456 // Should be string
                hours: 'invalid', // Should be object
                details: []
            };

            expect(validatePayStubDomainModel(invalidPayStub)).toBe(false);
        });
    });

    describe('validatePayStubDetailDomainModel', () => {
        it('should validate correct PayStubDetail domain model', () => {
            const validDetail = {
                id: 'detail-1',
                workDate: '2023-12-01',
                employeeId: '456',
                hours: { standard: 8, overtime: 0, doubletime: 0, total: 8 },
                job: { jobCode: 'JOB001' },
                agreements: { agreementId: 1 },
                amounts: { bonus: 0, expenses: 0 },
                earnings: { earningsCode: 'REG' },
                ui: { isEditing: false, validationErrors: [] }
            };

            expect(validatePayStubDetailDomainModel(validDetail)).toBe(true);
        });

        it('should reject invalid PayStubDetail domain model', () => {
            const invalidDetail = {
                id: '123', // Should be string
                workDate: new Date(), // Should be string
                employeeId: 'RW1wbG95ZWU6NDU2', // Global ID for Employee:456 // Should be string
                hours: 'invalid' // Should be object
            };

            expect(validatePayStubDetailDomainModel(invalidDetail)).toBe(false);
        });
    });
});

// =============================================================================
// PERFORMANCE AND STRESS TESTS
// =============================================================================

describe.skip('Performance and Stress Tests [TEMP DISABLED]', () => {
    it('should handle large timesheet validation efficiently', () => {
        const largeTimesheet: ModifyTimeSheetInput = {
            id: '123',
            employerGuid: 'test-guid',
            modifyPayStubs: Array.from({ length: 100 }, (_, i) => ({
                id: `ps-${i + 1}`,
                employeeId: `RW1wbG95ZWU6${btoa(String(i + 1))}`, // Generate Global ID
                employeeName: `Employee ${i + 1}`,
                stHours: 40,
                otHours: 5,
                details: Array.from({ length: 7 }, (_, j) => ({
                    workDate: `2023-12-${String(j + 1).padStart(2, '0')}`,
                    stHours: 8,
                    otHours: j > 4 ? 1 : 0
                }))
            }))
        };

        const startTime = performance.now();
        const result = validateTimesheetInputComprehensive(largeTimesheet);
        const endTime = performance.now();

        expect(result.isValid).toBe(true);
        expect(endTime - startTime).toBeLessThan(1000); // Should complete within 1 second
    });

    it('should handle validation of malformed data gracefully', () => {
        const malformedInputs = [
            null,
            undefined,
            '',
            0,
            false,
            [],
            { circular: null },
            { deeply: { nested: { invalid: { object: { structure: 'test' } } } } }
        ];

        malformedInputs.forEach((input) => {
            expect(() => {
                validateModifyTimeSheetInput(input);
                validateTimesheetInputComprehensive(input);
            }).not.toThrow();
        });
    });
});
