# Global ID Developer Guide

**Phase 2.5: Enhanced Developer Experience**

This guide provides comprehensive information for developers working with Global IDs in the timesheet application after the GUID to INT migration.

## Table of Contents

1. [Overview](#overview)
2. [Type System](#type-system)
3. [Utilities and Helpers](#utilities-and-helpers)
4. [Common Patterns](#common-patterns)
5. [Migration Guide](#migration-guide)
6. [Debugging and Development](#debugging-and-development)
7. [Best Practices](#best-practices)
8. [Troubleshooting](#troubleshooting)

## Overview

### What are Global IDs?

Global IDs are <PERSON><PERSON>'s solution for uniquely identifying nodes in a GraphQL schema. They are base64-encoded strings that contain both the entity type and the raw ID.

**Format**: `base64(EntityType:rawId)`

**Examples**:
- `UGF5U3R1YjoxMjM=` → decodes to `PayStub:123`
- `RW1wbG95ZWU6NDU2` → decodes to `Employee:456`

### Client Temporary IDs

For optimistic updates, we use client-generated temporary IDs with the format:
- `client:temp:paystub:${timestamp}_${random}`
- `client:temp:detail:${timestamp}_${random}`

## Type System

### Nominal Types

We use TypeScript nominal types to prevent accidental mixing of different ID types:

```typescript
import { PayStubGlobalID, PayStubDetailGlobalID, EmployeeGlobalID } from '@/src/types/GlobalID';

// These are type-safe at compile time
function processPayStub(payStubId: PayStubGlobalID) { ... }
function processEmployee(employeeId: EmployeeGlobalID) { ... }

// This would cause a TypeScript error:
// processPayStub(employeeId); // ❌ Type error
```

### Type Guards

Use type guards to check ID formats at runtime:

```typescript
import { GlobalIDGuards } from '@/src/types/GlobalID';

if (GlobalIDGuards.isAnyGlobalID(someId)) {
  // someId is confirmed to be a valid Global ID
}

if (GlobalIDGuards.isClientTempID(someId)) {
  // someId is a client temporary ID
}

if (GlobalIDGuards.isGlobalIDFormat(someId)) {
  // someId is a server-generated Global ID
}
```

## Utilities and Helpers

### Safe ID Conversion

Replace silent casting patterns with explicit error handling:

```typescript
import { safePayStubDetailId, safePayStubIdLenient } from '@/src/utils/safeIdConversion';

// ❌ Old pattern (silent failures)
const detailId = String(detailData.id || '');

// ✅ New pattern (explicit error handling)
const detailId = safePayStubDetailId(detailData);

// ✅ For optional IDs (returns empty string if missing)
const payStubId = safePayStubIdLenient(detailData);
```

### Global ID Helpers

Use the comprehensive helper utilities:

```typescript
import globalIdHelpers from '@/src/utils/globalIdHelpers';

// Debugging and inspection
console.log(globalIdHelpers.inspect('UGF5U3R1YjoxMjM=')); 
// Output: 🔍 [PayStub:123] encoded as "UGF5U3R1YjoxMjM="

// Analyze arrays of IDs
const analysis = globalIdHelpers.analyze([id1, id2, id3]);
console.log(`Found ${analysis.validGlobalIds} valid IDs, ${analysis.clientTempIds} temp IDs`);

// React key generation
const reactKey = globalIdHelpers.toKey(payStubId, 'paystub-row');

// Performance grouping
const groupedIds = globalIdHelpers.groupByType(allIds);
console.log(groupedIds.PayStub); // All PayStub IDs
```

### Relay ID Service

Use the enhanced RelayIdService for type-safe conversions:

```typescript
import { RelayIdService } from '@/src/services/RelayIdService';

// Type-safe conversion methods
const payStubGlobalId = RelayIdService.toPayStubGlobalId('123');
const detailGlobalId = RelayIdService.toPayStubDetailGlobalId('456');

// Validation with type checking
const typedId = RelayIdService.toTypedGlobalId(someValue, 'PayStub');
```

## Common Patterns

### Component Props

Always use typed Global IDs in component props:

```typescript
interface PayStubRowProps {
  payStubId: PayStubGlobalID;
  detailIds: PayStubDetailGlobalID[];
  employeeId: EmployeeGlobalID;
}

const PayStubRow: React.FC<PayStubRowProps> = ({ payStubId, detailIds, employeeId }) => {
  // Use safe conversion for any operations
  const safePayStubId = safePayStubId({ id: payStubId });
  
  return (
    <div data-testid={`paystub-row-${payStubId}`}>
      {/* Component content */}
    </div>
  );
};
```

### Optimistic Updates

Follow the client temporary ID pattern:

```typescript
// Generate client temp ID
const tempId = `client:temp:paystub:${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

// Use in optimistic response
const optimisticPayStub = {
  id: tempId,
  // ... other fields
};

// Server will replace with real Global ID
```

### Mutation Input Handling

Use type-safe converters in mutation preparation:

```typescript
import { TimesheetIdConverter } from '@/src/utils/timesheet-id-converters';

const buildMutationInput = (payStub: PayStubDomainModel) => {
  return {
    id: payStub.id, // Already a Global ID
    // Only convert if backend expects numeric ID
    numericEmployeeId: TimesheetIdConverter.employeeToNumeric(payStub.employeeId)
  };
};
```

### Error Handling

Handle ID conversion errors gracefully:

```typescript
import { SafeIdConversionError } from '@/src/utils/safeIdConversion';

try {
  const detailId = safePayStubDetailId(fragmentData);
  // Process with valid ID
} catch (error) {
  if (error instanceof SafeIdConversionError) {
    console.error(`ID conversion failed for ${error.context}:`, error.message);
    // Handle gracefully - maybe show error to user
  } else {
    throw error; // Re-throw unexpected errors
  }
}
```

## Migration Guide

### Updating Existing Code

1. **Replace Silent Casting**:
   ```typescript
   // ❌ Before
   const id = String(data.id || '');
   
   // ✅ After
   const id = safePayStubId(data);
   ```

2. **Update Test Data**:
   ```typescript
   // ❌ Before
   const mockPayStub = { id: 'paystub-123' };
   
   // ✅ After
   const mockPayStub = createMockPayStub({ 
     id: RelayIdService.toGlobalId('PayStub', '123') 
   });
   ```

3. **Remove UUID Dependencies**:
   ```typescript
   // ❌ Before
   import { v4 as uuidv4 } from 'uuid';
   const id = uuidv4();
   
   // ✅ After
   const id = `client:temp:paystub:${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
   ```

### Legacy Support

Use migration utilities for transitioning legacy code:

```typescript
import { GlobalIdMigrationUtils } from '@/src/utils/globalIdHelpers';

// Convert legacy numeric/GUID IDs
const globalId = GlobalIdMigrationUtils.legacyToGlobalId(legacyId, 'PayStub');

// Batch convert arrays
const globalIds = GlobalIdMigrationUtils.batchLegacyToGlobalId(legacyIds, 'PayStub');

// Update object properties
const updatedPayStub = GlobalIdMigrationUtils.updateObjectIds(oldPayStub, [
  { field: 'id', entityType: 'PayStub' },
  { field: 'employeeId', entityType: 'Employee' }
]);
```

## Debugging and Development

### Console Inspection

Use built-in debugging tools:

```typescript
import { GlobalIdDevUtils } from '@/src/utils/globalIdHelpers';

// Inspect individual IDs
console.log(GlobalIdDevUtils.inspect(suspiciousId));

// Analyze arrays
GlobalIdDevUtils.logAnalysis('PayStub IDs', payStubIds);

// Validate object structure
const validation = GlobalIdDevUtils.validateObjectIds(payStubData, [
  { field: 'id', entityType: 'PayStub', required: true },
  { field: 'employeeId', entityType: 'Employee', required: true }
]);

if (!validation.isValid) {
  console.error('Validation errors:', validation.errors);
}
```

### React Component Validation

Add prop validation for development:

```typescript
import { GlobalIdReactUtils } from '@/src/utils/globalIdHelpers';

const PayStubComponent: React.FC<PayStubProps> = (props) => {
  // Development-only validation
  if (process.env.NODE_ENV === 'development') {
    const validation = GlobalIdReactUtils.validateGlobalIdProps(props, [
      { field: 'payStubId', entityType: 'PayStub', required: true },
      { field: 'employeeId', entityType: 'Employee', required: true }
    ]);
    
    if (!validation.isValid) {
      validation.warnings.forEach(warning => console.warn(warning));
    }
  }
  
  // Component logic...
};
```

### Performance Monitoring

Monitor ID operations for performance:

```typescript
import { GlobalIdPerformanceUtils } from '@/src/utils/globalIdHelpers';

// Use caching for repeated operations
const entityType = GlobalIdPerformanceUtils.getCachedEntityType(id);

// Group large arrays efficiently
const groupedIds = GlobalIdPerformanceUtils.groupByEntityType(largeIdArray);

// Clear caches when needed (e.g., in tests)
GlobalIdPerformanceUtils.clearCaches();
```

## Best Practices

### 1. Type Safety First

- Always use nominal types for Global IDs
- Use safe conversion utilities instead of silent casting
- Validate IDs at component boundaries

### 2. Consistent Patterns

- Use client temp IDs for optimistic updates
- Follow the established naming conventions
- Maintain the entity type information

### 3. Error Handling

- Catch and handle SafeIdConversionError specifically
- Provide meaningful error messages to users
- Log conversion errors for debugging

### 4. Performance

- Use cached utilities for repeated operations
- Group operations when working with large ID arrays
- Clear caches in tests to avoid interference

### 5. Testing

- Use proper Global IDs in test data
- Test both success and error scenarios
- Include optimistic update test cases

## Troubleshooting

### Common Issues

1. **TypeScript Compilation Errors**
   ```
   Error: Argument of type 'unknown' is not assignable to parameter of type 'string'
   ```
   **Solution**: Use safe conversion utilities instead of direct casting.

2. **Runtime Errors: "Invalid Global ID format"**
   **Solution**: Check that the ID is properly formatted. Use inspection utilities to debug.

3. **React Key Warnings**
   **Solution**: Use `globalIdHelpers.toKey()` to generate stable React keys.

4. **Performance Issues with Large ID Arrays**
   **Solution**: Use the performance utilities for caching and grouping operations.

### Debugging Steps

1. **Inspect the ID**: Use `globalIdHelpers.inspect(id)` to see the decoded format
2. **Validate the Context**: Check where the ID is coming from (fragment, prop, etc.)
3. **Check the Type**: Ensure the ID type matches the expected entity type
4. **Review the Migration**: Ensure legacy IDs have been properly converted

### Getting Help

- Check the test files for examples of proper usage
- Review the Phase 2 implementation report for migration details
- Use the debugging utilities to understand ID formats and issues

---

## Quick Reference

### Import Statements
```typescript
// Type system
import { PayStubGlobalID, GlobalIDGuards, GlobalIDUtils } from '@/src/types/GlobalID';

// Safe conversion
import { safePayStubDetailId, SafeIdConversionError } from '@/src/utils/safeIdConversion';

// Comprehensive helpers
import globalIdHelpers from '@/src/utils/globalIdHelpers';

// Relay service
import { RelayIdService } from '@/src/services/RelayIdService';

// ID converters (for backend compatibility)
import { TimesheetIdConverter } from '@/src/utils/timesheet-id-converters';
```

### Common Operations
```typescript
// Safe ID extraction
const id = safePayStubDetailId(fragmentData);

// Type checking
if (GlobalIDGuards.isAnyGlobalID(someValue)) { /* ... */ }

// Debugging
console.log(globalIdHelpers.inspect(suspiciousId));

// React keys
const key = globalIdHelpers.toKey(id, 'row');

// Legacy conversion
const globalId = globalIdHelpers.fromLegacy(legacyId, 'PayStub');
```

This guide should help you work effectively with Global IDs in the timesheet application. For specific implementation details, refer to the source code and tests.