#!/usr/bin/env ts-node

/**
 * Codemod script to migrate error handling imports to the consolidated module
 * 
 * This script will:
 * 1. Find all imports from legacy error handling files
 * 2. Replace them with imports from @/errorHandling
 * 3. Update any usage patterns that need to change
 * 
 * Usage: npx ts-node scripts/codemod-error-imports.ts
 */

import * as fs from 'fs';
import * as path from 'path';

interface ImportReplacement {
  from: string;
  to: string;
  namedImports?: { [oldName: string]: string };
}

// Define the import replacements
const IMPORT_REPLACEMENTS: ImportReplacement[] = [
  {
    from: '../../utils/error-handling',
    to: '@/errorHandling',
    namedImports: {
      'TimesheetError': 'TimesheetError',
      'ErrorType': 'ErrorType', 
      'ErrorSeverity': 'ErrorSeverity',
      'parseRelayError': 'parseError',
      'shouldRetryError': 'shouldRetryError',
      'incrementRetryCount': 'incrementRetryCount'
    }
  },
  {
    from: '../utils/error-handling',
    to: '@/errorHandling'
  },
  {
    from: '@/src/utils/error-handling',
    to: '@/errorHandling'
  },
  {
    from: '../../lib/core/errors',
    to: '@/errorHandling',
    namedImports: {
      'AuthError': 'TimesheetError',
      'ValidationError': 'TimesheetError'
    }
  },
  {
    from: '../lib/core/errors',
    to: '@/errorHandling'
  },
  {
    from: '@/lib/core/errors',
    to: '@/errorHandling'
  },
  {
    from: '../../services/error-handling',
    to: '@/errorHandling',
    namedImports: {
      'ErrorHandlingService': 'ErrorService'
    }
  },
  {
    from: '../services/error-handling',
    to: '@/errorHandling'
  },
  {
    from: '@/src/services/error-handling',
    to: '@/errorHandling'
  },
  {
    from: '../../components/ErrorBoundary/TimesheetErrorBoundary',
    to: '@/errorHandling'
  },
  {
    from: '../components/ErrorBoundary/ErrorBoundary',
    to: '@/errorHandling'
  },
  {
    from: '../../components/Common/ErrorBoundary',
    to: '@/errorHandling'
  },
  {
    from: '@/src/utils/errorBoundaryUtils',
    to: '@/errorHandling',
    namedImports: {
      'EnhancedErrorBoundary': 'ErrorBoundary',
      'useErrorHandler': 'useErrorHandler'
    }
  }
];

// File extensions to process
const EXTENSIONS = ['.ts', '.tsx', '.js', '.jsx'];

// Directories to ignore
const IGNORE_DIRS = ['node_modules', '.git', 'dist', 'build', '__generated__'];

function isIgnoredDir(dirPath: string): boolean {
  return IGNORE_DIRS.some(dir => dirPath.includes(dir));
}

function shouldProcessFile(filePath: string): boolean {
  const ext = path.extname(filePath);
  return EXTENSIONS.includes(ext) && !isIgnoredDir(filePath);
}

function findFiles(dir: string): string[] {
  const files: string[] = [];
  
  try {
    const entries = fs.readdirSync(dir, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = path.join(dir, entry.name);
      
      if (entry.isDirectory() && !isIgnoredDir(fullPath)) {
        files.push(...findFiles(fullPath));
      } else if (entry.isFile() && shouldProcessFile(fullPath)) {
        files.push(fullPath);
      }
    }
  } catch (error) {
    console.error(`Error reading directory ${dir}:`, error);
  }
  
  return files;
}

function replaceImports(content: string): { content: string; changed: boolean } {
  let newContent = content;
  let changed = false;
  
  for (const replacement of IMPORT_REPLACEMENTS) {
    // Pattern to match import statements
    const importPattern = new RegExp(
      `import\\s*{([^}]*)}\\s*from\\s*['"]${replacement.from.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&')}['"]`,
      'g'
    );
    
    const defaultImportPattern = new RegExp(
      `import\\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\\s+from\\s*['"]${replacement.from.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&')}['"]`,
      'g'
    );
    
    // Replace named imports
    newContent = newContent.replace(importPattern, (match, imports) => {
      changed = true;
      const importList = imports.split(',').map((imp: string) => imp.trim());
      const newImports: string[] = [];
      
      for (const imp of importList) {
        const cleanImp = imp.trim();
        if (replacement.namedImports && replacement.namedImports[cleanImp]) {
          newImports.push(replacement.namedImports[cleanImp]);
        } else {
          newImports.push(cleanImp);
        }
      }
      
      return `import { ${newImports.join(', ')} } from '${replacement.to}'`;
    });
    
    // Replace default imports
    newContent = newContent.replace(defaultImportPattern, (match, importName) => {
      changed = true;
      return `import { ${importName} } from '${replacement.to}'`;
    });
  }
  
  // Special case replacements for class instantiation
  if (newContent.includes('new AuthError(')) {
    newContent = newContent.replace(
      /new AuthError\\(([^,]+),\\s*([^,]+),\\s*([^)]+)\\)/g,
      'TimesheetError.createAuthError($1, $2, $3)'
    );
    changed = true;
  }
  
  if (newContent.includes('new ValidationError(')) {
    newContent = newContent.replace(
      /new ValidationError\\(([^,)]+)(?:,\\s*([^)]+))?\\)/g,
      'TimesheetError.createValidationError($1$2 ? , $2 : "")'
    );
    changed = true;
  }
  
  // Replace ErrorHandlingService static calls
  if (newContent.includes('ErrorHandlingService.')) {
    newContent = newContent.replace(/ErrorHandlingService\\./g, 'ErrorService.');
    changed = true;
  }
  
  return { content: newContent, changed };
}

function processFile(filePath: string): boolean {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const { content: newContent, changed } = replaceImports(content);
    
    if (changed) {
      fs.writeFileSync(filePath, newContent, 'utf8');
      console.log(`✓ Updated: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`✗ Error processing ${filePath}:`, error);
    return false;
  }
}

function main() {
  console.log('🔄 Starting error handling import migration...');
  console.log('📁 Scanning for files to process...');
  
  const srcDir = path.join(__dirname, '../src');
  const files = findFiles(srcDir);
  
  console.log(`📄 Found ${files.length} files to check`);
  
  let processedCount = 0;
  let changedCount = 0;
  
  for (const file of files) {
    processedCount++;
    if (processFile(file)) {
      changedCount++;
    }
    
    if (processedCount % 50 === 0) {
      console.log(`📊 Progress: ${processedCount}/${files.length} files processed`);
    }
  }
  
  console.log('\\n✅ Migration complete!');
  console.log(`📊 Summary:`);
  console.log(`   - Files processed: ${processedCount}`);
  console.log(`   - Files changed: ${changedCount}`);
  console.log(`   - Files unchanged: ${processedCount - changedCount}`);
  
  if (changedCount > 0) {
    console.log('\\n🔧 Next steps:');
    console.log('   1. Run: pnpm relay');
    console.log('   2. Run: pnpm check');
    console.log('   3. Run: pnpm test');
    console.log('   4. Run: pnpm lint');
    console.log('   5. Run: pnpm build');
  }
}

if (require.main === module) {
  main();
}