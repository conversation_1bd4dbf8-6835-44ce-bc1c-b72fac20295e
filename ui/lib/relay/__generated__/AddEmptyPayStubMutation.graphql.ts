/**
 * @generated SignedSource<<41bcd872522267ce09ce158d6186991a>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ConcreteRequest } from 'relay-runtime';
export type AddEmptyPayStubInput = {
  employeeId: string;
  timeSheetId: string;
};
export type AddEmptyPayStubMutation$variables = {
  connections: ReadonlyArray<string>;
  input: AddEmptyPayStubInput;
};
export type AddEmptyPayStubMutation$data = {
  readonly addEmptyPayStub: {
    readonly errors: ReadonlyArray<string>;
    readonly payStubEdge: {
      readonly cursor: string;
      readonly node: {
        readonly details: ReadonlyArray<{
          readonly agreementId: number | null | undefined;
          readonly classificationId: number | null | undefined;
          readonly dtHours: number | null | undefined;
          readonly id: string;
          readonly otHours: number | null | undefined;
          readonly stHours: number | null | undefined;
          readonly subClassificationId: number | null | undefined;
          readonly workDate: any;
        }>;
        readonly employee: {
          readonly id: string;
        };
        readonly employeeId: string;
        readonly id: string;
      } | null | undefined;
    } | null | undefined;
  };
};
export type AddEmptyPayStubMutation = {
  response: AddEmptyPayStubMutation$data;
  variables: AddEmptyPayStubMutation$variables;
};

const node: ConcreteRequest = (function(){
var v0 = {
  "defaultValue": null,
  "kind": "LocalArgument",
  "name": "connections"
},
v1 = {
  "defaultValue": null,
  "kind": "LocalArgument",
  "name": "input"
},
v2 = [
  {
    "kind": "Variable",
    "name": "input",
    "variableName": "input"
  }
],
v3 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "id",
  "storageKey": null
},
v4 = {
  "alias": null,
  "args": null,
  "concreteType": "EdgeOfPayStub",
  "kind": "LinkedField",
  "name": "payStubEdge",
  "plural": false,
  "selections": [
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "cursor",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "concreteType": "PayStub",
      "kind": "LinkedField",
      "name": "node",
      "plural": false,
      "selections": [
        (v3/*: any*/),
        {
          "alias": null,
          "args": null,
          "kind": "ScalarField",
          "name": "employeeId",
          "storageKey": null
        },
        {
          "alias": null,
          "args": null,
          "concreteType": "Employee",
          "kind": "LinkedField",
          "name": "employee",
          "plural": false,
          "selections": [
            (v3/*: any*/)
          ],
          "storageKey": null
        },
        {
          "alias": null,
          "args": null,
          "concreteType": "PayStubDetail",
          "kind": "LinkedField",
          "name": "details",
          "plural": true,
          "selections": [
            (v3/*: any*/),
            {
              "alias": null,
              "args": null,
              "kind": "ScalarField",
              "name": "workDate",
              "storageKey": null
            },
            {
              "alias": null,
              "args": null,
              "kind": "ScalarField",
              "name": "stHours",
              "storageKey": null
            },
            {
              "alias": null,
              "args": null,
              "kind": "ScalarField",
              "name": "otHours",
              "storageKey": null
            },
            {
              "alias": null,
              "args": null,
              "kind": "ScalarField",
              "name": "dtHours",
              "storageKey": null
            },
            {
              "alias": null,
              "args": null,
              "kind": "ScalarField",
              "name": "agreementId",
              "storageKey": null
            },
            {
              "alias": null,
              "args": null,
              "kind": "ScalarField",
              "name": "classificationId",
              "storageKey": null
            },
            {
              "alias": null,
              "args": null,
              "kind": "ScalarField",
              "name": "subClassificationId",
              "storageKey": null
            }
          ],
          "storageKey": null
        }
      ],
      "storageKey": null
    }
  ],
  "storageKey": null
},
v5 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "errors",
  "storageKey": null
};
return {
  "fragment": {
    "argumentDefinitions": [
      (v0/*: any*/),
      (v1/*: any*/)
    ],
    "kind": "Fragment",
    "metadata": null,
    "name": "AddEmptyPayStubMutation",
    "selections": [
      {
        "alias": null,
        "args": (v2/*: any*/),
        "concreteType": "AddEmptyPayStubPayload",
        "kind": "LinkedField",
        "name": "addEmptyPayStub",
        "plural": false,
        "selections": [
          (v4/*: any*/),
          (v5/*: any*/)
        ],
        "storageKey": null
      }
    ],
    "type": "Mutation",
    "abstractKey": null
  },
  "kind": "Request",
  "operation": {
    "argumentDefinitions": [
      (v1/*: any*/),
      (v0/*: any*/)
    ],
    "kind": "Operation",
    "name": "AddEmptyPayStubMutation",
    "selections": [
      {
        "alias": null,
        "args": (v2/*: any*/),
        "concreteType": "AddEmptyPayStubPayload",
        "kind": "LinkedField",
        "name": "addEmptyPayStub",
        "plural": false,
        "selections": [
          (v4/*: any*/),
          {
            "alias": null,
            "args": null,
            "filters": null,
            "handle": "prependEdge",
            "key": "",
            "kind": "LinkedHandle",
            "name": "payStubEdge",
            "handleArgs": [
              {
                "kind": "Variable",
                "name": "connections",
                "variableName": "connections"
              }
            ]
          },
          (v5/*: any*/)
        ],
        "storageKey": null
      }
    ]
  },
  "params": {
    "cacheID": "aaa85b3922ec103a1fa33eb3906c56c2",
    "id": null,
    "metadata": {},
    "name": "AddEmptyPayStubMutation",
    "operationKind": "mutation",
    "text": "mutation AddEmptyPayStubMutation(\n  $input: AddEmptyPayStubInput!\n) {\n  addEmptyPayStub(input: $input) {\n    payStubEdge {\n      cursor\n      node {\n        id\n        employeeId\n        employee {\n          id\n        }\n        details {\n          id\n          workDate\n          stHours\n          otHours\n          dtHours\n          agreementId\n          classificationId\n          subClassificationId\n        }\n      }\n    }\n    errors\n  }\n}\n"
  }
};
})();

(node as any).hash = "80cf43497942ee241f52a6df454bced2";

export default node;
