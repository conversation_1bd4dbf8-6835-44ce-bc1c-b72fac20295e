/**
 * @generated SignedSource<<8949fecde61f2f873dd62e547f49e0d0>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ConcreteRequest } from 'relay-runtime';
export type ModifyTimeSheetInput = {
  addPayStubs?: ReadonlyArray<AddPayStubInput> | null | undefined;
  deletePayStubIds?: ReadonlyArray<string> | null | undefined;
  employerGuid: any;
  id: string;
  modificationDate?: any | null | undefined;
  modifyPayStubs?: ReadonlyArray<ModifyPayStubInput> | null | undefined;
  name?: string | null | undefined;
  readOnly?: boolean | null | undefined;
  showBonusColumn?: boolean | null | undefined;
  showCostCenterColumn?: boolean | null | undefined;
  showDTHoursColumn?: boolean | null | undefined;
  showEarningsCodesColumn?: boolean | null | undefined;
  showExpensesColumn?: boolean | null | undefined;
  status?: string | null | undefined;
  timeSheetId?: any | null | undefined;
  type?: string | null | undefined;
};
export type AddPayStubInput = {
  bonus?: number | null | undefined;
  delete?: boolean | null | undefined;
  details?: ReadonlyArray<AddPayStubDetailInput> | null | undefined;
  dtHours?: number | null | undefined;
  employeeId: string;
  employeeName?: string | null | undefined;
  expanded?: boolean | null | undefined;
  expenses?: number | null | undefined;
  id?: string | null | undefined;
  inEdit?: boolean | null | undefined;
  name?: string | null | undefined;
  otHours?: number | null | undefined;
  stHours?: number | null | undefined;
};
export type AddPayStubDetailInput = {
  agreementId?: number | null | undefined;
  bonus?: number | null | undefined;
  classificationId?: number | null | undefined;
  costCenter?: string | null | undefined;
  delete?: boolean | null | undefined;
  dtHours?: number | null | undefined;
  earningsCode?: string | null | undefined;
  expenses?: number | null | undefined;
  hourlyRate?: number | null | undefined;
  id?: string | null | undefined;
  jobCode?: string | null | undefined;
  name?: string | null | undefined;
  otHours?: number | null | undefined;
  payStubId?: string | null | undefined;
  reportLineItemId?: number | null | undefined;
  stHours?: number | null | undefined;
  subClassificationId?: number | null | undefined;
  workDate: any;
};
export type ModifyPayStubInput = {
  bonus?: number | null | undefined;
  details?: ReadonlyArray<ModifyPayStubDetailInput> | null | undefined;
  dtHours?: number | null | undefined;
  employeeId: string;
  employeeName?: string | null | undefined;
  expanded?: boolean | null | undefined;
  expenses?: number | null | undefined;
  id: string;
  inEdit?: boolean | null | undefined;
  name?: string | null | undefined;
  otHours?: number | null | undefined;
  stHours?: number | null | undefined;
};
export type ModifyPayStubDetailInput = {
  agreementId?: number | null | undefined;
  bonus?: number | null | undefined;
  classificationId?: number | null | undefined;
  costCenter?: string | null | undefined;
  delete?: boolean | null | undefined;
  dtHours?: number | null | undefined;
  earningsCode?: string | null | undefined;
  expenses?: number | null | undefined;
  hourlyRate?: number | null | undefined;
  id?: string | null | undefined;
  jobCode?: string | null | undefined;
  name?: string | null | undefined;
  otHours?: number | null | undefined;
  payStubId?: string | null | undefined;
  reportLineItemId?: number | null | undefined;
  stHours?: number | null | undefined;
  subClassificationId?: number | null | undefined;
  workDate: any;
};
export type TimeSheetResetMutation$variables = {
  first?: number | null | undefined;
  input: ModifyTimeSheetInput;
};
export type TimeSheetResetMutation$data = {
  readonly modifyTimeSheet: {
    readonly timeSheet: {
      readonly id: string;
      readonly payStubs: {
        readonly edges: ReadonlyArray<{
          readonly node: {
            readonly details: ReadonlyArray<{
              readonly agreementId: number | null | undefined;
              readonly bonus: number | null | undefined;
              readonly classificationId: number | null | undefined;
              readonly costCenter: string | null | undefined;
              readonly dtHours: number | null | undefined;
              readonly earningsCode: string | null | undefined;
              readonly expenses: number | null | undefined;
              readonly hourlyRate: number | null | undefined;
              readonly id: string;
              readonly jobCode: string | null | undefined;
              readonly name: string | null | undefined;
              readonly otHours: number | null | undefined;
              readonly stHours: number | null | undefined;
              readonly subClassificationId: number | null | undefined;
              readonly totalHours: number | null | undefined;
              readonly workDate: any;
            }>;
            readonly id: string;
          };
        }> | null | undefined;
      } | null | undefined;
    } | null | undefined;
  };
};
export type TimeSheetResetMutation = {
  response: TimeSheetResetMutation$data;
  variables: TimeSheetResetMutation$variables;
};

const node: ConcreteRequest = (function(){
var v0 = {
  "defaultValue": 500,
  "kind": "LocalArgument",
  "name": "first"
},
v1 = {
  "defaultValue": null,
  "kind": "LocalArgument",
  "name": "input"
},
v2 = [
  {
    "kind": "Variable",
    "name": "input",
    "variableName": "input"
  }
],
v3 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "id",
  "storageKey": null
},
v4 = [
  {
    "alias": null,
    "args": null,
    "concreteType": "PayStubEdge",
    "kind": "LinkedField",
    "name": "edges",
    "plural": true,
    "selections": [
      {
        "alias": null,
        "args": null,
        "concreteType": "PayStub",
        "kind": "LinkedField",
        "name": "node",
        "plural": false,
        "selections": [
          (v3/*: any*/),
          {
            "alias": null,
            "args": null,
            "concreteType": "PayStubDetail",
            "kind": "LinkedField",
            "name": "details",
            "plural": true,
            "selections": [
              (v3/*: any*/),
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "name",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "workDate",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "stHours",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "otHours",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "dtHours",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "totalHours",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "jobCode",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "earningsCode",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "agreementId",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "classificationId",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "subClassificationId",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "costCenter",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "hourlyRate",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "bonus",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "expenses",
                "storageKey": null
              }
            ],
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "__typename",
            "storageKey": null
          }
        ],
        "storageKey": null
      },
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "cursor",
        "storageKey": null
      }
    ],
    "storageKey": null
  },
  {
    "alias": null,
    "args": null,
    "concreteType": "PageInfo",
    "kind": "LinkedField",
    "name": "pageInfo",
    "plural": false,
    "selections": [
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "endCursor",
        "storageKey": null
      },
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "hasNextPage",
        "storageKey": null
      }
    ],
    "storageKey": null
  }
],
v5 = [
  {
    "kind": "Variable",
    "name": "first",
    "variableName": "first"
  }
];
return {
  "fragment": {
    "argumentDefinitions": [
      (v0/*: any*/),
      (v1/*: any*/)
    ],
    "kind": "Fragment",
    "metadata": null,
    "name": "TimeSheetResetMutation",
    "selections": [
      {
        "alias": null,
        "args": (v2/*: any*/),
        "concreteType": "ModifyTimeSheetPayload",
        "kind": "LinkedField",
        "name": "modifyTimeSheet",
        "plural": false,
        "selections": [
          {
            "alias": null,
            "args": null,
            "concreteType": "TimeSheet",
            "kind": "LinkedField",
            "name": "timeSheet",
            "plural": false,
            "selections": [
              (v3/*: any*/),
              {
                "alias": "payStubs",
                "args": null,
                "concreteType": "PayStubConnection",
                "kind": "LinkedField",
                "name": "__TimeSheetReset_payStubs_connection",
                "plural": false,
                "selections": (v4/*: any*/),
                "storageKey": null
              }
            ],
            "storageKey": null
          }
        ],
        "storageKey": null
      }
    ],
    "type": "Mutation",
    "abstractKey": null
  },
  "kind": "Request",
  "operation": {
    "argumentDefinitions": [
      (v1/*: any*/),
      (v0/*: any*/)
    ],
    "kind": "Operation",
    "name": "TimeSheetResetMutation",
    "selections": [
      {
        "alias": null,
        "args": (v2/*: any*/),
        "concreteType": "ModifyTimeSheetPayload",
        "kind": "LinkedField",
        "name": "modifyTimeSheet",
        "plural": false,
        "selections": [
          {
            "alias": null,
            "args": null,
            "concreteType": "TimeSheet",
            "kind": "LinkedField",
            "name": "timeSheet",
            "plural": false,
            "selections": [
              (v3/*: any*/),
              {
                "alias": null,
                "args": (v5/*: any*/),
                "concreteType": "PayStubConnection",
                "kind": "LinkedField",
                "name": "payStubs",
                "plural": false,
                "selections": (v4/*: any*/),
                "storageKey": null
              },
              {
                "alias": null,
                "args": (v5/*: any*/),
                "filters": null,
                "handle": "connection",
                "key": "TimeSheetReset_payStubs",
                "kind": "LinkedHandle",
                "name": "payStubs"
              }
            ],
            "storageKey": null
          }
        ],
        "storageKey": null
      }
    ]
  },
  "params": {
    "cacheID": "df2da1c64d2394b2cc7d0a41ee43e549",
    "id": null,
    "metadata": {
      "connection": [
        {
          "count": "first",
          "cursor": null,
          "direction": "forward",
          "path": [
            "modifyTimeSheet",
            "timeSheet",
            "payStubs"
          ]
        }
      ]
    },
    "name": "TimeSheetResetMutation",
    "operationKind": "mutation",
    "text": "mutation TimeSheetResetMutation(\n  $input: ModifyTimeSheetInput!\n  $first: Int = 500\n) {\n  modifyTimeSheet(input: $input) {\n    timeSheet {\n      id\n      payStubs(first: $first) {\n        edges {\n          node {\n            id\n            details {\n              id\n              name\n              workDate\n              stHours\n              otHours\n              dtHours\n              totalHours\n              jobCode\n              earningsCode\n              agreementId\n              classificationId\n              subClassificationId\n              costCenter\n              hourlyRate\n              bonus\n              expenses\n            }\n            __typename\n          }\n          cursor\n        }\n        pageInfo {\n          endCursor\n          hasNextPage\n        }\n      }\n    }\n  }\n}\n"
  }
};
})();

(node as any).hash = "b611d9fcb8dff5c63336ea86f8a934da";

export default node;
