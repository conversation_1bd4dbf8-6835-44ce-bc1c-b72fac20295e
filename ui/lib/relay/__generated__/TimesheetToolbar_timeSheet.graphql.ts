/**
 * @generated SignedSource<<3b8c020590e43e25bd515fc07313bf90>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ReaderFragment } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type TimesheetToolbar_timeSheet$data = {
  readonly employerGuid: any;
  readonly id: string;
  readonly numericId: number;
  readonly payStubs: {
    readonly edges: ReadonlyArray<{
      readonly node: {
        readonly details: ReadonlyArray<{
          readonly id: string;
        }>;
        readonly id: string;
      };
    }> | null | undefined;
  } | null | undefined;
  readonly " $fragmentSpreads": FragmentRefs<"TimeSheetSettings_timeSheet" | "UploadTimeSheetFragments_timeSheetConsolidated">;
  readonly " $fragmentType": "TimesheetToolbar_timeSheet";
};
export type TimesheetToolbar_timeSheet$key = {
  readonly " $data"?: TimesheetToolbar_timeSheet$data;
  readonly " $fragmentSpreads": FragmentRefs<"TimesheetToolbar_timeSheet">;
};

const node: ReaderFragment = (function(){
var v0 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "id",
  "storageKey": null
};
return {
  "argumentDefinitions": [
    {
      "defaultValue": null,
      "kind": "LocalArgument",
      "name": "after"
    },
    {
      "defaultValue": 500,
      "kind": "LocalArgument",
      "name": "first"
    }
  ],
  "kind": "Fragment",
  "metadata": {
    "connection": [
      {
        "count": "first",
        "cursor": "after",
        "direction": "forward",
        "path": [
          "payStubs"
        ]
      }
    ]
  },
  "name": "TimesheetToolbar_timeSheet",
  "selections": [
    (v0/*: any*/),
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "numericId",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "employerGuid",
      "storageKey": null
    },
    {
      "alias": "payStubs",
      "args": null,
      "concreteType": "PayStubConnection",
      "kind": "LinkedField",
      "name": "__TimesheetToolbar_timeSheet_payStubs_connection",
      "plural": false,
      "selections": [
        {
          "alias": null,
          "args": null,
          "concreteType": "PayStubEdge",
          "kind": "LinkedField",
          "name": "edges",
          "plural": true,
          "selections": [
            {
              "alias": null,
              "args": null,
              "concreteType": "PayStub",
              "kind": "LinkedField",
              "name": "node",
              "plural": false,
              "selections": [
                (v0/*: any*/),
                {
                  "alias": null,
                  "args": null,
                  "concreteType": "PayStubDetail",
                  "kind": "LinkedField",
                  "name": "details",
                  "plural": true,
                  "selections": [
                    (v0/*: any*/)
                  ],
                  "storageKey": null
                },
                {
                  "alias": null,
                  "args": null,
                  "kind": "ScalarField",
                  "name": "__typename",
                  "storageKey": null
                }
              ],
              "storageKey": null
            },
            {
              "alias": null,
              "args": null,
              "kind": "ScalarField",
              "name": "cursor",
              "storageKey": null
            }
          ],
          "storageKey": null
        },
        {
          "alias": null,
          "args": null,
          "concreteType": "PageInfo",
          "kind": "LinkedField",
          "name": "pageInfo",
          "plural": false,
          "selections": [
            {
              "alias": null,
              "args": null,
              "kind": "ScalarField",
              "name": "endCursor",
              "storageKey": null
            },
            {
              "alias": null,
              "args": null,
              "kind": "ScalarField",
              "name": "hasNextPage",
              "storageKey": null
            }
          ],
          "storageKey": null
        }
      ],
      "storageKey": null
    },
    {
      "args": null,
      "kind": "FragmentSpread",
      "name": "TimeSheetSettings_timeSheet"
    },
    {
      "args": null,
      "kind": "FragmentSpread",
      "name": "UploadTimeSheetFragments_timeSheetConsolidated"
    }
  ],
  "type": "TimeSheet",
  "abstractKey": null
};
})();

(node as any).hash = "7cdb96c14b882a57a6940d03ff2c6801";

export default node;
