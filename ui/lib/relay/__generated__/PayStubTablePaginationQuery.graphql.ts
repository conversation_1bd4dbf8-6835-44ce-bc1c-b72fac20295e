/**
 * @generated SignedSource<<36642bd74bb2dd7fca95900e3f2b2327>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ConcreteRequest } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type PayStubTablePaginationQuery$variables = {
  after?: string | null | undefined;
  first?: number | null | undefined;
  id: string;
};
export type PayStubTablePaginationQuery$data = {
  readonly node: {
    readonly " $fragmentSpreads": FragmentRefs<"PayStubTable_connectionFragment">;
  } | null | undefined;
};
export type PayStubTablePaginationQuery = {
  response: PayStubTablePaginationQuery$data;
  variables: PayStubTablePaginationQuery$variables;
};

const node: ConcreteRequest = (function(){
var v0 = [
  {
    "defaultValue": null,
    "kind": "LocalArgument",
    "name": "after"
  },
  {
    "defaultValue": 500,
    "kind": "LocalArgument",
    "name": "first"
  },
  {
    "defaultValue": null,
    "kind": "LocalArgument",
    "name": "id"
  }
],
v1 = [
  {
    "kind": "Variable",
    "name": "id",
    "variableName": "id"
  }
],
v2 = [
  {
    "kind": "Variable",
    "name": "after",
    "variableName": "after"
  },
  {
    "kind": "Variable",
    "name": "first",
    "variableName": "first"
  }
],
v3 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "__typename",
  "storageKey": null
},
v4 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "id",
  "storageKey": null
},
v5 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "name",
  "storageKey": null
},
v6 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "totalHours",
  "storageKey": null
};
return {
  "fragment": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Fragment",
    "metadata": null,
    "name": "PayStubTablePaginationQuery",
    "selections": [
      {
        "alias": null,
        "args": (v1/*: any*/),
        "concreteType": null,
        "kind": "LinkedField",
        "name": "node",
        "plural": false,
        "selections": [
          {
            "args": (v2/*: any*/),
            "kind": "FragmentSpread",
            "name": "PayStubTable_connectionFragment"
          }
        ],
        "storageKey": null
      }
    ],
    "type": "Query",
    "abstractKey": null
  },
  "kind": "Request",
  "operation": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Operation",
    "name": "PayStubTablePaginationQuery",
    "selections": [
      {
        "alias": null,
        "args": (v1/*: any*/),
        "concreteType": null,
        "kind": "LinkedField",
        "name": "node",
        "plural": false,
        "selections": [
          (v3/*: any*/),
          (v4/*: any*/),
          {
            "kind": "InlineFragment",
            "selections": [
              {
                "alias": null,
                "args": (v2/*: any*/),
                "concreteType": "PayStubConnection",
                "kind": "LinkedField",
                "name": "payStubs",
                "plural": false,
                "selections": [
                  {
                    "alias": null,
                    "args": null,
                    "concreteType": "PayStubEdge",
                    "kind": "LinkedField",
                    "name": "edges",
                    "plural": true,
                    "selections": [
                      {
                        "alias": null,
                        "args": null,
                        "kind": "ScalarField",
                        "name": "cursor",
                        "storageKey": null
                      },
                      {
                        "alias": null,
                        "args": null,
                        "concreteType": "PayStub",
                        "kind": "LinkedField",
                        "name": "node",
                        "plural": false,
                        "selections": [
                          (v4/*: any*/),
                          {
                            "alias": null,
                            "args": null,
                            "kind": "ScalarField",
                            "name": "employeeId",
                            "storageKey": null
                          },
                          {
                            "alias": null,
                            "args": null,
                            "concreteType": "Employee",
                            "kind": "LinkedField",
                            "name": "employee",
                            "plural": false,
                            "selections": [
                              (v4/*: any*/),
                              {
                                "alias": null,
                                "args": null,
                                "kind": "ScalarField",
                                "name": "firstName",
                                "storageKey": null
                              },
                              {
                                "alias": null,
                                "args": null,
                                "kind": "ScalarField",
                                "name": "lastName",
                                "storageKey": null
                              },
                              {
                                "alias": null,
                                "args": null,
                                "kind": "ScalarField",
                                "name": "active",
                                "storageKey": null
                              },
                              {
                                "alias": null,
                                "args": null,
                                "kind": "ScalarField",
                                "name": "externalEmployeeId",
                                "storageKey": null
                              }
                            ],
                            "storageKey": null
                          },
                          (v5/*: any*/),
                          (v6/*: any*/),
                          {
                            "alias": null,
                            "args": null,
                            "concreteType": "PayStubDetail",
                            "kind": "LinkedField",
                            "name": "details",
                            "plural": true,
                            "selections": [
                              (v4/*: any*/),
                              {
                                "alias": null,
                                "args": null,
                                "kind": "ScalarField",
                                "name": "payStubId",
                                "storageKey": null
                              },
                              {
                                "alias": null,
                                "args": null,
                                "kind": "ScalarField",
                                "name": "reportLineItemId",
                                "storageKey": null
                              },
                              {
                                "alias": null,
                                "args": null,
                                "kind": "ScalarField",
                                "name": "workDate",
                                "storageKey": null
                              },
                              (v5/*: any*/),
                              {
                                "alias": null,
                                "args": null,
                                "kind": "ScalarField",
                                "name": "stHours",
                                "storageKey": null
                              },
                              {
                                "alias": null,
                                "args": null,
                                "kind": "ScalarField",
                                "name": "otHours",
                                "storageKey": null
                              },
                              {
                                "alias": null,
                                "args": null,
                                "kind": "ScalarField",
                                "name": "dtHours",
                                "storageKey": null
                              },
                              (v6/*: any*/),
                              {
                                "alias": null,
                                "args": null,
                                "kind": "ScalarField",
                                "name": "bonus",
                                "storageKey": null
                              },
                              {
                                "alias": null,
                                "args": null,
                                "kind": "ScalarField",
                                "name": "expenses",
                                "storageKey": null
                              },
                              {
                                "alias": null,
                                "args": null,
                                "kind": "ScalarField",
                                "name": "jobCode",
                                "storageKey": null
                              },
                              {
                                "alias": null,
                                "args": null,
                                "kind": "ScalarField",
                                "name": "agreementId",
                                "storageKey": null
                              },
                              {
                                "alias": null,
                                "args": null,
                                "kind": "ScalarField",
                                "name": "classificationId",
                                "storageKey": null
                              },
                              {
                                "alias": null,
                                "args": null,
                                "kind": "ScalarField",
                                "name": "subClassificationId",
                                "storageKey": null
                              },
                              {
                                "alias": null,
                                "args": null,
                                "kind": "ScalarField",
                                "name": "costCenter",
                                "storageKey": null
                              },
                              {
                                "alias": null,
                                "args": null,
                                "kind": "ScalarField",
                                "name": "hourlyRate",
                                "storageKey": null
                              },
                              {
                                "alias": null,
                                "args": null,
                                "kind": "ScalarField",
                                "name": "earningsCode",
                                "storageKey": null
                              }
                            ],
                            "storageKey": null
                          },
                          (v3/*: any*/)
                        ],
                        "storageKey": null
                      }
                    ],
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "concreteType": "PageInfo",
                    "kind": "LinkedField",
                    "name": "pageInfo",
                    "plural": false,
                    "selections": [
                      {
                        "alias": null,
                        "args": null,
                        "kind": "ScalarField",
                        "name": "hasNextPage",
                        "storageKey": null
                      },
                      {
                        "alias": null,
                        "args": null,
                        "kind": "ScalarField",
                        "name": "hasPreviousPage",
                        "storageKey": null
                      },
                      {
                        "alias": null,
                        "args": null,
                        "kind": "ScalarField",
                        "name": "startCursor",
                        "storageKey": null
                      },
                      {
                        "alias": null,
                        "args": null,
                        "kind": "ScalarField",
                        "name": "endCursor",
                        "storageKey": null
                      }
                    ],
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "totalCount",
                    "storageKey": null
                  }
                ],
                "storageKey": null
              },
              {
                "alias": null,
                "args": (v2/*: any*/),
                "filters": null,
                "handle": "connection",
                "key": "PayStubTable_connectionFragment__payStubs",
                "kind": "LinkedHandle",
                "name": "payStubs"
              }
            ],
            "type": "TimeSheet",
            "abstractKey": null
          }
        ],
        "storageKey": null
      }
    ]
  },
  "params": {
    "cacheID": "8718823fdc5e5506cae7a087717043c7",
    "id": null,
    "metadata": {},
    "name": "PayStubTablePaginationQuery",
    "operationKind": "query",
    "text": "query PayStubTablePaginationQuery(\n  $after: String\n  $first: Int = 500\n  $id: ID!\n) {\n  node(id: $id) {\n    __typename\n    ...PayStubTable_connectionFragment_2HEEH6\n    id\n  }\n}\n\nfragment EmployeeDisplayFragment_employee on Employee {\n  id\n  firstName\n  lastName\n  active\n  externalEmployeeId\n}\n\nfragment PayStubTable_connectionFragment_2HEEH6 on TimeSheet {\n  id\n  payStubs(first: $first, after: $after) {\n    edges {\n      cursor\n      node {\n        ...PayStubTable_payStub\n        ...PayStubTable_payStubs\n        id\n        __typename\n      }\n    }\n    pageInfo {\n      hasNextPage\n      hasPreviousPage\n      startCursor\n      endCursor\n    }\n    totalCount\n  }\n}\n\nfragment PayStubTable_payStub on PayStub {\n  id\n  employeeId\n  employee {\n    id\n    ...EmployeeDisplayFragment_employee\n    ...TimeSheetDetailRow_employee\n  }\n  name\n  totalHours\n  details {\n    id\n    payStubId\n    reportLineItemId\n    workDate\n    name\n    stHours\n    otHours\n    dtHours\n    totalHours\n    bonus\n    expenses\n    jobCode\n    agreementId\n    classificationId\n    subClassificationId\n    costCenter\n    hourlyRate\n    earningsCode\n    ...TimeSheetDetailRow_payStubDetail\n  }\n  ...TimeSheetDetailTableView_payStub\n}\n\nfragment PayStubTable_payStubs on PayStub {\n  id\n  employeeId\n  name\n  totalHours\n  details {\n    id\n    payStubId\n    stHours\n    otHours\n    dtHours\n    totalHours\n    bonus\n    expenses\n  }\n}\n\nfragment TimeSheetDetailRow_employee on Employee {\n  id\n  externalEmployeeId\n  firstName\n  lastName\n  active\n}\n\nfragment TimeSheetDetailRow_payStubDetail on PayStubDetail {\n  id\n  payStubId\n  workDate\n  jobCode\n  agreementId\n  classificationId\n  subClassificationId\n  hourlyRate\n  stHours\n  otHours\n  dtHours\n  bonus\n  expenses\n  earningsCode\n  costCenter\n}\n\nfragment TimeSheetDetailTableView_payStub on PayStub {\n  id\n  employeeId\n  name\n  totalHours\n  employee {\n    ...TimeSheetDetailRow_employee\n    id\n  }\n  details {\n    id\n    workDate\n    jobCode\n    agreementId\n    classificationId\n    subClassificationId\n    hourlyRate\n    stHours\n    otHours\n    dtHours\n    bonus\n    expenses\n    earningsCode\n    costCenter\n    payStubId\n    reportLineItemId\n    totalHours\n    name\n    ...TimeSheetDetailRow_payStubDetail\n  }\n}\n"
  }
};
})();

(node as any).hash = "801131f176f03ef982d4ea75c4875d4b";

export default node;
