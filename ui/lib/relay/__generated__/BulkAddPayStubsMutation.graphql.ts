/**
 * @generated SignedSource<<fde4c68a50dcc2d3c9764c1a361c34e2>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ConcreteRequest } from 'relay-runtime';
export type ModifyTimeSheetInput = {
  addPayStubs?: ReadonlyArray<AddPayStubInput> | null | undefined;
  deletePayStubIds?: ReadonlyArray<string> | null | undefined;
  employerGuid: any;
  id: string;
  modificationDate?: any | null | undefined;
  modifyPayStubs?: ReadonlyArray<ModifyPayStubInput> | null | undefined;
  name?: string | null | undefined;
  readOnly?: boolean | null | undefined;
  showBonusColumn?: boolean | null | undefined;
  showCostCenterColumn?: boolean | null | undefined;
  showDTHoursColumn?: boolean | null | undefined;
  showEarningsCodesColumn?: boolean | null | undefined;
  showExpensesColumn?: boolean | null | undefined;
  status?: string | null | undefined;
  timeSheetId?: any | null | undefined;
  type?: string | null | undefined;
};
export type AddPayStubInput = {
  bonus?: number | null | undefined;
  delete?: boolean | null | undefined;
  details?: ReadonlyArray<AddPayStubDetailInput> | null | undefined;
  dtHours?: number | null | undefined;
  employeeId: string;
  employeeName?: string | null | undefined;
  expanded?: boolean | null | undefined;
  expenses?: number | null | undefined;
  id?: string | null | undefined;
  inEdit?: boolean | null | undefined;
  name?: string | null | undefined;
  otHours?: number | null | undefined;
  stHours?: number | null | undefined;
};
export type AddPayStubDetailInput = {
  agreementId?: number | null | undefined;
  bonus?: number | null | undefined;
  classificationId?: number | null | undefined;
  costCenter?: string | null | undefined;
  delete?: boolean | null | undefined;
  dtHours?: number | null | undefined;
  earningsCode?: string | null | undefined;
  expenses?: number | null | undefined;
  hourlyRate?: number | null | undefined;
  id?: string | null | undefined;
  jobCode?: string | null | undefined;
  name?: string | null | undefined;
  otHours?: number | null | undefined;
  payStubId?: string | null | undefined;
  reportLineItemId?: number | null | undefined;
  stHours?: number | null | undefined;
  subClassificationId?: number | null | undefined;
  workDate: any;
};
export type ModifyPayStubInput = {
  bonus?: number | null | undefined;
  details?: ReadonlyArray<ModifyPayStubDetailInput> | null | undefined;
  dtHours?: number | null | undefined;
  employeeId: string;
  employeeName?: string | null | undefined;
  expanded?: boolean | null | undefined;
  expenses?: number | null | undefined;
  id: string;
  inEdit?: boolean | null | undefined;
  name?: string | null | undefined;
  otHours?: number | null | undefined;
  stHours?: number | null | undefined;
};
export type ModifyPayStubDetailInput = {
  agreementId?: number | null | undefined;
  bonus?: number | null | undefined;
  classificationId?: number | null | undefined;
  costCenter?: string | null | undefined;
  delete?: boolean | null | undefined;
  dtHours?: number | null | undefined;
  earningsCode?: string | null | undefined;
  expenses?: number | null | undefined;
  hourlyRate?: number | null | undefined;
  id?: string | null | undefined;
  jobCode?: string | null | undefined;
  name?: string | null | undefined;
  otHours?: number | null | undefined;
  payStubId?: string | null | undefined;
  reportLineItemId?: number | null | undefined;
  stHours?: number | null | undefined;
  subClassificationId?: number | null | undefined;
  workDate: any;
};
export type BulkAddPayStubsMutation$variables = {
  first?: number | null | undefined;
  input: ModifyTimeSheetInput;
};
export type BulkAddPayStubsMutation$data = {
  readonly modifyTimeSheet: {
    readonly timeSheet: {
      readonly id: string;
      readonly payStubs: {
        readonly edges: ReadonlyArray<{
          readonly node: {
            readonly employeeId: string;
            readonly id: string;
            readonly name: string | null | undefined;
          };
        }> | null | undefined;
      } | null | undefined;
    } | null | undefined;
  };
};
export type BulkAddPayStubsMutation = {
  response: BulkAddPayStubsMutation$data;
  variables: BulkAddPayStubsMutation$variables;
};

const node: ConcreteRequest = (function(){
var v0 = {
  "defaultValue": 500,
  "kind": "LocalArgument",
  "name": "first"
},
v1 = {
  "defaultValue": null,
  "kind": "LocalArgument",
  "name": "input"
},
v2 = [
  {
    "kind": "Variable",
    "name": "input",
    "variableName": "input"
  }
],
v3 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "id",
  "storageKey": null
},
v4 = [
  {
    "alias": null,
    "args": null,
    "concreteType": "PayStubEdge",
    "kind": "LinkedField",
    "name": "edges",
    "plural": true,
    "selections": [
      {
        "alias": null,
        "args": null,
        "concreteType": "PayStub",
        "kind": "LinkedField",
        "name": "node",
        "plural": false,
        "selections": [
          (v3/*: any*/),
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "employeeId",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "name",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "__typename",
            "storageKey": null
          }
        ],
        "storageKey": null
      },
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "cursor",
        "storageKey": null
      }
    ],
    "storageKey": null
  },
  {
    "alias": null,
    "args": null,
    "concreteType": "PageInfo",
    "kind": "LinkedField",
    "name": "pageInfo",
    "plural": false,
    "selections": [
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "endCursor",
        "storageKey": null
      },
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "hasNextPage",
        "storageKey": null
      }
    ],
    "storageKey": null
  }
],
v5 = [
  {
    "kind": "Variable",
    "name": "first",
    "variableName": "first"
  }
];
return {
  "fragment": {
    "argumentDefinitions": [
      (v0/*: any*/),
      (v1/*: any*/)
    ],
    "kind": "Fragment",
    "metadata": null,
    "name": "BulkAddPayStubsMutation",
    "selections": [
      {
        "alias": null,
        "args": (v2/*: any*/),
        "concreteType": "ModifyTimeSheetPayload",
        "kind": "LinkedField",
        "name": "modifyTimeSheet",
        "plural": false,
        "selections": [
          {
            "alias": null,
            "args": null,
            "concreteType": "TimeSheet",
            "kind": "LinkedField",
            "name": "timeSheet",
            "plural": false,
            "selections": [
              (v3/*: any*/),
              {
                "alias": "payStubs",
                "args": null,
                "concreteType": "PayStubConnection",
                "kind": "LinkedField",
                "name": "__BulkAddPayStubs_payStubs_connection",
                "plural": false,
                "selections": (v4/*: any*/),
                "storageKey": null
              }
            ],
            "storageKey": null
          }
        ],
        "storageKey": null
      }
    ],
    "type": "Mutation",
    "abstractKey": null
  },
  "kind": "Request",
  "operation": {
    "argumentDefinitions": [
      (v1/*: any*/),
      (v0/*: any*/)
    ],
    "kind": "Operation",
    "name": "BulkAddPayStubsMutation",
    "selections": [
      {
        "alias": null,
        "args": (v2/*: any*/),
        "concreteType": "ModifyTimeSheetPayload",
        "kind": "LinkedField",
        "name": "modifyTimeSheet",
        "plural": false,
        "selections": [
          {
            "alias": null,
            "args": null,
            "concreteType": "TimeSheet",
            "kind": "LinkedField",
            "name": "timeSheet",
            "plural": false,
            "selections": [
              (v3/*: any*/),
              {
                "alias": null,
                "args": (v5/*: any*/),
                "concreteType": "PayStubConnection",
                "kind": "LinkedField",
                "name": "payStubs",
                "plural": false,
                "selections": (v4/*: any*/),
                "storageKey": null
              },
              {
                "alias": null,
                "args": (v5/*: any*/),
                "filters": null,
                "handle": "connection",
                "key": "BulkAddPayStubs_payStubs",
                "kind": "LinkedHandle",
                "name": "payStubs"
              }
            ],
            "storageKey": null
          }
        ],
        "storageKey": null
      }
    ]
  },
  "params": {
    "cacheID": "3ba0ae78823e72ebfe500806e5a522ce",
    "id": null,
    "metadata": {
      "connection": [
        {
          "count": "first",
          "cursor": null,
          "direction": "forward",
          "path": [
            "modifyTimeSheet",
            "timeSheet",
            "payStubs"
          ]
        }
      ]
    },
    "name": "BulkAddPayStubsMutation",
    "operationKind": "mutation",
    "text": "mutation BulkAddPayStubsMutation(\n  $input: ModifyTimeSheetInput!\n  $first: Int = 500\n) {\n  modifyTimeSheet(input: $input) {\n    timeSheet {\n      id\n      payStubs(first: $first) {\n        edges {\n          node {\n            id\n            employeeId\n            name\n            __typename\n          }\n          cursor\n        }\n        pageInfo {\n          endCursor\n          hasNextPage\n        }\n      }\n    }\n  }\n}\n"
  }
};
})();

(node as any).hash = "592f79c5d294677aeb6f2934327b296c";

export default node;
