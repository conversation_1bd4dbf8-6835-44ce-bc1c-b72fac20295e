/**
 * @generated SignedSource<<04acc4960de5be7fd63ec8199bb9166a>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ReaderFragment } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type PayStubTable_payStubs$data = ReadonlyArray<{
  readonly details: ReadonlyArray<{
    readonly bonus: number | null | undefined;
    readonly dtHours: number | null | undefined;
    readonly expenses: number | null | undefined;
    readonly id: string;
    readonly otHours: number | null | undefined;
    readonly payStubId: string;
    readonly stHours: number | null | undefined;
    readonly totalHours: number | null | undefined;
  }>;
  readonly employeeId: string;
  readonly id: string;
  readonly name: string | null | undefined;
  readonly totalHours: number | null | undefined;
  readonly " $fragmentType": "PayStubTable_payStubs";
}>;
export type PayStubTable_payStubs$key = ReadonlyArray<{
  readonly " $data"?: PayStubTable_payStubs$data;
  readonly " $fragmentSpreads": FragmentRefs<"PayStubTable_payStubs">;
}>;

const node: ReaderFragment = (function(){
var v0 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "id",
  "storageKey": null
},
v1 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "totalHours",
  "storageKey": null
};
return {
  "argumentDefinitions": [],
  "kind": "Fragment",
  "metadata": {
    "plural": true
  },
  "name": "PayStubTable_payStubs",
  "selections": [
    (v0/*: any*/),
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "employeeId",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "name",
      "storageKey": null
    },
    (v1/*: any*/),
    {
      "alias": null,
      "args": null,
      "concreteType": "PayStubDetail",
      "kind": "LinkedField",
      "name": "details",
      "plural": true,
      "selections": [
        (v0/*: any*/),
        {
          "alias": null,
          "args": null,
          "kind": "ScalarField",
          "name": "payStubId",
          "storageKey": null
        },
        {
          "alias": null,
          "args": null,
          "kind": "ScalarField",
          "name": "stHours",
          "storageKey": null
        },
        {
          "alias": null,
          "args": null,
          "kind": "ScalarField",
          "name": "otHours",
          "storageKey": null
        },
        {
          "alias": null,
          "args": null,
          "kind": "ScalarField",
          "name": "dtHours",
          "storageKey": null
        },
        (v1/*: any*/),
        {
          "alias": null,
          "args": null,
          "kind": "ScalarField",
          "name": "bonus",
          "storageKey": null
        },
        {
          "alias": null,
          "args": null,
          "kind": "ScalarField",
          "name": "expenses",
          "storageKey": null
        }
      ],
      "storageKey": null
    }
  ],
  "type": "PayStub",
  "abstractKey": null
};
})();

(node as any).hash = "85752d9c01729a25b08caa5a34829b4f";

export default node;
