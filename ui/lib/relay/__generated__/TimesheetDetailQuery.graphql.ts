/**
 * @generated SignedSource<<0a4e0c7bc7a7833f60574766cada04f7>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ConcreteRequest } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type TimesheetDetailQuery$variables = {
  employerGuid: any;
  timeSheetId: string;
};
export type TimesheetDetailQuery$data = {
  readonly timeSheetById: {
    readonly employerGuid: any;
    readonly id: string;
    readonly " $fragmentSpreads": FragmentRefs<"TimesheetDetail_timeSheet">;
  } | null | undefined;
  readonly " $fragmentSpreads": FragmentRefs<"EmployeeDataFragment_pagination">;
};
export type TimesheetDetailQuery = {
  response: TimesheetDetailQuery$data;
  variables: TimesheetDetailQuery$variables;
};

const node: ConcreteRequest = (function(){
var v0 = {
  "defaultValue": null,
  "kind": "LocalArgument",
  "name": "employerGuid"
},
v1 = {
  "defaultValue": null,
  "kind": "LocalArgument",
  "name": "timeSheetId"
},
v2 = [
  {
    "kind": "Variable",
    "name": "id",
    "variableName": "timeSheetId"
  }
],
v3 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "id",
  "storageKey": null
},
v4 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "employerGuid",
  "storageKey": null
},
v5 = {
  "kind": "Variable",
  "name": "employerGuid",
  "variableName": "employerGuid"
},
v6 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "name",
  "storageKey": null
},
v7 = [
  {
    "kind": "Literal",
    "name": "first",
    "value": 500
  }
],
v8 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "cursor",
  "storageKey": null
},
v9 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "employeeId",
  "storageKey": null
},
v10 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "firstName",
  "storageKey": null
},
v11 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "lastName",
  "storageKey": null
},
v12 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "active",
  "storageKey": null
},
v13 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "externalEmployeeId",
  "storageKey": null
},
v14 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "totalHours",
  "storageKey": null
},
v15 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "payStubId",
  "storageKey": null
},
v16 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "reportLineItemId",
  "storageKey": null
},
v17 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "workDate",
  "storageKey": null
},
v18 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "stHours",
  "storageKey": null
},
v19 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "otHours",
  "storageKey": null
},
v20 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "dtHours",
  "storageKey": null
},
v21 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "bonus",
  "storageKey": null
},
v22 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "expenses",
  "storageKey": null
},
v23 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "jobCode",
  "storageKey": null
},
v24 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "agreementId",
  "storageKey": null
},
v25 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "classificationId",
  "storageKey": null
},
v26 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "subClassificationId",
  "storageKey": null
},
v27 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "costCenter",
  "storageKey": null
},
v28 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "hourlyRate",
  "storageKey": null
},
v29 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "earningsCode",
  "storageKey": null
},
v30 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "__typename",
  "storageKey": null
},
v31 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "hasNextPage",
  "storageKey": null
},
v32 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "endCursor",
  "storageKey": null
},
v33 = {
  "alias": null,
  "args": null,
  "concreteType": "PageInfo",
  "kind": "LinkedField",
  "name": "pageInfo",
  "plural": false,
  "selections": [
    (v31/*: any*/),
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "hasPreviousPage",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "startCursor",
      "storageKey": null
    },
    (v32/*: any*/)
  ],
  "storageKey": null
},
v34 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "totalCount",
  "storageKey": null
},
v35 = [
  (v5/*: any*/),
  {
    "kind": "Literal",
    "name": "first",
    "value": 50
  }
];
return {
  "fragment": {
    "argumentDefinitions": [
      (v0/*: any*/),
      (v1/*: any*/)
    ],
    "kind": "Fragment",
    "metadata": null,
    "name": "TimesheetDetailQuery",
    "selections": [
      {
        "alias": null,
        "args": (v2/*: any*/),
        "concreteType": "TimeSheet",
        "kind": "LinkedField",
        "name": "timeSheetById",
        "plural": false,
        "selections": [
          (v3/*: any*/),
          (v4/*: any*/),
          {
            "args": null,
            "kind": "FragmentSpread",
            "name": "TimesheetDetail_timeSheet"
          }
        ],
        "storageKey": null
      },
      {
        "args": [
          (v5/*: any*/)
        ],
        "kind": "FragmentSpread",
        "name": "EmployeeDataFragment_pagination"
      }
    ],
    "type": "Query",
    "abstractKey": null
  },
  "kind": "Request",
  "operation": {
    "argumentDefinitions": [
      (v1/*: any*/),
      (v0/*: any*/)
    ],
    "kind": "Operation",
    "name": "TimesheetDetailQuery",
    "selections": [
      {
        "alias": null,
        "args": (v2/*: any*/),
        "concreteType": "TimeSheet",
        "kind": "LinkedField",
        "name": "timeSheetById",
        "plural": false,
        "selections": [
          (v3/*: any*/),
          (v4/*: any*/),
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "numericId",
            "storageKey": null
          },
          (v6/*: any*/),
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "status",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "payPeriodEndDate",
            "storageKey": null
          },
          {
            "alias": null,
            "args": (v7/*: any*/),
            "concreteType": "PayStubConnection",
            "kind": "LinkedField",
            "name": "payStubs",
            "plural": false,
            "selections": [
              {
                "alias": null,
                "args": null,
                "concreteType": "PayStubEdge",
                "kind": "LinkedField",
                "name": "edges",
                "plural": true,
                "selections": [
                  (v8/*: any*/),
                  {
                    "alias": null,
                    "args": null,
                    "concreteType": "PayStub",
                    "kind": "LinkedField",
                    "name": "node",
                    "plural": false,
                    "selections": [
                      (v3/*: any*/),
                      (v9/*: any*/),
                      {
                        "alias": null,
                        "args": null,
                        "concreteType": "Employee",
                        "kind": "LinkedField",
                        "name": "employee",
                        "plural": false,
                        "selections": [
                          (v3/*: any*/),
                          (v10/*: any*/),
                          (v11/*: any*/),
                          (v12/*: any*/),
                          (v13/*: any*/)
                        ],
                        "storageKey": null
                      },
                      (v6/*: any*/),
                      (v14/*: any*/),
                      {
                        "alias": null,
                        "args": null,
                        "concreteType": "PayStubDetail",
                        "kind": "LinkedField",
                        "name": "details",
                        "plural": true,
                        "selections": [
                          (v3/*: any*/),
                          (v15/*: any*/),
                          (v16/*: any*/),
                          (v17/*: any*/),
                          (v6/*: any*/),
                          (v18/*: any*/),
                          (v19/*: any*/),
                          (v20/*: any*/),
                          (v14/*: any*/),
                          (v21/*: any*/),
                          (v22/*: any*/),
                          (v23/*: any*/),
                          (v24/*: any*/),
                          (v25/*: any*/),
                          (v26/*: any*/),
                          (v27/*: any*/),
                          (v28/*: any*/),
                          (v29/*: any*/)
                        ],
                        "storageKey": null
                      },
                      (v30/*: any*/)
                    ],
                    "storageKey": null
                  }
                ],
                "storageKey": null
              },
              (v33/*: any*/),
              (v34/*: any*/)
            ],
            "storageKey": "payStubs(first:500)"
          },
          {
            "alias": null,
            "args": (v7/*: any*/),
            "filters": null,
            "handle": "connection",
            "key": "PayStubTable_connectionFragment__payStubs",
            "kind": "LinkedHandle",
            "name": "payStubs"
          },
          {
            "alias": null,
            "args": (v7/*: any*/),
            "filters": null,
            "handle": "connection",
            "key": "TimesheetToolbar_timeSheet_payStubs",
            "kind": "LinkedHandle",
            "name": "payStubs"
          },
          {
            "alias": null,
            "args": (v7/*: any*/),
            "filters": null,
            "handle": "connection",
            "key": "UploadTimeSheetFragments_timeSheetConsolidated_payStubs",
            "kind": "LinkedHandle",
            "name": "payStubs"
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "showBonusColumn",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "showCostCenterColumn",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "showDTHoursColumn",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "showEarningsCodesColumn",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "showExpensesColumn",
            "storageKey": null
          },
          {
            "alias": "payStubsConnection",
            "args": (v7/*: any*/),
            "concreteType": "PayStubConnection",
            "kind": "LinkedField",
            "name": "payStubs",
            "plural": false,
            "selections": [
              {
                "alias": null,
                "args": null,
                "concreteType": "PayStubEdge",
                "kind": "LinkedField",
                "name": "edges",
                "plural": true,
                "selections": [
                  (v8/*: any*/),
                  {
                    "alias": null,
                    "args": null,
                    "concreteType": "PayStub",
                    "kind": "LinkedField",
                    "name": "node",
                    "plural": false,
                    "selections": [
                      (v3/*: any*/),
                      (v9/*: any*/),
                      {
                        "alias": null,
                        "args": null,
                        "concreteType": "Employee",
                        "kind": "LinkedField",
                        "name": "employee",
                        "plural": false,
                        "selections": [
                          (v3/*: any*/),
                          (v10/*: any*/),
                          (v11/*: any*/),
                          (v13/*: any*/),
                          (v12/*: any*/)
                        ],
                        "storageKey": null
                      },
                      {
                        "alias": null,
                        "args": null,
                        "concreteType": "PayStubDetail",
                        "kind": "LinkedField",
                        "name": "details",
                        "plural": true,
                        "selections": [
                          (v3/*: any*/),
                          (v15/*: any*/),
                          (v16/*: any*/),
                          (v17/*: any*/),
                          (v6/*: any*/),
                          (v18/*: any*/),
                          (v19/*: any*/),
                          (v20/*: any*/),
                          (v23/*: any*/),
                          (v29/*: any*/),
                          (v24/*: any*/),
                          (v25/*: any*/),
                          (v26/*: any*/),
                          (v27/*: any*/),
                          (v28/*: any*/),
                          (v21/*: any*/),
                          (v22/*: any*/)
                        ],
                        "storageKey": null
                      },
                      (v30/*: any*/)
                    ],
                    "storageKey": null
                  }
                ],
                "storageKey": null
              },
              (v33/*: any*/),
              (v34/*: any*/)
            ],
            "storageKey": "payStubs(first:500)"
          },
          {
            "alias": "payStubsConnection",
            "args": (v7/*: any*/),
            "filters": null,
            "handle": "connection",
            "key": "TimeSheetPayStubsConnectionFragment_payStubsConnection",
            "kind": "LinkedHandle",
            "name": "payStubs"
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "payStubCount",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "creationDate",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "modificationDate",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "modifiedByUserId",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "type",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "hoursWorked",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "oldId",
            "storageKey": null
          }
        ],
        "storageKey": null
      },
      {
        "alias": null,
        "args": (v35/*: any*/),
        "concreteType": "EmployeeConnection",
        "kind": "LinkedField",
        "name": "employeesByEmployerGuidAsync",
        "plural": false,
        "selections": [
          {
            "alias": null,
            "args": null,
            "concreteType": "EmployeeEdge",
            "kind": "LinkedField",
            "name": "edges",
            "plural": true,
            "selections": [
              {
                "alias": null,
                "args": null,
                "concreteType": "Employee",
                "kind": "LinkedField",
                "name": "node",
                "plural": false,
                "selections": [
                  (v3/*: any*/),
                  (v10/*: any*/),
                  (v11/*: any*/),
                  (v12/*: any*/),
                  (v13/*: any*/),
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "ssn",
                    "storageKey": null
                  },
                  (v30/*: any*/)
                ],
                "storageKey": null
              },
              (v8/*: any*/)
            ],
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "concreteType": "PageInfo",
            "kind": "LinkedField",
            "name": "pageInfo",
            "plural": false,
            "selections": [
              (v31/*: any*/),
              (v32/*: any*/)
            ],
            "storageKey": null
          },
          (v34/*: any*/)
        ],
        "storageKey": null
      },
      {
        "alias": null,
        "args": (v35/*: any*/),
        "filters": [
          "employerGuid"
        ],
        "handle": "connection",
        "key": "EmployeeDataFragment_pagination__employeesByEmployerGuidAsync",
        "kind": "LinkedHandle",
        "name": "employeesByEmployerGuidAsync"
      },
      {
        "alias": null,
        "args": [
          {
            "fields": [
              (v5/*: any*/),
              {
                "kind": "Literal",
                "name": "includeInactiveAgreements",
                "value": false
              }
            ],
            "kind": "ObjectValue",
            "name": "input"
          }
        ],
        "concreteType": "AgreementConnection",
        "kind": "LinkedField",
        "name": "signatoryAgreements",
        "plural": false,
        "selections": [
          {
            "alias": null,
            "args": null,
            "concreteType": "Agreement",
            "kind": "LinkedField",
            "name": "nodes",
            "plural": true,
            "selections": [
              (v3/*: any*/),
              (v6/*: any*/)
            ],
            "storageKey": null
          }
        ],
        "storageKey": null
      }
    ]
  },
  "params": {
    "cacheID": "38c31faf8c05a633c3b549669303d885",
    "id": null,
    "metadata": {},
    "name": "TimesheetDetailQuery",
    "operationKind": "query",
    "text": "query TimesheetDetailQuery(\n  $timeSheetId: ID!\n  $employerGuid: UUID!\n) {\n  timeSheetById(id: $timeSheetId) {\n    id\n    employerGuid\n    ...TimesheetDetail_timeSheet\n  }\n  ...EmployeeDataFragment_pagination_DcV1b\n}\n\nfragment EmployeeDataFragment_pagination_DcV1b on Query {\n  employeesByEmployerGuidAsync(employerGuid: $employerGuid, first: 50) {\n    edges {\n      node {\n        id\n        firstName\n        lastName\n        active\n        externalEmployeeId\n        ssn\n        __typename\n      }\n      cursor\n    }\n    pageInfo {\n      hasNextPage\n      endCursor\n    }\n    totalCount\n  }\n  signatoryAgreements(input: {employerGuid: $employerGuid, includeInactiveAgreements: false}) {\n    nodes {\n      id\n      name\n    }\n  }\n}\n\nfragment EmployeeDisplayFragment_employee on Employee {\n  id\n  firstName\n  lastName\n  active\n  externalEmployeeId\n}\n\nfragment PayStubTable_connectionFragment on TimeSheet {\n  id\n  payStubs(first: 500) {\n    edges {\n      cursor\n      node {\n        ...PayStubTable_payStub\n        ...PayStubTable_payStubs\n        id\n        __typename\n      }\n    }\n    pageInfo {\n      hasNextPage\n      hasPreviousPage\n      startCursor\n      endCursor\n    }\n    totalCount\n  }\n}\n\nfragment PayStubTable_payStub on PayStub {\n  id\n  employeeId\n  employee {\n    id\n    ...EmployeeDisplayFragment_employee\n    ...TimeSheetDetailRow_employee\n  }\n  name\n  totalHours\n  details {\n    id\n    payStubId\n    reportLineItemId\n    workDate\n    name\n    stHours\n    otHours\n    dtHours\n    totalHours\n    bonus\n    expenses\n    jobCode\n    agreementId\n    classificationId\n    subClassificationId\n    costCenter\n    hourlyRate\n    earningsCode\n    ...TimeSheetDetailRow_payStubDetail\n  }\n  ...TimeSheetDetailTableView_payStub\n}\n\nfragment PayStubTable_payStubs on PayStub {\n  id\n  employeeId\n  name\n  totalHours\n  details {\n    id\n    payStubId\n    stHours\n    otHours\n    dtHours\n    totalHours\n    bonus\n    expenses\n  }\n}\n\nfragment TimeSheetDetailRow_employee on Employee {\n  id\n  externalEmployeeId\n  firstName\n  lastName\n  active\n}\n\nfragment TimeSheetDetailRow_payStubDetail on PayStubDetail {\n  id\n  payStubId\n  workDate\n  jobCode\n  agreementId\n  classificationId\n  subClassificationId\n  hourlyRate\n  stHours\n  otHours\n  dtHours\n  bonus\n  expenses\n  earningsCode\n  costCenter\n}\n\nfragment TimeSheetDetailTableView_payStub on PayStub {\n  id\n  employeeId\n  name\n  totalHours\n  employee {\n    ...TimeSheetDetailRow_employee\n    id\n  }\n  details {\n    id\n    workDate\n    jobCode\n    agreementId\n    classificationId\n    subClassificationId\n    hourlyRate\n    stHours\n    otHours\n    dtHours\n    bonus\n    expenses\n    earningsCode\n    costCenter\n    payStubId\n    reportLineItemId\n    totalHours\n    name\n    ...TimeSheetDetailRow_payStubDetail\n  }\n}\n\nfragment TimeSheetGrid_timeSheet on TimeSheet {\n  payPeriodEndDate\n  ...PayStubTable_connectionFragment\n  ...TimesheetToolbar_timeSheet\n}\n\nfragment TimeSheetPayStubsConnectionFragment_timeSheet on TimeSheet {\n  id\n  payStubsConnection: payStubs(first: 500) {\n    edges {\n      cursor\n      node {\n        id\n        employeeId\n        employee {\n          id\n          firstName\n          lastName\n          externalEmployeeId\n          active\n        }\n        details {\n          id\n          payStubId\n          reportLineItemId\n          workDate\n          name\n          stHours\n          otHours\n          dtHours\n          jobCode\n          earningsCode\n          agreementId\n          classificationId\n          subClassificationId\n          costCenter\n          hourlyRate\n          bonus\n          expenses\n        }\n        __typename\n      }\n    }\n    pageInfo {\n      hasNextPage\n      hasPreviousPage\n      startCursor\n      endCursor\n    }\n    totalCount\n  }\n  payStubCount\n}\n\nfragment TimeSheetSettings_timeSheet on TimeSheet {\n  id\n  showBonusColumn\n  showCostCenterColumn\n  showDTHoursColumn\n  showEarningsCodesColumn\n  showExpensesColumn\n}\n\nfragment TimesheetDetailView_timeSheet on TimeSheet {\n  numericId\n  name\n  status\n  payPeriodEndDate\n  ...TimeSheetGrid_timeSheet\n}\n\nfragment TimesheetDetail_timeSheet on TimeSheet {\n  ...TimesheetDetailView_timeSheet\n  ...TimeSheetGrid_timeSheet\n  ...TimeSheetSettings_timeSheet\n  ...PayStubTable_connectionFragment\n  ...TimeSheetPayStubsConnectionFragment_timeSheet\n  id\n  numericId\n  name\n  status\n  payPeriodEndDate\n  showBonusColumn\n  showCostCenterColumn\n  showDTHoursColumn\n  showEarningsCodesColumn\n  showExpensesColumn\n  employerGuid\n  creationDate\n  modificationDate\n  modifiedByUserId\n  type\n  hoursWorked\n  oldId\n  ...TimesheetToolbar_timeSheet\n}\n\nfragment TimesheetToolbar_timeSheet on TimeSheet {\n  id\n  numericId\n  employerGuid\n  payStubs(first: 500) {\n    edges {\n      node {\n        id\n        details {\n          id\n        }\n        __typename\n      }\n      cursor\n    }\n    pageInfo {\n      endCursor\n      hasNextPage\n    }\n  }\n  ...TimeSheetSettings_timeSheet\n  ...UploadTimeSheetFragments_timeSheetConsolidated\n}\n\nfragment UploadTimeSheetFragments_timeSheetConsolidated on TimeSheet {\n  id\n  numericId\n  employerGuid\n  payStubs(first: 500) {\n    edges {\n      node {\n        id\n        employeeId\n        name\n        __typename\n      }\n      cursor\n    }\n    pageInfo {\n      endCursor\n      hasNextPage\n    }\n  }\n}\n"
  }
};
})();

(node as any).hash = "6575bbea6b334b93a0c705fcd45d18ae";

export default node;
