/**
 * @generated SignedSource<<5d2551c55ec97d9e30d04ad1fb90a394>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ConcreteRequest } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type SortEnumType = "ASC" | "DESC" | "%future added value";
export type TimeSheetSortInput = {
  createdByUserId?: SortEnumType | null | undefined;
  creationDate?: SortEnumType | null | undefined;
  employerGuid?: SortEnumType | null | undefined;
  id?: SortEnumType | null | undefined;
  modificationDate?: SortEnumType | null | undefined;
  modifiedByUserId?: SortEnumType | null | undefined;
  name?: SortEnumType | null | undefined;
  numericId?: SortEnumType | null | undefined;
  oldId?: SortEnumType | null | undefined;
  payPeriodEndDate?: SortEnumType | null | undefined;
  showBonusColumn?: SortEnumType | null | undefined;
  showCostCenterColumn?: SortEnumType | null | undefined;
  showDTHoursColumn?: SortEnumType | null | undefined;
  showEarningsCodesColumn?: SortEnumType | null | undefined;
  showExpensesColumn?: SortEnumType | null | undefined;
  status?: SortEnumType | null | undefined;
  type?: SortEnumType | null | undefined;
};
export type TimeSheetFilterInput = {
  and?: ReadonlyArray<TimeSheetFilterInput> | null | undefined;
  createdByUserId?: StringOperationFilterInput | null | undefined;
  creationDate?: DateTimeOperationFilterInput | null | undefined;
  employerGuid?: UuidOperationFilterInput | null | undefined;
  id?: IdOperationFilterInput | null | undefined;
  modificationDate?: DateTimeOperationFilterInput | null | undefined;
  modifiedByUserId?: StringOperationFilterInput | null | undefined;
  name?: StringOperationFilterInput | null | undefined;
  numericId?: IntOperationFilterInput | null | undefined;
  oldId?: UuidOperationFilterInput | null | undefined;
  or?: ReadonlyArray<TimeSheetFilterInput> | null | undefined;
  payPeriodEndDate?: LocalDateOperationFilterInput | null | undefined;
  payStubs?: ListFilterInputTypeOfPayStubFilterInput | null | undefined;
  showBonusColumn?: BooleanOperationFilterInput | null | undefined;
  showCostCenterColumn?: BooleanOperationFilterInput | null | undefined;
  showDTHoursColumn?: BooleanOperationFilterInput | null | undefined;
  showEarningsCodesColumn?: BooleanOperationFilterInput | null | undefined;
  showExpensesColumn?: BooleanOperationFilterInput | null | undefined;
  status?: StringOperationFilterInput | null | undefined;
  type?: StringOperationFilterInput | null | undefined;
};
export type IdOperationFilterInput = {
  eq?: string | null | undefined;
  in?: ReadonlyArray<string | null | undefined> | null | undefined;
  neq?: string | null | undefined;
  nin?: ReadonlyArray<string | null | undefined> | null | undefined;
};
export type UuidOperationFilterInput = {
  eq?: any | null | undefined;
  gt?: any | null | undefined;
  gte?: any | null | undefined;
  in?: ReadonlyArray<any | null | undefined> | null | undefined;
  lt?: any | null | undefined;
  lte?: any | null | undefined;
  neq?: any | null | undefined;
  ngt?: any | null | undefined;
  ngte?: any | null | undefined;
  nin?: ReadonlyArray<any | null | undefined> | null | undefined;
  nlt?: any | null | undefined;
  nlte?: any | null | undefined;
};
export type IntOperationFilterInput = {
  eq?: number | null | undefined;
  gt?: number | null | undefined;
  gte?: number | null | undefined;
  in?: ReadonlyArray<number | null | undefined> | null | undefined;
  lt?: number | null | undefined;
  lte?: number | null | undefined;
  neq?: number | null | undefined;
  ngt?: number | null | undefined;
  ngte?: number | null | undefined;
  nin?: ReadonlyArray<number | null | undefined> | null | undefined;
  nlt?: number | null | undefined;
  nlte?: number | null | undefined;
};
export type StringOperationFilterInput = {
  and?: ReadonlyArray<StringOperationFilterInput> | null | undefined;
  contains?: string | null | undefined;
  endsWith?: string | null | undefined;
  eq?: string | null | undefined;
  in?: ReadonlyArray<string | null | undefined> | null | undefined;
  ncontains?: string | null | undefined;
  nendsWith?: string | null | undefined;
  neq?: string | null | undefined;
  nin?: ReadonlyArray<string | null | undefined> | null | undefined;
  nstartsWith?: string | null | undefined;
  or?: ReadonlyArray<StringOperationFilterInput> | null | undefined;
  startsWith?: string | null | undefined;
};
export type LocalDateOperationFilterInput = {
  eq?: any | null | undefined;
  gt?: any | null | undefined;
  gte?: any | null | undefined;
  in?: ReadonlyArray<any | null | undefined> | null | undefined;
  lt?: any | null | undefined;
  lte?: any | null | undefined;
  neq?: any | null | undefined;
  ngt?: any | null | undefined;
  ngte?: any | null | undefined;
  nin?: ReadonlyArray<any | null | undefined> | null | undefined;
  nlt?: any | null | undefined;
  nlte?: any | null | undefined;
};
export type DateTimeOperationFilterInput = {
  eq?: any | null | undefined;
  gt?: any | null | undefined;
  gte?: any | null | undefined;
  in?: ReadonlyArray<any | null | undefined> | null | undefined;
  lt?: any | null | undefined;
  lte?: any | null | undefined;
  neq?: any | null | undefined;
  ngt?: any | null | undefined;
  ngte?: any | null | undefined;
  nin?: ReadonlyArray<any | null | undefined> | null | undefined;
  nlt?: any | null | undefined;
  nlte?: any | null | undefined;
};
export type BooleanOperationFilterInput = {
  eq?: boolean | null | undefined;
  neq?: boolean | null | undefined;
};
export type ListFilterInputTypeOfPayStubFilterInput = {
  all?: PayStubFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: PayStubFilterInput | null | undefined;
  some?: PayStubFilterInput | null | undefined;
};
export type PayStubFilterInput = {
  and?: ReadonlyArray<PayStubFilterInput> | null | undefined;
  details?: ListFilterInputTypeOfPayStubDetailFilterInput | null | undefined;
  employee?: EmployeeFilterInput | null | undefined;
  employeeId?: IdOperationFilterInput | null | undefined;
  id?: IdOperationFilterInput | null | undefined;
  name?: StringOperationFilterInput | null | undefined;
  or?: ReadonlyArray<PayStubFilterInput> | null | undefined;
  timeSheet?: TimeSheetFilterInput | null | undefined;
  timeSheetId?: IntOperationFilterInput | null | undefined;
  totalHours?: FloatOperationFilterInput | null | undefined;
};
export type FloatOperationFilterInput = {
  eq?: number | null | undefined;
  gt?: number | null | undefined;
  gte?: number | null | undefined;
  in?: ReadonlyArray<number | null | undefined> | null | undefined;
  lt?: number | null | undefined;
  lte?: number | null | undefined;
  neq?: number | null | undefined;
  ngt?: number | null | undefined;
  ngte?: number | null | undefined;
  nin?: ReadonlyArray<number | null | undefined> | null | undefined;
  nlt?: number | null | undefined;
  nlte?: number | null | undefined;
};
export type ListFilterInputTypeOfPayStubDetailFilterInput = {
  all?: PayStubDetailFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: PayStubDetailFilterInput | null | undefined;
  some?: PayStubDetailFilterInput | null | undefined;
};
export type PayStubDetailFilterInput = {
  agreementId?: IntOperationFilterInput | null | undefined;
  and?: ReadonlyArray<PayStubDetailFilterInput> | null | undefined;
  bonus?: FloatOperationFilterInput | null | undefined;
  classificationId?: IntOperationFilterInput | null | undefined;
  costCenter?: StringOperationFilterInput | null | undefined;
  dtHours?: FloatOperationFilterInput | null | undefined;
  earningsCode?: StringOperationFilterInput | null | undefined;
  expenses?: FloatOperationFilterInput | null | undefined;
  hourlyRate?: FloatOperationFilterInput | null | undefined;
  id?: IdOperationFilterInput | null | undefined;
  jobCode?: StringOperationFilterInput | null | undefined;
  name?: StringOperationFilterInput | null | undefined;
  or?: ReadonlyArray<PayStubDetailFilterInput> | null | undefined;
  otHours?: FloatOperationFilterInput | null | undefined;
  payStub?: PayStubFilterInput | null | undefined;
  payStubId?: IdOperationFilterInput | null | undefined;
  reportLineItem?: ReportLineItemFilterInput | null | undefined;
  reportLineItemId?: IntOperationFilterInput | null | undefined;
  stHours?: FloatOperationFilterInput | null | undefined;
  subClassificationId?: IntOperationFilterInput | null | undefined;
  totalHours?: FloatOperationFilterInput | null | undefined;
  workDate?: LocalDateOperationFilterInput | null | undefined;
};
export type ReportLineItemFilterInput = {
  amendedLineItemId?: IntOperationFilterInput | null | undefined;
  and?: ReadonlyArray<ReportLineItemFilterInput> | null | undefined;
  classificationName?: ClassificationNameFilterInput | null | undefined;
  classificationNameId?: IntOperationFilterInput | null | undefined;
  damendmentAction?: DamendmentActionFilterInput | null | undefined;
  damendmentActionId?: IntOperationFilterInput | null | undefined;
  employee?: EmployeeFilterInput | null | undefined;
  employeeId?: IntOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  idNavigation?: RootFilterInput | null | undefined;
  or?: ReadonlyArray<ReportLineItemFilterInput> | null | undefined;
  payStubDetails?: ListFilterInputTypeOfPayStubDetailFilterInput | null | undefined;
  report?: ReportFilterInput | null | undefined;
  reportId?: IntOperationFilterInput | null | undefined;
  reportLineItemDetails?: ListFilterInputTypeOfReportLineItemDetailFilterInput | null | undefined;
  subClassification?: SubClassificationFilterInput | null | undefined;
  subClassificationId?: IntOperationFilterInput | null | undefined;
};
export type ClassificationNameFilterInput = {
  and?: ReadonlyArray<ClassificationNameFilterInput> | null | undefined;
  chapter?: ChapterFilterInput | null | undefined;
  chapterId?: IntOperationFilterInput | null | undefined;
  dclassificationCode?: DclassificationCodeFilterInput | null | undefined;
  dclassificationCodeId?: IntOperationFilterInput | null | undefined;
  description?: StringOperationFilterInput | null | undefined;
  dstatus?: DstatusFilterInput | null | undefined;
  dstatusId?: IntOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  idNavigation?: RootFilterInput | null | undefined;
  name?: StringOperationFilterInput | null | undefined;
  or?: ReadonlyArray<ClassificationNameFilterInput> | null | undefined;
  reportLineItems?: ListFilterInputTypeOfReportLineItemFilterInput | null | undefined;
};
export type ChapterFilterInput = {
  agreements?: ListFilterInputTypeOfAgreementFilterInput | null | undefined;
  and?: ReadonlyArray<ChapterFilterInput> | null | undefined;
  benefits?: ListFilterInputTypeOfBenefitFilterInput | null | undefined;
  classificationNames?: ListFilterInputTypeOfClassificationNameFilterInput | null | undefined;
  employeeAssociationId?: StringOperationFilterInput | null | undefined;
  employerAssociationId?: StringOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  idNavigation?: OrganizationFilterInput | null | undefined;
  limited?: BooleanOperationFilterInput | null | undefined;
  or?: ReadonlyArray<ChapterFilterInput> | null | undefined;
  subClassifications?: ListFilterInputTypeOfSubClassificationFilterInput | null | undefined;
};
export type ListFilterInputTypeOfAgreementFilterInput = {
  all?: AgreementFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: AgreementFilterInput | null | undefined;
  some?: AgreementFilterInput | null | undefined;
};
export type AgreementFilterInput = {
  adHoc?: BooleanOperationFilterInput | null | undefined;
  agreementsToBenefits?: ListFilterInputTypeOfAgreementsToBenefitFilterInput | null | undefined;
  allowCurrentMonthReporting?: BooleanOperationFilterInput | null | undefined;
  allowDuplicateLineItems?: BooleanOperationFilterInput | null | undefined;
  allowPublishing?: BooleanOperationFilterInput | null | undefined;
  and?: ReadonlyArray<AgreementFilterInput> | null | undefined;
  certificationLanguage?: StringOperationFilterInput | null | undefined;
  chapter?: ChapterFilterInput | null | undefined;
  chapterId?: IntOperationFilterInput | null | undefined;
  costCodes?: ListFilterInputTypeOfCostCodeFilterInput | null | undefined;
  dagreementType?: DagreementTypeFilterInput | null | undefined;
  dagreementTypeId?: IntOperationFilterInput | null | undefined;
  disableEmployerAmendments?: BooleanOperationFilterInput | null | undefined;
  effectiveEndDate?: DateTimeOperationFilterInput | null | undefined;
  effectiveStartDate?: DateTimeOperationFilterInput | null | undefined;
  employersToAgreements?: ListFilterInputTypeOfEmployersToAgreementFilterInput | null | undefined;
  fundingComments?: ListFilterInputTypeOfFundingCommentFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  idNavigation?: RootFilterInput | null | undefined;
  name?: StringOperationFilterInput | null | undefined;
  or?: ReadonlyArray<AgreementFilterInput> | null | undefined;
  organizations?: ListFilterInputTypeOfOrganizationFilterInput | null | undefined;
  rateSchedules?: ListFilterInputTypeOfRateScheduleFilterInput | null | undefined;
  reports?: ListFilterInputTypeOfReportFilterInput | null | undefined;
  requiresSignatoryStatus?: BooleanOperationFilterInput | null | undefined;
  suppressFromEmployerRoster?: BooleanOperationFilterInput | null | undefined;
  union?: UnionFilterInput | null | undefined;
  unionContact?: EmployeeFilterInput | null | undefined;
  unionContactId?: IntOperationFilterInput | null | undefined;
  unionId?: IntOperationFilterInput | null | undefined;
  weekly?: BooleanOperationFilterInput | null | undefined;
};
export type ListFilterInputTypeOfAgreementsToBenefitFilterInput = {
  all?: AgreementsToBenefitFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: AgreementsToBenefitFilterInput | null | undefined;
  some?: AgreementsToBenefitFilterInput | null | undefined;
};
export type AgreementsToBenefitFilterInput = {
  agreement?: AgreementFilterInput | null | undefined;
  agreementId?: IntOperationFilterInput | null | undefined;
  and?: ReadonlyArray<AgreementsToBenefitFilterInput> | null | undefined;
  benefit?: BenefitFilterInput | null | undefined;
  benefitId?: IntOperationFilterInput | null | undefined;
  collectingAgent?: OrganizationFilterInput | null | undefined;
  collectingAgentId?: IntOperationFilterInput | null | undefined;
  fundAdministrator?: OrganizationFilterInput | null | undefined;
  fundAdministratorId?: IntOperationFilterInput | null | undefined;
  or?: ReadonlyArray<AgreementsToBenefitFilterInput> | null | undefined;
  remitToInstructions?: StringOperationFilterInput | null | undefined;
};
export type BenefitFilterInput = {
  agreementsToBenefits?: ListFilterInputTypeOfAgreementsToBenefitFilterInput | null | undefined;
  and?: ReadonlyArray<BenefitFilterInput> | null | undefined;
  applyToAssociationReport?: BooleanOperationFilterInput | null | undefined;
  chapter?: ChapterFilterInput | null | undefined;
  chapterAdministratorOnly?: BooleanOperationFilterInput | null | undefined;
  chapterId?: IntOperationFilterInput | null | undefined;
  costCodes?: ListFilterInputTypeOfCostCodeFilterInput | null | undefined;
  defaultOn?: BooleanOperationFilterInput | null | undefined;
  description?: StringOperationFilterInput | null | undefined;
  displayOnFundingOnly?: BooleanOperationFilterInput | null | undefined;
  doNotTotal?: BooleanOperationFilterInput | null | undefined;
  dstatusId?: IntOperationFilterInput | null | undefined;
  electronicPaymentConfigurations?: ListFilterInputTypeOfElectronicPaymentConfigurationFilterInput | null | undefined;
  electronicPayments?: ListFilterInputTypeOfElectronicPaymentFilterInput | null | undefined;
  employeeDeduction?: BooleanOperationFilterInput | null | undefined;
  employeeElectionOverridable?: BooleanOperationFilterInput | null | undefined;
  employeeRateOverridable?: BooleanOperationFilterInput | null | undefined;
  employerElectionOverridable?: BooleanOperationFilterInput | null | undefined;
  employerRateOverridable?: BooleanOperationFilterInput | null | undefined;
  fundingComments?: ListFilterInputTypeOfFundingCommentFilterInput | null | undefined;
  hourComponent?: BooleanOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  idNavigation?: RootFilterInput | null | undefined;
  informationalOnly?: BooleanOperationFilterInput | null | undefined;
  name?: StringOperationFilterInput | null | undefined;
  oncePerEe?: BooleanOperationFilterInput | null | undefined;
  or?: ReadonlyArray<BenefitFilterInput> | null | undefined;
  paymentDetails?: ListFilterInputTypeOfPaymentDetailFilterInput | null | undefined;
  reportLineItemDetails?: ListFilterInputTypeOfReportLineItemDetailFilterInput | null | undefined;
  reportedBenefitReleaseAuthorizations?: ListFilterInputTypeOfReportedBenefitReleaseAuthorizationFilterInput | null | undefined;
  selectable?: BooleanOperationFilterInput | null | undefined;
  taxable?: BooleanOperationFilterInput | null | undefined;
  variantType?: StringOperationFilterInput | null | undefined;
};
export type ListFilterInputTypeOfCostCodeFilterInput = {
  all?: CostCodeFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: CostCodeFilterInput | null | undefined;
  some?: CostCodeFilterInput | null | undefined;
};
export type CostCodeFilterInput = {
  agreement?: AgreementFilterInput | null | undefined;
  agreementId?: IntOperationFilterInput | null | undefined;
  and?: ReadonlyArray<CostCodeFilterInput> | null | undefined;
  benefit?: BenefitFilterInput | null | undefined;
  benefitId?: IntOperationFilterInput | null | undefined;
  code?: StringOperationFilterInput | null | undefined;
  employer?: EmployerFilterInput | null | undefined;
  employerId?: IntOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  or?: ReadonlyArray<CostCodeFilterInput> | null | undefined;
};
export type EmployerFilterInput = {
  and?: ReadonlyArray<EmployerFilterInput> | null | undefined;
  associationId?: StringOperationFilterInput | null | undefined;
  businessDescription?: StringOperationFilterInput | null | undefined;
  costCodes?: ListFilterInputTypeOfCostCodeFilterInput | null | undefined;
  dba?: StringOperationFilterInput | null | undefined;
  employersToAgreements?: ListFilterInputTypeOfEmployersToAgreementFilterInput | null | undefined;
  fein?: StringOperationFilterInput | null | undefined;
  fundingComments?: ListFilterInputTypeOfFundingCommentFilterInput | null | undefined;
  id?: IdOperationFilterInput | null | undefined;
  isAssociationMember?: BooleanOperationFilterInput | null | undefined;
  or?: ReadonlyArray<EmployerFilterInput> | null | undefined;
  organization?: OrganizationFilterInput | null | undefined;
  reports?: ListFilterInputTypeOfReportFilterInput | null | undefined;
  root?: RootFilterInput | null | undefined;
};
export type OrganizationFilterInput = {
  agreements?: ListFilterInputTypeOfAgreementFilterInput | null | undefined;
  agreementsToBenefitCollectingAgents?: ListFilterInputTypeOfAgreementsToBenefitFilterInput | null | undefined;
  agreementsToBenefitFundAdministrators?: ListFilterInputTypeOfAgreementsToBenefitFilterInput | null | undefined;
  and?: ReadonlyArray<OrganizationFilterInput> | null | undefined;
  chapter?: ChapterFilterInput | null | undefined;
  dorganizationType?: DorganizationTypeFilterInput | null | undefined;
  dorganizationTypeId?: IntOperationFilterInput | null | undefined;
  electronicPaymentConfigurationChapters?: ListFilterInputTypeOfElectronicPaymentConfigurationFilterInput | null | undefined;
  electronicPaymentConfigurationOrganizations?: ListFilterInputTypeOfElectronicPaymentConfigurationFilterInput | null | undefined;
  employer?: EmployerFilterInput | null | undefined;
  eprusers?: ListFilterInputTypeOfEpruserFilterInput | null | undefined;
  fundAdministrator?: FundAdministratorFilterInput | null | undefined;
  fundingCommentDetails?: ListFilterInputTypeOfFundingCommentDetailFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  idNavigation?: PartyFilterInput | null | undefined;
  logoFilePath?: StringOperationFilterInput | null | undefined;
  name?: StringOperationFilterInput | null | undefined;
  optimisticLockField?: IntOperationFilterInput | null | undefined;
  or?: ReadonlyArray<OrganizationFilterInput> | null | undefined;
  orders?: ListFilterInputTypeOfOrderFilterInput | null | undefined;
  serviceSubscriptions?: ListFilterInputTypeOfServiceSubscriptionFilterInput | null | undefined;
  trade?: TradeFilterInput | null | undefined;
  union?: UnionFilterInput | null | undefined;
};
export type DorganizationTypeFilterInput = {
  and?: ReadonlyArray<DorganizationTypeFilterInput> | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  name?: StringOperationFilterInput | null | undefined;
  or?: ReadonlyArray<DorganizationTypeFilterInput> | null | undefined;
  organizations?: ListFilterInputTypeOfOrganizationFilterInput | null | undefined;
  productsToOrganizationTypes?: ListFilterInputTypeOfProductsToOrganizationTypeFilterInput | null | undefined;
};
export type ListFilterInputTypeOfOrganizationFilterInput = {
  all?: OrganizationFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: OrganizationFilterInput | null | undefined;
  some?: OrganizationFilterInput | null | undefined;
};
export type ListFilterInputTypeOfProductsToOrganizationTypeFilterInput = {
  all?: ProductsToOrganizationTypeFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: ProductsToOrganizationTypeFilterInput | null | undefined;
  some?: ProductsToOrganizationTypeFilterInput | null | undefined;
};
export type ProductsToOrganizationTypeFilterInput = {
  and?: ReadonlyArray<ProductsToOrganizationTypeFilterInput> | null | undefined;
  createdBy?: StringOperationFilterInput | null | undefined;
  creationDate?: DateTimeOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  lastModifiedBy?: StringOperationFilterInput | null | undefined;
  lastModifiedDate?: DateTimeOperationFilterInput | null | undefined;
  or?: ReadonlyArray<ProductsToOrganizationTypeFilterInput> | null | undefined;
  organizationType?: DorganizationTypeFilterInput | null | undefined;
  organizationTypeId?: IntOperationFilterInput | null | undefined;
  product?: Product1FilterInput | null | undefined;
  productId?: IntOperationFilterInput | null | undefined;
};
export type Product1FilterInput = {
  active?: BooleanOperationFilterInput | null | undefined;
  and?: ReadonlyArray<Product1FilterInput> | null | undefined;
  createdBy?: StringOperationFilterInput | null | undefined;
  creationDate?: DateTimeOperationFilterInput | null | undefined;
  customReportProducts?: ListFilterInputTypeOfCustomReportProductFilterInput | null | undefined;
  description?: StringOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  keywords?: StringOperationFilterInput | null | undefined;
  largeImagePath?: StringOperationFilterInput | null | undefined;
  lastModifiedBy?: StringOperationFilterInput | null | undefined;
  lastModifiedDate?: DateTimeOperationFilterInput | null | undefined;
  mediumImagePath?: StringOperationFilterInput | null | undefined;
  name?: StringOperationFilterInput | null | undefined;
  or?: ReadonlyArray<Product1FilterInput> | null | undefined;
  orderDetails?: ListFilterInputTypeOfOrderDetailFilterInput | null | undefined;
  paymentType?: DpaymentTypeFilterInput | null | undefined;
  paymentTypeId?: IntOperationFilterInput | null | undefined;
  price?: DecimalOperationFilterInput | null | undefined;
  productCategories?: ListFilterInputTypeOfProductCategoryFilterInput | null | undefined;
  productsToOrganizationTypes?: ListFilterInputTypeOfProductsToOrganizationTypeFilterInput | null | undefined;
  shortDescription?: StringOperationFilterInput | null | undefined;
  smallImagePath?: StringOperationFilterInput | null | undefined;
};
export type DecimalOperationFilterInput = {
  eq?: any | null | undefined;
  gt?: any | null | undefined;
  gte?: any | null | undefined;
  in?: ReadonlyArray<any | null | undefined> | null | undefined;
  lt?: any | null | undefined;
  lte?: any | null | undefined;
  neq?: any | null | undefined;
  ngt?: any | null | undefined;
  ngte?: any | null | undefined;
  nin?: ReadonlyArray<any | null | undefined> | null | undefined;
  nlt?: any | null | undefined;
  nlte?: any | null | undefined;
};
export type ListFilterInputTypeOfCustomReportProductFilterInput = {
  all?: CustomReportProductFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: CustomReportProductFilterInput | null | undefined;
  some?: CustomReportProductFilterInput | null | undefined;
};
export type CustomReportProductFilterInput = {
  and?: ReadonlyArray<CustomReportProductFilterInput> | null | undefined;
  createdBy?: StringOperationFilterInput | null | undefined;
  creationDate?: DateTimeOperationFilterInput | null | undefined;
  customReport?: CustomReportFilterInput | null | undefined;
  customReportId?: IntOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  lastModifiedBy?: StringOperationFilterInput | null | undefined;
  lastModifiedDate?: DateTimeOperationFilterInput | null | undefined;
  or?: ReadonlyArray<CustomReportProductFilterInput> | null | undefined;
  product?: Product1FilterInput | null | undefined;
  productId?: IntOperationFilterInput | null | undefined;
};
export type CustomReportFilterInput = {
  and?: ReadonlyArray<CustomReportFilterInput> | null | undefined;
  customReportPivot?: CustomReportPivotFilterInput | null | undefined;
  customReportProducts?: ListFilterInputTypeOfCustomReportProductFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  name?: StringOperationFilterInput | null | undefined;
  newApp?: BooleanOperationFilterInput | null | undefined;
  notes?: StringOperationFilterInput | null | undefined;
  optimisticLockField?: IntOperationFilterInput | null | undefined;
  or?: ReadonlyArray<CustomReportFilterInput> | null | undefined;
  reportPath?: StringOperationFilterInput | null | undefined;
  reportTypeId?: IntOperationFilterInput | null | undefined;
  suppressDuplicatesThroughColumnName?: StringOperationFilterInput | null | undefined;
  usesGuidSubstitution?: BooleanOperationFilterInput | null | undefined;
};
export type CustomReportPivotFilterInput = {
  and?: ReadonlyArray<CustomReportPivotFilterInput> | null | undefined;
  nameColumn?: StringOperationFilterInput | null | undefined;
  or?: ReadonlyArray<CustomReportPivotFilterInput> | null | undefined;
  report?: CustomReportFilterInput | null | undefined;
  reportId?: IntOperationFilterInput | null | undefined;
  sort?: BooleanOperationFilterInput | null | undefined;
  valueColumn?: StringOperationFilterInput | null | undefined;
};
export type ListFilterInputTypeOfOrderDetailFilterInput = {
  all?: OrderDetailFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: OrderDetailFilterInput | null | undefined;
  some?: OrderDetailFilterInput | null | undefined;
};
export type OrderDetailFilterInput = {
  and?: ReadonlyArray<OrderDetailFilterInput> | null | undefined;
  createdBy?: StringOperationFilterInput | null | undefined;
  creationDate?: DateTimeOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  lastModifiedBy?: StringOperationFilterInput | null | undefined;
  lastModifiedDate?: DateTimeOperationFilterInput | null | undefined;
  or?: ReadonlyArray<OrderDetailFilterInput> | null | undefined;
  order?: OrderFilterInput | null | undefined;
  orderId?: IntOperationFilterInput | null | undefined;
  paymentType?: DpaymentTypeFilterInput | null | undefined;
  paymentTypeId?: IntOperationFilterInput | null | undefined;
  price?: DecimalOperationFilterInput | null | undefined;
  product?: Product1FilterInput | null | undefined;
  productId?: IntOperationFilterInput | null | undefined;
};
export type OrderFilterInput = {
  and?: ReadonlyArray<OrderFilterInput> | null | undefined;
  confirmationNumber?: StringOperationFilterInput | null | undefined;
  createdBy?: StringOperationFilterInput | null | undefined;
  creationDate?: DateTimeOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  lastModifiedBy?: StringOperationFilterInput | null | undefined;
  lastModifiedDate?: DateTimeOperationFilterInput | null | undefined;
  or?: ReadonlyArray<OrderFilterInput> | null | undefined;
  orderDetails?: ListFilterInputTypeOfOrderDetailFilterInput | null | undefined;
  organization?: OrganizationFilterInput | null | undefined;
  organizationId?: IntOperationFilterInput | null | undefined;
  status?: DorderStatusFilterInput | null | undefined;
  statusId?: IntOperationFilterInput | null | undefined;
};
export type DorderStatusFilterInput = {
  and?: ReadonlyArray<DorderStatusFilterInput> | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  name?: StringOperationFilterInput | null | undefined;
  or?: ReadonlyArray<DorderStatusFilterInput> | null | undefined;
  orders?: ListFilterInputTypeOfOrderFilterInput | null | undefined;
};
export type ListFilterInputTypeOfOrderFilterInput = {
  all?: OrderFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: OrderFilterInput | null | undefined;
  some?: OrderFilterInput | null | undefined;
};
export type DpaymentTypeFilterInput = {
  and?: ReadonlyArray<DpaymentTypeFilterInput> | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  name?: StringOperationFilterInput | null | undefined;
  or?: ReadonlyArray<DpaymentTypeFilterInput> | null | undefined;
  orderDetails?: ListFilterInputTypeOfOrderDetailFilterInput | null | undefined;
  product1s?: ListFilterInputTypeOfProduct1FilterInput | null | undefined;
};
export type ListFilterInputTypeOfProduct1FilterInput = {
  all?: Product1FilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: Product1FilterInput | null | undefined;
  some?: Product1FilterInput | null | undefined;
};
export type ListFilterInputTypeOfProductCategoryFilterInput = {
  all?: ProductCategoryFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: ProductCategoryFilterInput | null | undefined;
  some?: ProductCategoryFilterInput | null | undefined;
};
export type ProductCategoryFilterInput = {
  and?: ReadonlyArray<ProductCategoryFilterInput> | null | undefined;
  createdBy?: StringOperationFilterInput | null | undefined;
  creationDate?: DateTimeOperationFilterInput | null | undefined;
  dcategory?: DcategoryFilterInput | null | undefined;
  dcategoryId?: IntOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  lastModifiedBy?: StringOperationFilterInput | null | undefined;
  lastModifiedDate?: DateTimeOperationFilterInput | null | undefined;
  or?: ReadonlyArray<ProductCategoryFilterInput> | null | undefined;
  product?: Product1FilterInput | null | undefined;
  productId?: IntOperationFilterInput | null | undefined;
};
export type DcategoryFilterInput = {
  active?: BooleanOperationFilterInput | null | undefined;
  and?: ReadonlyArray<DcategoryFilterInput> | null | undefined;
  createdBy?: StringOperationFilterInput | null | undefined;
  creationDate?: DateTimeOperationFilterInput | null | undefined;
  description?: StringOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  keywords?: StringOperationFilterInput | null | undefined;
  largeImagePath?: StringOperationFilterInput | null | undefined;
  lastModifiedBy?: StringOperationFilterInput | null | undefined;
  lastModifiedDate?: DateTimeOperationFilterInput | null | undefined;
  mediumImagePath?: StringOperationFilterInput | null | undefined;
  name?: StringOperationFilterInput | null | undefined;
  or?: ReadonlyArray<DcategoryFilterInput> | null | undefined;
  parentCategory?: IntOperationFilterInput | null | undefined;
  productCategories?: ListFilterInputTypeOfProductCategoryFilterInput | null | undefined;
  smallImagePath?: StringOperationFilterInput | null | undefined;
};
export type ListFilterInputTypeOfElectronicPaymentConfigurationFilterInput = {
  all?: ElectronicPaymentConfigurationFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: ElectronicPaymentConfigurationFilterInput | null | undefined;
  some?: ElectronicPaymentConfigurationFilterInput | null | undefined;
};
export type ElectronicPaymentConfigurationFilterInput = {
  and?: ReadonlyArray<ElectronicPaymentConfigurationFilterInput> | null | undefined;
  benefit?: BenefitFilterInput | null | undefined;
  benefitId?: IntOperationFilterInput | null | undefined;
  chapter?: OrganizationFilterInput | null | undefined;
  chapterId?: IntOperationFilterInput | null | undefined;
  createdBy?: StringOperationFilterInput | null | undefined;
  creationDate?: DateTimeOperationFilterInput | null | undefined;
  delectronicPaymentOption?: DelectronicPaymentOptionFilterInput | null | undefined;
  delectronicPaymentOptionId?: IntOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  idNavigation?: RootFilterInput | null | undefined;
  lastModifiedBy?: StringOperationFilterInput | null | undefined;
  lastModifiedDate?: DateTimeOperationFilterInput | null | undefined;
  mandatory?: BooleanOperationFilterInput | null | undefined;
  nachaconfiguration?: NachaconfigurationFilterInput | null | undefined;
  nachaconfigurationId?: IntOperationFilterInput | null | undefined;
  or?: ReadonlyArray<ElectronicPaymentConfigurationFilterInput> | null | undefined;
  organization?: OrganizationFilterInput | null | undefined;
  organizationId?: IntOperationFilterInput | null | undefined;
  unionId?: IntOperationFilterInput | null | undefined;
};
export type DelectronicPaymentOptionFilterInput = {
  and?: ReadonlyArray<DelectronicPaymentOptionFilterInput> | null | undefined;
  electronicPaymentConfigurations?: ListFilterInputTypeOfElectronicPaymentConfigurationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  name?: StringOperationFilterInput | null | undefined;
  or?: ReadonlyArray<DelectronicPaymentOptionFilterInput> | null | undefined;
};
export type RootFilterInput = {
  agreement?: AgreementFilterInput | null | undefined;
  and?: ReadonlyArray<RootFilterInput> | null | undefined;
  benefit?: BenefitFilterInput | null | undefined;
  benefitOverridesOrig?: BenefitOverridesOrigFilterInput | null | undefined;
  classificationName?: ClassificationNameFilterInput | null | undefined;
  contactMechanism?: ContactMechanismFilterInput | null | undefined;
  createdBy?: StringOperationFilterInput | null | undefined;
  creationDate?: DateTimeOperationFilterInput | null | undefined;
  electronicBatch?: ElectronicBatchFilterInput | null | undefined;
  electronicPayment?: ElectronicPaymentFilterInput | null | undefined;
  electronicPaymentConfiguration?: ElectronicPaymentConfigurationFilterInput | null | undefined;
  employersToAgreement?: EmployersToAgreementFilterInput | null | undefined;
  guid?: UuidOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  image?: ImageFilterInput | null | undefined;
  lastModificationDate?: DateTimeOperationFilterInput | null | undefined;
  lastModifiedBy?: StringOperationFilterInput | null | undefined;
  noteIdNavigations?: ListFilterInputTypeOfNoteFilterInput | null | undefined;
  noteRoots?: ListFilterInputTypeOfNoteFilterInput | null | undefined;
  or?: ReadonlyArray<RootFilterInput> | null | undefined;
  partiesToContactMechanism?: PartiesToContactMechanismFilterInput | null | undefined;
  party?: PartyFilterInput | null | undefined;
  payment?: PaymentFilterInput | null | undefined;
  paymentMethods?: ListFilterInputTypeOfPaymentMethodFilterInput | null | undefined;
  rateSchedule?: RateScheduleFilterInput | null | undefined;
  relationship?: RelationshipFilterInput | null | undefined;
  relationshipStatus?: RelationshipStatusFilterInput | null | undefined;
  report?: ReportFilterInput | null | undefined;
  reportLineItem?: ReportLineItemFilterInput | null | undefined;
  reportSuppression?: ReportSuppressionFilterInput | null | undefined;
  reportedBenefitReleaseAuthorization?: ReportedBenefitReleaseAuthorizationFilterInput | null | undefined;
  subClassification?: SubClassificationFilterInput | null | undefined;
  timelinesOrig?: TimelinesOrigFilterInput | null | undefined;
};
export type BenefitOverridesOrigFilterInput = {
  and?: ReadonlyArray<BenefitOverridesOrigFilterInput> | null | undefined;
  benefitId?: IntOperationFilterInput | null | undefined;
  chapterId?: IntOperationFilterInput | null | undefined;
  election?: BooleanOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  idNavigation?: RootFilterInput | null | undefined;
  or?: ReadonlyArray<BenefitOverridesOrigFilterInput> | null | undefined;
  party?: PartyFilterInput | null | undefined;
  partyId?: IntOperationFilterInput | null | undefined;
  value?: DecimalOperationFilterInput | null | undefined;
};
export type PartyFilterInput = {
  and?: ReadonlyArray<PartyFilterInput> | null | undefined;
  benefitOverridesOrigs?: ListFilterInputTypeOfBenefitOverridesOrigFilterInput | null | undefined;
  dpartyType?: DpartyTypeFilterInput | null | undefined;
  dpartyTypeId?: IntOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  idNavigation?: RootFilterInput | null | undefined;
  or?: ReadonlyArray<PartyFilterInput> | null | undefined;
  organization?: OrganizationFilterInput | null | undefined;
  partiesToContactMechanisms?: ListFilterInputTypeOfPartiesToContactMechanismFilterInput | null | undefined;
  person?: PersonFilterInput | null | undefined;
  relationshipLeftParties?: ListFilterInputTypeOfRelationshipFilterInput | null | undefined;
  relationshipRightParties?: ListFilterInputTypeOfRelationshipFilterInput | null | undefined;
};
export type ListFilterInputTypeOfBenefitOverridesOrigFilterInput = {
  all?: BenefitOverridesOrigFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: BenefitOverridesOrigFilterInput | null | undefined;
  some?: BenefitOverridesOrigFilterInput | null | undefined;
};
export type DpartyTypeFilterInput = {
  and?: ReadonlyArray<DpartyTypeFilterInput> | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  name?: StringOperationFilterInput | null | undefined;
  or?: ReadonlyArray<DpartyTypeFilterInput> | null | undefined;
  parties?: ListFilterInputTypeOfPartyFilterInput | null | undefined;
};
export type ListFilterInputTypeOfPartyFilterInput = {
  all?: PartyFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: PartyFilterInput | null | undefined;
  some?: PartyFilterInput | null | undefined;
};
export type ListFilterInputTypeOfPartiesToContactMechanismFilterInput = {
  all?: PartiesToContactMechanismFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: PartiesToContactMechanismFilterInput | null | undefined;
  some?: PartiesToContactMechanismFilterInput | null | undefined;
};
export type PartiesToContactMechanismFilterInput = {
  and?: ReadonlyArray<PartiesToContactMechanismFilterInput> | null | undefined;
  contactMechanism?: ContactMechanismFilterInput | null | undefined;
  contactMechanismId?: IntOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  idNavigation?: RootFilterInput | null | undefined;
  or?: ReadonlyArray<PartiesToContactMechanismFilterInput> | null | undefined;
  party?: PartyFilterInput | null | undefined;
  partyId?: IntOperationFilterInput | null | undefined;
};
export type ContactMechanismFilterInput = {
  address?: AddressFilterInput | null | undefined;
  and?: ReadonlyArray<ContactMechanismFilterInput> | null | undefined;
  dcontactMechanismType?: DcontactMechanismTypeFilterInput | null | undefined;
  dcontactMechanismTypeId?: IntOperationFilterInput | null | undefined;
  emailAddress?: EmailAddressFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  idNavigation?: RootFilterInput | null | undefined;
  or?: ReadonlyArray<ContactMechanismFilterInput> | null | undefined;
  partiesToContactMechanisms?: ListFilterInputTypeOfPartiesToContactMechanismFilterInput | null | undefined;
  phoneNumber?: PhoneNumberFilterInput | null | undefined;
  website?: WebsiteFilterInput | null | undefined;
};
export type AddressFilterInput = {
  addressLines?: StringOperationFilterInput | null | undefined;
  and?: ReadonlyArray<AddressFilterInput> | null | undefined;
  city?: StringOperationFilterInput | null | undefined;
  county?: StringOperationFilterInput | null | undefined;
  daddressType?: DaddressTypeFilterInput | null | undefined;
  daddressTypeId?: IntOperationFilterInput | null | undefined;
  dcountry?: DcountryFilterInput | null | undefined;
  dcountryId?: UuidOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  idNavigation?: ContactMechanismFilterInput | null | undefined;
  or?: ReadonlyArray<AddressFilterInput> | null | undefined;
  postalCode?: StringOperationFilterInput | null | undefined;
  province?: StringOperationFilterInput | null | undefined;
};
export type DaddressTypeFilterInput = {
  addresses?: ListFilterInputTypeOfAddressFilterInput | null | undefined;
  and?: ReadonlyArray<DaddressTypeFilterInput> | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  name?: StringOperationFilterInput | null | undefined;
  or?: ReadonlyArray<DaddressTypeFilterInput> | null | undefined;
};
export type ListFilterInputTypeOfAddressFilterInput = {
  all?: AddressFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: AddressFilterInput | null | undefined;
  some?: AddressFilterInput | null | undefined;
};
export type DcountryFilterInput = {
  addresses?: ListFilterInputTypeOfAddressFilterInput | null | undefined;
  and?: ReadonlyArray<DcountryFilterInput> | null | undefined;
  id?: UuidOperationFilterInput | null | undefined;
  name?: StringOperationFilterInput | null | undefined;
  numericCode?: StringOperationFilterInput | null | undefined;
  or?: ReadonlyArray<DcountryFilterInput> | null | undefined;
  threeLetterCode?: StringOperationFilterInput | null | undefined;
  twoLetterCode?: StringOperationFilterInput | null | undefined;
};
export type DcontactMechanismTypeFilterInput = {
  and?: ReadonlyArray<DcontactMechanismTypeFilterInput> | null | undefined;
  contactMechanisms?: ListFilterInputTypeOfContactMechanismFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  name?: StringOperationFilterInput | null | undefined;
  or?: ReadonlyArray<DcontactMechanismTypeFilterInput> | null | undefined;
};
export type ListFilterInputTypeOfContactMechanismFilterInput = {
  all?: ContactMechanismFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: ContactMechanismFilterInput | null | undefined;
  some?: ContactMechanismFilterInput | null | undefined;
};
export type EmailAddressFilterInput = {
  and?: ReadonlyArray<EmailAddressFilterInput> | null | undefined;
  demailAddressType?: DemailAddressTypeFilterInput | null | undefined;
  demailAddressTypeId?: IntOperationFilterInput | null | undefined;
  emailAddress1?: StringOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  idNavigation?: ContactMechanismFilterInput | null | undefined;
  or?: ReadonlyArray<EmailAddressFilterInput> | null | undefined;
};
export type DemailAddressTypeFilterInput = {
  and?: ReadonlyArray<DemailAddressTypeFilterInput> | null | undefined;
  emailAddresses?: ListFilterInputTypeOfEmailAddressFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  name?: StringOperationFilterInput | null | undefined;
  or?: ReadonlyArray<DemailAddressTypeFilterInput> | null | undefined;
};
export type ListFilterInputTypeOfEmailAddressFilterInput = {
  all?: EmailAddressFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: EmailAddressFilterInput | null | undefined;
  some?: EmailAddressFilterInput | null | undefined;
};
export type PhoneNumberFilterInput = {
  and?: ReadonlyArray<PhoneNumberFilterInput> | null | undefined;
  dphoneNumberType?: DphoneNumberTypeFilterInput | null | undefined;
  dphoneNumberTypeId?: IntOperationFilterInput | null | undefined;
  extension?: StringOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  idNavigation?: ContactMechanismFilterInput | null | undefined;
  or?: ReadonlyArray<PhoneNumberFilterInput> | null | undefined;
  phoneNumber1?: StringOperationFilterInput | null | undefined;
};
export type DphoneNumberTypeFilterInput = {
  and?: ReadonlyArray<DphoneNumberTypeFilterInput> | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  name?: StringOperationFilterInput | null | undefined;
  or?: ReadonlyArray<DphoneNumberTypeFilterInput> | null | undefined;
  phoneNumbers?: ListFilterInputTypeOfPhoneNumberFilterInput | null | undefined;
};
export type ListFilterInputTypeOfPhoneNumberFilterInput = {
  all?: PhoneNumberFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: PhoneNumberFilterInput | null | undefined;
  some?: PhoneNumberFilterInput | null | undefined;
};
export type WebsiteFilterInput = {
  and?: ReadonlyArray<WebsiteFilterInput> | null | undefined;
  dwebsiteType?: DwebsiteTypeFilterInput | null | undefined;
  dwebsiteTypeId?: IntOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  idNavigation?: ContactMechanismFilterInput | null | undefined;
  or?: ReadonlyArray<WebsiteFilterInput> | null | undefined;
  url?: StringOperationFilterInput | null | undefined;
};
export type DwebsiteTypeFilterInput = {
  and?: ReadonlyArray<DwebsiteTypeFilterInput> | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  name?: StringOperationFilterInput | null | undefined;
  or?: ReadonlyArray<DwebsiteTypeFilterInput> | null | undefined;
  websites?: ListFilterInputTypeOfWebsiteFilterInput | null | undefined;
};
export type ListFilterInputTypeOfWebsiteFilterInput = {
  all?: WebsiteFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: WebsiteFilterInput | null | undefined;
  some?: WebsiteFilterInput | null | undefined;
};
export type PersonFilterInput = {
  and?: ReadonlyArray<PersonFilterInput> | null | undefined;
  dgender?: DgenderFilterInput | null | undefined;
  dgenderId?: IntOperationFilterInput | null | undefined;
  dpersonType?: DpersonTypeFilterInput | null | undefined;
  dpersonTypeId?: IntOperationFilterInput | null | undefined;
  employee?: EmployeeFilterInput | null | undefined;
  firstName?: StringOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  idNavigation?: PartyFilterInput | null | undefined;
  lastName?: StringOperationFilterInput | null | undefined;
  middleName?: StringOperationFilterInput | null | undefined;
  or?: ReadonlyArray<PersonFilterInput> | null | undefined;
  suffix?: StringOperationFilterInput | null | undefined;
  title?: StringOperationFilterInput | null | undefined;
};
export type DgenderFilterInput = {
  and?: ReadonlyArray<DgenderFilterInput> | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  name?: StringOperationFilterInput | null | undefined;
  or?: ReadonlyArray<DgenderFilterInput> | null | undefined;
  people?: ListFilterInputTypeOfPersonFilterInput | null | undefined;
};
export type ListFilterInputTypeOfPersonFilterInput = {
  all?: PersonFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: PersonFilterInput | null | undefined;
  some?: PersonFilterInput | null | undefined;
};
export type DpersonTypeFilterInput = {
  and?: ReadonlyArray<DpersonTypeFilterInput> | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  name?: StringOperationFilterInput | null | undefined;
  or?: ReadonlyArray<DpersonTypeFilterInput> | null | undefined;
  people?: ListFilterInputTypeOfPersonFilterInput | null | undefined;
};
export type EmployeeFilterInput = {
  active?: BooleanOperationFilterInput | null | undefined;
  agreements?: ListFilterInputTypeOfAgreementFilterInput | null | undefined;
  and?: ReadonlyArray<EmployeeFilterInput> | null | undefined;
  birthDate?: DateTimeOperationFilterInput | null | undefined;
  dateOfHire?: DateTimeOperationFilterInput | null | undefined;
  dateOfTermination?: DateTimeOperationFilterInput | null | undefined;
  externalEmployeeId?: StringOperationFilterInput | null | undefined;
  firstName?: StringOperationFilterInput | null | undefined;
  homeLocalId?: IntOperationFilterInput | null | undefined;
  id?: IdOperationFilterInput | null | undefined;
  idNavigation?: PersonFilterInput | null | undefined;
  lastName?: StringOperationFilterInput | null | undefined;
  middleName?: StringOperationFilterInput | null | undefined;
  or?: ReadonlyArray<EmployeeFilterInput> | null | undefined;
  reportLineItems?: ListFilterInputTypeOfReportLineItemFilterInput | null | undefined;
  searchSsn?: ListByteOperationFilterInput | null | undefined;
  ssn?: ListByteOperationFilterInput | null | undefined;
  suffix?: StringOperationFilterInput | null | undefined;
};
export type ListByteOperationFilterInput = {
  all?: ByteOperationFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: ByteOperationFilterInput | null | undefined;
  some?: ByteOperationFilterInput | null | undefined;
};
export type ByteOperationFilterInput = {
  eq?: any | null | undefined;
  gt?: any | null | undefined;
  gte?: any | null | undefined;
  in?: ReadonlyArray<any | null | undefined> | null | undefined;
  lt?: any | null | undefined;
  lte?: any | null | undefined;
  neq?: any | null | undefined;
  ngt?: any | null | undefined;
  ngte?: any | null | undefined;
  nin?: ReadonlyArray<any | null | undefined> | null | undefined;
  nlt?: any | null | undefined;
  nlte?: any | null | undefined;
};
export type ListFilterInputTypeOfReportLineItemFilterInput = {
  all?: ReportLineItemFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: ReportLineItemFilterInput | null | undefined;
  some?: ReportLineItemFilterInput | null | undefined;
};
export type ListFilterInputTypeOfRelationshipFilterInput = {
  all?: RelationshipFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: RelationshipFilterInput | null | undefined;
  some?: RelationshipFilterInput | null | undefined;
};
export type RelationshipFilterInput = {
  and?: ReadonlyArray<RelationshipFilterInput> | null | undefined;
  chapterToEmployerRelationship?: ChapterToEmployerRelationshipFilterInput | null | undefined;
  drelationship?: DrelationshipSubTypeFilterInput | null | undefined;
  drelationshipSubTypeId?: IntOperationFilterInput | null | undefined;
  drelationshipType?: DrelationshipTypeFilterInput | null | undefined;
  drelationshipTypeId?: IntOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  idNavigation?: RootFilterInput | null | undefined;
  leftParty?: PartyFilterInput | null | undefined;
  leftPartyId?: IntOperationFilterInput | null | undefined;
  or?: ReadonlyArray<RelationshipFilterInput> | null | undefined;
  relationshipStatuses?: ListFilterInputTypeOfRelationshipStatusFilterInput | null | undefined;
  rightParty?: PartyFilterInput | null | undefined;
  rightPartyId?: IntOperationFilterInput | null | undefined;
};
export type ChapterToEmployerRelationshipFilterInput = {
  and?: ReadonlyArray<ChapterToEmployerRelationshipFilterInput> | null | undefined;
  associationId?: StringOperationFilterInput | null | undefined;
  datePriviledgedAuthorized?: DateTimeOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  isAssociationMember?: BooleanOperationFilterInput | null | undefined;
  or?: ReadonlyArray<ChapterToEmployerRelationshipFilterInput> | null | undefined;
  priviledgedAuthorizedBy?: StringOperationFilterInput | null | undefined;
  relationship?: RelationshipFilterInput | null | undefined;
};
export type DrelationshipSubTypeFilterInput = {
  and?: ReadonlyArray<DrelationshipSubTypeFilterInput> | null | undefined;
  description?: StringOperationFilterInput | null | undefined;
  drelationshipTypeId?: IntOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  name?: StringOperationFilterInput | null | undefined;
  or?: ReadonlyArray<DrelationshipSubTypeFilterInput> | null | undefined;
  relationships?: ListFilterInputTypeOfRelationshipFilterInput | null | undefined;
};
export type DrelationshipTypeFilterInput = {
  and?: ReadonlyArray<DrelationshipTypeFilterInput> | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  isOneToOne?: BooleanOperationFilterInput | null | undefined;
  leftDentityTypeId?: IntOperationFilterInput | null | undefined;
  name?: StringOperationFilterInput | null | undefined;
  notes?: StringOperationFilterInput | null | undefined;
  or?: ReadonlyArray<DrelationshipTypeFilterInput> | null | undefined;
  relationships?: ListFilterInputTypeOfRelationshipFilterInput | null | undefined;
  rightDentityTypeId?: IntOperationFilterInput | null | undefined;
};
export type ListFilterInputTypeOfRelationshipStatusFilterInput = {
  all?: RelationshipStatusFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: RelationshipStatusFilterInput | null | undefined;
  some?: RelationshipStatusFilterInput | null | undefined;
};
export type RelationshipStatusFilterInput = {
  and?: ReadonlyArray<RelationshipStatusFilterInput> | null | undefined;
  drelationshipStatus?: DrelationshipStatusFilterInput | null | undefined;
  drelationshipStatusId?: IntOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  idNavigation?: RootFilterInput | null | undefined;
  or?: ReadonlyArray<RelationshipStatusFilterInput> | null | undefined;
  relationship?: RelationshipFilterInput | null | undefined;
  relationshipId?: IntOperationFilterInput | null | undefined;
};
export type DrelationshipStatusFilterInput = {
  and?: ReadonlyArray<DrelationshipStatusFilterInput> | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  name?: StringOperationFilterInput | null | undefined;
  notes?: StringOperationFilterInput | null | undefined;
  or?: ReadonlyArray<DrelationshipStatusFilterInput> | null | undefined;
  relationshipStatuses?: ListFilterInputTypeOfRelationshipStatusFilterInput | null | undefined;
};
export type ElectronicBatchFilterInput = {
  and?: ReadonlyArray<ElectronicBatchFilterInput> | null | undefined;
  batchDate?: DateTimeOperationFilterInput | null | undefined;
  collectingAgentId?: IntOperationFilterInput | null | undefined;
  electronicPayments?: ListFilterInputTypeOfElectronicPaymentFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  idNavigation?: RootFilterInput | null | undefined;
  lastDownloaded?: DateTimeOperationFilterInput | null | undefined;
  or?: ReadonlyArray<ElectronicBatchFilterInput> | null | undefined;
};
export type ListFilterInputTypeOfElectronicPaymentFilterInput = {
  all?: ElectronicPaymentFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: ElectronicPaymentFilterInput | null | undefined;
  some?: ElectronicPaymentFilterInput | null | undefined;
};
export type ElectronicPaymentFilterInput = {
  amount?: DecimalOperationFilterInput | null | undefined;
  and?: ReadonlyArray<ElectronicPaymentFilterInput> | null | undefined;
  benefit?: BenefitFilterInput | null | undefined;
  benefitId?: IntOperationFilterInput | null | undefined;
  dpaymentMethodType?: DpaymentMethodTypeFilterInput | null | undefined;
  dpaymentMethodTypeId?: IntOperationFilterInput | null | undefined;
  eftpayment?: EftpaymentFilterInput | null | undefined;
  electronicBatch?: ElectronicBatchFilterInput | null | undefined;
  electronicBatchId?: IntOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  idNavigation?: RootFilterInput | null | undefined;
  or?: ReadonlyArray<ElectronicPaymentFilterInput> | null | undefined;
  payments?: PaymentFilterInput | null | undefined;
  paymentsId?: IntOperationFilterInput | null | undefined;
  report?: ReportFilterInput | null | undefined;
  reportId?: IntOperationFilterInput | null | undefined;
  suppress?: BooleanOperationFilterInput | null | undefined;
};
export type DpaymentMethodTypeFilterInput = {
  and?: ReadonlyArray<DpaymentMethodTypeFilterInput> | null | undefined;
  electronicPayments?: ListFilterInputTypeOfElectronicPaymentFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  name?: StringOperationFilterInput | null | undefined;
  or?: ReadonlyArray<DpaymentMethodTypeFilterInput> | null | undefined;
  paymentMethods?: ListFilterInputTypeOfPaymentMethodFilterInput | null | undefined;
};
export type ListFilterInputTypeOfPaymentMethodFilterInput = {
  all?: PaymentMethodFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: PaymentMethodFilterInput | null | undefined;
  some?: PaymentMethodFilterInput | null | undefined;
};
export type PaymentMethodFilterInput = {
  and?: ReadonlyArray<PaymentMethodFilterInput> | null | undefined;
  associatedGuid?: UuidOperationFilterInput | null | undefined;
  creditCardPaymentMethod?: CreditCardPaymentMethodFilterInput | null | undefined;
  dpaymentMethodType?: DpaymentMethodTypeFilterInput | null | undefined;
  dpaymentMethodTypeId?: IntOperationFilterInput | null | undefined;
  eftpaymentMethod?: EftpaymentMethodFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  idNavigation?: RootFilterInput | null | undefined;
  or?: ReadonlyArray<PaymentMethodFilterInput> | null | undefined;
};
export type CreditCardPaymentMethodFilterInput = {
  accountNumber?: StringOperationFilterInput | null | undefined;
  and?: ReadonlyArray<CreditCardPaymentMethodFilterInput> | null | undefined;
  associated?: PaymentMethodFilterInput | null | undefined;
  associatedGuid?: UuidOperationFilterInput | null | undefined;
  expiration?: DateTimeOperationFilterInput | null | undefined;
  name?: StringOperationFilterInput | null | undefined;
  or?: ReadonlyArray<CreditCardPaymentMethodFilterInput> | null | undefined;
  securityCode?: StringOperationFilterInput | null | undefined;
};
export type EftpaymentMethodFilterInput = {
  accountNumber?: StringOperationFilterInput | null | undefined;
  and?: ReadonlyArray<EftpaymentMethodFilterInput> | null | undefined;
  associated?: PaymentMethodFilterInput | null | undefined;
  associatedGuid?: UuidOperationFilterInput | null | undefined;
  or?: ReadonlyArray<EftpaymentMethodFilterInput> | null | undefined;
  routingNumber?: StringOperationFilterInput | null | undefined;
};
export type EftpaymentFilterInput = {
  accountNumber?: StringOperationFilterInput | null | undefined;
  and?: ReadonlyArray<EftpaymentFilterInput> | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  idNavigation?: ElectronicPaymentFilterInput | null | undefined;
  or?: ReadonlyArray<EftpaymentFilterInput> | null | undefined;
  routingNumber?: StringOperationFilterInput | null | undefined;
};
export type PaymentFilterInput = {
  amount?: DecimalOperationFilterInput | null | undefined;
  and?: ReadonlyArray<PaymentFilterInput> | null | undefined;
  collectingAgentId?: IntOperationFilterInput | null | undefined;
  electronicPayments?: ListFilterInputTypeOfElectronicPaymentFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  idNavigation?: RootFilterInput | null | undefined;
  or?: ReadonlyArray<PaymentFilterInput> | null | undefined;
  paymentDate?: DateTimeOperationFilterInput | null | undefined;
  paymentDetails?: ListFilterInputTypeOfPaymentDetailFilterInput | null | undefined;
  paymentNumber?: StringOperationFilterInput | null | undefined;
  report?: ReportFilterInput | null | undefined;
  reportId?: IntOperationFilterInput | null | undefined;
};
export type ListFilterInputTypeOfPaymentDetailFilterInput = {
  all?: PaymentDetailFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: PaymentDetailFilterInput | null | undefined;
  some?: PaymentDetailFilterInput | null | undefined;
};
export type PaymentDetailFilterInput = {
  amount?: DecimalOperationFilterInput | null | undefined;
  and?: ReadonlyArray<PaymentDetailFilterInput> | null | undefined;
  benefit?: BenefitFilterInput | null | undefined;
  benefitId?: IntOperationFilterInput | null | undefined;
  or?: ReadonlyArray<PaymentDetailFilterInput> | null | undefined;
  payment?: PaymentFilterInput | null | undefined;
  paymentId?: IntOperationFilterInput | null | undefined;
};
export type ReportFilterInput = {
  aggregateEmployeeCount?: IntOperationFilterInput | null | undefined;
  aggregatesOnly?: BooleanOperationFilterInput | null | undefined;
  agreement?: AgreementFilterInput | null | undefined;
  agreementId?: IntOperationFilterInput | null | undefined;
  amendedReport?: ReportFilterInput | null | undefined;
  amendedReportId?: IntOperationFilterInput | null | undefined;
  and?: ReadonlyArray<ReportFilterInput> | null | undefined;
  dreportStatus?: DreportStatusFilterInput | null | undefined;
  dreportStatusId?: IntOperationFilterInput | null | undefined;
  electronicPayments?: ListFilterInputTypeOfElectronicPaymentFilterInput | null | undefined;
  employer?: EmployerFilterInput | null | undefined;
  employerId?: IntOperationFilterInput | null | undefined;
  fullyPaid?: BooleanOperationFilterInput | null | undefined;
  fundingComments?: ListFilterInputTypeOfFundingCommentFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  idNavigation?: RootFilterInput | null | undefined;
  inverseAmendedReport?: ListFilterInputTypeOfReportFilterInput | null | undefined;
  notes?: StringOperationFilterInput | null | undefined;
  or?: ReadonlyArray<ReportFilterInput> | null | undefined;
  payments?: ListFilterInputTypeOfPaymentFilterInput | null | undefined;
  periodEndDate?: DateTimeOperationFilterInput | null | undefined;
  periodStartDate?: DateTimeOperationFilterInput | null | undefined;
  rateSchedule?: RateScheduleFilterInput | null | undefined;
  rateScheduleId?: IntOperationFilterInput | null | undefined;
  reportLineItems?: ListFilterInputTypeOfReportLineItemFilterInput | null | undefined;
  reportSuppressions?: ListFilterInputTypeOfReportSuppressionFilterInput | null | undefined;
  reportedBenefitReleaseAuthorizations?: ListFilterInputTypeOfReportedBenefitReleaseAuthorizationFilterInput | null | undefined;
  submissionDate?: DateTimeOperationFilterInput | null | undefined;
  workMonth?: DateTimeOperationFilterInput | null | undefined;
  zeroHour?: BooleanOperationFilterInput | null | undefined;
};
export type DreportStatusFilterInput = {
  and?: ReadonlyArray<DreportStatusFilterInput> | null | undefined;
  description?: StringOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  name?: StringOperationFilterInput | null | undefined;
  or?: ReadonlyArray<DreportStatusFilterInput> | null | undefined;
  reports?: ListFilterInputTypeOfReportFilterInput | null | undefined;
};
export type ListFilterInputTypeOfReportFilterInput = {
  all?: ReportFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: ReportFilterInput | null | undefined;
  some?: ReportFilterInput | null | undefined;
};
export type ListFilterInputTypeOfFundingCommentFilterInput = {
  all?: FundingCommentFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: FundingCommentFilterInput | null | undefined;
  some?: FundingCommentFilterInput | null | undefined;
};
export type FundingCommentFilterInput = {
  agreement?: AgreementFilterInput | null | undefined;
  agreementId?: IntOperationFilterInput | null | undefined;
  and?: ReadonlyArray<FundingCommentFilterInput> | null | undefined;
  benefit?: BenefitFilterInput | null | undefined;
  benefitId?: IntOperationFilterInput | null | undefined;
  createdBy?: StringOperationFilterInput | null | undefined;
  creationDate?: DateTimeOperationFilterInput | null | undefined;
  employer?: EmployerFilterInput | null | undefined;
  employerId?: IntOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  lastModifiedBy?: StringOperationFilterInput | null | undefined;
  lastModifiedDate?: DateTimeOperationFilterInput | null | undefined;
  or?: ReadonlyArray<FundingCommentFilterInput> | null | undefined;
  report?: ReportFilterInput | null | undefined;
  reportId?: IntOperationFilterInput | null | undefined;
  workMonth?: DateTimeOperationFilterInput | null | undefined;
};
export type ListFilterInputTypeOfPaymentFilterInput = {
  all?: PaymentFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: PaymentFilterInput | null | undefined;
  some?: PaymentFilterInput | null | undefined;
};
export type RateScheduleFilterInput = {
  agreement?: AgreementFilterInput | null | undefined;
  agreementId?: IntOperationFilterInput | null | undefined;
  and?: ReadonlyArray<RateScheduleFilterInput> | null | undefined;
  effectiveEndDate?: DateTimeOperationFilterInput | null | undefined;
  effectiveStartDate?: DateTimeOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  idNavigation?: RootFilterInput | null | undefined;
  or?: ReadonlyArray<RateScheduleFilterInput> | null | undefined;
  reports?: ListFilterInputTypeOfReportFilterInput | null | undefined;
};
export type ListFilterInputTypeOfReportSuppressionFilterInput = {
  all?: ReportSuppressionFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: ReportSuppressionFilterInput | null | undefined;
  some?: ReportSuppressionFilterInput | null | undefined;
};
export type ReportSuppressionFilterInput = {
  and?: ReadonlyArray<ReportSuppressionFilterInput> | null | undefined;
  collectingAgentId?: IntOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  idNavigation?: RootFilterInput | null | undefined;
  or?: ReadonlyArray<ReportSuppressionFilterInput> | null | undefined;
  report?: ReportFilterInput | null | undefined;
  reportId?: IntOperationFilterInput | null | undefined;
  suppress?: BooleanOperationFilterInput | null | undefined;
};
export type ListFilterInputTypeOfReportedBenefitReleaseAuthorizationFilterInput = {
  all?: ReportedBenefitReleaseAuthorizationFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: ReportedBenefitReleaseAuthorizationFilterInput | null | undefined;
  some?: ReportedBenefitReleaseAuthorizationFilterInput | null | undefined;
};
export type ReportedBenefitReleaseAuthorizationFilterInput = {
  and?: ReadonlyArray<ReportedBenefitReleaseAuthorizationFilterInput> | null | undefined;
  benefit?: BenefitFilterInput | null | undefined;
  benefitId?: IntOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  idNavigation?: RootFilterInput | null | undefined;
  or?: ReadonlyArray<ReportedBenefitReleaseAuthorizationFilterInput> | null | undefined;
  report?: ReportFilterInput | null | undefined;
  reportId?: IntOperationFilterInput | null | undefined;
};
export type EmployersToAgreementFilterInput = {
  agreement?: AgreementFilterInput | null | undefined;
  agreementId?: IntOperationFilterInput | null | undefined;
  and?: ReadonlyArray<EmployersToAgreementFilterInput> | null | undefined;
  employer?: EmployerFilterInput | null | undefined;
  employerId?: IntOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  idNavigation?: RootFilterInput | null | undefined;
  or?: ReadonlyArray<EmployersToAgreementFilterInput> | null | undefined;
};
export type ImageFilterInput = {
  active?: BooleanOperationFilterInput | null | undefined;
  and?: ReadonlyArray<ImageFilterInput> | null | undefined;
  eprusers?: ListFilterInputTypeOfEpruserFilterInput | null | undefined;
  fileName?: StringOperationFilterInput | null | undefined;
  filePath?: StringOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  idNavigation?: RootFilterInput | null | undefined;
  or?: ReadonlyArray<ImageFilterInput> | null | undefined;
};
export type ListFilterInputTypeOfEpruserFilterInput = {
  all?: EpruserFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: EpruserFilterInput | null | undefined;
  some?: EpruserFilterInput | null | undefined;
};
export type EpruserFilterInput = {
  and?: ReadonlyArray<EpruserFilterInput> | null | undefined;
  aspnetUser?: AspnetUserFilterInput | null | undefined;
  aspnetUserId?: UuidOperationFilterInput | null | undefined;
  bannerImage?: ImageFilterInput | null | undefined;
  bannerImageId?: IntOperationFilterInput | null | undefined;
  currentSessionId?: StringOperationFilterInput | null | undefined;
  firstName?: StringOperationFilterInput | null | undefined;
  lastName?: StringOperationFilterInput | null | undefined;
  or?: ReadonlyArray<EpruserFilterInput> | null | undefined;
  organization?: OrganizationFilterInput | null | undefined;
  organizationId?: IntOperationFilterInput | null | undefined;
  phoneNumber?: StringOperationFilterInput | null | undefined;
  phoneNumberExtension?: StringOperationFilterInput | null | undefined;
  phoneTypeId?: IntOperationFilterInput | null | undefined;
  preferredMfamethod?: StringOperationFilterInput | null | undefined;
};
export type AspnetUserFilterInput = {
  and?: ReadonlyArray<AspnetUserFilterInput> | null | undefined;
  application?: AspnetApplicationFilterInput | null | undefined;
  applicationId?: UuidOperationFilterInput | null | undefined;
  aspnetMembership?: AspnetMembershipFilterInput | null | undefined;
  aspnetPersonalizationPerUsers?: ListFilterInputTypeOfAspnetPersonalizationPerUserFilterInput | null | undefined;
  aspnetProfile?: AspnetProfileFilterInput | null | undefined;
  epruser?: EpruserFilterInput | null | undefined;
  isAnonymous?: BooleanOperationFilterInput | null | undefined;
  kbaanswers?: ListFilterInputTypeOfKbaanswerFilterInput | null | undefined;
  lastActivityDate?: DateTimeOperationFilterInput | null | undefined;
  loweredUserName?: StringOperationFilterInput | null | undefined;
  mobileAlias?: StringOperationFilterInput | null | undefined;
  or?: ReadonlyArray<AspnetUserFilterInput> | null | undefined;
  roleGroups?: ListFilterInputTypeOfRoleGroupFilterInput | null | undefined;
  roles?: ListFilterInputTypeOfAspnetRoleFilterInput | null | undefined;
  userId?: UuidOperationFilterInput | null | undefined;
  userName?: StringOperationFilterInput | null | undefined;
};
export type AspnetApplicationFilterInput = {
  and?: ReadonlyArray<AspnetApplicationFilterInput> | null | undefined;
  applicationId?: UuidOperationFilterInput | null | undefined;
  applicationName?: StringOperationFilterInput | null | undefined;
  aspnetMemberships?: ListFilterInputTypeOfAspnetMembershipFilterInput | null | undefined;
  aspnetPaths?: ListFilterInputTypeOfAspnetPathFilterInput | null | undefined;
  aspnetRoles?: ListFilterInputTypeOfAspnetRoleFilterInput | null | undefined;
  aspnetUsers?: ListFilterInputTypeOfAspnetUserFilterInput | null | undefined;
  description?: StringOperationFilterInput | null | undefined;
  loweredApplicationName?: StringOperationFilterInput | null | undefined;
  or?: ReadonlyArray<AspnetApplicationFilterInput> | null | undefined;
};
export type ListFilterInputTypeOfAspnetMembershipFilterInput = {
  all?: AspnetMembershipFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: AspnetMembershipFilterInput | null | undefined;
  some?: AspnetMembershipFilterInput | null | undefined;
};
export type AspnetMembershipFilterInput = {
  and?: ReadonlyArray<AspnetMembershipFilterInput> | null | undefined;
  application?: AspnetApplicationFilterInput | null | undefined;
  applicationId?: UuidOperationFilterInput | null | undefined;
  comment?: StringOperationFilterInput | null | undefined;
  createDate?: DateTimeOperationFilterInput | null | undefined;
  email?: StringOperationFilterInput | null | undefined;
  failedPasswordAnswerAttemptCount?: IntOperationFilterInput | null | undefined;
  failedPasswordAnswerAttemptWindowStart?: DateTimeOperationFilterInput | null | undefined;
  failedPasswordAttemptCount?: IntOperationFilterInput | null | undefined;
  failedPasswordAttemptWindowStart?: DateTimeOperationFilterInput | null | undefined;
  isApproved?: BooleanOperationFilterInput | null | undefined;
  isLockedOut?: BooleanOperationFilterInput | null | undefined;
  lastLockoutDate?: DateTimeOperationFilterInput | null | undefined;
  lastLoginDate?: DateTimeOperationFilterInput | null | undefined;
  lastPasswordChangedDate?: DateTimeOperationFilterInput | null | undefined;
  loweredEmail?: StringOperationFilterInput | null | undefined;
  mobilePin?: StringOperationFilterInput | null | undefined;
  or?: ReadonlyArray<AspnetMembershipFilterInput> | null | undefined;
  password?: StringOperationFilterInput | null | undefined;
  passwordAnswer?: StringOperationFilterInput | null | undefined;
  passwordFormat?: IntOperationFilterInput | null | undefined;
  passwordQuestion?: StringOperationFilterInput | null | undefined;
  passwordSalt?: StringOperationFilterInput | null | undefined;
  user?: AspnetUserFilterInput | null | undefined;
  userId?: UuidOperationFilterInput | null | undefined;
};
export type ListFilterInputTypeOfAspnetPathFilterInput = {
  all?: AspnetPathFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: AspnetPathFilterInput | null | undefined;
  some?: AspnetPathFilterInput | null | undefined;
};
export type AspnetPathFilterInput = {
  and?: ReadonlyArray<AspnetPathFilterInput> | null | undefined;
  application?: AspnetApplicationFilterInput | null | undefined;
  applicationId?: UuidOperationFilterInput | null | undefined;
  aspnetPersonalizationAllUser?: AspnetPersonalizationAllUserFilterInput | null | undefined;
  aspnetPersonalizationPerUsers?: ListFilterInputTypeOfAspnetPersonalizationPerUserFilterInput | null | undefined;
  loweredPath?: StringOperationFilterInput | null | undefined;
  or?: ReadonlyArray<AspnetPathFilterInput> | null | undefined;
  path?: StringOperationFilterInput | null | undefined;
  pathId?: UuidOperationFilterInput | null | undefined;
};
export type AspnetPersonalizationAllUserFilterInput = {
  and?: ReadonlyArray<AspnetPersonalizationAllUserFilterInput> | null | undefined;
  lastUpdatedDate?: DateTimeOperationFilterInput | null | undefined;
  or?: ReadonlyArray<AspnetPersonalizationAllUserFilterInput> | null | undefined;
  pageSettings?: ListByteOperationFilterInput | null | undefined;
  path?: AspnetPathFilterInput | null | undefined;
  pathId?: UuidOperationFilterInput | null | undefined;
};
export type ListFilterInputTypeOfAspnetPersonalizationPerUserFilterInput = {
  all?: AspnetPersonalizationPerUserFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: AspnetPersonalizationPerUserFilterInput | null | undefined;
  some?: AspnetPersonalizationPerUserFilterInput | null | undefined;
};
export type AspnetPersonalizationPerUserFilterInput = {
  and?: ReadonlyArray<AspnetPersonalizationPerUserFilterInput> | null | undefined;
  id?: UuidOperationFilterInput | null | undefined;
  lastUpdatedDate?: DateTimeOperationFilterInput | null | undefined;
  or?: ReadonlyArray<AspnetPersonalizationPerUserFilterInput> | null | undefined;
  pageSettings?: ListByteOperationFilterInput | null | undefined;
  path?: AspnetPathFilterInput | null | undefined;
  pathId?: UuidOperationFilterInput | null | undefined;
  user?: AspnetUserFilterInput | null | undefined;
  userId?: UuidOperationFilterInput | null | undefined;
};
export type ListFilterInputTypeOfAspnetRoleFilterInput = {
  all?: AspnetRoleFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: AspnetRoleFilterInput | null | undefined;
  some?: AspnetRoleFilterInput | null | undefined;
};
export type AspnetRoleFilterInput = {
  and?: ReadonlyArray<AspnetRoleFilterInput> | null | undefined;
  application?: AspnetApplicationFilterInput | null | undefined;
  applicationId?: UuidOperationFilterInput | null | undefined;
  description?: StringOperationFilterInput | null | undefined;
  loweredRoleName?: StringOperationFilterInput | null | undefined;
  or?: ReadonlyArray<AspnetRoleFilterInput> | null | undefined;
  roleGroups?: ListFilterInputTypeOfRoleGroupFilterInput | null | undefined;
  roleId?: UuidOperationFilterInput | null | undefined;
  roleName?: StringOperationFilterInput | null | undefined;
  users?: ListFilterInputTypeOfAspnetUserFilterInput | null | undefined;
};
export type ListFilterInputTypeOfRoleGroupFilterInput = {
  all?: RoleGroupFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: RoleGroupFilterInput | null | undefined;
  some?: RoleGroupFilterInput | null | undefined;
};
export type RoleGroupFilterInput = {
  and?: ReadonlyArray<RoleGroupFilterInput> | null | undefined;
  aspnetRoles?: ListFilterInputTypeOfAspnetRoleFilterInput | null | undefined;
  aspnetUsers?: ListFilterInputTypeOfAspnetUserFilterInput | null | undefined;
  description?: StringOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  name?: StringOperationFilterInput | null | undefined;
  newsItems?: ListFilterInputTypeOfNewsItemFilterInput | null | undefined;
  or?: ReadonlyArray<RoleGroupFilterInput> | null | undefined;
};
export type ListFilterInputTypeOfNewsItemFilterInput = {
  all?: NewsItemFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: NewsItemFilterInput | null | undefined;
  some?: NewsItemFilterInput | null | undefined;
};
export type NewsItemFilterInput = {
  active?: BooleanOperationFilterInput | null | undefined;
  and?: ReadonlyArray<NewsItemFilterInput> | null | undefined;
  createdBy?: StringOperationFilterInput | null | undefined;
  creationDate?: DateTimeOperationFilterInput | null | undefined;
  description?: StringOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  imageUrl?: StringOperationFilterInput | null | undefined;
  lastModifiedBy?: StringOperationFilterInput | null | undefined;
  lastModifiedDate?: DateTimeOperationFilterInput | null | undefined;
  navigationUrl?: StringOperationFilterInput | null | undefined;
  or?: ReadonlyArray<NewsItemFilterInput> | null | undefined;
  publishDate?: DateTimeOperationFilterInput | null | undefined;
  roleGroup?: IntOperationFilterInput | null | undefined;
  roleGroupNavigation?: RoleGroupFilterInput | null | undefined;
  title?: StringOperationFilterInput | null | undefined;
};
export type ListFilterInputTypeOfAspnetUserFilterInput = {
  all?: AspnetUserFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: AspnetUserFilterInput | null | undefined;
  some?: AspnetUserFilterInput | null | undefined;
};
export type AspnetProfileFilterInput = {
  and?: ReadonlyArray<AspnetProfileFilterInput> | null | undefined;
  lastUpdatedDate?: DateTimeOperationFilterInput | null | undefined;
  or?: ReadonlyArray<AspnetProfileFilterInput> | null | undefined;
  propertyNames?: StringOperationFilterInput | null | undefined;
  propertyValuesBinary?: ListByteOperationFilterInput | null | undefined;
  propertyValuesString?: StringOperationFilterInput | null | undefined;
  user?: AspnetUserFilterInput | null | undefined;
  userId?: UuidOperationFilterInput | null | undefined;
};
export type ListFilterInputTypeOfKbaanswerFilterInput = {
  all?: KbaanswerFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: KbaanswerFilterInput | null | undefined;
  some?: KbaanswerFilterInput | null | undefined;
};
export type KbaanswerFilterInput = {
  and?: ReadonlyArray<KbaanswerFilterInput> | null | undefined;
  answer?: StringOperationFilterInput | null | undefined;
  category?: IntOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  or?: ReadonlyArray<KbaanswerFilterInput> | null | undefined;
  question?: KbaquestionFilterInput | null | undefined;
  questionId?: IntOperationFilterInput | null | undefined;
  user?: AspnetUserFilterInput | null | undefined;
  userGuid?: UuidOperationFilterInput | null | undefined;
};
export type KbaquestionFilterInput = {
  and?: ReadonlyArray<KbaquestionFilterInput> | null | undefined;
  category?: IntOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  kbaanswers?: ListFilterInputTypeOfKbaanswerFilterInput | null | undefined;
  or?: ReadonlyArray<KbaquestionFilterInput> | null | undefined;
  question?: StringOperationFilterInput | null | undefined;
};
export type ListFilterInputTypeOfNoteFilterInput = {
  all?: NoteFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: NoteFilterInput | null | undefined;
  some?: NoteFilterInput | null | undefined;
};
export type NoteFilterInput = {
  and?: ReadonlyArray<NoteFilterInput> | null | undefined;
  body?: StringOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  idNavigation?: RootFilterInput | null | undefined;
  or?: ReadonlyArray<NoteFilterInput> | null | undefined;
  root?: RootFilterInput | null | undefined;
  rootId?: IntOperationFilterInput | null | undefined;
  subject?: StringOperationFilterInput | null | undefined;
};
export type SubClassificationFilterInput = {
  and?: ReadonlyArray<SubClassificationFilterInput> | null | undefined;
  chapter?: ChapterFilterInput | null | undefined;
  chapterId?: IntOperationFilterInput | null | undefined;
  description?: StringOperationFilterInput | null | undefined;
  dstatus?: DstatusFilterInput | null | undefined;
  dstatusId?: IntOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  idNavigation?: RootFilterInput | null | undefined;
  name?: StringOperationFilterInput | null | undefined;
  or?: ReadonlyArray<SubClassificationFilterInput> | null | undefined;
  reportLineItems?: ListFilterInputTypeOfReportLineItemFilterInput | null | undefined;
};
export type DstatusFilterInput = {
  and?: ReadonlyArray<DstatusFilterInput> | null | undefined;
  classificationNames?: ListFilterInputTypeOfClassificationNameFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  name?: StringOperationFilterInput | null | undefined;
  notes?: StringOperationFilterInput | null | undefined;
  or?: ReadonlyArray<DstatusFilterInput> | null | undefined;
  subClassifications?: ListFilterInputTypeOfSubClassificationFilterInput | null | undefined;
};
export type ListFilterInputTypeOfClassificationNameFilterInput = {
  all?: ClassificationNameFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: ClassificationNameFilterInput | null | undefined;
  some?: ClassificationNameFilterInput | null | undefined;
};
export type ListFilterInputTypeOfSubClassificationFilterInput = {
  all?: SubClassificationFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: SubClassificationFilterInput | null | undefined;
  some?: SubClassificationFilterInput | null | undefined;
};
export type TimelinesOrigFilterInput = {
  and?: ReadonlyArray<TimelinesOrigFilterInput> | null | undefined;
  effectiveEndDate?: DateTimeOperationFilterInput | null | undefined;
  effectiveStartDate?: DateTimeOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  idNavigation?: RootFilterInput | null | undefined;
  isOverridden?: BooleanOperationFilterInput | null | undefined;
  or?: ReadonlyArray<TimelinesOrigFilterInput> | null | undefined;
};
export type NachaconfigurationFilterInput = {
  and?: ReadonlyArray<NachaconfigurationFilterInput> | null | undefined;
  collectingAgentId?: IntOperationFilterInput | null | undefined;
  companyDescriptiveDate?: StringOperationFilterInput | null | undefined;
  companyDiscretionaryData?: StringOperationFilterInput | null | undefined;
  companyIdentification?: StringOperationFilterInput | null | undefined;
  companyName?: StringOperationFilterInput | null | undefined;
  electronicPaymentConfigurations?: ListFilterInputTypeOfElectronicPaymentConfigurationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  immediateDestinationName?: StringOperationFilterInput | null | undefined;
  immediateOrigin?: StringOperationFilterInput | null | undefined;
  immediateOriginName?: StringOperationFilterInput | null | undefined;
  name?: StringOperationFilterInput | null | undefined;
  offsetAccountNumber?: StringOperationFilterInput | null | undefined;
  offsetDescription?: StringOperationFilterInput | null | undefined;
  offsetEnabled?: BooleanOperationFilterInput | null | undefined;
  offsetRoutingNumber?: StringOperationFilterInput | null | undefined;
  or?: ReadonlyArray<NachaconfigurationFilterInput> | null | undefined;
  referenceCode?: StringOperationFilterInput | null | undefined;
  routingNumber?: StringOperationFilterInput | null | undefined;
};
export type FundAdministratorFilterInput = {
  and?: ReadonlyArray<FundAdministratorFilterInput> | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  idNavigation?: OrganizationFilterInput | null | undefined;
  or?: ReadonlyArray<FundAdministratorFilterInput> | null | undefined;
};
export type ListFilterInputTypeOfFundingCommentDetailFilterInput = {
  all?: FundingCommentDetailFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: FundingCommentDetailFilterInput | null | undefined;
  some?: FundingCommentDetailFilterInput | null | undefined;
};
export type FundingCommentDetailFilterInput = {
  and?: ReadonlyArray<FundingCommentDetailFilterInput> | null | undefined;
  comment?: StringOperationFilterInput | null | undefined;
  createdBy?: StringOperationFilterInput | null | undefined;
  creationDate?: DateTimeOperationFilterInput | null | undefined;
  fundingCommentsId?: IntOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  lastModifiedBy?: StringOperationFilterInput | null | undefined;
  lastModifiedDate?: DateTimeOperationFilterInput | null | undefined;
  or?: ReadonlyArray<FundingCommentDetailFilterInput> | null | undefined;
  organization?: OrganizationFilterInput | null | undefined;
  organizationId?: IntOperationFilterInput | null | undefined;
};
export type ListFilterInputTypeOfServiceSubscriptionFilterInput = {
  all?: ServiceSubscriptionFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: ServiceSubscriptionFilterInput | null | undefined;
  some?: ServiceSubscriptionFilterInput | null | undefined;
};
export type ServiceSubscriptionFilterInput = {
  and?: ReadonlyArray<ServiceSubscriptionFilterInput> | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  or?: ReadonlyArray<ServiceSubscriptionFilterInput> | null | undefined;
  party?: OrganizationFilterInput | null | undefined;
  partyId?: IntOperationFilterInput | null | undefined;
  subscriptionService?: SubscriptionServiceFilterInput | null | undefined;
  subscriptionServiceId?: IntOperationFilterInput | null | undefined;
};
export type SubscriptionServiceFilterInput = {
  and?: ReadonlyArray<SubscriptionServiceFilterInput> | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  name?: StringOperationFilterInput | null | undefined;
  or?: ReadonlyArray<SubscriptionServiceFilterInput> | null | undefined;
  serviceSubscriptions?: ListFilterInputTypeOfServiceSubscriptionFilterInput | null | undefined;
};
export type TradeFilterInput = {
  and?: ReadonlyArray<TradeFilterInput> | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  idNavigation?: OrganizationFilterInput | null | undefined;
  or?: ReadonlyArray<TradeFilterInput> | null | undefined;
};
export type UnionFilterInput = {
  agreements?: ListFilterInputTypeOfAgreementFilterInput | null | undefined;
  and?: ReadonlyArray<UnionFilterInput> | null | undefined;
  defaultDelinquentDay?: IntOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  idNavigation?: OrganizationFilterInput | null | undefined;
  or?: ReadonlyArray<UnionFilterInput> | null | undefined;
};
export type ListFilterInputTypeOfEmployersToAgreementFilterInput = {
  all?: EmployersToAgreementFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: EmployersToAgreementFilterInput | null | undefined;
  some?: EmployersToAgreementFilterInput | null | undefined;
};
export type ListFilterInputTypeOfReportLineItemDetailFilterInput = {
  all?: ReportLineItemDetailFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: ReportLineItemDetailFilterInput | null | undefined;
  some?: ReportLineItemDetailFilterInput | null | undefined;
};
export type ReportLineItemDetailFilterInput = {
  amount?: DecimalOperationFilterInput | null | undefined;
  and?: ReadonlyArray<ReportLineItemDetailFilterInput> | null | undefined;
  benefit?: BenefitFilterInput | null | undefined;
  benefitId?: IntOperationFilterInput | null | undefined;
  or?: ReadonlyArray<ReportLineItemDetailFilterInput> | null | undefined;
  reportLineItem?: ReportLineItemFilterInput | null | undefined;
  reportLineItemId?: IntOperationFilterInput | null | undefined;
};
export type DagreementTypeFilterInput = {
  agreements?: ListFilterInputTypeOfAgreementFilterInput | null | undefined;
  and?: ReadonlyArray<DagreementTypeFilterInput> | null | undefined;
  description?: StringOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  name?: StringOperationFilterInput | null | undefined;
  or?: ReadonlyArray<DagreementTypeFilterInput> | null | undefined;
};
export type ListFilterInputTypeOfRateScheduleFilterInput = {
  all?: RateScheduleFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: RateScheduleFilterInput | null | undefined;
  some?: RateScheduleFilterInput | null | undefined;
};
export type ListFilterInputTypeOfBenefitFilterInput = {
  all?: BenefitFilterInput | null | undefined;
  any?: boolean | null | undefined;
  none?: BenefitFilterInput | null | undefined;
  some?: BenefitFilterInput | null | undefined;
};
export type DclassificationCodeFilterInput = {
  and?: ReadonlyArray<DclassificationCodeFilterInput> | null | undefined;
  category?: StringOperationFilterInput | null | undefined;
  className?: StringOperationFilterInput | null | undefined;
  classificationNames?: ListFilterInputTypeOfClassificationNameFilterInput | null | undefined;
  description?: StringOperationFilterInput | null | undefined;
  exemptEmployee?: BooleanOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  or?: ReadonlyArray<DclassificationCodeFilterInput> | null | undefined;
};
export type DamendmentActionFilterInput = {
  and?: ReadonlyArray<DamendmentActionFilterInput> | null | undefined;
  description?: StringOperationFilterInput | null | undefined;
  id?: IntOperationFilterInput | null | undefined;
  name?: StringOperationFilterInput | null | undefined;
  or?: ReadonlyArray<DamendmentActionFilterInput> | null | undefined;
  reportLineItems?: ListFilterInputTypeOfReportLineItemFilterInput | null | undefined;
};
export type TimesheetRosterHeaderQuery$variables = {
  employerGuid: any;
  order?: ReadonlyArray<TimeSheetSortInput> | null | undefined;
  where: TimeSheetFilterInput;
};
export type TimesheetRosterHeaderQuery$data = {
  readonly " $fragmentSpreads": FragmentRefs<"TimesheetFilterModifiedByFragment">;
};
export type TimesheetRosterHeaderQuery = {
  response: TimesheetRosterHeaderQuery$data;
  variables: TimesheetRosterHeaderQuery$variables;
};

const node: ConcreteRequest = (function(){
var v0 = [
  {
    "defaultValue": null,
    "kind": "LocalArgument",
    "name": "employerGuid"
  },
  {
    "defaultValue": null,
    "kind": "LocalArgument",
    "name": "order"
  },
  {
    "defaultValue": null,
    "kind": "LocalArgument",
    "name": "where"
  }
],
v1 = {
  "kind": "Variable",
  "name": "employerGuid",
  "variableName": "employerGuid"
},
v2 = {
  "kind": "Variable",
  "name": "order",
  "variableName": "order"
},
v3 = {
  "kind": "Variable",
  "name": "where",
  "variableName": "where"
},
v4 = [
  (v1/*: any*/),
  {
    "kind": "Literal",
    "name": "first",
    "value": 20
  },
  (v2/*: any*/),
  (v3/*: any*/)
];
return {
  "fragment": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Fragment",
    "metadata": null,
    "name": "TimesheetRosterHeaderQuery",
    "selections": [
      {
        "args": [
          (v1/*: any*/),
          (v2/*: any*/),
          (v3/*: any*/)
        ],
        "kind": "FragmentSpread",
        "name": "TimesheetFilterModifiedByFragment"
      }
    ],
    "type": "Query",
    "abstractKey": null
  },
  "kind": "Request",
  "operation": {
    "argumentDefinitions": (v0/*: any*/),
    "kind": "Operation",
    "name": "TimesheetRosterHeaderQuery",
    "selections": [
      {
        "alias": null,
        "args": (v4/*: any*/),
        "concreteType": "TimeSheetConnection",
        "kind": "LinkedField",
        "name": "timesheetsByEmployerGuid",
        "plural": false,
        "selections": [
          {
            "alias": null,
            "args": null,
            "concreteType": "TimeSheetEdge",
            "kind": "LinkedField",
            "name": "edges",
            "plural": true,
            "selections": [
              {
                "alias": null,
                "args": null,
                "concreteType": "TimeSheet",
                "kind": "LinkedField",
                "name": "node",
                "plural": false,
                "selections": [
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "id",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "modifiedByUserId",
                    "storageKey": null
                  },
                  {
                    "alias": null,
                    "args": null,
                    "kind": "ScalarField",
                    "name": "__typename",
                    "storageKey": null
                  }
                ],
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "cursor",
                "storageKey": null
              }
            ],
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "concreteType": "PageInfo",
            "kind": "LinkedField",
            "name": "pageInfo",
            "plural": false,
            "selections": [
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "hasPreviousPage",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "startCursor",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "endCursor",
                "storageKey": null
              },
              {
                "alias": null,
                "args": null,
                "kind": "ScalarField",
                "name": "hasNextPage",
                "storageKey": null
              }
            ],
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "totalCount",
            "storageKey": null
          }
        ],
        "storageKey": null
      },
      {
        "alias": null,
        "args": (v4/*: any*/),
        "filters": [
          "employerGuid",
          "order",
          "where"
        ],
        "handle": "connection",
        "key": "TimesheetRosterTableFragment_timesheetsByEmployerGuid",
        "kind": "LinkedHandle",
        "name": "timesheetsByEmployerGuid"
      }
    ]
  },
  "params": {
    "cacheID": "3e056cf4758decf2f3d8b68bbbb32fba",
    "id": null,
    "metadata": {},
    "name": "TimesheetRosterHeaderQuery",
    "operationKind": "query",
    "text": "query TimesheetRosterHeaderQuery(\n  $employerGuid: UUID!\n  $order: [TimeSheetSortInput!]\n  $where: TimeSheetFilterInput!\n) {\n  ...TimesheetFilterModifiedByFragment_4vYtkT\n}\n\nfragment TimesheetFilterModifiedByFragment_4vYtkT on Query {\n  timesheetsByEmployerGuid(first: 20, employerGuid: $employerGuid, order: $order, where: $where) {\n    edges {\n      node {\n        id\n        modifiedByUserId\n        __typename\n      }\n      cursor\n    }\n    pageInfo {\n      hasPreviousPage\n      startCursor\n      endCursor\n      hasNextPage\n    }\n    totalCount\n  }\n}\n"
  }
};
})();

(node as any).hash = "46d0b7cc59aa085e1e3501146710a87a";

export default node;
