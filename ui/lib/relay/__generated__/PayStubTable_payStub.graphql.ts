/**
 * @generated SignedSource<<b9745a78e7a2aad822fea11d7314b800>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ReaderFragment } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type PayStubTable_payStub$data = {
  readonly details: ReadonlyArray<{
    readonly agreementId: number | null | undefined;
    readonly bonus: number | null | undefined;
    readonly classificationId: number | null | undefined;
    readonly costCenter: string | null | undefined;
    readonly dtHours: number | null | undefined;
    readonly earningsCode: string | null | undefined;
    readonly expenses: number | null | undefined;
    readonly hourlyRate: number | null | undefined;
    readonly id: string;
    readonly jobCode: string | null | undefined;
    readonly name: string | null | undefined;
    readonly otHours: number | null | undefined;
    readonly payStubId: string;
    readonly reportLineItemId: number | null | undefined;
    readonly stHours: number | null | undefined;
    readonly subClassificationId: number | null | undefined;
    readonly totalHours: number | null | undefined;
    readonly workDate: any;
    readonly " $fragmentSpreads": FragmentRefs<"TimeSheetDetailRow_payStubDetail">;
  }>;
  readonly employee: {
    readonly id: string;
    readonly " $fragmentSpreads": FragmentRefs<"EmployeeDisplayFragment_employee" | "TimeSheetDetailRow_employee">;
  };
  readonly employeeId: string;
  readonly id: string;
  readonly name: string | null | undefined;
  readonly totalHours: number | null | undefined;
  readonly " $fragmentSpreads": FragmentRefs<"TimeSheetDetailTableView_payStub">;
  readonly " $fragmentType": "PayStubTable_payStub";
};
export type PayStubTable_payStub$key = {
  readonly " $data"?: PayStubTable_payStub$data;
  readonly " $fragmentSpreads": FragmentRefs<"PayStubTable_payStub">;
};

const node: ReaderFragment = (function(){
var v0 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "id",
  "storageKey": null
},
v1 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "name",
  "storageKey": null
},
v2 = {
  "alias": null,
  "args": null,
  "kind": "ScalarField",
  "name": "totalHours",
  "storageKey": null
};
return {
  "argumentDefinitions": [],
  "kind": "Fragment",
  "metadata": null,
  "name": "PayStubTable_payStub",
  "selections": [
    (v0/*: any*/),
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "employeeId",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "concreteType": "Employee",
      "kind": "LinkedField",
      "name": "employee",
      "plural": false,
      "selections": [
        (v0/*: any*/),
        {
          "args": null,
          "kind": "FragmentSpread",
          "name": "EmployeeDisplayFragment_employee"
        },
        {
          "args": null,
          "kind": "FragmentSpread",
          "name": "TimeSheetDetailRow_employee"
        }
      ],
      "storageKey": null
    },
    (v1/*: any*/),
    (v2/*: any*/),
    {
      "alias": null,
      "args": null,
      "concreteType": "PayStubDetail",
      "kind": "LinkedField",
      "name": "details",
      "plural": true,
      "selections": [
        (v0/*: any*/),
        {
          "alias": null,
          "args": null,
          "kind": "ScalarField",
          "name": "payStubId",
          "storageKey": null
        },
        {
          "alias": null,
          "args": null,
          "kind": "ScalarField",
          "name": "reportLineItemId",
          "storageKey": null
        },
        {
          "alias": null,
          "args": null,
          "kind": "ScalarField",
          "name": "workDate",
          "storageKey": null
        },
        (v1/*: any*/),
        {
          "alias": null,
          "args": null,
          "kind": "ScalarField",
          "name": "stHours",
          "storageKey": null
        },
        {
          "alias": null,
          "args": null,
          "kind": "ScalarField",
          "name": "otHours",
          "storageKey": null
        },
        {
          "alias": null,
          "args": null,
          "kind": "ScalarField",
          "name": "dtHours",
          "storageKey": null
        },
        (v2/*: any*/),
        {
          "alias": null,
          "args": null,
          "kind": "ScalarField",
          "name": "bonus",
          "storageKey": null
        },
        {
          "alias": null,
          "args": null,
          "kind": "ScalarField",
          "name": "expenses",
          "storageKey": null
        },
        {
          "alias": null,
          "args": null,
          "kind": "ScalarField",
          "name": "jobCode",
          "storageKey": null
        },
        {
          "alias": null,
          "args": null,
          "kind": "ScalarField",
          "name": "agreementId",
          "storageKey": null
        },
        {
          "alias": null,
          "args": null,
          "kind": "ScalarField",
          "name": "classificationId",
          "storageKey": null
        },
        {
          "alias": null,
          "args": null,
          "kind": "ScalarField",
          "name": "subClassificationId",
          "storageKey": null
        },
        {
          "alias": null,
          "args": null,
          "kind": "ScalarField",
          "name": "costCenter",
          "storageKey": null
        },
        {
          "alias": null,
          "args": null,
          "kind": "ScalarField",
          "name": "hourlyRate",
          "storageKey": null
        },
        {
          "alias": null,
          "args": null,
          "kind": "ScalarField",
          "name": "earningsCode",
          "storageKey": null
        },
        {
          "args": null,
          "kind": "FragmentSpread",
          "name": "TimeSheetDetailRow_payStubDetail"
        }
      ],
      "storageKey": null
    },
    {
      "args": null,
      "kind": "FragmentSpread",
      "name": "TimeSheetDetailTableView_payStub"
    }
  ],
  "type": "PayStub",
  "abstractKey": null
};
})();

(node as any).hash = "9baf1137884f1a933a01a1fbcbda031d";

export default node;
