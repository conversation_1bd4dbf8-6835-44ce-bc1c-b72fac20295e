/**
 * @generated SignedSource<<b019e8e03be82d18199f09ebad9400fd>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ReaderFragment } from 'relay-runtime';
import { FragmentRefs } from "relay-runtime";
export type TimesheetDetail_timeSheet$data = {
  readonly creationDate: any | null | undefined;
  readonly employerGuid: any;
  readonly hoursWorked: number;
  readonly id: string;
  readonly modificationDate: any | null | undefined;
  readonly modifiedByUserId: string | null | undefined;
  readonly name: string | null | undefined;
  readonly numericId: number;
  readonly oldId: any | null | undefined;
  readonly payPeriodEndDate: any | null | undefined;
  readonly showBonusColumn: boolean | null | undefined;
  readonly showCostCenterColumn: boolean | null | undefined;
  readonly showDTHoursColumn: boolean | null | undefined;
  readonly showEarningsCodesColumn: boolean | null | undefined;
  readonly showExpensesColumn: boolean | null | undefined;
  readonly status: string;
  readonly type: string;
  readonly " $fragmentSpreads": FragmentRefs<"PayStubTable_connectionFragment" | "TimeSheetGrid_timeSheet" | "TimeSheetPayStubsConnectionFragment_timeSheet" | "TimeSheetSettings_timeSheet" | "TimesheetDetailView_timeSheet" | "TimesheetToolbar_timeSheet">;
  readonly " $fragmentType": "TimesheetDetail_timeSheet";
};
export type TimesheetDetail_timeSheet$key = {
  readonly " $data"?: TimesheetDetail_timeSheet$data;
  readonly " $fragmentSpreads": FragmentRefs<"TimesheetDetail_timeSheet">;
};

const node: ReaderFragment = {
  "argumentDefinitions": [],
  "kind": "Fragment",
  "metadata": null,
  "name": "TimesheetDetail_timeSheet",
  "selections": [
    {
      "args": null,
      "kind": "FragmentSpread",
      "name": "TimesheetDetailView_timeSheet"
    },
    {
      "args": null,
      "kind": "FragmentSpread",
      "name": "TimeSheetGrid_timeSheet"
    },
    {
      "args": null,
      "kind": "FragmentSpread",
      "name": "TimeSheetSettings_timeSheet"
    },
    {
      "args": null,
      "kind": "FragmentSpread",
      "name": "PayStubTable_connectionFragment"
    },
    {
      "args": null,
      "kind": "FragmentSpread",
      "name": "TimeSheetPayStubsConnectionFragment_timeSheet"
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "id",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "numericId",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "name",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "status",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "payPeriodEndDate",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "showBonusColumn",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "showCostCenterColumn",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "showDTHoursColumn",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "showEarningsCodesColumn",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "showExpensesColumn",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "employerGuid",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "creationDate",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "modificationDate",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "modifiedByUserId",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "type",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "hoursWorked",
      "storageKey": null
    },
    {
      "alias": null,
      "args": null,
      "kind": "ScalarField",
      "name": "oldId",
      "storageKey": null
    },
    {
      "args": null,
      "kind": "FragmentSpread",
      "name": "TimesheetToolbar_timeSheet"
    }
  ],
  "type": "TimeSheet",
  "abstractKey": null
};

(node as any).hash = "b8b9debc8212cad34aa4f359c5aa3e8d";

export default node;
