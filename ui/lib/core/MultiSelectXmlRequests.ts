interface SelectOption {
    text: string;
    value: number;
}

interface XmlStructure {
    [key: string]: unknown;
}

interface IdArray {
    id: number[];
}

interface RelationshipTypesXml {
    RelationshipType: string[];
}

interface AgreementTypesXml {
    AgreementTypeId: number[];
}

export namespace MultiSelectXmlRequests {
    export function employersByUnion(
        selectedUnions: SelectOption[],
        selectedAssociationMembers: SelectOption[],
        selectedRelationshipTypes: SelectOption[] = []
    ) {
        const unionsXml = getUnionIdsXml(selectedUnions);
        const filter = selectedAssociationMembers.length === 1 ? selectedAssociationMembers[0].text : 'All';
        const associationMemberXml = { '@_filter': filter };
        const relationshipTypesXml: RelationshipTypesXml = { RelationshipType: [] };
        selectedRelationshipTypes.forEach((r) => {
            relationshipTypesXml.RelationshipType.push(r.text);
        });
        const employerXml: XmlStructure = {
            EmployersSearch: {
                Unions: unionsXml,
                AssociationMembership: associationMemberXml
            }
        };

        if (relationshipTypesXml.RelationshipType.length > 0) {
            (employerXml.EmployersSearch as XmlStructure).RelationshipTypes = relationshipTypesXml;
        }

        return employerXml;
    }

    export function agreementsByEmployer(
        selectedEmployers: SelectOption[],
        selectedUnions: SelectOption[],
        selectedAgreementTypes: SelectOption[]
    ) {
        const employerIdsXml = getEmployerIdsXml(selectedEmployers);
        const unionIdsXml = getUnionIdsXml(selectedUnions);
        const agreementTypes: AgreementTypesXml = { AgreementTypeId: [] };
        selectedAgreementTypes.forEach((a) => {
            agreementTypes.AgreementTypeId.push(a.value);
        });
        const agreementsXml = {
            AgreementsSearch: {
                Employers: employerIdsXml,
                Unions: unionIdsXml,
                AgreementTypes: agreementTypes
            }
        };
        return agreementsXml;
    }

    export function getAgreementIds(selectedAgreements: SelectOption[]): IdArray {
        const agreementIds: IdArray = { id: [] };
        selectedAgreements.forEach((e) => {
            agreementIds.id.push(e.value);
        });

        return agreementIds;
    }

    export function getEmployerIdsXml(selectedEmployers: SelectOption[]): IdArray {
        const employersIds: IdArray = { id: [] };
        selectedEmployers.forEach((e) => {
            employersIds.id.push(e.value);
        });

        return employersIds;
    }

    export function getClassificationIdsXml(selectedClassifications: SelectOption[]): IdArray {
        const classificationIds: IdArray = { id: [] };
        selectedClassifications.forEach((e) => {
            classificationIds.id.push(e.value);
        });

        return classificationIds;
    }

    function getUnionIdsXml(selectedUnions: SelectOption[]): IdArray {
        const unionsXml: IdArray = { id: [] };
        selectedUnions.forEach((u) => {
            unionsXml.id.push(u.value);
        });
        return unionsXml;
    }
}
