/**
 * Custom error classes for the application
 */

export class AuthError extends Error {
    constructor(
        message: string,
        public code?: string
    ) {
        super(message);
        this.name = 'AuthError';
    }
}

export class ValidationError extends Error {
    constructor(
        message: string,
        public field?: string
    ) {
        super(message);
        this.name = 'ValidationError';
    }
}
