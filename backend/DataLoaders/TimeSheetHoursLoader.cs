using backend.Data.Models;
using Microsoft.EntityFrameworkCore;

namespace backend.DataLoaders;

public class TimeSheetHoursLoader : BatchDataLoader<int, float>
{
    private readonly IDbContextFactory<EPRLiveDBContext> _dbContextFactory;

    public TimeSheetHoursLoader(
        IDbContextFactory<EPRLiveDBContext> dbContextFactory,
        IBatchScheduler batchScheduler,
        DataLoaderOptions? options = null)
        : base(batchScheduler, options ?? new DataLoaderOptions())
    {
        _dbContextFactory = dbContextFactory ?? throw new ArgumentNullException(nameof(dbContextFactory));
    }

    protected override async Task<IReadOnlyDictionary<int, float>> LoadBatchAsync(
        IReadOnlyList<int> timesheetIds,
        CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(timesheetIds);

        await using var context = await _dbContextFactory.CreateDbContextAsync(cancellationToken);

        var hoursData = await context.PayStubs
            .Where(ps => timesheetIds.Contains(ps.TimeSheetId))
            .SelectMany(ps => ps.Details)
            .GroupBy(psd => psd.PayStub.TimeSheetId)
            .Select(g => new
            {
                TimesheetId = g.Key,
                TotalHours = g.Sum(psd => 
                    (psd.STHours ?? 0) + 
                    (psd.OTHours ?? 0) + 
                    (psd.DTHours ?? 0))
            })
            .ToListAsync(cancellationToken);

        var dict = hoursData.ToDictionary(h => h.TimesheetId, h => (float)h.TotalHours);

// SQL Server supports at most 2 100 parameters – the caller (BatchDataLoader) may chunk already; 
// if not, use smaller batches here (simple guard – improvement later).
foreach (var id in timesheetIds)
{
    if (!dict.ContainsKey(id))
        dict[id] = 0;
}
return dict;
    }
}