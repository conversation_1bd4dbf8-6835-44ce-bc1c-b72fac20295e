﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace backend.Migrations
{
    /// <inheritdoc />
    public partial class ConvertPayStubGuidsToInts : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // ========================================================================
            // Phase 0: Drop existing FK & index that reference PayStubs so the table
            //           can be recreated later in this migration.
            // ========================================================================
            migrationBuilder.DropForeignKey(
                name: "FK_PayStubDetails_PayStubs_PayStubId",
                table: "PayStubDetails"
            );

            migrationBuilder.DropIndex(
                name: "IX_PayStubDetails_PayStubId",
                table: "PayStubDetails"
            );

            // ========================================================================
            // Phase 1: Migrate PayStubs Primary Key from GUID to INT IDENTITY
            // ========================================================================

            // Step 1: Create temp table with new INT IDs using ROW_NUMBER()
            migrationBuilder.Sql(
                @"
                SELECT Id AS OldId, TimeSheetId, EmployeeId, Name,
                       ROW_NUMBER() OVER (ORDER BY Id) AS NewId
                INTO #TempPayStubs
                FROM PayStubs;
            "
            );

            // Step 2: Create new PayStubs table with INT IDENTITY
            migrationBuilder.Sql(
                @"
                CREATE TABLE PayStubs_New (
                    Id INT IDENTITY(1,1) PRIMARY KEY,
                    OldId UNIQUEIDENTIFIER NULL,
                    TimeSheetId INT NOT NULL,
                    EmployeeId INT NOT NULL,
                    Name NVARCHAR(MAX) NULL
                );
            "
            );

            // Step 3: Insert data preserving the ROW_NUMBER() assigned IDs
            migrationBuilder.Sql(
                @"
                SET IDENTITY_INSERT PayStubs_New ON;
                INSERT INTO PayStubs_New (Id, OldId, TimeSheetId, EmployeeId, Name)
                SELECT NewId, OldId, TimeSheetId, EmployeeId, Name
                FROM #TempPayStubs
                ORDER BY NewId;
                SET IDENTITY_INSERT PayStubs_New OFF;
            "
            );

            // Step 4: Drop original PayStubs table and rename new one
            migrationBuilder.Sql("DROP TABLE PayStubs;");
            migrationBuilder.Sql("EXEC sp_rename 'PayStubs_New', 'PayStubs';");

            // ========================================================================
            // Phase 2: Update PayStubDetails.PayStubId to reference new INT IDs
            // ========================================================================

            // Step 1: Add new PayStubId_New column
            migrationBuilder.Sql(
                @"
                ALTER TABLE PayStubDetails
                ADD PayStubId_New INT NULL;
            "
            );

            // Step 2: Update PayStubId_New with corresponding INT values
            migrationBuilder.Sql(
                @"
                UPDATE psd
                SET PayStubId_New = t.NewId
                FROM PayStubDetails psd
                INNER JOIN #TempPayStubs t ON psd.PayStubId = t.OldId;
            "
            );

            // Step 3: Drop old PayStubId column and rename new one
            migrationBuilder.Sql("ALTER TABLE PayStubDetails DROP COLUMN PayStubId;");
            migrationBuilder.Sql(
                "EXEC sp_rename 'PayStubDetails.PayStubId_New', 'PayStubId', 'COLUMN';"
            );
            migrationBuilder.Sql("ALTER TABLE PayStubDetails ALTER COLUMN PayStubId INT NOT NULL;");

            // ========================================================================
            // Phase 3: Migrate PayStubDetails Primary Key from GUID to INT IDENTITY
            // ========================================================================

            // Step 1: Create temp table with new INT IDs using ROW_NUMBER()
            migrationBuilder.Sql(
                @"
                SELECT Id AS OldId, PayStubId, Name, WorkDate, STHours, OTHours, DTHours,
                       JobCode, AgreementId, ClassificationId, CostCenter, HourlyRate,
                       Bonus, Expenses, ReportLineItemId, SubClassificationId, EarningsCode,
                       ROW_NUMBER() OVER (ORDER BY Id) AS NewId
                INTO #TempPayStubDetails
                FROM PayStubDetails;
            "
            );

            // Step 2: Create new PayStubDetails table with INT IDENTITY
            migrationBuilder.Sql(
                @"
                CREATE TABLE PayStubDetails_New (
                    Id INT IDENTITY(1,1) PRIMARY KEY,
                    OldId UNIQUEIDENTIFIER NULL,
                    PayStubId INT NOT NULL,
                    Name NVARCHAR(MAX) NULL,
                    WorkDate DATE NOT NULL,
                    STHours REAL NULL,
                    OTHours REAL NULL,
                    DTHours REAL NULL,
                    JobCode NVARCHAR(MAX) NULL,
                    AgreementId INT NULL,
                    ClassificationId INT NULL,
                    CostCenter NVARCHAR(MAX) NULL,
                    HourlyRate REAL NULL,
                    Bonus REAL NULL,
                    Expenses REAL NULL,
                    ReportLineItemId INT NULL,
                    SubClassificationId INT NULL,
                    EarningsCode NVARCHAR(MAX) NULL
                );
            "
            );

            // Step 3: Insert data preserving the ROW_NUMBER() assigned IDs
            migrationBuilder.Sql(
                @"
                SET IDENTITY_INSERT PayStubDetails_New ON;
                INSERT INTO PayStubDetails_New (Id, OldId, PayStubId, Name, WorkDate, STHours, OTHours, DTHours,
                                                JobCode, AgreementId, ClassificationId, CostCenter, HourlyRate,
                                                Bonus, Expenses, ReportLineItemId, SubClassificationId, EarningsCode)
                SELECT NewId, OldId, PayStubId, Name, WorkDate, STHours, OTHours, DTHours,
                       JobCode, AgreementId, ClassificationId, CostCenter, HourlyRate,
                       Bonus, Expenses, ReportLineItemId, SubClassificationId, EarningsCode
                FROM #TempPayStubDetails
                ORDER BY NewId;
                SET IDENTITY_INSERT PayStubDetails_New OFF;
            "
            );

            // Step 4: Drop original PayStubDetails table and rename new one
            migrationBuilder.Sql("DROP TABLE PayStubDetails;");
            migrationBuilder.Sql("EXEC sp_rename 'PayStubDetails_New', 'PayStubDetails';");

            // ========================================================================
            // Phase 4: Restore Foreign Key Constraints and Indexes
            // ========================================================================

            // Restore PayStubs constraints
            migrationBuilder.Sql(
                @"
                ALTER TABLE PayStubs
                ADD CONSTRAINT FK_PayStubs_TimeSheets_TimeSheetId
                FOREIGN KEY (TimeSheetId) REFERENCES TimeSheets(Id) ON DELETE CASCADE;
            "
            );

            migrationBuilder.AddForeignKey(
                name: "FK_PayStubs_Employees_EmployeeId",
                table: "PayStubs",
                column: "EmployeeId",
                principalSchema: "Core",
                principalTable: "Employees",
                principalColumn: "ID",
                onDelete: ReferentialAction.Cascade
            );

            // Restore PayStubDetails constraints
            migrationBuilder.Sql(
                @"
                ALTER TABLE PayStubDetails
                ADD CONSTRAINT FK_PayStubDetails_PayStubs_PayStubId
                FOREIGN KEY (PayStubId) REFERENCES PayStubs(Id) ON DELETE CASCADE;
            "
            );

            migrationBuilder.AddForeignKey(
                name: "FK_PayStubDetails_ReportLineItems_ReportLineItemId",
                table: "PayStubDetails",
                column: "ReportLineItemId",
                principalSchema: "Core",
                principalTable: "ReportLineItems",
                principalColumn: "ID"
            );

            // Restore Indexes
            migrationBuilder.Sql("CREATE INDEX IX_PayStubs_TimeSheetId ON PayStubs(TimeSheetId);");
            migrationBuilder.Sql("CREATE INDEX IX_PayStubs_EmployeeId ON PayStubs(EmployeeId);");
            migrationBuilder.Sql(
                "CREATE INDEX IX_PayStubDetails_PayStubId ON PayStubDetails(PayStubId);"
            );
            migrationBuilder.Sql(
                "CREATE INDEX IX_PayStubDetails_ReportLineItemId ON PayStubDetails(ReportLineItemId);"
            );

            // Clean up temporary tables
            migrationBuilder.Sql("DROP TABLE #TempPayStubs;");
            migrationBuilder.Sql("DROP TABLE #TempPayStubDetails;");

            // Verify data integrity
            migrationBuilder.Sql(
                @"
                DECLARE @PayStubCount INT, @PayStubDetailCount INT;
                SELECT @PayStubCount = COUNT(*) FROM PayStubs;
                SELECT @PayStubDetailCount = COUNT(*) FROM PayStubDetails;

                IF @PayStubCount = 0 OR @PayStubDetailCount = 0
                BEGIN
                    PRINT 'Migration completed successfully. PayStubs: ' + CAST(@PayStubCount AS NVARCHAR(10)) + ', PayStubDetails: ' + CAST(@PayStubDetailCount AS NVARCHAR(10));
                END
            "
            );
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // ========================================================================
            // Rollback Phase 1: Convert PayStubs back to GUID primary key
            // ========================================================================

            // Step 1: Create temp table with current INT IDs and restore original GUIDs
            migrationBuilder.Sql(
                @"
                SELECT Id AS NewId, OldId, TimeSheetId, EmployeeId, Name
                INTO #TempPayStubsRollback
                FROM PayStubs
                WHERE OldId IS NOT NULL;
            "
            );

            // Step 2: Create new PayStubs table with GUID primary key
            migrationBuilder.Sql(
                @"
                CREATE TABLE PayStubs_Rollback (
                    Id UNIQUEIDENTIFIER PRIMARY KEY,
                    TimeSheetId INT NOT NULL,
                    EmployeeId INT NOT NULL,
                    Name NVARCHAR(MAX) NULL
                );
            "
            );

            // Step 3: Insert data using original GUID IDs
            migrationBuilder.Sql(
                @"
                INSERT INTO PayStubs_Rollback (Id, TimeSheetId, EmployeeId, Name)
                SELECT OldId, TimeSheetId, EmployeeId, Name
                FROM #TempPayStubsRollback;
            "
            );

            // Step 4: Drop current PayStubs table and rename rollback table
            migrationBuilder.Sql("DROP TABLE PayStubs;");
            migrationBuilder.Sql("EXEC sp_rename 'PayStubs_Rollback', 'PayStubs';");

            // ========================================================================
            // Rollback Phase 2: Update PayStubDetails.PayStubId back to GUID
            // ========================================================================

            // Step 1: Add new PayStubId_Rollback column
            migrationBuilder.Sql(
                @"
                ALTER TABLE PayStubDetails
                ADD PayStubId_Rollback UNIQUEIDENTIFIER NULL;
            "
            );

            // Step 2: Update PayStubId_Rollback with corresponding GUID values
            migrationBuilder.Sql(
                @"
                UPDATE psd
                SET PayStubId_Rollback = t.OldId
                FROM PayStubDetails psd
                INNER JOIN #TempPayStubsRollback t ON psd.PayStubId = t.NewId;
            "
            );

            // Step 3: Drop old PayStubId column and rename rollback column
            migrationBuilder.Sql("ALTER TABLE PayStubDetails DROP COLUMN PayStubId;");
            migrationBuilder.Sql(
                "EXEC sp_rename 'PayStubDetails.PayStubId_Rollback', 'PayStubId', 'COLUMN';"
            );
            migrationBuilder.Sql(
                "ALTER TABLE PayStubDetails ALTER COLUMN PayStubId UNIQUEIDENTIFIER NOT NULL;"
            );

            // ========================================================================
            // Rollback Phase 3: Convert PayStubDetails back to GUID primary key
            // ========================================================================

            // Step 1: Create temp table with current INT IDs and restore original GUIDs
            migrationBuilder.Sql(
                @"
                SELECT Id AS NewId, OldId, PayStubId, Name, WorkDate, STHours, OTHours, DTHours,
                       JobCode, AgreementId, ClassificationId, CostCenter, HourlyRate,
                       Bonus, Expenses, ReportLineItemId, SubClassificationId, EarningsCode
                INTO #TempPayStubDetailsRollback
                FROM PayStubDetails
                WHERE OldId IS NOT NULL;
            "
            );

            // Step 2: Create new PayStubDetails table with GUID primary key
            migrationBuilder.Sql(
                @"
                CREATE TABLE PayStubDetails_Rollback (
                    Id UNIQUEIDENTIFIER PRIMARY KEY,
                    PayStubId UNIQUEIDENTIFIER NOT NULL,
                    Name NVARCHAR(MAX) NULL,
                    WorkDate DATE NOT NULL,
                    STHours REAL NULL,
                    OTHours REAL NULL,
                    DTHours REAL NULL,
                    JobCode NVARCHAR(MAX) NULL,
                    AgreementId INT NULL,
                    ClassificationId INT NULL,
                    CostCenter NVARCHAR(MAX) NULL,
                    HourlyRate REAL NULL,
                    Bonus REAL NULL,
                    Expenses REAL NULL,
                    ReportLineItemId INT NULL,
                    SubClassificationId INT NULL,
                    EarningsCode NVARCHAR(MAX) NULL
                );
            "
            );

            // Step 3: Insert data using original GUID IDs
            migrationBuilder.Sql(
                @"
                INSERT INTO PayStubDetails_Rollback (Id, PayStubId, Name, WorkDate, STHours, OTHours, DTHours,
                                                    JobCode, AgreementId, ClassificationId, CostCenter, HourlyRate,
                                                    Bonus, Expenses, ReportLineItemId, SubClassificationId, EarningsCode)
                SELECT OldId, PayStubId, Name, WorkDate, STHours, OTHours, DTHours,
                       JobCode, AgreementId, ClassificationId, CostCenter, HourlyRate,
                       Bonus, Expenses, ReportLineItemId, SubClassificationId, EarningsCode
                FROM #TempPayStubDetailsRollback;
            "
            );

            // Step 4: Drop current PayStubDetails table and rename rollback table
            migrationBuilder.Sql("DROP TABLE PayStubDetails;");
            migrationBuilder.Sql("EXEC sp_rename 'PayStubDetails_Rollback', 'PayStubDetails';");

            // ========================================================================
            // Rollback Phase 4: Restore Foreign Key Constraints and Indexes
            // ========================================================================

            // Restore PayStubs constraints
            migrationBuilder.Sql(
                @"
                ALTER TABLE PayStubs
                ADD CONSTRAINT FK_PayStubs_TimeSheets_TimeSheetId
                FOREIGN KEY (TimeSheetId) REFERENCES TimeSheets(Id) ON DELETE CASCADE;
            "
            );

            migrationBuilder.AddForeignKey(
                name: "FK_PayStubs_Employees_EmployeeId",
                table: "PayStubs",
                column: "EmployeeId",
                principalSchema: "Core",
                principalTable: "Employees",
                principalColumn: "ID",
                onDelete: ReferentialAction.Cascade
            );

            // Restore PayStubDetails constraints
            migrationBuilder.Sql(
                @"
                ALTER TABLE PayStubDetails
                ADD CONSTRAINT FK_PayStubDetails_PayStubs_PayStubId
                FOREIGN KEY (PayStubId) REFERENCES PayStubs(Id) ON DELETE CASCADE;
            "
            );

            migrationBuilder.AddForeignKey(
                name: "FK_PayStubDetails_ReportLineItems_ReportLineItemId",
                table: "PayStubDetails",
                column: "ReportLineItemId",
                principalSchema: "Core",
                principalTable: "ReportLineItems",
                principalColumn: "ID"
            );

            // Restore Indexes
            migrationBuilder.Sql("CREATE INDEX IX_PayStubs_TimeSheetId ON PayStubs(TimeSheetId);");
            migrationBuilder.Sql("CREATE INDEX IX_PayStubs_EmployeeId ON PayStubs(EmployeeId);");
            migrationBuilder.Sql(
                "CREATE INDEX IX_PayStubDetails_PayStubId ON PayStubDetails(PayStubId);"
            );
            migrationBuilder.Sql(
                "CREATE INDEX IX_PayStubDetails_ReportLineItemId ON PayStubDetails(ReportLineItemId);"
            );

            // Clean up temporary tables
            migrationBuilder.Sql("DROP TABLE #TempPayStubsRollback;");
            migrationBuilder.Sql("DROP TABLE #TempPayStubDetailsRollback;");

            // Verify rollback data integrity
            migrationBuilder.Sql(
                @"
                DECLARE @PayStubCount INT, @PayStubDetailCount INT;
                SELECT @PayStubCount = COUNT(*) FROM PayStubs;
                SELECT @PayStubDetailCount = COUNT(*) FROM PayStubDetails;

                IF @PayStubCount = 0 OR @PayStubDetailCount = 0
                BEGIN
                    PRINT 'Rollback completed successfully. PayStubs: ' + CAST(@PayStubCount AS NVARCHAR(10)) + ', PayStubDetails: ' + CAST(@PayStubDetailCount AS NVARCHAR(10));
                END
            "
            );
        }
    }
}
