﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace backend.Migrations
{
    /// <inheritdoc />
    public partial class RemoveTotalHours : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "TotalHours",
                table: "PayStubs");

            migrationBuilder.DropColumn(
                name: "TotalHours",
                table: "PayStubDetails");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<float>(
                name: "TotalHours",
                table: "PayStubs",
                type: "real",
                nullable: true);

            migrationBuilder.AddColumn<float>(
                name: "TotalHours",
                table: "PayStubDetails",
                type: "real",
                nullable: true);
        }
    }
}
