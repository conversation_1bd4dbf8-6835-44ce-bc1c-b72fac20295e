using AppAny.HotChocolate.FluentValidation;
using backend.Data.Models;
using backend.ErrorHandling;
using backend.Extensions;
using backend.Security;
using backend.Security.Identity;
using backend.Services;
using EPREvents.Interfaces;
using EPREvents.Producers;
using FluentValidation;
using Mapster;
using Microsoft.AspNetCore.HttpLogging;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.ResponseCompression;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.FileProviders;
using Microsoft.OpenApi.Models;
using OpenIddict.Validation.AspNetCore;
using Serilog;

var builder = WebApplication.CreateBuilder(args);

// Clear default logging providers and configure Serilog
builder.Logging.ClearProviders();

// Configure Serilog with conditional file logging
var loggerConfig = new LoggerConfiguration()
    .MinimumLevel.Debug()
    .Enrich.FromLogContext()
    .WriteTo.Console();

// Add file logging if enabled (check both config and environment variable)
var fileLoggingEnabled = builder.Configuration.GetValue<bool>("Logging:EnableFileLogging", false) ||
                        Environment.GetEnvironmentVariable("ENABLE_FILE_LOGGING")?.ToLower() == "true";

if (fileLoggingEnabled)
{
    loggerConfig.WriteTo.File("logs/backend-service-.txt",
        rollingInterval: RollingInterval.Day,
        outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}");
}

Log.Logger = loggerConfig.CreateBootstrapLogger();

builder.Host.UseSerilog((context, services, configuration) => 
{
    configuration
        .ReadFrom.Configuration(context.Configuration)
        .ReadFrom.Services(services)
        .Enrich.FromLogContext()
        .WriteTo.Console();

    // Add file logging if enabled
    var fileEnabled = context.Configuration.GetValue<bool>("Logging:EnableFileLogging", false) ||
                     Environment.GetEnvironmentVariable("ENABLE_FILE_LOGGING")?.ToLower() == "true";
    
    if (fileEnabled)
    {
        configuration.WriteTo.File("logs/backend-service-.txt",
            rollingInterval: RollingInterval.Day,
            outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}");
    }
});


//Add shared configuration file
builder
    .Configuration.SetBasePath(AppContext.BaseDirectory)
    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);

builder.Services.AddControllers();
builder.Services.AddResponseCompression(options =>
{
    options.Providers.Add<BrotliCompressionProvider>();
    options.Providers.Add<GzipCompressionProvider>();
    options.EnableForHttps = true;
});

if (builder.Environment.IsDevelopment())
{
    builder.Services.AddHttpLogging(logging =>
    {
        // log request path and response header
        logging.LoggingFields = HttpLoggingFields.RequestBody;
    });
}

builder.Services.AddCors();

builder
    .Services.AddIdentity<EPRUser, IdentityRole>()
    .AddUserStore<EPRUserStore>()
    .AddRoleStore<EPRRoleStore>()
    .AddUserManager<EPRUserManager>()
    .AddDefaultTokenProviders();

builder.Services.AddAuthentication(options =>
{
    options.DefaultScheme = OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme;
    options.DefaultAuthenticateScheme = OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme;
});
builder
    .Services.AddOpenIddict()
    .AddValidation(options =>
    {
        options.SetIssuer(
            builder.Configuration["Identity:Server"]
                ?? throw new ArgumentNullException("Identity:Server")
        );

        // Configure for reference token validation via introspection
        options
            .UseIntrospection()
            .SetClientId(builder.Configuration["Identity:ClientId"] ?? throw new InvalidOperationException("Identity:ClientId configuration is missing"))
            .SetClientSecret(builder.Configuration["Identity:ClientSecret"] ?? throw new InvalidOperationException("Identity:ClientSecret configuration is missing"));

        // Configure claim type mapping to match the identity server's role claim type
        options.Configure(o =>
        {
            o.TokenValidationParameters.RoleClaimType = System.Security.Claims.ClaimTypes.Role;
        });

        options.UseSystemNetHttp();
        options.UseAspNetCore();
    });

// Add authorization with policies
builder.Services.AddAuthorization(options =>
{
    options.AddPolicy("RequireOrganization", policy =>
        policy.Requirements.Add(new backend.Security.HasOrganizationClaimRequirement()));
        
    options.AddPolicy("SystemAdminOnly", policy =>
        policy.RequireRole("System Administrator"));
});

// Register authorization handlers
builder.Services.AddSingleton<Microsoft.AspNetCore.Authorization.IAuthorizationHandler, backend.Security.HasOrganizationClaimHandler>();
builder.Services.AddSingleton<Microsoft.AspNetCore.Authorization.IAuthorizationHandler, backend.Security.ChapterAccessHandler>();

builder.Services.AddDbContextPool<EPRLiveDBContext>(options =>
{
    options
        .UseSqlServer(
            builder.Configuration.GetConnectionString("EPRLiveConnection"),
            sqlServerOptions =>
            {
                sqlServerOptions.CommandTimeout(1200);
                sqlServerOptions.UseCompatibilityLevel(100);
            }
        )
        .LogTo(
            Console.WriteLine,
            LogLevel.Information
        ) // Uncomment this line to enable SQL debugging.
    ;
});

// Add DbContextFactory for DataLoaders
// NOTE: We keep BOTH AddDbContextPool (scoped injection) **and** this factory registration.
// The former is injected into resolvers/mutations, while the factory is used by
// BatchDataLoaders that may run on background threads and therefore cannot safely
// reuse the scoped instance.
builder.Services.AddPooledDbContextFactory<EPRLiveDBContext>(options =>
{
    options
        .UseSqlServer(
            builder.Configuration.GetConnectionString("EPRLiveConnection"),
            sqlServerOptions =>
            {
                sqlServerOptions.CommandTimeout(1200);
                sqlServerOptions.UseCompatibilityLevel(100);
            }
        );
});

// Register services
builder.Services.AddScoped<backend.Services.IUserInfoService, backend.Services.UserInfoService>();
builder.Services.AddScoped<backend.Services.IChapterAuthorizationService, backend.Services.ChapterAuthorizationService>();
// Global Object Identification configured for Relay compatibility


builder
    .Services.AddGraphQLServer()
    .ModifyCostOptions(o =>
    {
        o.EnforceCostLimits = false;
    })
    .AddAuthorization()
    .AddErrorFilter<EPRErrorFilter>()
    .AddDataLoader<backend.DataLoaders.TimeSheetHoursLoader>()
    .AddTypes()
    .AddFluentValidation()
    .AddProjections()
    .AddFiltering()
    .AddSorting()
    .ModifyOptions(o =>
    {
        o.EnableDefer = true;
        o.EnableStream = true;
    })
    .ModifyRequestOptions(o =>
    {
        o.IncludeExceptionDetails = builder.Environment.IsDevelopment();
        o.ExecutionTimeout = TimeSpan.FromSeconds(120);
    })
    .ModifyPagingOptions(o =>
    {
        o.InferConnectionNameFromField = false;
        o.MaxPageSize = 4000;
        o.DefaultPageSize = 400;
    })
    .AddMutationConventions(applyToAllMutations: true) // Enable standard Relay payloads automatically
    .AddGlobalObjectIdentification(); // Relay compatibility - HotChocolate will handle Global ID conversion automatically

builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo { Title = "EPRLiveAPI", Version = "v1" });

    c.AddSecurityDefinition(
        "Bearer",
        new OpenApiSecurityScheme
        {
            Name = "Authorization",
            Type = SecuritySchemeType.Http,
            Scheme = "Bearer",
            BearerFormat = "Opaque",
            In = ParameterLocation.Header,
            Description =
                "Reference token Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {reference_token}\"",
        }
    );

    c.AddSecurityRequirement(
        new OpenApiSecurityRequirement
        {
            {
                new OpenApiSecurityScheme
                {
                    Reference = new OpenApiReference
                    {
                        Type = ReferenceType.SecurityScheme,
                        Id = "Bearer",
                    },
                },
                new string[] { }
            },
        }
    );
});

builder.Services.Configure<MailSettings>(builder.Configuration.GetSection("MailSettings"));
builder.Services.AddTransient<IMailService, MailService>();
builder.Services.AddScoped<IProducerService, ProducerService>();
builder.Services.AddMemoryCache();
builder.Services.AddHttpContextAccessor();
builder.Services.AddScoped<IChapterAuthorizationService, ChapterAuthorizationService>();

// Register token introspection cache service
builder.Services.AddSingleton<TokenIntrospectionCache>();

// Add Mapster
builder.Services.AddMapster();

// *** Set Global Mapster Settings ***
TypeAdapterConfig.GlobalSettings.Default.NameMatchingStrategy(NameMatchingStrategy.IgnoreCase);

// *** Add Assembly Scanning for Mapster Configurations ***
TypeAdapterConfig.GlobalSettings.Scan(typeof(Program).Assembly);

// Add FluentValidation
builder.Services.AddValidatorsFromAssemblyContaining<Program>(); // Scan assembly for validators
#region Health Check Services

builder.Services.AddSingleton<KafkaHealthService>();

#endregion


var app = builder.Build();

// Enable logging of all incoming requests when in development mode
if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
    app.UseHttpLogging();
    app.UseSwagger();
    app.UseSwaggerUI(c => c.SwaggerEndpoint("/swagger/v1/swagger.json", "EPRLiveAPI v1"));
}

// Configure static files for UploadedImages
if (!app.Environment.IsEnvironment("Testing"))
{
    var uploadedImagesPath = Environment.GetEnvironmentVariable("UPLOADEDIMAGES_PATH")
        ?? System.IO.Path.Combine(app.Environment.ContentRootPath, "UploadedImages");

    // Ensure the directory exists
    if (!Directory.Exists(uploadedImagesPath))
    {
        Directory.CreateDirectory(uploadedImagesPath);
    }

    app.UseStaticFiles(
        new StaticFileOptions
        {
            FileProvider = new PhysicalFileProvider(uploadedImagesPath),
            RequestPath = "/UploadedImages",
        }
    );
}

app.UseResponseCompression();

// Update middleware order
app.UseRouting();
app.UseCors(options =>
{
    var trustedOrigins = builder.Configuration.GetSection("TrustedOrigins").Get<string[]>() ?? Array.Empty<string>();
    options
        .WithOrigins(trustedOrigins)
        .AllowAnyMethod()
        .AllowAnyHeader()
        .AllowCredentials()
        .SetIsOriginAllowedToAllowWildcardSubdomains(); // Optional: allow subdomains
});

// Add security headers
app.Use(async (context, next) =>
{
    context.Response.Headers["X-Content-Type-Options"] = "nosniff";
    context.Response.Headers["X-Frame-Options"] = "DENY";
    context.Response.Headers["Referrer-Policy"] = "strict-origin-when-cross-origin";
    await next();
});

// Add middleware to read tokens from HttpOnly cookies and set Authorization headers
app.UseCookieToHeaderMiddleware();

app.UseAuthentication();
app.UseAuthorization();
app.MapControllers();
app.MapGraphQL("/graphql");
app.Run();

// Make Program class public for testing
public partial class Program { }
