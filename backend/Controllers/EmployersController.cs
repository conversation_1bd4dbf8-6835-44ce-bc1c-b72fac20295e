using System.Data;
using System.Reflection;
using backend.Data.DTOs;
using backend.Data.HttpQueries;
using backend.Data.Models;
using backend.Services;
using backend.Types.Queries;
using backend.Utils;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Telerik.Windows.Documents.Spreadsheet.FormatProviders;
using static backend.Utils.Constants;

namespace backend.Controllers;

[Route("api/[controller]")]
[ApiController]
public class EmployersController(
    ILogger<EmployersController> logger,
    EPRLiveDBContext db,
    IWebHostEnvironment env,
    IChapterAuthorizationService authService,
    IHttpContextAccessor httpContextAccessor
) : ControllerBase
{
    private const int PRIMARY_CONTACT_RELATIONSHIP_TYPE_ID = 31;
    private const int PAYROLL_CONTACT_RELATIONSHIP_TYPE_ID = 32;
    private readonly ILogger<EmployersController> _logger = logger;
    private readonly EPRLiveDBContext _db = db;
    private readonly IWebHostEnvironment _env = env;
    private readonly IChapterAuthorizationService _authService = authService;
    private readonly IHttpContextAccessor _httpContextAccessor = httpContextAccessor;

    [HttpGet("contacts")]
    [Authorize]
    public EmployerContactInfo GetEmployerContactInfo([FromQuery] GuidQuery query)
    {
        int employerID = _db.Roots.Where((r) => r.Guid == query.GUID).First().Id;
        TfEmail? employerEmail = _db
            .TfEmails.FromSql(
                $"select * from Core.tfEmailAddresses() where PartyGUID = {query.GUID}"
            )
            .AsEnumerable()
            .FirstOrDefault();
        Relationship? primaryContact = _db
            .Relationships.Where(
                (r) =>
                    r.LeftPartyId == employerID
                    && r.DrelationshipTypeId == PRIMARY_CONTACT_RELATIONSHIP_TYPE_ID
            )
            .FirstOrDefault();
        TfEmail? primaryContactEmail = null;
        if (primaryContact != null)
        {
            primaryContactEmail = _db
                .TfEmails.FromSql(
                    $"select * from Core.tfEmailAddresses() where PartyID = {primaryContact.RightPartyId}"
                )
                .AsEnumerable()
                .FirstOrDefault();
        }
        Relationship? payrollContact = _db
            .Relationships.Where(
                (r) =>
                    r.LeftPartyId == employerID
                    && r.DrelationshipTypeId == PAYROLL_CONTACT_RELATIONSHIP_TYPE_ID
            )
            .FirstOrDefault();
        TfEmail? payrollContactEmail = null;
        if (payrollContact != null)
        {
            payrollContactEmail = _db
                .TfEmails.FromSql(
                    $"select * from Core.tfEmailAddresses() where PartyID = {payrollContact.RightPartyId}"
                )
                .AsEnumerable()
                .FirstOrDefault();
        }

        return new EmployerContactInfo
        {
            EmployerEmail = employerEmail?.EmailAddress,
            PrimaryContactEmail = primaryContactEmail?.EmailAddress,
            PayrollContactEmail = payrollContactEmail?.EmailAddress,
        };
    }

    [HttpGet("by-union")]
    [Authorize]
    public IEnumerable<EmployerSimpleId> GetEmployersByUnion([FromQuery] EmployerByUnionQuery query)
    {
        int chapterId = AuthUtils.GetCurrentChapterId(query, Request, _db, _env);
        return _db.EmployersSimpleId.FromSqlRaw(
            "exec [Core].[EmployersByUnionSelect] @employerRequest, @chapterID",
            new SqlParameter("@employerRequest", query.EmployerXml),
            new SqlParameter("@chapterID", chapterId)
        );
    }

    [HttpGet("export-employer-roster")]
    [Authorize]
    public async Task<IActionResult> ExportEmployerRoster(
        [FromQuery] ExportEmployerRosterQuery query
    )
    {
        var fileType = Enum.Parse<FileTypes>(query.FileFormat, true);
        var data = await EmployerRosterQuery.GetEmployerRosterByChapterId(
            query.ChapterId,
            _db,
            _authService,
            _httpContextAccessor
        );

        var table = CreateDataTable<EmployerRosterView>(data.OrderBy(x => x.Name), query.Columns);

        var provider = new DataTableFormatProvider();
        var workbook = provider.Import(table);
        var fileName = $"EmployerRoster{DateTime.Now.ToString("yyyyMMdd")}";

        return ExportHelper.ExportData(fileName, fileType, workbook);
    }

    [HttpGet("export-benefit-elections")]
    [Authorize]
    public async Task<IActionResult> ExportBenefitElections(
        [FromQuery] ExportEmployerRosterQuery query,
        CancellationToken cancellationToken
    )
    {
        var fileType = Enum.Parse<FileTypes>(query.FileFormat, true);
        var data = await BenefitElectionsRoster.GetBenefitElectionRosterByChapterIdAsync(
            query.ChapterId,
            _db,
            cancellationToken
        );

        var table = CreateDataTable<BenefitElectionsRosterDto>(
            data.OrderBy(x => x.EmployerName).AsQueryable(),
            query.Columns
        );

        var provider = new DataTableFormatProvider();
        var workbook = provider.Import(table);
        var fileName = $"BenefitElections{DateTime.Now.ToString("yyyyMMdd")}";

        return ExportHelper.ExportData(fileName, fileType, workbook);
    }

    [HttpGet("export-timesheets")]
    [Authorize]
    public async Task<IActionResult> ExportTimesheets(
        [FromQuery] ExportTimesheetsQuery query,
        CancellationToken cancellationToken
    )
    {
        var fileType = Enum.Parse<FileTypes>(query.FileFormat, true);
        var data = await TimesheetsQuery.GetTimesheetsByEmployerGuidAsync(
            query.EmployerId,
            _db,
            cancellationToken
        );

        //ToDo: Hardcoded limit will be removed later!
        var table = CreateDataTable<TimeSheet>(data.Take(10000), query.Columns);

        var provider = new DataTableFormatProvider();
        var workbook = provider.Import(table);

        var fileName = $"Timesheets{DateTime.Now.ToString("yyyyMMdd")}";

        return ExportHelper.ExportData(fileName, fileType, workbook);
    }

    private DataTable CreateDataTable<T>(IQueryable<T> data, string[] columns)
    {
        var table = new DataTable();

        // Get all metadata fields using MetadataQuery.GetFieldDefinitions()
        var metadataFields = MetadataQuery
            .GetFieldDefinitions()
            .ToDictionary(m => m.Value.FieldName, StringComparer.OrdinalIgnoreCase);

        // Add columns - use metadata display name if available, otherwise use column name directly
        foreach (var column in columns)
        {
            // Check if this column has a corresponding metadata field
            if (metadataFields.TryGetValue(column, out var metadata))
            {
                // Use the display name from metadata
                table.Columns.Add(metadata.Value.DisplayName);
            }
            else
            {
                // Fall back to the original column name if no metadata match
                table.Columns.Add(column);
            }
        }

        foreach (var item in data)
        {
            var row = table.NewRow();
            foreach (var column in columns)
            {
                var property = item?.GetType()
                    ?.GetProperty(
                        column,
                        BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance
                    );

                if (property != null)
                {
                    var value = property.GetValue(item, null);

                    // Find the column name to use in the table
                    string columnName = column;
                    if (metadataFields.TryGetValue(column, out var metadata))
                    {
                        columnName = metadata.Value.DisplayName;

                        // Format the value if there's a display format and the value is not null
                        if (
                            !string.IsNullOrEmpty(metadata.Value.DisplayFormat)
                            && value is DateTime dateValue
                        )
                        {
                            row[columnName] = dateValue.ToString(metadata.Value.DisplayFormat);
                            continue;
                        }
                    }

                    row[columnName] = value ?? DBNull.Value;
                }
                else
                {
                    // Property not found, set to DBNull
                    string columnName = column;
                    if (metadataFields.TryGetValue(column, out var metadata))
                    {
                        columnName = metadata.Value.DisplayName;
                    }

                    row[columnName] = DBNull.Value;
                }
            }
            table.Rows.Add(row);
        }

        return table;
    }

    public class EmployerContactInfo
    {
        public string? EmployerEmail { get; set; }
        public string? PrimaryContactEmail { get; set; }
        public string? PayrollContactEmail { get; set; }
    }

    public class ExportTimesheetsQuery
    {
        public Guid EmployerId { get; set; }
        public required string[] Columns { get; set; }
        public required string FileFormat { get; set; }
    }

    public class ExportEmployerRosterQuery
    {
        public int ChapterId { get; set; }
        public required string[] Columns { get; set; }
        public required string FileFormat { get; set; }
    }

    public class EmployerByUnionQuery : OptionalGuidQuery
    {
        public required string EmployerXml { get; set; }
    }
}
