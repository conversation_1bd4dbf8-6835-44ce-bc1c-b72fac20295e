using backend.Data.Models;
using backend.Utils;
using HotChocolate;
using HotChocolate.Authorization;
using Microsoft.EntityFrameworkCore;

namespace backend.Types.Queries
{
    [QueryType]
    [Authorize]
    public static class EmployeeQuery
    {
        /// <summary>
        /// Gets an employee by their ID
        /// </summary>
        /// <param name="employeeId">The employee's ID</param>
        /// <param name="dbContext">Database context</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Employee or null if not found</returns>
        public static async Task<Employee?> GetEmployeeByIdAsync(
            [ID(nameof(Employee))] int employeeId,
            EPRLiveDBContext dbContext,
            CancellationToken cancellationToken
        )
        {
            return await dbContext
                .Employees.Include(e => e.IdNavigation)
                .FirstOrDefaultAsync(e => e.Id == employeeId, cancellationToken);
        }

        /// <summary>
        /// Gets employees by employer ID with pagination
        /// </summary>
        /// <param name="employerId">The employer's ID</param>
        /// <param name="dbContext">Database context</param>
        /// <returns>Queryable of employees</returns>
        [UsePaging(IncludeTotalCount = true, MaxPageSize = 1000, DefaultPageSize = 100)]
        public static IQueryable<Employee> GetEmployeesByEmployerIdAsync(
            [ID(nameof(Employer))] int employerId,
            EPRLiveDBContext dbContext
        )
        {
            return GetEmployeesByEmployerInternal(dbContext, employerId: employerId);
        }

        /// <summary>
        /// Gets employees by employer GUID with pagination
        /// </summary>
        /// <param name="employerGuid">The employer's GUID</param>
        /// <param name="dbContext">Database context</param>
        /// <returns>Queryable of employees</returns>
        [UsePaging(IncludeTotalCount = true, MaxPageSize = 1000, DefaultPageSize = 100)]
        public static IQueryable<Employee> GetEmployeesByEmployerGuidAsync(
            Guid employerGuid,
            EPRLiveDBContext dbContext
        )
        {
            return GetEmployeesByEmployerInternal(dbContext, employerGuid: employerGuid);
        }

        /// <summary>
        /// Internal method that contains the shared logic for getting employees by employer ID or GUID
        /// This keeps the code DRY between the ID and GUID-based methods and uses a single database query
        /// </summary>
        /// <param name="dbContext">Database context</param>
        /// <param name="employerId">The employer's integer ID (optional)</param>
        /// <param name="employerGuid">The employer's GUID (optional)</param>
        /// <returns>Queryable of employees</returns>
        private static IQueryable<Employee> GetEmployeesByEmployerInternal(
            EPRLiveDBContext dbContext,
            int? employerId = null,
            Guid? employerGuid = null
        )
        {
            // Start with Root table to handle both ID and GUID lookups
            var rootQuery = dbContext.Roots.AsQueryable();

            if (employerId.HasValue)
                rootQuery = rootQuery.Where(r => r.Id == employerId.Value);
            else if (employerGuid.HasValue)
                rootQuery = rootQuery.Where(r => r.Guid == employerGuid.Value);
            else
                return dbContext.Employees.Where(e => false); // Return empty result for invalid parameters

            // Join Root -> Relationships -> Employees in a single query
            return rootQuery
                .Join(
                    dbContext.Relationships.Where(rel =>
                        rel.DrelationshipTypeId == (int)Constants.RelationshipTypes.EmployerEmployee),
                    root => root.Id,
                    rel => rel.LeftPartyId,
                    (root, rel) => rel
                )
                .Join(
                    dbContext.Employees.Include(e => e.IdNavigation),
                    rel => rel.RightPartyId,
                    emp => emp.Id,
                    (rel, emp) => emp
                );
        }

        /// <summary>
        /// Node resolver for Employee - required for Relay Global ID support
        /// </summary>
        /// <param name="id">The employee's ID</param>
        /// <param name="dbContext">Database context</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Employee or null if not found</returns>
        [NodeResolver]
        public static Task<Employee?> GetEmployeeNodeById(
            [ID(nameof(Employee))] int id,
            EPRLiveDBContext dbContext,
            CancellationToken cancellationToken = default
        )
        {
            return dbContext
                .Employees.Include(e => e.IdNavigation)
                .FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
        }
    }
}
