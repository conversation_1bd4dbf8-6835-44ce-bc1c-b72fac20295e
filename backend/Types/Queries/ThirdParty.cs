using System.Linq.Expressions;
using backend.Data.Models;
using backend.Data.DTOs;
using Microsoft.EntityFrameworkCore;
using HotChocolate.Authorization;
using System.Security.Claims;
using backend.Utils;
using System.Threading.Tasks;
using HotChocolate.Execution;

namespace backend.Types.Queries;

[QueryType]
[Authorize]

public static class ThirdPartyQuery
{
    [UsePaging(IncludeTotalCount = true, MaxPageSize = 4000, DefaultPageSize = 400)]
    [UseFiltering]
    [UseSorting]
    public static async Task<IEnumerable<ThirdPartyInfoDto>> GetThirdPartiesAsync(
        EPRLiveDBContext dbContext,
        int chapterId,
        CancellationToken cancellationToken
    )
    {
        IQueryable<ThirdPartyInfoDto> unionQuery =
            from unions in dbContext.Unions
            join organizations in dbContext.Organizations on unions.Id equals organizations.Id
            join relationships in dbContext.Relationships on unions.Id equals relationships.RightPartyId
            where relationships.DrelationshipTypeId == 3 && relationships.LeftPartyId == chapterId
            join root in dbContext.Roots on unions.Id equals root.Id
            select new ThirdPartyInfoDto
            {
                Value = unions.Id,
                Label = organizations.Name,
                Guid = root.Guid,
                Id = unions.Id
            };

        IQueryable<ThirdPartyInfoDto> fundAdminQuery =
            from fundAdmin in dbContext.FundAdministrators
            join organizations in dbContext.Organizations on fundAdmin.Id equals organizations.Id
            join relationships in dbContext.Relationships on fundAdmin.Id equals relationships.RightPartyId
            where relationships.DrelationshipTypeId == 4 && relationships.LeftPartyId == chapterId
            join root in dbContext.Roots on fundAdmin.Id equals root.Id
            select new ThirdPartyInfoDto
            {
                Value = fundAdmin.Id,
                Label = organizations.Name,
                Guid = root.Guid,
                Id = fundAdmin.Id
            };

        IQueryable<ThirdPartyInfoDto> chapterQuery =
            from chapter in dbContext.Chapters
            join organizations in dbContext.Organizations on chapter.Id equals organizations.Id
            where chapter.Id == chapterId
            join root in dbContext.Roots on chapter.Id equals root.Id
            select new ThirdPartyInfoDto
            {
                Value = chapter.Id,
                Label = organizations.Name,
                Guid = root.Guid,
                Id = chapter.Id
            };

        List<ThirdPartyInfoDto> unionsList = await unionQuery.ToListAsync(cancellationToken);
        List<ThirdPartyInfoDto> fundAdminsList = await fundAdminQuery.ToListAsync(cancellationToken);
        List<ThirdPartyInfoDto> chaptersList = await chapterQuery.ToListAsync(cancellationToken);

        return unionsList.Concat(fundAdminsList).Concat(chaptersList).OrderBy(x => x.Label);
    }

    [NodeResolver]
    public static async Task<ThirdPartyInfoDto> GetThirdPartyNodeByIdAsync(
        [ID(nameof(ThirdPartyInfoDto))] int id,
        EPRLiveDBContext dbContext,
        CancellationToken cancellationToken
    )
    {
        ThirdPartyInfoDto? thirdPartyRecord = await (
            from fundAdmin in dbContext.FundAdministrators
            where fundAdmin.Id == id
            join organizations in dbContext.Organizations on fundAdmin.Id equals organizations.Id
            join root in dbContext.Roots on fundAdmin.Id equals root.Id
            select new ThirdPartyInfoDto
            {
                Value = fundAdmin.Id,
                Label = organizations.Name,
                Guid = root.Guid,
                Id = fundAdmin.Id
            }
        ).FirstOrDefaultAsync(cancellationToken);

        if (thirdPartyRecord != null)
        {
            return thirdPartyRecord;
        }

        thirdPartyRecord = await (
            from unions in dbContext.Unions
            where unions.Id == id
            join organizations in dbContext.Organizations on unions.Id equals organizations.Id
            join root in dbContext.Roots on unions.Id equals root.Id
            select new ThirdPartyInfoDto
            {
                Value = unions.Id,
                Label = organizations.Name,
                Guid = root.Guid,
                Id = unions.Id
            }
        ).FirstOrDefaultAsync(cancellationToken);

        if (thirdPartyRecord != null)
        {
            return thirdPartyRecord;
        }

        thirdPartyRecord = await (
            from chapter in dbContext.Chapters
            where chapter.Id == id
            join organizations in dbContext.Organizations on chapter.Id equals organizations.Id
            join root in dbContext.Roots on chapter.Id equals root.Id
            select new ThirdPartyInfoDto
            {
                Value = chapter.Id,
                Label = organizations.Name,
                Guid = root.Guid,
                Id = chapter.Id
            }
        ).FirstOrDefaultAsync(cancellationToken);
        return thirdPartyRecord!;
    }

    // Get Organizations which aren't linked to the provied chapter Id (excluding the already linked organizations)
    [UsePaging(IncludeTotalCount = true, MaxPageSize = 4000, DefaultPageSize = 400)]
    [UseFiltering]
    [UseSorting]
    public static async Task<IEnumerable<ThirdPartyInfoDto>> GetOrganizations(
        EPRLiveDBContext dbContext,
        int chapterId,
        CancellationToken cancellationToken
    )
    {
        IQueryable<ThirdPartyInfoDto> unionQuery =
            from unions in dbContext.Unions
            join organizations in dbContext.Organizations on unions.Id equals organizations.Id
            join relationships in dbContext.Relationships on unions.Id equals relationships.RightPartyId
            where relationships.DrelationshipTypeId == 3 && relationships.LeftPartyId != chapterId
            join root in dbContext.Roots on unions.Id equals root.Id
            select new ThirdPartyInfoDto
            {
                Value = unions.Id,
                Label = organizations.Name,
                Guid = root.Guid,
                Id = unions.Id
            };

        IQueryable<ThirdPartyInfoDto> fundAdminQuery =
            from fundAdmin in dbContext.FundAdministrators
            join organizations in dbContext.Organizations on fundAdmin.Id equals organizations.Id
            join relationships in dbContext.Relationships on fundAdmin.Id equals relationships.RightPartyId
            where relationships.DrelationshipTypeId == 4 && relationships.LeftPartyId != chapterId
            join root in dbContext.Roots on fundAdmin.Id equals root.Id
            select new ThirdPartyInfoDto
            {
                Value = fundAdmin.Id,
                Label = organizations.Name,
                Guid = root.Guid,
                Id = fundAdmin.Id
            };

        IQueryable<ThirdPartyInfoDto> chapterQuery =
            from chapter in dbContext.Chapters
            join organizations in dbContext.Organizations on chapter.Id equals organizations.Id
            where chapter.Id != chapterId && chapter.Limited == false
            join root in dbContext.Roots on chapter.Id equals root.Id
            select new ThirdPartyInfoDto
            {
                Value = chapter.Id,
                Label = organizations.Name,
                Guid = root.Guid,
                Id = chapter.Id
            };

    
        List<ThirdPartyInfoDto> unionsList = await unionQuery.ToListAsync(cancellationToken);
        List<ThirdPartyInfoDto> fundAdminsList = await fundAdminQuery.ToListAsync(cancellationToken);
        List<ThirdPartyInfoDto> chaptersList = await chapterQuery.ToListAsync(cancellationToken);
    
        return unionsList.Concat(fundAdminsList).Concat(chaptersList).OrderBy(x => x.Label);
    }
}
