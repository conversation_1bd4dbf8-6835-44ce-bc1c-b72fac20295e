using backend.Data.DTOs;
using backend.Data.Models;
using HotChocolate.Authorization;
using Microsoft.EntityFrameworkCore;

namespace backend.Types.Queries;

[QueryType]
[Authorize]
public static class BenefitElectionsRoster
{
    [UsePaging(IncludeTotalCount = true, MaxPageSize = 4000, DefaultPageSize = 400)]
    [UseFiltering]
    [UseSorting]
    public static async Task<
        IEnumerable<BenefitElectionsRosterDto>
    > GetBenefitElectionRosterByChapterIdAsync(
        [ID(nameof(Chapter))] int chapterId,
        EPRLiveDBContext dbContext,
        CancellationToken cancellationToken
    )
    {
        var query =
            from agreement in dbContext.Agreements
            where agreement.ChapterId == chapterId
            join agreementToBenefits in dbContext.AgreementsToBenefits
                on agreement.Id equals agreementToBenefits.AgreementId
            join benefit in dbContext.Benefits on agreementToBenefits.BenefitId equals benefit.Id
            where benefit.EmployerElectionOverridable || benefit.EmployerRateOverridable
            join employerToAgreement in dbContext.EmployersToAgreements
                on agreement.Id equals employerToAgreement.AgreementId
            join employer in dbContext.Employers
                on employerToAgreement.EmployerId equals employer.Id
            join organization in dbContext.Organizations on employer.Id equals organization.Id
            join root in dbContext.Roots on employer.Id equals root.Id
            join relationship in dbContext.Relationships
                on new
                {
                    LeftPartyId = chapterId,
                    RightPartyId = employer.Id,
                    RelTypeId = 5,
                } equals new
                {
                    relationship.LeftPartyId,
                    RightPartyId = relationship.RightPartyId!.Value,
                    RelTypeId = relationship.DrelationshipTypeId,
                }
            join chapterEmployerRelationship in dbContext.ChapterToEmployerRelationships
                on relationship.Id equals chapterEmployerRelationship.Id
            join benefitOverride in dbContext.BenefitOverrides
                on new { BenefitId = benefit.Id, PartyId = employer.Id } equals new
                {
                    benefitOverride.BenefitId,
                    benefitOverride.PartyId,
                }
            where !benefitOverride.ChapterId.HasValue || benefitOverride.ChapterId == chapterId
            join timeline in dbContext.Timelines on benefitOverride.Id equals timeline.Id
            where
                !timeline.IsOverridden
                && (timeline.EffectiveEndDate == null || timeline.EffectiveEndDate > DateTime.Now)
            join memberDiscount in dbContext.MemberDiscountsForAssociationReports
                on chapterEmployerRelationship.AssociationId equals memberDiscount.Necaid
                into md
            from discount in md.DefaultIfEmpty()
            // Join with EmployerRosterView to get the pre-calculated LastReportedDate
            join rosterView in dbContext.EmployerRosterViews
                on employer.Id equals rosterView.Id
            select new BenefitElectionsRosterDto
            {
                EmployerName = organization.Name,
                FEIN = employer.FEIN,
                DBA = employer.Dba,
                BenefitName = benefit.Name,
                Override = benefitOverride.Value,
                EffectiveStartDate = timeline.EffectiveStartDate.ToUniversalTime(),
                EffectiveEndDate = timeline.EffectiveEndDate.HasValue ? timeline.EffectiveEndDate.Value.ToUniversalTime() : (DateTime?)null,
                Elected = benefitOverride.Election,
                EmployerGUID = root.Guid,
                AssociationID = chapterEmployerRelationship.AssociationId,
                Discount = discount.Discount,
                LastReport = rosterView.LastReportedDate, // Use date from the view
                Id = benefit.Id + employer.Id,
            };

        List<BenefitElectionsRosterDto> benefitOverrides = await query
            .AsNoTracking()
            .Distinct()
            .ToListAsync(cancellationToken);

        List<BenefitElectionsRosterDto> defaultBenefitElections = await (
            from employersToAgreements in dbContext.EmployersToAgreements
            join agreement in dbContext.Agreements
                on employersToAgreements.AgreementId equals agreement.Id
            where agreement.ChapterId == chapterId
            join employer in dbContext.Employers
                on employersToAgreements.EmployerId equals employer.Id
            join organization in dbContext.Organizations on employer.Id equals organization.Id
            join relationship in dbContext.Relationships
                on new { LeftPartyId = chapterId, RightPartyId = employer.Id } equals new
                {
                    relationship.LeftPartyId,
                    RightPartyId = relationship.RightPartyId!.Value,
                }
            join chapterEmployerRelationship in dbContext.ChapterToEmployerRelationships
                on relationship.Id equals chapterEmployerRelationship.Id
                into gj
            from x in gj.DefaultIfEmpty()
            join root in dbContext.Roots on employer.Id equals root.Id
            join agreementsToBenefits in dbContext.AgreementsToBenefits
                on agreement.Id equals agreementsToBenefits.AgreementId
            join benefit in dbContext.Benefits on agreementsToBenefits.BenefitId equals benefit.Id
            where
                (benefit.EmployerElectionOverridable || benefit.EmployerRateOverridable)
                && benefit.DstatusId == 1
            join memberDiscount in dbContext.MemberDiscountsForAssociationReports
                on x.AssociationId equals memberDiscount.Necaid
                into md
            from discount in md.DefaultIfEmpty()
            // Join with EmployerRosterView to get the pre-calculated LastReportedDate
            join rosterView in dbContext.EmployerRosterViews
                on employer.Id equals rosterView.Id
            select new BenefitElectionsRosterDto
            {
                EmployerName = organization.Name,
                FEIN = employer.FEIN,
                DBA = employer.Dba,
                BenefitName = benefit.Name,
                Override = null,
                EffectiveStartDate = null,
                EffectiveEndDate = null,
                Elected = benefit.DefaultOn,
                EmployerGUID = root.Guid,
                AssociationID = x.AssociationId,
                Discount = discount.Discount,
                LastReport = rosterView.LastReportedDate, // Use date from the view
                Id = benefit.Id + employer.Id,
            }
        ).ToListAsync(cancellationToken);

        foreach (BenefitElectionsRosterDto defaultBenefitElection in defaultBenefitElections)
        {
            if (
                benefitOverrides.Find(
                    (b) =>
                        b.BenefitName == defaultBenefitElection.BenefitName
                        && b.EmployerGUID == defaultBenefitElection.EmployerGUID
                ) == null
            )
            {
                benefitOverrides.Add(defaultBenefitElection);
            }
        }

        List<BenefitElectionsRosterDto> sortedList = benefitOverrides
            .OrderBy(b => b.EmployerName)
            .ThenBy(b => b.BenefitName)
            .ThenBy(b => b.EffectiveStartDate)
            .ToList();

        return sortedList;
    }
}
