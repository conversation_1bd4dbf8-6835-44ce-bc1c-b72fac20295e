﻿using System.Linq.Expressions;
using backend.Data.Models;
using Microsoft.EntityFrameworkCore;
using HotChocolate.Authorization;
using backend.Data.DTOs;

namespace backend.Types.Queries;

[QueryType]
[Authorize]
public static class UnionRosterQuery
{
    private static IQueryable<UnionRosterDto> GetUnionsBaseQuery(
        EPRLiveDBContext dbContext,
        IQueryable<Union> unions
    )
    {
        return from uni in unions
               join organization in dbContext.Organizations on uni.Id equals organization.Id
               join rel in dbContext.Relationships on uni.Id equals rel.LeftPartyId into relGroup
               from relationship in relGroup.DefaultIfEmpty()
               where relationship.DrelationshipTypeId == 11
               join employer in dbContext.Organizations
                   on relationship.RightPartyId equals employer.Id
                   into employerGroup
               from employer in employerGroup.DefaultIfEmpty()
               join agreement in dbContext.Agreements on uni.Id equals agreement.UnionId into agrGroup
               group new { employer, agrGroup } by new { uni.Id, organization.Name } into grouped
               select new UnionRosterDto
               {
                   Id = grouped.Key.Id,
                   UnionName = grouped.Key.Name,
                   EmployerCount = grouped
                       .Where(r => r.employer != null)
                       .Select(r => r.employer)
                       .Distinct()
                       .Count(),
                   AgreementCount = grouped
                       .SelectMany(g => g.agrGroup)
                       .Select(a => a.Id)
                       .Distinct()
                       .Count()
               };
    }

    [UsePaging(IncludeTotalCount = true, MaxPageSize = 4000, DefaultPageSize = 400)]
    // [UseProjection]
    [UseFiltering]
    [UseSorting]
    public static IQueryable<UnionRosterDto> GetUnionsByChapterId(
        int chapterId,
        EPRLiveDBContext dbContext,
        CancellationToken cancellationToken
    )
    {
        Expression<Func<Relationship, bool>> condition = relationship =>
            relationship.DrelationshipTypeId == 3 && relationship.LeftPartyId == chapterId;

        var unions = dbContext.Relationships
            .Where(condition)
            .Join(
                dbContext.Unions,
                relationship => relationship.RightPartyId,
                union => union.Id,
                (relationship, union) => union
            );
        return GetUnionsBaseQuery(dbContext, unions);
    }

    [NodeResolver]
    public static Task<UnionRosterDto?> GetUnionRosterNodeByIdAsync(
        [ID(nameof(UnionRosterDto))] int id,
        EPRLiveDBContext dbContext,
        CancellationToken cancellationToken
    )
    {
        var unions = dbContext.Unions.Where(e => e.Id == id);
        return GetUnionsBaseQuery(dbContext, unions).FirstOrDefaultAsync(cancellationToken);
    }
}
