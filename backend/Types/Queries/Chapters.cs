using backend.Data.DTOs;
using backend.Data.Models;
using backend.Utils;
using HotChocolate.Authorization;
using Microsoft.EntityFrameworkCore;
using OpenIddict.Abstractions;
using static OpenIddict.Abstractions.OpenIddictConstants;

namespace backend.Types.Queries;

[QueryType]
[Authorize]
public static class ChaptersQuery
{
    [UsePaging(IncludeTotalCount = true, MaxPageSize = 4000, DefaultPageSize = 400)]
    [UseFiltering]
    [UseSorting]
    public static async Task<IQueryable<ChaptersInfoDto>> GetChaptersAsync(
        EPRLiveDBContext dbContext,
        [Service] IHttpContextAccessor httpContextAccessor,
        CancellationToken cancellationToken
    )
    {
        var username = httpContextAccessor.HttpContext?.User.GetClaim(Claims.Username) ?? throw new InvalidOperationException("HttpContext or user claims not available");

        var currentUser = dbContext.AspnetUsers.Where((u) => u.UserName == username).First();
        HashSet<string> roleGroups = new HashSet<string>(
            currentUser.RoleGroups.Select(r => r.Name)
        );

        return await Task.FromResult(
            from chapter in dbContext.Chapters
            join organization in dbContext.Organizations on chapter.Id equals organization.Id
            join root in dbContext.Roots on organization.Id equals root.Id
            where roleGroups.Contains(Constants.Roles.TradeAdministrator) ? chapter.Limited : true
            orderby organization.Name
            select new ChaptersInfoDto
            {
                Label = organization.Name,
                Value = chapter.Id,
                Guid = root.Guid,
                Id = chapter.Id,
            }
        );
    }

    [NodeResolver]
    public static Task<ChaptersInfoDto?> GetChapterNodeByIdAsync(
        [ID(nameof(ChaptersInfoDto))] int id,
        EPRLiveDBContext dbContext,
        CancellationToken cancellationToken
    )
    {
        return (
            from chapter in dbContext.Chapters
            join organization in dbContext.Organizations on chapter.Id equals organization.Id
            join root in dbContext.Roots on organization.Id equals root.Id
            where chapter.Id == id
            orderby organization.Name
            select new ChaptersInfoDto
            {
                Label = organization.Name,
                Value = chapter.Id,
                Guid = root.Guid,
                Id = chapter.Id,
            }
        ).FirstOrDefaultAsync(cancellationToken);
    }
}
