using System.Linq;
using backend.Data.Models;
using backend.Types.Enums;
using backend.Types.Outputs;
using backend.Utils;
using HotChocolate.Authorization;
using Microsoft.EntityFrameworkCore;

namespace backend.Types.Queries
{
    [QueryType]
    [Authorize]
    public static class EmployeeDefaultSettingsQuery
    {
        /// <summary>
        /// Gets default settings for a specific employee
        /// </summary>
        /// <param name="employeeId">The ID of the employee</param>
        /// <param name="dbContext">Database context</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Employee default settings</returns>
        public static async Task<EmployeeDefaultSettings?> GetEmployeeDefaultSettingsAsync(
            [ID(nameof(Employee))] int employeeId,
            EPRLiveDBContext dbContext,
            CancellationToken cancellationToken
        )
        {
            if (employeeId <= 0)
            {
                throw new ArgumentException(
                    "Employee ID must be a positive integer",
                    nameof(employeeId)
                );
            }

            var queryResults = await dbContext
                .Roots.Where(r => r.Id == employeeId)
                .Select(r => new
                {
                    EmployeeId = r.Id,
                    EmployeeGuid = r.Guid,
                    Settings = dbContext
                        .Settings.Where(s =>
                            s.OwnerId == r.Guid
                            && (
                                s.DSettingsTypeId == (int)EmployeeSettingType.DefaultAgreementId
                                || s.DSettingsTypeId
                                    == (int)EmployeeSettingType.DefaultClassificationId
                                || s.DSettingsTypeId == (int)EmployeeSettingType.DefaultHourlyRate
                            )
                        )
                        .ToList(),
                })
                .FirstOrDefaultAsync(cancellationToken);

            if (queryResults == null)
            {
                return null;
            }

            var result = new EmployeeDefaultSettings { EmployeeId = queryResults.EmployeeId };

            if (!queryResults.Settings.Any())
            {
                return result;
            }

            foreach (var setting in queryResults.Settings)
            {
                PopulateSettingFromResult(result, setting);
            }

            return result;
        }

        private static void PopulateSettingFromResult(
            EmployeeDefaultSettings target,
            Setting settingSource
        )
        {
            if (string.IsNullOrEmpty(settingSource.Value))
                return;

            switch (settingSource.DSettingsTypeId)
            {
                case (int)EmployeeSettingType.DefaultAgreementId:
                    if (int.TryParse(settingSource.Value, out int agreementId))
                    {
                        target.DefaultAgreementId = agreementId;
                    }
                    break;
                case (int)EmployeeSettingType.DefaultClassificationId:
                    if (int.TryParse(settingSource.Value, out int classificationId))
                    {
                        target.DefaultClassificationId = classificationId;
                    }
                    break;
                case (int)EmployeeSettingType.DefaultHourlyRate:
                    if (
                        decimal.TryParse(
                            settingSource.Value,
                            System.Globalization.NumberStyles.Any,
                            System.Globalization.CultureInfo.InvariantCulture,
                            out decimal hourlyRate
                        )
                    )
                    {
                        target.DefaultHourlyRate = hourlyRate;
                    }
                    break;
            }
        }

        /// <summary>
        /// Gets default settings for all employees of a specific employer
        /// </summary>
        /// <param name="employerId">The ID of the employer</param>
        /// <param name="dbContext">Database context</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of employee default settings</returns>
        [UsePaging(IncludeTotalCount = true, MaxPageSize = 1000, DefaultPageSize = 100)]
        public static async Task<
            IQueryable<EmployeeDefaultSettings>
        > GetEmployeeDefaultSettingsByEmployerIdAsync(
            [ID(nameof(Employer))] int employerId,
            EPRLiveDBContext dbContext,
            CancellationToken cancellationToken
        )
        {
            if (employerId <= 0)
            {
                throw new ArgumentException(
                    "Employer ID must be a positive integer",
                    nameof(employerId)
                );
            }

            // Step 1: Get all employees (Id and Guid) for the employer
            var employeesInfo = await dbContext
                .Relationships.Where(r =>
                    r.DrelationshipTypeId == (int)Constants.RelationshipTypes.EmployerEmployee
                    && r.LeftPartyId == employerId
                )
                .Join(
                    dbContext.Roots,
                    rel => rel.RightPartyId,
                    root => root.Id,
                    (rel, root) => new { EmployeeId = root.Id, EmployeeGuid = root.Guid }
                )
                .Where(e => e.EmployeeGuid.HasValue) // Ensure Guid is present for linking settings
                .ToListAsync(cancellationToken);

            if (!employeesInfo.Any())
            {
                return new List<EmployeeDefaultSettings>().AsQueryable();
            }

            var employeeGuids = employeesInfo.Select(e => e.EmployeeGuid!.Value).ToList();

            // Step 2: Get all relevant settings for these employees in one go
            var allSettings = await dbContext
                .Settings.Where(s =>
                    employeeGuids.Contains(s.OwnerId)
                    && (
                        s.DSettingsTypeId == (int)EmployeeSettingType.DefaultAgreementId
                        || s.DSettingsTypeId == (int)EmployeeSettingType.DefaultClassificationId
                        || s.DSettingsTypeId == (int)EmployeeSettingType.DefaultHourlyRate
                    )
                )
                .ToListAsync(cancellationToken);

            // Group settings by OwnerId (EmployeeGuid) for efficient lookup
            var settingsByEmployeeGuid = allSettings.ToLookup(s => s.OwnerId);

            var results = new List<EmployeeDefaultSettings>();

            foreach (var empInfo in employeesInfo)
            {
                var result = new EmployeeDefaultSettings { EmployeeId = empInfo.EmployeeId };
                // empInfo.EmployeeGuid is guaranteed to have a value here due to the .Where(e => e.EmployeeGuid.HasValue) filter earlier.
                var employeeSettings = settingsByEmployeeGuid[empInfo.EmployeeGuid!.Value];

                foreach (var setting in employeeSettings)
                {
                    PopulateSettingFromResult(result, setting); // Use refactored helper
                }
                results.Add(result);
            }

            return results.AsQueryable();
        }
    }
}
