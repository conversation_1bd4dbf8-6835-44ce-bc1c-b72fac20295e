using backend.Data.Models;
using backend.Types.Inputs;
using HotChocolate.Authorization;
using Microsoft.EntityFrameworkCore;

namespace backend.Types.Queries;

[QueryType]
[Authorize]
public static class ClassificationsQuery
{
    /// <summary>
    /// Get classifications for a single agreement ID using EF Core and LINQ
    /// This replaces the REST endpoint: GET /api/Classifications/agreement/{agreementId}
    /// </summary>
    [UsePaging(IncludeTotalCount = true, MaxPageSize = 4000, DefaultPageSize = 400)]
    [UseFiltering]
    [UseSorting]
    public static async Task<IQueryable<ClassificationName>> GetClassificationsByAgreementIdAsync(
        [ID(nameof(Agreement))] int agreementId,
        EPRLiveDBContext dbContext,
        CancellationToken cancellationToken = default
    )
    {
        return await Task.FromResult(
            dbContext
                .AgreementClassifications.AsNoTracking()
                .Where(ac => ac.AgreementId == agreementId)
                .Join(
                    dbContext.ClassificationNames.AsNoTracking(),
                    agreementClassification => agreementClassification.ClassificationNameId,
                    classification => classification.Id,
                    (agreementClassification, classification) => classification
                )
                .Distinct()
        );
    }

    /// <summary>
    /// Get classifications for multiple agreement IDs using EF Core and LINQ
    /// This replaces the REST endpoint: POST /api/Classifications/by-agreement
    /// Returns the same format as the stored procedure: ClassificationSimpleId with ID and Name
    /// </summary>
    [UsePaging(IncludeTotalCount = true, MaxPageSize = 4000, DefaultPageSize = 400)]
    [UseFiltering]
    [UseSorting]
    public static async Task<IQueryable<ClassificationSimpleId>> GetClassificationsByAgreementIdsAsync(
        ClassificationsByAgreementsInput input,
        EPRLiveDBContext dbContext,
        CancellationToken cancellationToken = default
    )
    {
        return await Task.FromResult(
            dbContext
                .AgreementClassifications.AsNoTracking()
                .Where(ac => input.AgreementIds.Contains(ac.AgreementId))
                .Join(
                    dbContext.ClassificationNames.AsNoTracking(),
                    agreementClassification => agreementClassification.ClassificationNameId,
                    classification => classification.Id,
                    (agreementClassification, classification) => new ClassificationSimpleId
                    {
                        ID = classification.Id,
                        Name = classification.Name
                    }
                )
                .Distinct()
                .OrderBy(c => c.Name)
        );
    }

    /// <summary>
    /// Get subclassifications for a specific agreement and classification
    /// This replaces the REST endpoint: GET /api/Classifications/agreement/{agreementId}/subclassifications/{classificationId}
    /// </summary>
    [UsePaging(IncludeTotalCount = true, MaxPageSize = 4000, DefaultPageSize = 400)]
    [UseFiltering]
    [UseSorting]
    public static async Task<IQueryable<SubClassification>> GetSubClassificationsByAgreementAndClassificationAsync(
        [ID(nameof(Agreement))] int agreementId,
        [ID(nameof(ClassificationName))] int classificationId,
        EPRLiveDBContext dbContext,
        CancellationToken cancellationToken = default
    )
    {
        return await Task.FromResult(
            dbContext
                .AgreementClassifications.AsNoTracking()
                .Where(ac => ac.AgreementId == agreementId && ac.ClassificationNameId == classificationId)
                .Join(
                    dbContext.SubClassifications.AsNoTracking(),
                    agreementClassification => agreementClassification.SubClassificationId,
                    subClassification => subClassification.Id,
                    (agreementClassification, subClassification) => subClassification
                )
                .Distinct()
        );
    }

    /// <summary>
    /// Node resolver for ClassificationName entities (Relay support)
    /// </summary>
    [NodeResolver]
    public static async Task<ClassificationName?> GetClassificationNodeByIdAsync(
        [ID(nameof(ClassificationName))] int id,
        EPRLiveDBContext dbContext,
        CancellationToken cancellationToken = default
    )
    {
        return await dbContext
            .ClassificationNames.AsNoTracking()
            .Include(c => c.Chapter)
            .Include(c => c.DclassificationCode)
            .Include(c => c.Dstatus)
            .Where(c => c.Id == id)
            .FirstOrDefaultAsync(cancellationToken);
    }

    /// <summary>
    /// Node resolver for SubClassification entities (Relay support)
    /// </summary>
    [NodeResolver]
    public static async Task<SubClassification?> GetSubClassificationNodeByIdAsync(
        [ID(nameof(SubClassification))] int id,
        EPRLiveDBContext dbContext,
        CancellationToken cancellationToken = default
    )
    {
        return await dbContext
            .SubClassifications.AsNoTracking()
            .Include(sc => sc.Chapter)
            .Include(sc => sc.Dstatus)
            .Where(sc => sc.Id == id)
            .FirstOrDefaultAsync(cancellationToken);
    }
}
