using backend.Data.Models;
using backend.Utils;
using HotChocolate.Authorization;
using Microsoft.EntityFrameworkCore;

namespace backend.Types.Queries;

[QueryType]
[Authorize]
public static class EmployersQuery
{
    [UsePaging(IncludeTotalCount = true, MaxPageSize = 10000, DefaultPageSize = 400)]
    [UseProjection]
    [UseFiltering]
    [UseSorting]
    public static IQueryable<Employer> GetEmployersByChapterId(
        EPRLiveDBContext dbContext,
        [ID(nameof(Chapter))] int chapterId
    )
    {
        return dbContext
            .Employers.AsNoTracking()
            .Include(e => e.Root)
            .Include(e => e.Organization)
            .Where(employer =>
                dbContext
                    .Relationships.AsNoTracking()
                    .Any(relationship =>
                        relationship.DrelationshipTypeId
                            == (int)Constants.RelationshipTypes.ChapterEmployer
                        && relationship.LeftPartyId == chapterId
                        && relationship.RightPartyId == employer.Id
                    )
            );
    }

    [NodeResolver]
    public static Task<Employer?> GetEmployerNodeById(
        [ID(nameof(Employer))] int id,
        EPRLiveDBContext dbContext,
        CancellationToken cancellationToken = default
    )
    {
        return dbContext
            .Employers.AsNoTracking()
            .Include(e => e.Root)
            .Include(e => e.Organization)
            .Where(employer => employer.Id == id)
            .FirstOrDefaultAsync(cancellationToken);
    }
}
