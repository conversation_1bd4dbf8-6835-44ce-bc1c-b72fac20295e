using backend.Data.Models;
using backend.Types.Outputs;
using HotChocolate.Authorization;
using Microsoft.EntityFrameworkCore;

namespace backend.Types.Queries;

[QueryType]
[Authorize]
public static class TimesheetsQuery
{
    [UsePaging(IncludeTotalCount = true, MaxPageSize = 4000, DefaultPageSize = 400)]
    [UseFiltering]
    [UseSorting]
    public static async Task<IQueryable<TimeSheet>> GetTimesheetsByEmployerGuidAsync(
        Guid employerGuid,
        EPRLiveDBContext dbContext,
        CancellationToken cancellationToken
    )
    {
#if DEBUG
        // TEMPORARY: Add delay for testing cache persistence functionality
        // Set environment variable ENABLE_CACHE_TESTING_DELAY=true to enable
        if (Environment.GetEnvironmentVariable("ENABLE_CACHE_TESTING_DELAY") == "true")
        {
            await Task.Delay(5000, cancellationToken);
        }
#endif
        
        return await Task.FromResult(
            dbContext.TimeSheets.Where(t => t.EmployerGuid == employerGuid).Include(t => t.PayStubs)
        );
    }

    [GraphQLName("payStubCount")]
    public static int GetPayStubCount([Parent] TimeSheet timeSheet)
    {
        return timeSheet.GetPayStubCount();
    }


    // NodeResolver for TimeSheet entity - provides both timeSheetById query field and Relay node interface
    // Includes PayStubs with Details and Employee data for optimal query performance
    [NodeResolver]
    public static async Task<TimeSheet?> GetTimeSheetByIdAsync(
        int id,
        [Service] EPRLiveDBContext dbContext,
        CancellationToken cancellationToken = default
    )
    {
#if DEBUG
        // TEMPORARY: Add delay for testing cache persistence functionality
        // Set environment variable ENABLE_CACHE_TESTING_DELAY=true to enable
        if (Environment.GetEnvironmentVariable("ENABLE_CACHE_TESTING_DELAY") == "true")
        {
            await Task.Delay(5000, cancellationToken);
        }
#endif

        return await dbContext
            .TimeSheets.Include(timesheet => timesheet.PayStubs)
            .ThenInclude(paystub => paystub.Details)
            .Include(timesheet => timesheet.PayStubs)
            .ThenInclude(paystub => paystub.Employee)
            .ThenInclude(employee => employee.IdNavigation)
            .Where(timesheet => timesheet.Id == id)
            .FirstOrDefaultAsync(cancellationToken);
    }

    [UsePaging(IncludeTotalCount = true, MaxPageSize = 4000, DefaultPageSize = 400)]
    [UseSorting]
    public static IQueryable<User> GetTimesheetUsersByEmployerGuid(
        Guid employerGuid,
        EPRLiveDBContext dbContext,
        CancellationToken cancellationToken
    )
    {
        return dbContext
            .TimeSheets.Where(t => t.EmployerGuid == employerGuid)
            .Join(
                dbContext.AspnetUsers,
                timesheet => timesheet.ModifiedByUserId,
                aspnetUser => aspnetUser.UserName.ToString(),
                (timesheet, aspnetUser) => new { timesheet, aspnetUser }
            )
            .Join(
                dbContext.Eprusers,
                combined => combined.aspnetUser.UserId,
                epruser => epruser.AspnetUserId,
                (combined, epruser) =>
                    new User
                    {
                        Id = epruser.AspnetUserId,
                        UserName = combined.aspnetUser.UserName,
                        FullName = $"{epruser.FirstName} {epruser.LastName}",
                    }
            )
            .Distinct()
            .AsQueryable();
    }
}
