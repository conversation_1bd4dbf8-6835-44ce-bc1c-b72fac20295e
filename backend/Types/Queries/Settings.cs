﻿using backend.Data.Models;
using backend.Utils;
using HotChocolate.Authorization;
using Microsoft.EntityFrameworkCore;

namespace backend.Types.Queries
{
    [QueryType]
    [Authorize]
    public static class SettingsQuery
    {
        //TODO: Add organization/chapter, employer, and user level security to all queries!

        [UsePaging(IncludeTotalCount = true, MaxPageSize = 4000, DefaultPageSize = 400)]
        [UseFiltering]
        public static async Task<IQueryable<Setting>> GetSettingsAsync(
            EPRLiveDBContext dbContext,
            IHttpContextAccessor httpContextAccessor,
            CancellationToken cancellationToken,
            Guid employerGuid,
            int? settingTypeId
        )
        {
            //TODO: Add organization/chapter, employer, and user level security!

            Guid userId = Guid.Empty;
            if (
                !AuthUtils.TryGetUserClaim(
                    httpContextAccessor.HttpContext!,
                    Constants.Claims.Subject,
                    out userId
                )
            )
            {
                // Handle missing claim
                throw new UnauthorizedAccessException("User identifier not found in token");
            }

            Guid organizationId = Guid.Empty;
            if (
                !AuthUtils.TryGetUserClaim(
                    httpContextAccessor.HttpContext!,
                    Constants.Claims.OrganizationGuid,
                    out organizationId
                )
            )
            {
                // Handle missing claim
                throw new UnauthorizedAccessException("Organization identifier not found in token");
            }

            var settings = dbContext
                .Settings.Include(x => x.SettingsType)
                .Where(x =>
                    x.Value != null && (settingTypeId == null || (x.SettingsType != null && x.SettingsType.Id == settingTypeId))
                );

            return await Task.FromResult(settings);
        }

        [UsePaging(IncludeTotalCount = true, MaxPageSize = 4000, DefaultPageSize = 400)]
        [UseFiltering]
        public static async Task<IQueryable<DsettingsType>> GetSettingTypesAsync(
            EPRLiveDBContext dbContext,
            IHttpContextAccessor httpContextAccessor,
            CancellationToken cancellationToken
        )
        {
            //TODO: Add organization/chapter, employer, and user level security!

            Guid userId = Guid.Empty;
            if (
                !AuthUtils.TryGetUserClaim(
                    httpContextAccessor.HttpContext!,
                    Constants.Claims.Subject,
                    out userId
                )
            )
            {
                // Handle missing claim
                throw new UnauthorizedAccessException("User identifier not found in token");
            }

            Guid organizationId = Guid.Empty;
            if (
                !AuthUtils.TryGetUserClaim(
                    httpContextAccessor.HttpContext!,
                    Constants.Claims.OrganizationGuid,
                    out organizationId
                )
            )
            {
                // Handle missing claim
                throw new UnauthorizedAccessException("Organization identifier not found in token");
            }

            var settingTypes = dbContext.DsettingsTypes;

            return await Task.FromResult(settingTypes);
        }

        [NodeResolver]
        public static async Task<Setting?> GetSettingNodeByIdAsync(
            [ID(nameof(Setting))] int id,
            [Service] EPRLiveDBContext dbContext,
            [Service] IHttpContextAccessor httpContextAccessor,
            CancellationToken cancellationToken = default
        )
        {
            //TODO: Add organization/chapter, employer, and user level security!

            Guid userId = Guid.Empty;
            if (
                !AuthUtils.TryGetUserClaim(
                    httpContextAccessor.HttpContext!,
                    Constants.Claims.Subject,
                    out userId
                )
            )
            {
                // Handle missing claim
                throw new UnauthorizedAccessException("User identifier not found in token");
            }

            Guid organizationId = Guid.Empty;
            if (
                !AuthUtils.TryGetUserClaim(
                    httpContextAccessor.HttpContext!,
                    Constants.Claims.OrganizationGuid,
                    out organizationId
                )
            )
            {
                // Handle missing claim
                throw new UnauthorizedAccessException("Organization identifier not found in token");
            }

            var setting = await dbContext
                .Settings.Include(x => x.SettingsType)
                .FirstOrDefaultAsync(x => x.Id == id, cancellationToken);

            return setting;
        }

        public static async Task<DsettingsType?> GetSettingBySettingTypeNameAsync(
            EPRLiveDBContext dbContext,
            IHttpContextAccessor httpContextAccessor,
            CancellationToken cancellationToken,
            string name,
            Guid ownerId,
            string ownerType
        )
        {
            //TODO: Add organization/chapter, employer, and user level security!

            Guid userId = Guid.Empty;
            if (
                !AuthUtils.TryGetUserClaim(
                    httpContextAccessor.HttpContext!,
                    Constants.Claims.Subject,
                    out userId
                )
            )
            {
                // Handle missing claim
                throw new UnauthorizedAccessException("User identifier not found in token");
            }

            Guid organizationId = Guid.Empty;
            if (
                !AuthUtils.TryGetUserClaim(
                    httpContextAccessor.HttpContext!,
                    Constants.Claims.OrganizationGuid,
                    out organizationId
                )
            )
            {
                // Handle missing claim
                throw new UnauthorizedAccessException("Organization identifier not found in token");
            }

            var setting = await dbContext
                .DsettingsTypes.Include(x =>
                    x.Settings!.Where(s =>
                        s != null && s.OwnerId == ownerId && s.OwnerType == ownerType
                    )
                )
                .Where(x => x.Name == name)
                .FirstOrDefaultAsync(cancellationToken);

            return setting;
        }
    }
}
