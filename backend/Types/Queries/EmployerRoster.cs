﻿using System.Linq.Expressions;
using backend.Data.Models;
using backend.Services;
using backend.Utils;
using HotChocolate.Authorization;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace backend.Types.Queries;

[QueryType]
[Authorize]
public static class EmployerRosterQuery
{
    // Configuration option: Set to true to throw authorization errors instead of returning empty lists
    // This is useful for debugging authorization issues
    private const bool THROW_AUTHORIZATION_ERRORS = true;
    [UsePaging(IncludeTotalCount = true, MaxPageSize = 4000, DefaultPageSize = 400)]
    [UseProjection]
    [UseFiltering]
    [UseSorting]
    public static async Task<IQueryable<EmployerRosterView>> GetEmployerRosterByChapterId(
        [ID(nameof(Chapter))] int chapterId,
        EPRLiveDBContext dbContext,
        [Service] IChapterAuthorizationService authService,
        [Service] IHttpContextAccessor httpContextAccessor,
        CancellationToken cancellationToken = default
    )
    {
#if DEBUG
        // TEMPORARY: Add delay for testing cache persistence functionality
        // Set environment variable ENABLE_CACHE_TESTING_DELAY=true to enable
        if (Environment.GetEnvironmentVariable("ENABLE_CACHE_TESTING_DELAY") == "true")
        {
            await Task.Delay(5000, cancellationToken);
        }
#endif

        // Verify user has access to the requested chapter
        if (!authService.IsUserAuthorizedForChapter(chapterId))
        {
            if (THROW_AUTHORIZATION_ERRORS)
            {
                throw new GraphQLException(
                    ErrorBuilder.New()
                        .SetMessage($"User is not authorized to access chapter {chapterId}. Check logs for details.")
                        .SetCode("CHAPTER_AUTHORIZATION_FAILED")
                        .Build()
                );
            }
            else
            {
                // For unauthorized access, return empty result set instead of throwing exception
                // This prevents information disclosure about what chapters exist
                return Enumerable.Empty<EmployerRosterView>().AsQueryable();
            }
        }

        Expression<Func<Relationship, bool>> condition = relationship =>
            relationship.DrelationshipTypeId == (int)Constants.RelationshipTypes.ChapterEmployer
            && relationship.LeftPartyId == chapterId;

        return await Task.FromResult(dbContext.EmployerRosterViews.Where(view => view.ChapterId == chapterId));
    }

    // TODO:  Does this really need to exsit when the Employer node is already defined in Employers class?
    [NodeResolver]
    public static async Task<EmployerRosterView?> GetEmployerRosterNodeById(
        [ID(nameof(EmployerRosterView))] int id,
        EPRLiveDBContext dbContext,
        [Service] IChapterAuthorizationService authService,
        [Service] IHttpContextAccessor httpContextAccessor,
        CancellationToken cancellationToken = default
    )
    {
        // First find the record to get its chapterId
        var employerRosterView = await dbContext.EmployerRosterViews.FirstOrDefaultAsync(
            view => view.Id == id,
            cancellationToken
        );

        if (employerRosterView == null)
        {
            return null;
        }

        // Verify user has access to this employer's chapter
        if (!authService.IsUserAuthorizedForChapter(employerRosterView.ChapterId))
        {
            // Return null for unauthorized access
            return null;
        }

        return employerRosterView;
    }

    public static async Task<CanDeleteEmployerResponse> CanDeleteEmployer(
        [ID(nameof(Employer))] int employerId,
        [ID(nameof(Chapter))] int chapterId,
        EPRLiveDBContext dbContext,
        [Service] IChapterAuthorizationService authService,
        [Service] IHttpContextAccessor httpContextAccessor,
        CancellationToken cancellationToken
    )
    {
        // Verify user has access to the requested chapter
        if (!authService.IsUserAuthorizedForChapter(chapterId))
        {
            return new CanDeleteEmployerResponse
            {
                CanDelete = false,
                CanDeleteRelationship = false,
                Message = "Unauthorized to perform this operation",
            };
        }

        Organization? employerOrg = dbContext.Organizations.SingleOrDefault(o =>
            o.Id == employerId
        );
        if (employerOrg == null)
        {
            return new CanDeleteEmployerResponse
            {
                CanDelete = false,
                CanDeleteRelationship = false,
                Message = "Employer does not exist",
            };
        }

        if (employerOrg.DorganizationTypeId != (int)Constants.OrganizationTypes.Employer)
        {
            return new CanDeleteEmployerResponse
            {
                CanDelete = false,
                CanDeleteRelationship = false,
                Message = "Organization is not of type employer",
            };
        }

        List<Report> reports = await dbContext
            .Reports.Where(r => r.EmployerId == employerId)
            .ToListAsync(cancellationToken);
        if (reports.Count > 0)
        {
            return new CanDeleteEmployerResponse
            {
                CanDelete = false,
                CanDeleteRelationship = false,
                Message = "Employer has existing reports in EPRLive",
            };
        }

        Guid employerGuid = (
            await dbContext.Roots.Where(r => r.Id == employerId).FirstAsync(cancellationToken)
        )
            .Guid!.Value;
        List<TimeSheet> timesheets = await dbContext
            .TimeSheets.Where(t => t.EmployerGuid == employerGuid)
            .ToListAsync(cancellationToken);
        if (timesheets.Count > 0)
        {
            return new CanDeleteEmployerResponse
            {
                CanDelete = false,
                CanDeleteRelationship = false,
                Message = "Employer has existing timesheets in EPRLive",
            };
        }

        List<Relationship> employees = await dbContext
            .Relationships.Where(r =>
                r.LeftPartyId == employerId
                && r.DrelationshipTypeId == (int)Constants.RelationshipTypes.EmployerEmployee
            )
            .ToListAsync(cancellationToken);
        if (employees.Count > 0)
        {
            return new CanDeleteEmployerResponse
            {
                CanDelete = false,
                CanDeleteRelationship = false,
                Message = "Employer has existing employees in EPRLive",
            };
        }

        List<Relationship> otherSiteSponsors = await dbContext
            .Relationships.Where(r =>
                r.RightPartyId == employerId
                && r.LeftPartyId != chapterId
                && r.DrelationshipTypeId == (int)Constants.RelationshipTypes.ChapterEmployer
            )
            .ToListAsync(cancellationToken);
        if (otherSiteSponsors.Count > 0)
        {
            return new CanDeleteEmployerResponse
            {
                CanDelete = false,
                CanDeleteRelationship = true,
                Message = "Employer has relationship with another site sponsor",
            };
        }

        return new CanDeleteEmployerResponse { CanDelete = true, CanDeleteRelationship = true };
    }

    public class CanDeleteEmployerResponse
    {
        public bool CanDelete { get; set; }
        public bool CanDeleteRelationship { get; set; }
        public string? Message { get; set; }
    }
}
