﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using backend.Data.Models;
using Mapster;

namespace backend.Types.Outputs
{
    public class CustomViews
    {
        [ID(nameof(Setting))]
        [GraphQLType(typeof(IdType))]
        public int? Id { get; set; }
        public string? Name { get; set; }
        public string? Description { get; set; }

        [ID(nameof(DsettingsType))]
        [Required]
        [GraphQLType(typeof(IdType))]
        [AdaptMember("SettingId")]
        public int ViewTypeId { get; set; }

        [GraphQLType(typeof(JsonType))]
        public object? DefaultValue { get; set; }

        [GraphQLType(typeof(JsonType))]
        public object? Value { get; set; }

        [GraphQLType(typeof(IdType))]
        public required string OwnerId { get; set; }

        public string OwnerType { get; set; } = null!;

        public Guid? LastModifiedBy { get; set; }

        public DateTime? LastModificationDate { get; set; }
    }

    public class CustomViewTypes
    {
        [ID(nameof(DsettingsType))]
        [GraphQLType(typeof(IdType))]
        public int? Id { get; set; }

        public string? Name { get; set; }

        public string? Description { get; set; }

        [GraphQLType(typeof(JsonType))]
        public object? DefaultValue { get; set; }

        [AdaptMember("Settings")]
        public virtual ICollection<CustomViews?>? CustomViews { get; set; }
    }
}
