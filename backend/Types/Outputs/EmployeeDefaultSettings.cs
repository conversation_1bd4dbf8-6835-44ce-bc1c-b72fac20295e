using System.ComponentModel.DataAnnotations;
using backend.Data.Models;

namespace backend.Types.Outputs
{
    /// <summary>
    /// Output type for employee default settings used in GraphQL queries
    /// </summary>
    public class EmployeeDefaultSettings
    {
        [ID(nameof(Employee))]
        [GraphQLType(typeof(IdType))]
        public int EmployeeId { get; set; }

        public int? DefaultAgreementId { get; set; }

        public int? DefaultClassificationId { get; set; }

        public decimal? DefaultHourlyRate { get; set; }
    }
}
