using backend.Data.Models;
using backend.DataLoaders;

namespace backend.Types.Resolvers;

[ExtendObjectType(typeof(TimeSheet))]
public class TimeSheetHoursResolver
{
    [GraphQLName("hoursWorked")]
    public async Task<float> GetHoursWorkedAsync(
        [Parent] TimeSheet timesheet,
        TimeSheetHoursLoader hoursLoader,
        CancellationToken cancellationToken)
    {
        ArgumentNullException.ThrowIfNull(timesheet);
        ArgumentNullException.ThrowIfNull(hoursLoader);

        return await hoursLoader.LoadAsync(timesheet.Id, cancellationToken);
    }
}