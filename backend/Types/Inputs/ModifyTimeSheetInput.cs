using System;
using backend.Data.Models; // For nameof(TimeSheet)
using backend.Types.Inputs;
using backend.Utils; // For Optional<>
using HotChocolate; // For [ID]

namespace backend.Types.Inputs;

public record ModifyTimeSheetInput(
    [property: ID(nameof(TimeSheet))] int Id,
    Guid employerGuid,
    string? name,
    Optional<string?> status,
    Optional<string?> type,
    DateTimeOffset? modificationDate,
    bool? ShowDTHoursColumn,
    bool? ShowCostCenterColumn,
    bool? ShowBonusColumn,
    bool? ShowExpensesColumn,
    bool? ShowEarningsCodesColumn,
    bool? readOnly,
    List<AddPayStubInput>? addPayStubs,
    List<ModifyPayStubInput>? modifyPayStubs,
    [property: ID(nameof(PayStub))] List<int>? deletePayStubIds,
    Guid? timeSheetId
);
