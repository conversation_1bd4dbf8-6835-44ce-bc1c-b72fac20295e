using System;
using backend.Data.Models; // For nameof references
using HotChocolate;

namespace backend.Types.Inputs;

public abstract record PayStubDetailBaseInput(
    string? name,
    DateOnly workDate,
    float? OTHours,
    float? STHours,
    float? DTHours,
    // totalHours is now computed automatically from STHours + OTHours + DTHours
    string? jobCode,
    string? earningsCode,
    int? agreementId,
    int? classificationId,
    int? subClassificationId,
    string? costCenter,
    float? hourlyRate,
    float? bonus,
    float? expenses,
    [ID(nameof(PayStub))] int? payStubId,
    int? reportLineItemId
);
