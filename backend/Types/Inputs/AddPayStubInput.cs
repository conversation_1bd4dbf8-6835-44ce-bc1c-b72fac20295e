using backend.Data.Models; // For nameof() if needed
using backend.Types.Inputs;
using HotChocolate;

namespace backend.Types.Inputs;

public record AddPayStubInput(
    [property: ID(nameof(PayStub))] int? Id,
    [property: ID(nameof(Employee))] int employeeId,
    string? employeeName,
    bool? inEdit,
    bool? expanded,
    string? name,
    List<AddPayStubDetailInput>? details,
    bool? delete,
    float? STHours,
    float? OTHours,
    float? DTHours,
    float? bonus,
    float? expenses
) : PayStubBaseInput();
