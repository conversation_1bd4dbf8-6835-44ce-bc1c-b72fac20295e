﻿using backend.Data.Models;
using Mapster;

namespace backend.Types.Inputs
{
    [AdaptTo(typeof(Setting))]
    public class CreateCustomViews
    {
        [GraphQLNonNullType]
        public required string Name { get; set; }

        public string? Description { get; set; }

        [GraphQLType(typeof(JsonType))]
        public object? DefaultValue { get; set; }

        [GraphQLType(typeof(JsonType))]
        public object? Value { get; set; }

        public Guid? OwnerId { get; set; }

        public string? OwnerType { get; set; } = null!;
    }

    [AdaptTo(typeof(Setting))]
    public class UpdateCustomViews
    {
        [ID(nameof(Setting))]
        [GraphQLType(typeof(IdType))]
        public int? Id { get; set; }

        public string? Name { get; set; }

        [GraphQLType(typeof(JsonType))]
        public object? Value { get; set; }
    }
}
