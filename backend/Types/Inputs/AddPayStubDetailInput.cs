using backend.Data.Models; // For nameof references
using HotChocolate;

namespace backend.Types.Inputs;

public record AddPayStubDetailInput(
    [property: ID(nameof(PayStubDetail))] int? Id, // Input might have an ID for linking?
    string? name,
    DateOnly workDate,
    float? OTHours,
    float? STHours,
    float? DTHours,
    // totalHours is computed automatically from STHours + OTHours + DTHours
    string? jobCode,
    string? earningsCode,
    int? agreementId,
    int? classificationId,
    int? subClassificationId,
    string? costCenter,
    float? hourlyRate,
    float? bonus,
    float? expenses,
    [property: ID(nameof(PayStub))] int? payStubId,
    int? reportLineItemId,
    bool? delete
);
