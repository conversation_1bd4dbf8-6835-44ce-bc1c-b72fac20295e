﻿using backend.Data.Models;
using System.ComponentModel.DataAnnotations;

namespace backend.Types.Inputs
{
    public class SaveSettingInput
    {
        [ID(nameof(Setting))]
        [GraphQLType(typeof(IdType))]
        public int? Id { get; set; }

        [ID(nameof(DsettingsType))]
        [Required]
        [GraphQLType(typeof(IdType))]
        public int SettingId { get; set; }

        [GraphQLType(typeof(AnyType))]
        public object? Value { get; set; }

        [Required]
        [GraphQLType(typeof(IdType))]
        public required string OwnerId { get; set; }

        [Required]
        public string OwnerType { get; set; } = null!;

        public Guid? LastModifiedBy { get; set; }

        public DateTime? LastModificationDate { get; set; }
    }

    public class SaveSettingTypeInput
    {
        [Required]
        [GraphQLType(typeof(IdType))]
        public int Id { get; set; }

        [GraphQLNonNullType]
        public string Name { get; set; } = null!;

        public string? Description { get; set; }

        [Required]
        public bool AllowOverride { get; set; }

        public string ValueType { get; set; } = null!;

        [GraphQLType(typeof(AnyType))]
        public object? DefaultValue { get; set; }

        public string? ValidValues { get; set; }
    }
}
