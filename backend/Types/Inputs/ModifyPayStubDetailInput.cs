using backend.Data.Models; // For nameof(PayStubDetail)
using HotChocolate;

namespace backend.Types.Inputs;

public record ModifyPayStubDetailInput(
    [property: ID(nameof(PayStubDetail))] int? id,
    string? name,
    DateOnly workDate,
    float? OTHours,
    float? STHours,
    float? DTHours,
    // totalHours is computed automatically from STHours + OTHours + DTHours
    string? jobCode,
    string? earningsCode,
    int? agreementId,
    int? classificationId,
    int? subClassificationId,
    string? costCenter,
    float? hourlyRate,
    float? bonus,
    float? expenses,
    [property: ID(nameof(PayStub))] int? payStubId,
    bool? delete,
    int? reportLineItemId
);
