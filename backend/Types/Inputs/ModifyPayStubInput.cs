using backend.Data.Models; // For nameof(PayStub)
using backend.Types.Inputs;
using HotChocolate; // For [ID]

namespace backend.Types.Inputs;

public record ModifyPayStubInput(
    [property: ID(nameof(PayStub))] int Id,
    [property: ID(nameof(Employee))] int employeeId,
    string? name,
    List<ModifyPayStubDetailInput>? details,
    bool? expanded,
    bool? inEdit,
    string? employeeName,
    float? STHours,
    float? OTHours,
    float? DTHours,
    float? bonus,
    float? expenses
);
