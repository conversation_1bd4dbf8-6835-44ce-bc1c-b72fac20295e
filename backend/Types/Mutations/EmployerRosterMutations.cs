using System.Text;
using backend.Data.Models;
using backend.Services;
using backend.Types.Payloads;
using backend.Types.Queries;
using backend.Types.Relay;
using backend.Utils;
using HotChocolate.Authorization;
using Microsoft.EntityFrameworkCore;

namespace backend.Types.Mutations;

[MutationType]
[Authorize]
public class EmployerRosterMutations
{
    public async Task<AddEmployerPayload> AddEmployer(
        Employer employer,
        [ID(nameof(Chapter))] int chapterId,
        EPRLiveDBContext dbContext,
        [Service] IChapterAuthorizationService authService,
        [Service] IHttpContextAccessor httpContextAccessor,
        CancellationToken cancellationToken
    )
    {
        using var transaction = await dbContext.Database.BeginTransactionAsync(cancellationToken);

        try
        {
            Organization newOrg = await MutationSharedUtils.createOrganization(
                organizationName: employer.Name
                    ?? throw new ArgumentNullException(nameof(employer.Name)),
                organizationTypeId: (int)Constants.OrganizationTypes.Employer,
                createdByUsername: "web", // TODO: Replace with the ID of the logged in user
                dbContext,
                cancellationToken
            );

            Employer newEmployer = new Employer
            {
                FEIN = employer.FEIN,
                Organization = newOrg,
                Dba = employer.Dba,
                BusinessDescription = employer.BusinessDescription,
            };

            Relationship chapterEmployerRelationship = await MutationSharedUtils.createRelationship(
                dbContext,
                chapterId,
                newOrg.Id,
                (int)Constants.RelationshipTypes.ChapterEmployer,
                null,
                "web", // TODO: Replace with the ID of the logged in user
                cancellationToken
            );

            ChapterToEmployerRelationship otherChapterEmployerRelationship =
                new ChapterToEmployerRelationship
                {
                    IsAssociationMember = false,
                    Relationship = chapterEmployerRelationship,
                };

            await dbContext.Employers.AddAsync(newEmployer, cancellationToken);
            await dbContext.Relationships.AddAsync(chapterEmployerRelationship, cancellationToken);
            await dbContext.ChapterToEmployerRelationships.AddAsync(
                otherChapterEmployerRelationship,
                cancellationToken
            );
            await dbContext.SaveChangesAsync(cancellationToken);

            var employerRosterView =
                await EmployerRosterQuery.GetEmployerRosterNodeById(
                    newEmployer.Id,
                    dbContext,
                    authService,
                    httpContextAccessor,
                    cancellationToken
                ) ?? throw new InvalidOperationException("Failed to save employer information.");

            await transaction.CommitAsync(cancellationToken);

            var cursor = Convert.ToBase64String(
                Encoding.ASCII.GetBytes(employerRosterView.Id.ToString())
            );

            // Create a strongly-typed edge object
            var edge = new Edge<EmployerRosterView> { Cursor = cursor, Node = employerRosterView };

            return new AddEmployerPayload(employerRosterViewEdge: edge);
        }
        catch
        {
            await transaction.RollbackAsync(cancellationToken);
            throw;
        }
    }

    public async Task<OperationResult> DeleteEmployer(
        [ID(nameof(Employer))] int employerId,
        EPRLiveDBContext dbContext,
        [Service] IChapterAuthorizationService authService,
        [Service] IHttpContextAccessor httpContextAccessor,
        CancellationToken cancellationToken
    )
    {
        using var transaction = await dbContext.Database.BeginTransactionAsync(cancellationToken);
        try
        {
            var employerToDelete =
                await EmployerRosterQuery.GetEmployerRosterNodeById(
                    employerId,
                    dbContext,
                    authService,
                    httpContextAccessor,
                    cancellationToken
                )
                ?? throw new InvalidOperationException(
                    "Employer with ID " + employerId + " could not be found"
                );

            List<PartiesToContactMechanism> partiesToContactMechanisms = await dbContext
                .PartiesToContactMechanisms.Where(p => p.PartyId == employerId)
                .ToListAsync(cancellationToken);
            dbContext.RemoveRange(
                dbContext.Roots.Where(root =>
                    partiesToContactMechanisms.Select(c => c.ContactMechanismId).Contains(root.Id)
                )
            );
            dbContext.RemoveRange(
                dbContext.Roots.Where(root =>
                    partiesToContactMechanisms.Select(c => c.Id).Contains(root.Id)
                )
            );

            List<Relationship> relationships = await dbContext
                .Relationships.Where(r =>
                    r.LeftPartyId == employerId || r.RightPartyId == employerId
                )
                .ToListAsync(cancellationToken);
            dbContext.RemoveRange(
                dbContext.Roots.Where(root => relationships.Select(r => r.Id).Contains(root.Id))
            );

            List<RelationshipStatus> relationshipStatuses = await dbContext
                .RelationshipStatuses.Where(r =>
                    relationships.Select(relationship => relationship.Id).Contains(r.RelationshipId)
                )
                .ToListAsync(cancellationToken);
            dbContext.RemoveRange(
                dbContext.Roots.Where(root =>
                    relationshipStatuses.Select(r => r.Id).Contains(root.Id)
                )
            );

            List<EmployersToAgreement> employersToAgreements = await dbContext
                .EmployersToAgreements.Where(ea => ea.EmployerId == employerId)
                .ToListAsync(cancellationToken);
            dbContext.RemoveRange(
                dbContext.Roots.Where(root =>
                    employersToAgreements.Select(ea => ea.Id).Contains(root.Id)
                )
            );

            List<BenefitOverride> benefitOverrides = await dbContext
                .BenefitOverrides.Where(b => b.PartyId == employerId)
                .ToListAsync(cancellationToken);
            dbContext.RemoveRange(
                dbContext.Roots.Where(root => benefitOverrides.Select(b => b.Id).Contains(root.Id))
            );

            await dbContext.SaveChangesAsync(cancellationToken);
            var employerToRemove = dbContext.Employers.Find(employerId);
            if (employerToRemove != null)
            {
                dbContext.Employers.Remove(employerToRemove);
            }
            await dbContext.SaveChangesAsync(cancellationToken);

            await transaction.CommitAsync(cancellationToken);

            return new OperationResult(true, "Employer deleted successfully.");
        }
        catch (InvalidOperationException e)
        {
            await transaction.RollbackAsync(cancellationToken);
            return new OperationResult(false, e.Message);
        }
        catch (Exception e)
        {
            await transaction.RollbackAsync(cancellationToken);
            return new OperationResult(
                false,
                $"An error occurred while deleting the employer: {e.Message}"
            );
        }
    }

    public async Task<DeleteRelationshipWithSiteSponsorPayload> DeleteRelationshipWithSiteSponsor(
        [ID(nameof(Employer))] int employerId,
        [ID(nameof(Chapter))] int chapterId,
        EPRLiveDBContext dbContext,
        [Service] IChapterAuthorizationService authService,
        [Service] IHttpContextAccessor httpContextAccessor,
        CancellationToken cancellationToken
    )
    {
        using var transaction = await dbContext.Database.BeginTransactionAsync(cancellationToken);
        try
        {
            var employerRosterView =
                await EmployerRosterQuery.GetEmployerRosterNodeById(
                    employerId,
                    dbContext,
                    authService,
                    httpContextAccessor,
                    cancellationToken
                )
                ?? throw new InvalidOperationException(
                    $"Employer with ID {employerId} could not be found"
                );

            // Retrieve chapter to ensure it exists and to include in any potential error messages
            var chapter =
                await dbContext.Chapters.FindAsync(chapterId)
                ?? throw new InvalidOperationException(
                    $"Chapter with ID {chapterId} could not be found"
                );

            // Retrieve relationships
            var relationships = await dbContext
                .Relationships.Where(r =>
                    r.LeftPartyId == employerId || r.RightPartyId == employerId
                )
                .ToListAsync(cancellationToken);

            if (!relationships.Any())
                throw new InvalidOperationException(
                    $"No relationships found for employer '{employerRosterView.Name}' with site sponsor."
                );

            // Remove relationships
            dbContext.Relationships.RemoveRange(relationships);

            // Remove relationship statuses
            var relationshipStatuses = await dbContext
                .RelationshipStatuses.Where(rs =>
                    relationships.Select(r => r.Id).Contains(rs.RelationshipId)
                )
                .ToListAsync(cancellationToken);
            dbContext.RelationshipStatuses.RemoveRange(relationshipStatuses);

            // Delete EmployersToAgreement relationships
            var employersToAgreementRelationships = await dbContext
                .EmployersToAgreements.Where(e =>
                    e.EmployerId == employerId
                    && relationships.Select(r => r.Id).Contains(e.AgreementId)
                )
                .ToListAsync(cancellationToken);
            dbContext.EmployersToAgreements.RemoveRange(employersToAgreementRelationships);

            // Delete Benefit Overrides
            var benefitOverrides = await dbContext
                .BenefitOverrides.Where(bo =>
                    bo.PartyId == employerId
                    && relationships.Select(r => r.Id).Contains(bo.BenefitId)
                )
                .ToListAsync(cancellationToken);
            dbContext.BenefitOverrides.RemoveRange(benefitOverrides);

            await dbContext.SaveChangesAsync(cancellationToken);
            await transaction.CommitAsync(cancellationToken);

            var cursor = Convert.ToBase64String(
                Encoding.ASCII.GetBytes(employerRosterView.Id.ToString())
            );

            // Create a strongly-typed edge object
            var edge = new Edge<EmployerRosterView> { Cursor = cursor, Node = employerRosterView };

            return new DeleteRelationshipWithSiteSponsorPayload(employerRosterViewEdge: edge);
        }
        catch (Exception)
        {
            await transaction.RollbackAsync(cancellationToken);
            throw;
        }
    }
}
