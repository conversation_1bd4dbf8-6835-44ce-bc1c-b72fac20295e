using backend.Data.Models;
using Microsoft.EntityFrameworkCore;

namespace backend.Tests.Infrastructure;

/// <summary>
/// Builder for creating test data with proper handling of IDENTITY columns
/// </summary>
public class TestDataBuilder
{
    private readonly EPRLiveDBContext _context;
    private readonly Dictionary<string, object> _createdEntities = new();
    
    public TestDataBuilder(EPRLiveDBContext context)
    {
        _context = context;
    }
    
    /// <summary>
    /// Creates a TimeSheet without setting the ID (let database generate it)
    /// </summary>
    public async Task<TimeSheet> CreateTimeSheetAsync(
        string name = "Test Timesheet",
        string status = "Draft",
        string type = "Regular",
        Guid? employerGuid = null)
    {
        var timeSheet = new TimeSheet
        {
            // Do NOT set Id - let database generate it
            Name = name,
            PayPeriodEndDate = DateOnly.FromDateTime(DateTime.Now),
            Status = status,
            Type = type,
            EmployerGuid = employerGuid ?? Guid.Parse("11111111-1111-1111-1111-111111111111"),
            CreationDate = DateTimeOffset.UtcNow,
            ModificationDate = DateTimeOffset.UtcNow,
            CreatedByUserId = "test-user",
            ModifiedByUserId = "test-user"
        };
        
        _context.TimeSheets.Add(timeSheet);
        await _context.SaveChangesAsync();
        
        _createdEntities[$"TimeSheet_{name}"] = timeSheet;
        return timeSheet;
    }
    
    /// <summary>
    /// Creates a PayStub without setting the ID
    /// </summary>
    public async Task<PayStub> CreatePayStubAsync(
        int timeSheetId,
        int employeeId,
        string? name = null)
    {
        // Clear change tracker to avoid conflicts
        _context.ChangeTracker.Clear();
        
        var payStub = new PayStub
        {
            // Do NOT set Id - let database generate it
            TimeSheetId = timeSheetId,
            EmployeeId = employeeId,
            Name = name,
            Details = new List<PayStubDetail>()
        };
        
        _context.PayStubs.Add(payStub);
        await _context.SaveChangesAsync();
        
        _createdEntities[$"PayStub_{timeSheetId}_{employeeId}"] = payStub;
        return payStub;
    }
    
    /// <summary>
    /// Creates PayStubDetails without setting IDs
    /// </summary>
    public async Task<List<PayStubDetail>> CreatePayStubDetailsAsync(
        int payStubId,
        params (DateOnly workDate, float? stHours, float? otHours, float? dtHours)[] details)
    {
        // Clear change tracker to avoid conflicts
        _context.ChangeTracker.Clear();
        
        var payStubDetails = new List<PayStubDetail>();
        
        foreach (var (workDate, stHours, otHours, dtHours) in details)
        {
            var detail = new PayStubDetail
            {
                // Do NOT set Id - let database generate it
                PayStubId = payStubId,
                WorkDate = workDate,
                STHours = stHours,
                OTHours = otHours,
                DTHours = dtHours
            };
            
            payStubDetails.Add(detail);
            _context.PayStubDetails.Add(detail);
        }
        
        await _context.SaveChangesAsync();
        return payStubDetails;
    }
    
    /// <summary>
    /// Creates a complete PayStub with details in a transaction
    /// </summary>
    public async Task<PayStub> CreatePayStubWithDetailsAsync(
        int timeSheetId,
        int employeeId,
        params (DateOnly workDate, float? stHours, float? otHours, float? dtHours)[] details)
    {
        using var transaction = await _context.Database.BeginTransactionAsync();
        
        try
        {
            var payStub = await CreatePayStubAsync(timeSheetId, employeeId);
            
            if (details.Any())
            {
                await CreatePayStubDetailsAsync(payStub.Id, details);
            }
            
            await transaction.CommitAsync();
            
            // Reload with details
            await _context.Entry(payStub)
                .Collection(ps => ps.Details)
                .LoadAsync();
                
            return payStub;
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }
    
    /// <summary>
    /// Gets a previously created entity by key
    /// </summary>
    public T? GetCreatedEntity<T>(string key) where T : class
    {
        return _createdEntities.TryGetValue(key, out var entity) ? entity as T : null;
    }
    
    /// <summary>
    /// Clears all change tracking
    /// </summary>
    public void ClearTracking()
    {
        _context.ChangeTracker.Clear();
    }
}