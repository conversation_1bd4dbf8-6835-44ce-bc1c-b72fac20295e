using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using backend.Data.Models;

namespace backend.Tests.Infrastructure;

/// <summary>
/// Factory for creating isolated DbContext instances for testing with proper IDENTITY column support
/// </summary>
public class TestDbContextFactory
{
    private readonly string _databaseName;
    private readonly ServiceProvider _serviceProvider;
    
    public TestDbContextFactory()
    {
        _databaseName = $"TestDb_{Guid.NewGuid():N}";
        
        var services = new ServiceCollection();
        services.AddDbContext<EPRLiveDBContext>(options =>
        {
            options.UseInMemoryDatabase(_databaseName);
            options.ConfigureWarnings(w =>
                w.Ignore(Microsoft.EntityFrameworkCore.Diagnostics.InMemoryEventId.TransactionIgnoredWarning));
        });
        
        _serviceProvider = services.BuildServiceProvider();
    }
    
    /// <summary>
    /// Creates a new DbContext instance with proper configuration for IDENTITY columns
    /// </summary>
    public EPRLiveDBContext CreateContext()
    {
        var context = new TestDbContextWithIdentitySupport(
            new DbContextOptionsBuilder<EPRLiveDBContext>()
                .UseInMemoryDatabase(_databaseName)
                .Options);
                
        context.Database.EnsureCreated();
        return context;
    }
    
    /// <summary>
    /// Creates a new isolated scope with its own DbContext
    /// </summary>
    public IServiceScope CreateScope()
    {
        return _serviceProvider.CreateScope();
    }
    
    public void Dispose()
    {
        _serviceProvider?.Dispose();
    }
}

/// <summary>
/// Custom DbContext that properly handles IDENTITY columns for in-memory testing
/// </summary>
public class TestDbContextWithIdentitySupport : EPRLiveDBContext
{
    public TestDbContextWithIdentitySupport(DbContextOptions<EPRLiveDBContext> options)
        : base(options) { }
    
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);
        
        // Override the ValueGeneratedNever() configuration for PayStub and PayStubDetail
        // to allow in-memory database to generate IDs
        modelBuilder.Entity<PayStub>(entity =>
        {
            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd(); // Override to enable auto-generation
        });
        
        modelBuilder.Entity<PayStubDetail>(entity =>
        {
            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd(); // Override to enable auto-generation
        });
        
        // For other entities that might have similar issues
        modelBuilder.Entity<TimeSheet>(entity =>
        {
            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd();
        });
        
        // Handle other in-memory database limitations
        modelBuilder.Entity<ReportLineItemDetail>().Ignore(e => e.VariantValue);
        
        // Configure primary key for entities that are keyless in production
        modelBuilder.Entity<AgreementClassification>()
            .HasKey(e => new { e.AgreementId, e.ClassificationNameId });
            
        // Override EmployersToAgreement to allow ID generation
        modelBuilder.Entity<EmployersToAgreement>()
            .Property(e => e.Id)
            .ValueGeneratedOnAdd();
    }
    
    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        // For in-memory database, we need to handle ID generation manually since IDENTITY is not supported
        var addedEntries = ChangeTracker.Entries()
            .Where(e => e.State == EntityState.Added)
            .ToList();
            
        foreach (var entry in addedEntries)
        {
            if (entry.Entity is PayStub || entry.Entity is PayStubDetail || entry.Entity is TimeSheet)
            {
                var idProperty = entry.Property("Id");
                if (idProperty.CurrentValue is int idValue && idValue == 0)
                {
                    // Generate a unique ID for in-memory testing
                    var entityType = entry.Entity.GetType();
                    var maxId = await GetMaxIdForEntity(entityType);
                    idProperty.CurrentValue = maxId + 1;
                }
            }
        }
        
        return await base.SaveChangesAsync(cancellationToken);
    }
    
    private async Task<int> GetMaxIdForEntity(Type entityType)
    {
        if (entityType == typeof(PayStub))
        {
            var maxId = await PayStubs.AsNoTracking().Select(x => x.Id).DefaultIfEmpty(0).MaxAsync();
            return maxId;
        }
        else if (entityType == typeof(PayStubDetail))
        {
            var maxId = await PayStubDetails.AsNoTracking().Select(x => x.Id).DefaultIfEmpty(0).MaxAsync();
            return maxId;
        }
        else if (entityType == typeof(TimeSheet))
        {
            var maxId = await TimeSheets.AsNoTracking().Select(x => x.Id).DefaultIfEmpty(0).MaxAsync();
            return maxId;
        }
        
        return 0;
    }
}