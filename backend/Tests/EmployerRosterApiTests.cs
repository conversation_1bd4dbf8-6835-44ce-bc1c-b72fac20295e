using FluentAssertions;
using Microsoft.AspNetCore.Mvc.Testing;
using Xunit;

namespace backend.Tests;

public class EmployerRosterApiTests : TestBase
{
    public EmployerRosterApiTests(TestWebApplicationFactory factory)
        : base(factory) { }

    [Fact]
    public async Task GetEmployersByChapterId_BasicQuery_ReturnsData()
    {
        // Arrange
        await SeedTestDataAsync();
        var chapterId = CreateNodeId("Chapter", 1);

        var query =
            @"
            query GetEmployersByChapterId($chapterId: ID!) {
                employersByChapterId(chapterId: $chapterId) {
                    nodes {
                        id
                        fein
                        dba
                        organization {
                            name
                        }
                    }
                    totalCount
                }
            }";

        // Act
        var result = await ExecuteAuthenticatedGraphQLQueryAsync<EmployersByChapterIdResponse>(
            query,
            new { chapterId }
        );

        // Assert
        result.Should().NotBeNull();
        result.EmployersByChapterId.Should().NotBeNull();
        result.EmployersByChapterId.Nodes.Should().NotBeNull();
    }

    [Fact]
    public async Task EmployerRosterByChapterId_WithChapterId_ReturnsCorrectData()
    {
        // Arrange
        await SeedTestDataAsync();
        var chapterId = CreateNodeId("Chapter", 1);
        var first = 5;

        var query =
            @"
            query EmployerRosterByChapterId($chapterId: ID!, $first: Int!) {
                employerRosterByChapterId(chapterId: $chapterId, first: $first) {
                    edges {
                        node {
                            id
                            name
                            fein
                        }
                    }
                    pageInfo {
                        hasNextPage
                        endCursor
                    }
                    totalCount
                }
            }";

        // Act
        var result = await ExecuteAuthenticatedGraphQLQueryAsync<EmployerRosterByChapterIdResponse>(
            query,
            new { chapterId, first }
        );

        // Assert
        result.Should().NotBeNull();
        result.EmployerRosterByChapterId.Should().NotBeNull();
        result.EmployerRosterByChapterId.Edges.Should().NotBeNull();
    }

    [Fact]
    public async Task CanConnect_ToGraphQLEndpoint()
    {
        // Act
        var response = await HttpClient.GetAsync("/graphql");

        // Assert
        // GraphQL endpoints typically return 405 Method Not Allowed for GET requests
        // or 400 Bad Request, but should not return 404
        response.StatusCode.Should().NotBe(System.Net.HttpStatusCode.NotFound);
    }
}
