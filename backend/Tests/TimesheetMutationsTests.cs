using System.Text.Json;
using System.Text.Json.Serialization;
using backend.Data.Models;
using backend.Tests.Infrastructure;
using backend.Types.Payloads;
using backend.Types.Relay;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit;

namespace backend.Tests;

/// <summary>
/// Tests for timesheet mutations, specifically the AddEmptyPayStub mutation
/// </summary>
public class TimesheetMutationsTests : TestBase
{
    private readonly TestDataBuilder _testDataBuilder;

    // Generates unique, non-colliding IDs within this test run (for legacy tests)
    private static int NextId() => Random.Shared.Next(100_000, 999_999);

    public TimesheetMutationsTests(TestWebApplicationFactory factory)
        : base(factory) 
    {
        _testDataBuilder = GetTestDataBuilder();
    }

    [Fact]
    public async Task AddEmptyPayStub_WithValidInput_ReturnsPayStubEdge()
    {
        // Arrange
        await SeedTestDataAsync();
        ClearChangeTracker();

        // Create a test timesheet using TestDataBuilder
        var employeeId = 90002; // Use existing test employee from SeedTestDataAsync
        var timeSheet = await _testDataBuilder.CreateTimeSheetAsync(
            name: "Test Timesheet",
            status: "Draft",
            type: "Regular",
            employerGuid: Guid.Parse("11111111-1111-1111-1111-111111111111")
        );

        var mutation =
            @"
            mutation AddEmptyPayStubMutation($input: AddEmptyPayStubInput!) {
                addEmptyPayStub(input: $input) {
                    payStubEdge {
                        cursor
                        node {
                            id
                            timeSheetId
                            employeeId
                        }
                    }
                    errors
                }
            }";

        var variables = new
        {
            input = new
            {
                timeSheetId = FormatGlobalId("TimeSheet", timeSheet.Id),
                employeeId = FormatGlobalId("Employee", employeeId), // Test employee from SeedTestDataAsync
            },
        };

        // Act
        // Create and set JWT token
        var testEmployerGuid = Guid.Parse("11111111-1111-1111-1111-111111111111");
        var token = CreateTestJwtToken(testEmployerGuid);
        HttpClient.DefaultRequestHeaders.Authorization =
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);

        var response = await ExecuteGraphQLQueryWithErrorsAsync<AddEmptyPayStubMutationResponse>(
            mutation,
            variables
        );

        // Assert
        // Debug: Check for GraphQL errors first
        if (response.Errors != null && response.Errors.Length > 0)
        {
            var errorMessages = string.Join("; ", response.Errors.Select(e => e.Message));
            Assert.Fail($"GraphQL errors occurred: {errorMessages}");
        }

        // Debug: Check what we actually got
        if (response.Data == null)
        {
            Assert.Fail("Response.Data is null");
        }

        if (response.Data.AddEmptyPayStub == null)
        {
            Assert.Fail("AddEmptyPayStub is null");
        }

        Assert.NotNull(response.Data);
        Assert.NotNull(response.Data.AddEmptyPayStub);
        Assert.NotNull(response.Data.AddEmptyPayStub.PayStubEdge);
        Assert.NotNull(response.Data.AddEmptyPayStub.PayStubEdge.Node);
        Assert.Empty(response.Data.AddEmptyPayStub.Errors);

        // Verify the PayStub edge structure
        var payStubEdge = response.Data.AddEmptyPayStub.PayStubEdge;
        Assert.NotNull(payStubEdge.Cursor);
        Assert.NotEmpty(payStubEdge.Cursor);

        // Verify the PayStub node properties
        var payStub = payStubEdge.Node;
        Assert.NotNull(payStub.Id);
        Assert.Equal(timeSheet.Id, payStub.TimeSheetId);
        Assert.Equal(FormatGlobalId("Employee", employeeId), payStub.EmployeeId);
        // totalHours is now computed automatically from details, no need to assert a specific value

        // Verify the PayStub was actually created in the database
        ClearChangeTracker(); // Clear before querying
        var createdPayStub = await DbContext.PayStubs.FirstOrDefaultAsync(ps =>
            ps.TimeSheetId == timeSheet.Id && ps.EmployeeId == employeeId
        );
        Assert.NotNull(createdPayStub);
        // totalHours is now computed automatically from details, verify it's calculated correctly
        Assert.Empty(createdPayStub.Details);
    }

    [Fact]
    public async Task AddEmptyPayStub_WithNonExistentTimeSheet_ReturnsError()
    {
        // Arrange
        await SeedTestDataAsync();

        var mutation =
            @"
            mutation AddEmptyPayStubMutation($input: AddEmptyPayStubInput!) {
                addEmptyPayStub(input: $input) {
                    payStubEdge {
                        cursor
                        node {
                            id
                        }
                    }
                    errors
                }
            }";

        var employeeId = 90002; // Use existing test employee from SeedTestDataAsync
        var variables = new
        {
            input = new
            {
                timeSheetId = FormatGlobalId("TimeSheet", 99999), // Non-existent timesheet
                employeeId = FormatGlobalId("Employee", employeeId),
            },
        };

        // Act
        // Create and set JWT token
        var testEmployerGuid = Guid.Parse("11111111-1111-1111-1111-111111111111");
        var token = CreateTestJwtToken(testEmployerGuid);
        HttpClient.DefaultRequestHeaders.Authorization =
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);

        var response = await ExecuteGraphQLQueryWithErrorsAsync<AddEmptyPayStubMutationResponse>(
            mutation,
            variables
        );

        // Assert
        Assert.NotNull(response.Data);
        Assert.NotNull(response.Data.AddEmptyPayStub);
        Assert.Null(response.Data.AddEmptyPayStub.PayStubEdge);
        Assert.NotEmpty(response.Data.AddEmptyPayStub.Errors);
        Assert.Contains("TimeSheet not found", response.Data.AddEmptyPayStub.Errors);
    }

    [Fact]
    public async Task AddEmptyPayStub_WithNonExistentEmployee_ReturnsError()
    {
        // Arrange
        await SeedTestDataAsync();
        ClearChangeTracker();

        // Create a test timesheet using TestDataBuilder
        var timeSheet = await _testDataBuilder.CreateTimeSheetAsync(
            name: "Test Timesheet",
            status: "Draft",
            type: "Regular",
            employerGuid: Guid.Parse("11111111-1111-1111-1111-111111111111")
        );

        var mutation =
            @"
            mutation AddEmptyPayStubMutation($input: AddEmptyPayStubInput!) {
                addEmptyPayStub(input: $input) {
                    payStubEdge {
                        cursor
                        node {
                            id
                        }
                    }
                    errors
                }
            }";

        var variables = new
        {
            input = new
            {
                timeSheetId = FormatGlobalId("TimeSheet", timeSheet.Id),
                employeeId = FormatGlobalId("Employee", 99999), // Non-existent employee
            },
        };

        // Act
        // Create and set JWT token
        var testEmployerGuid = Guid.Parse("11111111-1111-1111-1111-111111111111");
        var token = CreateTestJwtToken(testEmployerGuid);
        HttpClient.DefaultRequestHeaders.Authorization =
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);

        var response = await ExecuteGraphQLQueryWithErrorsAsync<AddEmptyPayStubMutationResponse>(
            mutation,
            variables
        );

        // Assert
        Assert.NotNull(response.Data);
        Assert.NotNull(response.Data.AddEmptyPayStub);
        Assert.Null(response.Data.AddEmptyPayStub.PayStubEdge);
        Assert.NotEmpty(response.Data.AddEmptyPayStub.Errors);
        Assert.Contains("Employee not found", response.Data.AddEmptyPayStub.Errors);
    }

    [Fact]
    public async Task AddEmptyPayStub_WithDuplicateEmployeeTimesheet_ReturnsError()
    {
        // Arrange
        await SeedTestDataAsync();
        ClearChangeTracker();

        // Create a test timesheet using TestDataBuilder
        var employeeId = 90002; // Use existing test employee from SeedTestDataAsync
        var timeSheet = await _testDataBuilder.CreateTimeSheetAsync(
            name: "Test Timesheet",
            status: "Draft",
            type: "Regular",
            employerGuid: Guid.Parse("11111111-1111-1111-1111-111111111111")
        );

        // Create an existing PayStub for the same employee/timesheet combination
        var existingPayStub = await _testDataBuilder.CreatePayStubAsync(
            timeSheet.Id,
            employeeId,
            "Existing PayStub"
        );

        var mutation =
            @"
            mutation AddEmptyPayStubMutation($input: AddEmptyPayStubInput!) {
                addEmptyPayStub(input: $input) {
                    payStubEdge {
                        cursor
                        node {
                            id
                        }
                    }
                    errors
                }
            }";

        var variables = new
        {
            input = new
            {
                timeSheetId = FormatGlobalId("TimeSheet", timeSheet.Id),
                employeeId = FormatGlobalId("Employee", employeeId),
            },
        };

        // Act
        // Create and set JWT token
        var testEmployerGuid = Guid.Parse("11111111-1111-1111-1111-111111111111");
        var token = CreateTestJwtToken(testEmployerGuid);
        HttpClient.DefaultRequestHeaders.Authorization =
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);

        var response = await ExecuteGraphQLQueryWithErrorsAsync<AddEmptyPayStubMutationResponse>(
            mutation,
            variables
        );

        // Assert
        Assert.NotNull(response.Data);
        Assert.NotNull(response.Data.AddEmptyPayStub);
        Assert.Null(response.Data.AddEmptyPayStub.PayStubEdge);
        Assert.NotEmpty(response.Data.AddEmptyPayStub.Errors);
        Assert.Contains(
            "PayStub already exists for this employee in this timesheet",
            response.Data.AddEmptyPayStub.Errors
        );
    }

    [Fact]
    public async Task AddEmptyPayStub_CursorFormat_IsValidRelayNodeId()
    {
        // Arrange
        await SeedTestDataAsync();
        ClearChangeTracker();

        // Create a test timesheet using TestDataBuilder
        var employeeId = 90002; // Use existing test employee from SeedTestDataAsync
        var timeSheet = await _testDataBuilder.CreateTimeSheetAsync(
            name: "Test Timesheet",
            status: "Draft",
            type: "Regular",
            employerGuid: Guid.Parse("11111111-1111-1111-1111-111111111111")
        );

        var mutation =
            @"
            mutation AddEmptyPayStubMutation($input: AddEmptyPayStubInput!) {
                addEmptyPayStub(input: $input) {
                    payStubEdge {
                        cursor
                        node {
                            id
                        }
                    }
                    errors
                }
            }";

        var variables = new
        {
            input = new
            {
                timeSheetId = FormatGlobalId("TimeSheet", timeSheet.Id),
                employeeId = FormatGlobalId("Employee", employeeId),
            },
        };

        // Act
        // Create and set JWT token
        var testEmployerGuid = Guid.Parse("11111111-1111-1111-1111-111111111111");
        var token = CreateTestJwtToken(testEmployerGuid);
        HttpClient.DefaultRequestHeaders.Authorization =
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);

        var response = await ExecuteGraphQLQueryWithErrorsAsync<AddEmptyPayStubMutationResponse>(
            mutation,
            variables
        );

        // Assert
        Assert.NotNull(response.Data?.AddEmptyPayStub?.PayStubEdge);

        var cursor = response.Data.AddEmptyPayStub.PayStubEdge.Cursor;
        Assert.NotNull(cursor);
        Assert.NotEmpty(cursor);

        // Verify cursor is a valid base64 encoded Relay Global ID
        // The cursor should be in the format "PayStub:{integer}" encoded in base64
        try
        {
            var decodedBytes = Convert.FromBase64String(cursor);
            var decodedString = System.Text.Encoding.UTF8.GetString(decodedBytes);

            Assert.StartsWith("PayStub:", decodedString);

            // Extract the integer part and verify it's a valid integer
            var idPart = decodedString.Substring("PayStub:".Length);
            Assert.True(int.TryParse(idPart, out var parsedId), "Cursor should contain a valid integer ID");
            Assert.True(parsedId > 0, "PayStub ID should be positive");
        }
        catch (FormatException)
        {
            Assert.Fail("Cursor should be valid base64");
        }
    }

    [Fact]
    public async Task PayStub_ComputedTotalHours_MatchesSumOfDetailHours()
    {
        // Arrange
        await SeedTestDataAsync();
        ClearChangeTracker();

        // Create test data with specific known hours using TestDataBuilder
        var employeeId = 90002; // Use existing test employee from SeedTestDataAsync
        var timeSheet = await _testDataBuilder.CreateTimeSheetAsync(
            name: "Test Timesheet for Computed Hours",
            status: "Draft",
            type: "Regular",
            employerGuid: Guid.Parse("11111111-1111-1111-1111-111111111111")
        );

        // Create PayStub with specific details
        var payStub = await _testDataBuilder.CreatePayStubWithDetailsAsync(
            timeSheet.Id,
            employeeId,
            (DateOnly.FromDateTime(DateTime.Now.AddDays(-2)), 8.0f, 2.0f, 1.0f),
            (DateOnly.FromDateTime(DateTime.Now.AddDays(-1)), 7.5f, 1.5f, 0.0f)
        );

        // Act - Verify computed totalHours property using direct database access
        var retrievedPayStub = await DbContext.PayStubs
            .Include(ps => ps.Details)
            .FirstOrDefaultAsync(ps => ps.Id == payStub.Id);

        // Assert
        Assert.NotNull(retrievedPayStub);
        Assert.NotEmpty(retrievedPayStub.Details);
        
        // Calculate expected total from details
        var expectedTotal = retrievedPayStub.Details.Sum(d => (d.STHours ?? 0) + (d.OTHours ?? 0) + (d.DTHours ?? 0));
        var actualTotal = retrievedPayStub.TotalHours; // This should be computed automatically
        
        Assert.Equal(expectedTotal, actualTotal);
        Assert.Equal(20.0f, actualTotal); // (8.0 + 2.0 + 1.0) + (7.5 + 1.5 + 0.0) = 20.0

        // Test edge case: PayStub with no details should have totalHours = 0
        var emptyPayStub = await _testDataBuilder.CreatePayStubAsync(
            timeSheet.Id,
            employeeId,
            "Empty PayStub"
        );

        ClearChangeTracker();
        var retrievedEmptyPayStub = await DbContext.PayStubs
            .Include(ps => ps.Details)
            .FirstOrDefaultAsync(ps => ps.Id == emptyPayStub.Id);

        Assert.NotNull(retrievedEmptyPayStub);
        Assert.Empty(retrievedEmptyPayStub.Details);
        Assert.Equal(0.0f, retrievedEmptyPayStub.TotalHours);
    }

    /// <summary>
    /// Response classes for AddEmptyPayStub mutation testing
    /// </summary>
    protected class AddEmptyPayStubMutationResponse
    {
        [JsonPropertyName("addEmptyPayStub")]
        public AddEmptyPayStubPayloadResponse AddEmptyPayStub { get; set; } = new();
    }

    protected class AddEmptyPayStubPayloadResponse
    {
        [JsonPropertyName("payStubEdge")]
        public PayStubEdgeResponse? PayStubEdge { get; set; }

        [JsonPropertyName("errors")]
        public List<string> Errors { get; set; } = new();
    }

    protected class PayStubEdgeResponse
    {
        [JsonPropertyName("cursor")]
        public string Cursor { get; set; } = string.Empty;

        [JsonPropertyName("node")]
        public PayStubNodeResponse Node { get; set; } = new();
    }

    protected class PayStubNodeResponse
    {
        [JsonPropertyName("id")]
        public string Id { get; set; } = string.Empty;

        [JsonPropertyName("timeSheetId")]
        public int TimeSheetId { get; set; }

        [JsonPropertyName("employeeId")]
        public string EmployeeId { get; set; } = string.Empty;

        // totalHours is now computed automatically and not included in the response
    }

    [Fact]
    public async Task ModifyTimeSheet_WithExplicitDeletePolicyEnabled_OnlyDeletesMarkedPayStubs()
    {
        // Arrange
        await SeedTestDataAsync();
        ClearChangeTracker();

        var employee1Id = 90002;
        var employee2Id = 90003;
        var employee3Id = 90004;
        var employerGuid = Guid.Parse("11111111-1111-1111-1111-111111111111");

        var timeSheet = await _testDataBuilder.CreateTimeSheetAsync("Test Timesheet", "Draft", "Regular", employerGuid);
        await _testDataBuilder.CreatePayStubAsync(timeSheet.Id, employee1Id, "Employee 1 PayStub");
        await _testDataBuilder.CreatePayStubAsync(timeSheet.Id, employee2Id, "Employee 2 PayStub");

        ClearChangeTracker();

        var mutation = @"
            mutation ModifyTimeSheetMutation($input: ModifyTimeSheetInput!) {
                modifyTimeSheet(input: $input) {
                    timeSheet {
                        id
                        payStubs(first: 100) {
                            edges {
                                node {
                                    id
                                    employeeId
                                }
                            }
                        }
                    }
                }
            }";

        var variables = new
        {
            input = new
            {
                id = FormatGlobalId("TimeSheet", timeSheet.Id),
                employerGuid = employerGuid.ToString(),
                addPayStubs = new[]
                {
                    new
                    {
                        employeeId = FormatGlobalId("Employee", employee3Id),
                        name = "Employee 3 PayStub",
                        details = new object[0]
                    }
                }
            }
        };

        // Act
        var token = CreateTestJwtToken(employerGuid);
        HttpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
        var response = await ExecuteGraphQLQueryWithErrorsAsync<ModifyTimesheetResponse>(mutation, variables);

        // Assert
        // Debug: Check for GraphQL errors first
        if (response.Errors != null && response.Errors.Length > 0)
        {
            var errorMessages = string.Join("; ", response.Errors.Select(e => e.Message));
            Assert.Fail($"GraphQL errors occurred: {errorMessages}");
        }
        
        Assert.NotNull(response.Data?.ModifyTimeSheet?.TimeSheet);
        var payStubs = response.Data.ModifyTimeSheet.TimeSheet.PayStubs.Edges;
        Assert.Equal(3, payStubs.Count);

        var employeeIds = payStubs.Select(e => e.Node.EmployeeId).ToHashSet();
        Assert.Contains(FormatGlobalId("Employee", employee1Id), employeeIds);
        Assert.Contains(FormatGlobalId("Employee", employee2Id), employeeIds);
        Assert.Contains(FormatGlobalId("Employee", employee3Id), employeeIds);
    }

    [Fact]
    public async Task ModifyTimeSheet_WithExplicitDeletePolicyEnabled_DeletesMarkedPayStubs()
    {
        // Arrange
        await SeedTestDataAsync();
        ClearChangeTracker();

        var employee1Id = 90002;
        var employee2Id = 90003;
        var employerGuid = Guid.Parse("11111111-1111-1111-1111-111111111111");

        var timeSheet = await _testDataBuilder.CreateTimeSheetAsync("Test Timesheet", "Draft", "Regular", employerGuid);
        var payStub1 = await _testDataBuilder.CreatePayStubAsync(timeSheet.Id, employee1Id, "Employee 1 PayStub");
        var payStub2 = await _testDataBuilder.CreatePayStubAsync(timeSheet.Id, employee2Id, "Employee 2 PayStub");

        ClearChangeTracker();

        var mutation = @"
            mutation ModifyTimeSheetMutation($input: ModifyTimeSheetInput!) {
                modifyTimeSheet(input: $input) {
                    timeSheet {
                        id
                        payStubs(first: 100) {
                            edges {
                                node {
                                    id
                                    employeeId
                                }
                            }
                        }
                    }
                }
            }";

        var variables = new
        {
            input = new
            {
                id = FormatGlobalId("TimeSheet", timeSheet.Id),
                employerGuid = employerGuid.ToString(),
                deletePayStubIds = new[]
                {
                    payStub1.Id
                },
                modifyPayStubs = new[]
                {
                    new
                    {
                        id = FormatGlobalId("PayStub", payStub2.Id),
                        employeeId = FormatGlobalId("Employee", employee2Id),
                        name = "Employee 2 PayStub",
                        details = new object[0]
                    }
                }
            }
        };

        // Act
        var token = CreateTestJwtToken(employerGuid);
        HttpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
        var response = await ExecuteGraphQLQueryWithErrorsAsync<ModifyTimesheetResponse>(mutation, variables);

        // Assert
        Assert.NotNull(response.Data?.ModifyTimeSheet?.TimeSheet);
        var payStubs = response.Data.ModifyTimeSheet.TimeSheet.PayStubs.Edges;
        Assert.Single(payStubs);
        Assert.Equal(FormatGlobalId("Employee", employee2Id), payStubs[0].Node.EmployeeId);
    }

    // Response classes for the new tests  
    public class ModifyTimesheetResponse
    {
        [JsonPropertyName("modifyTimeSheet")]
        public ModifyTimesheetData ModifyTimeSheet { get; set; } = new();
    }

    public class ModifyTimesheetData
    {
        [JsonPropertyName("timeSheet")]
        public TimesheetWithPayStubs TimeSheet { get; set; } = new();
    }

    public class TimesheetWithPayStubs
    {
        [JsonPropertyName("id")]
        public string Id { get; set; } = string.Empty;

        [JsonPropertyName("payStubs")]
        public PayStubConnection PayStubs { get; set; } = new();
    }

    public class PayStubConnection
    {
        [JsonPropertyName("edges")]
        public List<PayStubEdge> Edges { get; set; } = new();
    }

    public class PayStubEdge
    {
        [JsonPropertyName("node")]
        public PayStubNode Node { get; set; } = new();
    }

    public class PayStubNode
    {
        [JsonPropertyName("id")]
        public string Id { get; set; } = string.Empty;

        [JsonPropertyName("employeeId")]
        public string EmployeeId { get; set; } = string.Empty;
    }

    [Fact(Skip="Legacy ExplicitDeletePolicy removed")]
    public async Task ModifyTimeSheet_WithExplicitDeletePolicyDisabled_IgnoresExplicitDeleteFlags()
    {
        // Arrange - Test with feature flag disabled (legacy behavior)
        await SeedTestDataAsync();
        ClearChangeTracker();

        var employee1Id = 90002;
        var employee2Id = 90003;
        var employerGuid = Guid.Parse("11111111-1111-1111-1111-111111111111");

        var timeSheet = await _testDataBuilder.CreateTimeSheetAsync("Test Timesheet", "Draft", "Regular", employerGuid);
        var payStub1 = await _testDataBuilder.CreatePayStubAsync(timeSheet.Id, employee1Id, "Employee 1 PayStub");
        await _testDataBuilder.CreatePayStubAsync(timeSheet.Id, employee2Id, "Employee 2 PayStub");

        ClearChangeTracker();

        var mutation = @"
            mutation ModifyTimeSheetMutation($input: ModifyTimeSheetInput!) {
                modifyTimeSheet(input: $input) {
                    timeSheet {
                        id
                        payStubs(first: 100) {
                            edges {
                                node {
                                    id
                                    employeeId
                                }
                            }
                        }
                    }
                }
            }";

        var variables = new
        {
            input = new
            {
                id = FormatGlobalId("TimeSheet", timeSheet.Id),
                employerGuid = employerGuid.ToString(),
                payStubs = new[]
                {
                    new
                    {
                        id = FormatGlobalId("PayStub", payStub1.Id),
                        employeeId = FormatGlobalId("Employee", employee1Id),
                        name = "Employee 1 PayStub",
                        details = new object[0]
                    }
                }
            }
        };

        // Act
        var token = CreateTestJwtToken(employerGuid);
        HttpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
        var response = await ExecuteGraphQLQueryAsync<ModifyTimesheetResponse>(mutation, variables);

        // Assert
        Assert.NotNull(response.ModifyTimeSheet?.TimeSheet);
        var payStubs = response.ModifyTimeSheet.TimeSheet.PayStubs.Edges;
        Assert.Single(payStubs);
        Assert.Equal(FormatGlobalId("Employee", employee1Id), payStubs[0].Node.EmployeeId);
    }

    [Fact]
    public async Task ModifyTimeSheet_WithExplicitDeletePolicyEnabled_MixedScenarios()
    {
        // Arrange
        await SeedTestDataAsync();
        ClearChangeTracker();

        var employee1Id = 90002;
        var employee2Id = 90003;
        var employee3Id = 90004;
        var employerGuid = Guid.Parse("11111111-1111-1111-1111-111111111111");

        var timeSheet = await _testDataBuilder.CreateTimeSheetAsync("Test Timesheet", "Draft", "Regular", employerGuid);
        var payStub1 = await _testDataBuilder.CreatePayStubAsync(timeSheet.Id, employee1Id, "Employee 1 PayStub");
        var payStub2 = await _testDataBuilder.CreatePayStubAsync(timeSheet.Id, employee2Id, "Employee 2 PayStub");
        await _testDataBuilder.CreatePayStubAsync(timeSheet.Id, employee3Id, "Employee 3 PayStub");

        ClearChangeTracker();

        var mutation = @"
            mutation ModifyTimeSheetMutation($input: ModifyTimeSheetInput!) {
                modifyTimeSheet(input: $input) {
                    timeSheet {
                        id
                        payStubs(first: 100) {
                            edges {
                                node {
                                    id
                                    employeeId
                                }
                            }
                        }
                    }
                }
            }";

        var variables = new
        {
            input = new
            {
                id = FormatGlobalId("TimeSheet", timeSheet.Id),
                employerGuid = employerGuid.ToString(),
                deletePayStubIds = new[]
                {
                    payStub1.Id
                },
                modifyPayStubs = new[]
                {
                    new
                    {
                        id = FormatGlobalId("PayStub", payStub2.Id),
                        employeeId = FormatGlobalId("Employee", employee2Id),
                        name = "Employee 2 PayStub",
                        details = new object[0]
                    }
                }
            }
        };

        // Act
        var token = CreateTestJwtToken(employerGuid);
        HttpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
        var response = await ExecuteGraphQLQueryWithErrorsAsync<ModifyTimesheetResponse>(mutation, variables);

        // Assert
        Assert.NotNull(response.Data?.ModifyTimeSheet?.TimeSheet);
        var payStubs = response.Data.ModifyTimeSheet.TimeSheet.PayStubs.Edges;
        Assert.Equal(2, payStubs.Count);

        var employeeIds = payStubs.Select(e => e.Node.EmployeeId).ToHashSet();
        Assert.DoesNotContain(FormatGlobalId("Employee", employee1Id), employeeIds);
        Assert.Contains(FormatGlobalId("Employee", employee2Id), employeeIds);
        Assert.Contains(FormatGlobalId("Employee", employee3Id), employeeIds);
    }

    [Fact(Skip="Legacy ExplicitDeletePolicy removed")]
    public async Task ModifyTimeSheet_WithMissingFeatureFlag_UsesDefaultBehavior()
    {
        // Arrange - Test behavior when feature flag configuration is missing/invalid
        await SeedTestDataAsync();

        // This test relies on the fact that the default value in the code is 'false'
        // when the configuration key is missing
        var timeSheetId = NextId();
        var employee1Id = 90002;
        var employee2Id = 90003;
        
        var timeSheet = new TimeSheet
        {
            Id = timeSheetId,
            Name = "Test Timesheet",
            PayPeriodEndDate = DateOnly.FromDateTime(DateTime.Now),
            Status = "Draft",
            Type = "Regular",
            EmployerGuid = Guid.Parse("11111111-1111-1111-1111-111111111111"),
            CreationDate = DateTimeOffset.UtcNow,
            ModificationDate = DateTimeOffset.UtcNow,
            CreatedByUserId = "test-user",
            ModifiedByUserId = "test-user",
        };

        var payStub1 = new PayStub
        {
            TimeSheetId = timeSheetId,
            EmployeeId = employee1Id,
            Name = "Employee 1 PayStub",
            Details = new List<PayStubDetail>()
        };

        var payStub2 = new PayStub
        {
            TimeSheetId = timeSheetId,
            EmployeeId = employee2Id,
            Name = "Employee 2 PayStub",
            Details = new List<PayStubDetail>()
        };

        timeSheet.PayStubs = new List<PayStub> { payStub1, payStub2 };

        DbContext.TimeSheets.Add(timeSheet);
        await DbContext.SaveChangesAsync();

        var mutation = @"
            mutation ModifyTimeSheetMutation($input: ModifyTimeSheetInput!) {
                modifyTimeSheet(input: $input) {
                    timeSheet {
                        id
                        payStubs(first: 100) {
                            edges {
                                node {
                                    id
                                    employeeId
                                }
                            }
                        }
                    }
                }
            }";

        // Include only one pay stub, testing default behavior when config is missing
        var variables = new
        {
            input = new
            {
                id = timeSheetId,
                employerGuid = "11111111-1111-1111-1111-111111111111",
                payStubs = new[]
                {
                    new
                    {
                        id = payStub1.Id,
                        employeeId = FormatGlobalId("Employee", employee1Id),
                        name = "Employee 1 PayStub",
                        details = new object[0]
                    }
                }
            }
        };

        // Act
        var testEmployerGuid = Guid.Parse("11111111-1111-1111-1111-111111111111");
        var token = CreateTestJwtToken(testEmployerGuid);
        HttpClient.DefaultRequestHeaders.Authorization =
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);

        var response = await ExecuteGraphQLQueryAsync<ModifyTimesheetResponse>(mutation, variables);

        // Assert
        Assert.NotNull(response.ModifyTimeSheet);
        Assert.NotNull(response.ModifyTimeSheet.TimeSheet);

        var payStubs = response.ModifyTimeSheet.TimeSheet.PayStubs.Edges;
        
        // With default behavior (false), should use legacy deletion logic
        // So only 1 pay stub should remain (payStub2 gets deleted)
        Assert.Single(payStubs);
        Assert.Equal(FormatGlobalId("Employee", employee1Id), payStubs[0].Node.EmployeeId);
    }

    /// <summary>
    /// Helper method to format Global IDs for GraphQL
    /// </summary>
    private static string FormatGlobalId(string typeName, object id)
    {
        var globalIdString = $"{typeName}:{id}";
        var bytes = System.Text.Encoding.UTF8.GetBytes(globalIdString);
        return Convert.ToBase64String(bytes);
    }
}
