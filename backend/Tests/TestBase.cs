using System.IdentityModel.Tokens.Jwt;
using System.Net.Http.Headers;
using System.Security.Claims;
using System.Text;
using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.Json.Serialization;
using backend.Data.Models;
using backend.Tests.Infrastructure;
using backend.Utils;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using OpenIddict.Abstractions;
using Serilog;
using Serilog.Extensions.Logging;
using Xunit;

namespace backend.Tests;

/// <summary>
/// Isolated test factory that creates completely separate service instances
/// Each test gets its own host with no shared global state
/// </summary>
public class TestWebApplicationFactory : WebApplicationFactory<Program>
{
    private readonly string _uniqueId = Guid.NewGuid().ToString("N")[..8];

    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        // Isolated test environment
        builder.UseEnvironment($"Testing-{_uniqueId}");

        // Set the content root to the backend directory
        var contentRoot = System.IO.Path.GetFullPath(
            System.IO.Path.Combine(AppContext.BaseDirectory, "..", "..", "..", "..")
        );
        builder.UseContentRoot(contentRoot);

        builder.ConfigureAppConfiguration(
            (context, config) =>
            {
                // Create test-specific UploadedImages directory
                var testUploadedImagesPath = System.IO.Path.Combine(
                    contentRoot,
                    "Tests",
                    "TestUploadedImages"
                );
                if (!System.IO.Directory.Exists(testUploadedImagesPath))
                {
                    System.IO.Directory.CreateDirectory(testUploadedImagesPath);
                }

                // Override the content root path for UploadedImages
                Environment.SetEnvironmentVariable("UPLOADEDIMAGES_PATH", testUploadedImagesPath);
            }
        );

        // Detailed service registration logging for debugging
        builder.ConfigureServices(services =>
        {
            LogServiceRegistrations(services, "BEFORE-MODIFICATIONS");
            ConfigureSerilogIsolation(services);
            ConfigureTestDatabase(services);
            ConfigureTestAuthentication(services);
            LogServiceRegistrations(services, "AFTER-MODIFICATIONS");
        });
    }

    private void ConfigureSerilogIsolation(IServiceCollection services)
    {
        // Remove existing Serilog services to prevent conflicts
        var serilogServices = services
            .Where(s => s.ServiceType.FullName?.Contains("Serilog") == true)
            .ToList();

        Console.WriteLine($"Removing {serilogServices.Count} Serilog services for isolation");
        foreach (var service in serilogServices)
        {
            Console.WriteLine($"  - {service.ServiceType.Name}");
            services.Remove(service);
        }

        // Add isolated logger factory
        services.AddSingleton<ILoggerFactory>(provider =>
        {
            var config = new LoggerConfiguration()
                .WriteTo.Debug(outputTemplate: $"[TEST-{_uniqueId}] {{Message}}{{NewLine}}")
                .CreateLogger();

            return new SerilogLoggerFactory(config, dispose: true);
        });

        // Add generic logger
        services.AddSingleton(typeof(ILogger<>), typeof(Logger<>));
    }

    private void LogServiceRegistrations(IServiceCollection services, string phase)
    {
        var relevantServices = services
            .Where(s =>
                s.ServiceType.FullName?.Contains("Serilog") == true
                || s.ServiceType.FullName?.Contains("Authentication") == true
                || s.ServiceType.FullName?.Contains("Authorization") == true
                || s.ServiceType.FullName?.Contains("HotChocolate") == true
            )
            .ToList();

        Console.WriteLine($"[{phase}] {relevantServices.Count} relevant services registered");
        foreach (var service in relevantServices.Take(10)) // Limit output
        {
            Console.WriteLine(
                $"  {service.ServiceType.Name} -> {service.ImplementationType?.Name ?? "Factory"}"
            );
        }
    }

    private void ConfigureTestDatabase(IServiceCollection services)
    {
        // Remove DbContext registrations more comprehensively
        for (int i = services.Count - 1; i >= 0; i--)
        {
            var serviceType = services[i].ServiceType;
            if (
                serviceType == typeof(EPRLiveDBContext)
                || serviceType == typeof(DbContextOptions<EPRLiveDBContext>)
                || serviceType == typeof(DbContextOptions)
                || serviceType.Name.Contains("DbContext")
                || services[i].ImplementationType?.Name.Contains("DbContext") == true
            )
            {
                services.RemoveAt(i);
            }
        }

        // Add an in-memory database for testing
        services.AddDbContext<EPRLiveDBContext>(options =>
        {
            options.UseInMemoryDatabase("TestDb_" + _uniqueId);
            // Configure to ignore warnings that InMemory database can't handle
            options.ConfigureWarnings(w =>
                w.Ignore(
                    Microsoft
                        .EntityFrameworkCore
                        .Diagnostics
                        .InMemoryEventId
                        .TransactionIgnoredWarning
                )
            );
        });

        // Replace the DbContext with a custom one that handles InMemory limitations and IDENTITY columns
        services.AddTransient<EPRLiveDBContext>(provider =>
        {
            var options = provider.GetRequiredService<DbContextOptions<EPRLiveDBContext>>();
            return new TestDbContextWithIdentitySupport(options);
        });
    }

    private void ConfigureTestAuthentication(IServiceCollection services)
    {
        // Document what we're removing and why
        var authServicesToRemove = services
            .Where(s => IsAuthenticationService(s.ServiceType))
            .ToList();

        Console.WriteLine($"Removing {authServicesToRemove.Count} authentication services:");
        foreach (var service in authServicesToRemove)
        {
            Console.WriteLine($"  - {service.ServiceType.Name}");
            services.Remove(service);
        }

        // Add test-only authentication that properly handles unauthorized requests
        services
            .AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = "TestScheme";
                options.DefaultChallengeScheme = "TestScheme";
                options.DefaultScheme = "TestScheme";
            })
            .AddScheme<AuthenticationSchemeOptions, TestAuthenticationHandler>(
                "TestScheme",
                _ => { }
            );

        services.AddAuthorization(options =>
        {
            options.DefaultPolicy =
                new Microsoft.AspNetCore.Authorization.AuthorizationPolicyBuilder("TestScheme")
                    .RequireAuthenticatedUser()
                    .Build();

            // Add the same policies as production for testing
            options.AddPolicy(
                "RequireOrganization",
                policy =>
                    policy.Requirements.Add(new backend.Security.HasOrganizationClaimRequirement())
            );

            options.AddPolicy(
                "SystemAdminOnly",
                policy => policy.RequireRole("System Administrator")
            );
        });

        // Register the same authorization handlers as production
        services.AddSingleton<
            Microsoft.AspNetCore.Authorization.IAuthorizationHandler,
            backend.Security.HasOrganizationClaimHandler
        >();
        services.AddSingleton<
            Microsoft.AspNetCore.Authorization.IAuthorizationHandler,
            backend.Security.ChapterAccessHandler
        >();
    }

    private bool IsAuthenticationService(Type serviceType)
    {
        return serviceType.FullName?.Contains("OpenIddict") == true
            || serviceType.FullName?.Contains("Authentication") == true
            || serviceType
                == typeof(Microsoft.AspNetCore.Authentication.IAuthenticationSchemeProvider)
            || serviceType
                == typeof(Microsoft.AspNetCore.Authentication.IAuthenticationHandlerProvider);
    }
}

/// <summary>
/// Custom DbContext for testing that handles InMemory database limitations
/// </summary>
[Obsolete("Use TestDbContextWithIdentitySupport from TestDbContextFactory instead")]
public class TestEPRLiveDBContext : EPRLiveDBContext
{
    public TestEPRLiveDBContext(DbContextOptions<EPRLiveDBContext> options)
        : base(options) { }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Ignore properties that InMemory database can't handle
        modelBuilder.Entity<ReportLineItemDetail>().Ignore(e => e.VariantValue);

        // Configure primary key for entities that are keyless in production but need keys for testing
        modelBuilder
            .Entity<AgreementClassification>()
            .HasKey(e => new { e.AgreementId, e.ClassificationNameId });

        // Configure EmployersToAgreement to allow ID generation for testing
        modelBuilder.Entity<EmployersToAgreement>().Property(e => e.Id).ValueGeneratedOnAdd(); // Override the ValueGeneratedNever() for testing
    }
}

/// <summary>
/// Test authentication handler with explicit test controls for different token scenarios
/// </summary>
public class TestAuthenticationHandler : AuthenticationHandler<AuthenticationSchemeOptions>
{
    public const string VALID_TOKEN = "valid-test-token";
    public const string INVALID_TOKEN = "invalid-test-token";
    public const string EXPIRED_TOKEN = "expired-test-token";

    public TestAuthenticationHandler(
        IOptionsMonitor<AuthenticationSchemeOptions> options,
        ILoggerFactory logger,
        UrlEncoder encoder
    )
        : base(options, logger, encoder) { }

    protected override Task<AuthenticateResult> HandleAuthenticateAsync()
    {
        var authHeader = Request.Headers["Authorization"].ToString();

        if (string.IsNullOrEmpty(authHeader))
        {
            return Task.FromResult(AuthenticateResult.NoResult());
        }

        if (!authHeader.StartsWith("Bearer "))
        {
            return Task.FromResult(AuthenticateResult.Fail("Invalid header format"));
        }

        var token = authHeader[7..];

        return token switch
        {
            VALID_TOKEN => Task.FromResult(CreateSuccessResult()),
            INVALID_TOKEN => Task.FromResult(AuthenticateResult.Fail("Invalid token")),
            EXPIRED_TOKEN => Task.FromResult(AuthenticateResult.Fail("Token expired")),
            _ => Task.FromResult(HandleJwtToken(token)),
        };
    }

    protected override Task HandleChallengeAsync(AuthenticationProperties properties)
    {
        // NOTE: JWT expiration handling removed - application now uses reference tokens
        // Token validation happens via introspection endpoint in production

        // For GraphQL requests without auth or with invalid tokens, let GraphQL handle it
        // This allows GraphQL to return proper error responses in the response body
        if (Request.Path.StartsWithSegments("/graphql"))
        {
            Response.StatusCode = 200;
            return Task.CompletedTask;
        }

        // For other requests, return 401
        Response.StatusCode = 401;
        return Task.CompletedTask;
    }

    private AuthenticateResult CreateSuccessResult()
    {
        var claims = new[]
        {
            new Claim("sub", "test-user-id"),
            new Claim("username", "cheema"),
            new Claim("org_id", "90000"),
            new Claim("org_guid", "11111111-1111-1111-1111-111111111111"),
            new Claim(System.Security.Claims.ClaimTypes.Role, "Chapter Administrator"),
        };

        var identity = new ClaimsIdentity(claims, Scheme.Name);
        var principal = new ClaimsPrincipal(identity);

        return AuthenticateResult.Success(new AuthenticationTicket(principal, Scheme.Name));
    }

    private AuthenticateResult HandleJwtToken(string token)
    {
        try
        {
            var handler = new JwtSecurityTokenHandler();

            if (!handler.CanReadToken(token))
            {
                return AuthenticateResult.Fail("Invalid token format");
            }

            var jsonToken = handler.ReadToken(token) as JwtSecurityToken;
            if (jsonToken == null)
            {
                return AuthenticateResult.Fail("Invalid token");
            }

            // NOTE: JWT expiration check removed - application now uses reference tokens
            // In production, token validation happens via introspection endpoint

            // Extract claims from the JWT
            var claims = jsonToken.Claims.ToList();
            var identity = new ClaimsIdentity(claims, Scheme.Name);
            var principal = new ClaimsPrincipal(identity);

            return AuthenticateResult.Success(new AuthenticationTicket(principal, Scheme.Name));
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error in test authentication handler");
            return AuthenticateResult.Fail("Authentication error");
        }
    }
}

public abstract class TestBase : IClassFixture<TestWebApplicationFactory>
{
    protected readonly TestWebApplicationFactory Factory;
    protected readonly HttpClient HttpClient;
    private readonly IServiceScope _scope;
    protected readonly EPRLiveDBContext DbContext;

    protected TestBase(TestWebApplicationFactory factory)
    {
        // Use the new TestWebApplicationFactory directly - no need to reconfigure
        Factory = factory;

        // Create HTTP client
        HttpClient = Factory.CreateClient();
        HttpClient.DefaultRequestHeaders.Accept.Add(
            new MediaTypeWithQualityHeaderValue("application/json")
        );

        // Get database context for test data setup (keep scope alive for the test lifetime)
        _scope = Factory.Services.CreateScope();
        DbContext = _scope.ServiceProvider.GetRequiredService<EPRLiveDBContext>();

        // Ensure database is created
        DbContext.Database.EnsureCreated();
    }

    /// <summary>
    /// Gets a TestDataBuilder instance for creating test data
    /// </summary>
    protected TestDataBuilder GetTestDataBuilder()
    {
        return new TestDataBuilder(DbContext);
    }

    /// <summary>
    /// Clears all change tracking to avoid Entity Framework conflicts
    /// </summary>
    protected void ClearChangeTracker()
    {
        DbContext.ChangeTracker.Clear();
    }

    /// <summary>
    /// Creates a new isolated DbContext instance for operations that need separation
    /// </summary>
    protected EPRLiveDBContext CreateIsolatedContext()
    {
        var optionsBuilder = new DbContextOptionsBuilder<EPRLiveDBContext>();
        optionsBuilder.UseInMemoryDatabase(DbContext.Database.GetDbConnection().Database);
        return new TestDbContextWithIdentitySupport(optionsBuilder.Options);
    }

    /// <summary>
    /// Executes an action in an isolated DbContext to avoid tracking conflicts
    /// </summary>
    protected async Task<T> ExecuteInIsolatedContextAsync<T>(Func<EPRLiveDBContext, Task<T>> action)
    {
        using var isolatedContext = CreateIsolatedContext();
        return await action(isolatedContext);
    }

    /// <summary>
    /// Creates a test JWT token for authentication
    /// </summary>
    protected string CreateTestJwtToken(Guid employerGuid, int chapterId = 1)
    {
        var claims = new List<Claim>
        {
            new Claim(Constants.Claims.Subject, "test-user-id"),
            new Claim(Constants.Claims.Username, "test-user"),
            new Claim(Constants.Claims.Email, "<EMAIL>"),
            new Claim(Constants.Claims.OrganizationGuid, employerGuid.ToString()),
            new Claim(Constants.Claims.OrganizationId, chapterId.ToString()),
            new Claim(
                "http://schemas.microsoft.com/ws/2008/06/identity/claims/role",
                Constants.Roles.ChapterAdministrator
            ),
            new Claim("permission", "read"),
            new Claim("permission", "write"),
            new Claim(OpenIddictConstants.Claims.JwtId, Guid.NewGuid().ToString()),
        };

        // Create a symmetric security key for testing
        var key = new SymmetricSecurityKey(
            Encoding.UTF8.GetBytes("test-secret-key-for-jwt-token-that-is-at-least-32-chars")
        );
        var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

        var token = new JwtSecurityToken(
            issuer: "test-issuer",
            audience: "test-audience",
            claims: claims,
            expires: DateTime.UtcNow.AddHours(1),
            signingCredentials: credentials
        );

        var tokenHandler = new JwtSecurityTokenHandler();
        return tokenHandler.WriteToken(token);
    }

    /// <summary>
    /// Executes a GraphQL query via HTTP POST
    /// </summary>
    protected async Task<T> ExecuteGraphQLQueryAsync<T>(string query, object? variables = null)
    {
        var request = new { query = query, variables = variables };

        var json = JsonSerializer.Serialize(request);
        var content = new StringContent(json, Encoding.UTF8, "application/json");

        var response = await HttpClient.PostAsync("/graphql", content);
        response.EnsureSuccessStatusCode();

        var responseJson = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<GraphQLResponse<T>>(
            responseJson,
            new JsonSerializerOptions { PropertyNameCaseInsensitive = true }
        );

        return result!.Data;
    }

    /// <summary>
    /// Executes a GraphQL query and returns the raw response for debugging
    /// </summary>
    protected async Task<string> ExecuteGraphQLQueryRawAsync(string query, object? variables = null)
    {
        var request = new { query = query, variables = variables };

        var json = JsonSerializer.Serialize(request);
        var content = new StringContent(json, Encoding.UTF8, "application/json");

        var response = await HttpClient.PostAsync("/graphql", content);
        response.EnsureSuccessStatusCode();

        return await response.Content.ReadAsStringAsync();
    }

    /// <summary>
    /// Executes a GraphQL query via HTTP POST with proper error handling
    /// </summary>
    protected async Task<GraphQLResponse<T>> ExecuteGraphQLQueryWithErrorsAsync<T>(
        string query,
        object? variables = null
    )
    {
        var request = new { query = query, variables = variables };

        var json = JsonSerializer.Serialize(request);
        var content = new StringContent(json, Encoding.UTF8, "application/json");

        var response = await HttpClient.PostAsync("/graphql", content);
        response.EnsureSuccessStatusCode();

        var responseJson = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<GraphQLResponse<T>>(
            responseJson,
            new JsonSerializerOptions { PropertyNameCaseInsensitive = true }
        );

        return result!;
    }

    /// <summary>
    /// Executes a GraphQL query with authentication and returns strongly-typed result
    /// </summary>
    protected async Task<T> ExecuteAuthenticatedGraphQLQueryAsync<T>(
        string query,
        object? variables = null,
        Guid? employerGuid = null
    )
    {
        // Create and set JWT token
        var testEmployerGuid = employerGuid ?? Guid.NewGuid();
        var token = CreateTestJwtToken(testEmployerGuid);
        HttpClient.DefaultRequestHeaders.Authorization =
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);

        var request = new { query = query, variables = variables };

        var json = JsonSerializer.Serialize(request);
        var content = new StringContent(json, Encoding.UTF8, "application/json");

        var response = await HttpClient.PostAsync("/graphql", content);
        response.EnsureSuccessStatusCode();

        var responseJson = await response.Content.ReadAsStringAsync();
        var result = JsonSerializer.Deserialize<GraphQLResponse<T>>(
            responseJson,
            new JsonSerializerOptions { PropertyNameCaseInsensitive = true }
        );

        return result!.Data;
    }

    /// <summary>
    /// Executes a GraphQL query with authentication and returns raw response for debugging
    /// </summary>
    protected async Task<string> ExecuteAuthenticatedGraphQLQueryRawAsync(
        string query,
        object? variables = null,
        Guid? employerGuid = null
    )
    {
        // Create and set JWT token
        var testEmployerGuid = employerGuid ?? Guid.NewGuid();
        var token = CreateTestJwtToken(testEmployerGuid);
        HttpClient.DefaultRequestHeaders.Authorization =
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);

        var request = new { query = query, variables = variables };

        var json = JsonSerializer.Serialize(request);
        var content = new StringContent(json, Encoding.UTF8, "application/json");

        var response = await HttpClient.PostAsync("/graphql", content);
        response.EnsureSuccessStatusCode();

        return await response.Content.ReadAsStringAsync();
    }

    /// <summary>
    /// GraphQL response wrapper
    /// </summary>
    protected class GraphQLResponse<T>
    {
        public T Data { get; set; } = default!;
        public GraphQLError[]? Errors { get; set; }
    }

    /// <summary>
    /// GraphQL error
    /// </summary>
    protected class GraphQLError
    {
        public string Message { get; set; } = string.Empty;
        public object[]? Path { get; set; }
    }

    /// <summary>
    /// Common GraphQL response structures for testing
    /// </summary>
    protected class AgreementNode
    {
        public string Id { get; set; } = string.Empty; // Agreement entities (Node interface) serialize as strings
        public string Name { get; set; } = string.Empty;
        public DateTime EffectiveStartDate { get; set; }
        public DateTime? EffectiveEndDate { get; set; }
        public ChapterNode? Chapter { get; set; }
        public UnionNode? Union { get; set; }
    }

    /// <summary>
    /// Simple Agreement response for queries that return AgreementSimpleId DTOs
    /// </summary>
    protected class AgreementSimpleNode
    {
        public int Id { get; set; } // AgreementSimpleId DTOs serialize as integers
        public string Name { get; set; } = string.Empty;
    }

    protected class ChapterNode
    {
        public int Id { get; set; }
        public OrganizationNode? IdNavigation { get; set; }
    }

    protected class UnionNode
    {
        public int Id { get; set; }
        public OrganizationNode? IdNavigation { get; set; }
    }

    protected class ClassificationNode
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public ChapterNode? Chapter { get; set; }
        public DclassificationCodeNode? DclassificationCode { get; set; }
    }

    protected class SubClassificationNode
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
    }

    protected class OrganizationNode
    {
        public string Name { get; set; } = string.Empty;
    }

    protected class DclassificationCodeNode
    {
        public string ClassName { get; set; } = string.Empty;
    }

    protected class EmployerRosterNode
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? CompanyNumber { get; set; }
    }

    protected class ConnectionResponse<T>
    {
        public List<T> Nodes { get; set; } = new List<T>();
        public int TotalCount { get; set; }
    }

    protected class AgreementsResponse
    {
        [JsonPropertyName("agreementsByEmployerIds")]
        public ConnectionResponse<AgreementSimpleNode> AgreementsByEmployerIds { get; set; } =
            new();
    }

    protected class SignatoryAgreementsResponse
    {
        [JsonPropertyName("signatoryAgreements")]
        public ConnectionResponse<AgreementNode> SignatoryAgreements { get; set; } = new();
    }

    protected class ClassificationsResponse
    {
        [JsonPropertyName("classificationsByAgreementId")]
        public ConnectionResponse<ClassificationNode> ClassificationsByAgreementId { get; set; } =
            new();
    }

    protected class ClassificationsByAgreementIdsResponse
    {
        [JsonPropertyName("classificationsByAgreementIds")]
        public ConnectionResponse<ClassificationNode> ClassificationsByAgreementIds { get; set; } =
            new();
    }

    protected class SubClassificationsResponse
    {
        [JsonPropertyName("subClassificationsByAgreementAndClassification")]
        public ConnectionResponse<SubClassificationNode> SubClassificationsByAgreementAndClassification { get; set; } =
            new();
    }

    protected class EmployerRosterResponse
    {
        [JsonPropertyName("employers")]
        public ConnectionResponse<EmployerRosterNode> Employers { get; set; } = new();
    }

    protected class EmployerRosterGridResponse
    {
        [JsonPropertyName("employerRosterGrid")]
        public ConnectionResponse<EmployerRosterNode> EmployerRosterGrid { get; set; } = new();
    }

    protected class EmployersResponse
    {
        [JsonPropertyName("employers")]
        public ConnectionResponse<EmployerRosterNode> Employers { get; set; } = new();
    }

    protected class EmployersByChapterIdResponse
    {
        [JsonPropertyName("employersByChapterId")]
        public ConnectionResponse<EmployerNode> EmployersByChapterId { get; set; } = new();
    }

    protected class EmployerRosterByChapterIdResponse
    {
        [JsonPropertyName("employerRosterByChapterId")]
        public ConnectionResponseWithEdges<EmployerRosterViewNode> EmployerRosterByChapterId { get; set; } =
            new();
    }

    protected class EmployerNode
    {
        public int Id { get; set; }
        public string? Fein { get; set; }
        public string? Dba { get; set; }
        public OrganizationNode? Organization { get; set; }
    }

    protected class EmployerRosterViewNode
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Fein { get; set; }
    }

    protected class EmployerRosterByChapterResponse
    {
        [JsonPropertyName("employerRosterByChapterId")]
        public ConnectionResponseWithEdges<EmployerRosterByChapterNode> EmployerRosterByChapterId { get; set; } =
            new();
    }

    protected class EmployerRosterByChapterNode
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Fein { get; set; }
    }

    protected class ConnectionResponseWithEdges<T>
    {
        public List<EdgeNode<T>> Edges { get; set; } = new List<EdgeNode<T>>();
        public PageInfo PageInfo { get; set; } = new();
        public int TotalCount { get; set; }
    }

    protected class EdgeNode<T>
    {
        public T Node { get; set; } = default!;
    }

    protected class PageInfo
    {
        public bool HasNextPage { get; set; }
        public string? EndCursor { get; set; }
    }

    /// <summary>
    /// Creates a proper Relay Global Node ID for a given type and ID
    /// </summary>
    protected string CreateNodeId(string typeName, int id)
    {
        var nodeIdString = $"{typeName}:{id}";
        var bytes = System.Text.Encoding.UTF8.GetBytes(nodeIdString);
        return Convert.ToBase64String(bytes);
    }

    /// <summary>
    /// Creates a proper Relay Global Node ID for Agreement entities
    /// </summary>
    protected string CreateAgreementNodeId(int agreementId)
    {
        return CreateNodeId("Agreement", agreementId);
    }

    /// <summary>
    /// Creates a proper Relay Global Node ID for Classification entities
    /// </summary>
    protected string CreateClassificationNodeId(int classificationId)
    {
        return CreateNodeId("ClassificationName", classificationId);
    }

    /// <summary>
    /// Sets up test data in the database
    /// </summary>
    protected async Task SeedTestDataAsync()
    {
        // Clear existing test data
        await CleanupTestDataAsync();

        // Add test data
        AddTestRoots();
        AddTestParties();
        AddTestOrganizations();
        AddTestChapters();
        AddTestUnions();
        AddTestAgreementTypes();
        AddTestEmployers();
        AddTestPersons();
        AddTestEmployees();
        AddTestRelationships();
        AddTestAgreements();
        AddTestEmployersToAgreements();
        AddTestClassifications();
        AddTestServiceSubscriptions();
        AddTestAuthenticationData();

        await DbContext.SaveChangesAsync();
    }

    /// <summary>
    /// Cleans up test data from the database
    /// </summary>
    protected async Task CleanupTestDataAsync()
    {
        // Remove test data in reverse dependency order
        DbContext.Eprusers.RemoveRange(
            DbContext.Eprusers.Where(eu => eu.AspnetUserId.ToString().StartsWith("90000000"))
        );
        DbContext.AspnetMemberships.RemoveRange(
            DbContext.AspnetMemberships.Where(am => am.UserId.ToString().StartsWith("90000000"))
        );
        DbContext.AspnetUsers.RemoveRange(
            DbContext.AspnetUsers.Where(au => au.UserId.ToString().StartsWith("90000000"))
        );
        DbContext.AgreementClassifications.RemoveRange(
            DbContext.AgreementClassifications.Where(ac => ac.AgreementId >= 90000)
        );
        DbContext.ServiceSubscriptions.RemoveRange(
            DbContext.ServiceSubscriptions.Where(ss => ss.Id >= 90000)
        );
        DbContext.SubscriptionServices.RemoveRange(
            DbContext.SubscriptionServices.Where(ss => ss.Id >= 1 && ss.Id <= 10)
        );
        DbContext.EmployersToAgreements.RemoveRange(
            DbContext.EmployersToAgreements.Where(eta => eta.Id >= 90000)
        );
        DbContext.Relationships.RemoveRange(DbContext.Relationships.Where(r => r.Id >= 90000));
        DbContext.Employees.RemoveRange(DbContext.Employees.Where(e => e.Id >= 90000));
        DbContext.Agreements.RemoveRange(DbContext.Agreements.Where(a => a.Id >= 90000));
        DbContext.ClassificationNames.RemoveRange(
            DbContext.ClassificationNames.Where(cn => cn.Id >= 90000)
        );
        DbContext.DagreementTypes.RemoveRange(
            DbContext.DagreementTypes.Where(dt => dt.Id >= 1 && dt.Id <= 10)
        );
        DbContext.Unions.RemoveRange(DbContext.Unions.Where(u => u.Id >= 1 && u.Id <= 10));
        DbContext.Chapters.RemoveRange(DbContext.Chapters.Where(c => c.Id >= 1 && c.Id <= 10));
        DbContext.Persons.RemoveRange(DbContext.Persons.Where(p => p.Id >= 90000));
        DbContext.Employers.RemoveRange(DbContext.Employers.Where(e => e.Id >= 90000));
        DbContext.Organizations.RemoveRange(
            DbContext.Organizations.Where(o => o.Id >= 90000 || o.Id == 1)
        );
        DbContext.Parties.RemoveRange(DbContext.Parties.Where(p => p.Id >= 90000 || p.Id == 1));
        DbContext.Roots.RemoveRange(DbContext.Roots.Where(r => r.Id >= 90000 || r.Id == 1));

        await DbContext.SaveChangesAsync();
    }

    private void AddTestAgreements()
    {
        var testAgreements = new[]
        {
            new Agreement
            {
                Id = 90101, // Use Root ID 90101
                Name = "Test Agreement 1",
                EffectiveStartDate = DateTime.Now.AddYears(-1),
                EffectiveEndDate = DateTime.Now.AddYears(1),
                ChapterId = 1,
                UnionId = 1, // Required field
                DagreementTypeId = 1, // Required field
                // Required fields with default values in SQL Server
                AllowCurrentMonthReporting = true,
                RequiresSignatoryStatus = true,
                // Other fields with default values
                SuppressFromEmployerRoster = false,
                AdHoc = false,
                Weekly = false,
                AllowDuplicateLineItems = false,
                DisableEmployerAmendments = false,
            },
            new Agreement
            {
                Id = 90102, // Use Root ID 90102
                Name = "Test Agreement 2",
                EffectiveStartDate = DateTime.Now.AddYears(-2),
                EffectiveEndDate = null, // Active agreement
                ChapterId = 1,
                UnionId = 1, // Required field
                DagreementTypeId = 1, // Required field
                // Required fields with default values in SQL Server
                AllowCurrentMonthReporting = true,
                RequiresSignatoryStatus = false, // Different value for variety
                // Other fields with default values
                SuppressFromEmployerRoster = false,
                AdHoc = false,
                Weekly = false,
                AllowDuplicateLineItems = false,
                DisableEmployerAmendments = false,
            },
        };

        DbContext.Agreements.AddRange(testAgreements);
    }

    private void AddTestClassifications()
    {
        var testClassifications = new[]
        {
            new ClassificationName
            {
                Id = 90001,
                Name = "Test Classification 1",
                ChapterId = 1,
                DclassificationCodeId = 1, // Use existing DclassificationCode
                DstatusId = 1, // Use existing Dstatus (active)
            },
            new ClassificationName
            {
                Id = 90002,
                Name = "Test Classification 2",
                ChapterId = 1,
                DclassificationCodeId = 1, // Use existing DclassificationCode
                DstatusId = 1, // Use existing Dstatus (active)
            },
        };

        DbContext.ClassificationNames.AddRange(testClassifications);

        // Link classifications to agreements
        var agreementClassifications = new[]
        {
            new AgreementClassification { AgreementId = 90101, ClassificationNameId = 90001 },
            new AgreementClassification { AgreementId = 90101, ClassificationNameId = 90002 },
            new AgreementClassification { AgreementId = 90102, ClassificationNameId = 90001 },
        };

        DbContext.AgreementClassifications.AddRange(agreementClassifications);
    }

    private void AddTestRoots()
    {
        var testRoots = new[]
        {
            // Add root for chapter/organization ID 1 to match JWT org_id claim
            new Root
            {
                Id = 1,
                Guid = Guid.Parse("2b81e44b-8627-4d3d-87cf-9d5fdbf830f6"), // Matches JWT org_guid from debug log
                CreationDate = DateTime.Now,
                CreatedBy = "TestUser",
                LastModificationDate = DateTime.Now,
                LastModifiedBy = "TestUser",
            },
            new Root
            {
                Id = 90001,
                Guid = Guid.Parse("11111111-1111-1111-1111-111111111111"), // This is the employer GUID used in tests
                CreationDate = DateTime.Now,
                CreatedBy = "TestUser",
                LastModificationDate = DateTime.Now,
                LastModifiedBy = "TestUser",
            },
            new Root
            {
                Id = 90002,
                Guid = Guid.Parse("*************-2222-2222-************"),
                CreationDate = DateTime.Now,
                CreatedBy = "TestUser",
                LastModificationDate = DateTime.Now,
                LastModifiedBy = "TestUser",
            },
            new Root
            {
                Id = 90003,
                Guid = Guid.Parse("*************-3333-3333-************"),
                CreationDate = DateTime.Now,
                CreatedBy = "TestUser",
                LastModificationDate = DateTime.Now,
                LastModifiedBy = "TestUser",
            },
            // Root entities for agreements (one-to-one relationship)
            new Root
            {
                Id = 90101, // Agreement ID 90001 needs its own Root
                Guid = Guid.Parse("*************-4444-4444-************"),
                CreationDate = DateTime.Now,
                CreatedBy = "TestUser",
                LastModificationDate = DateTime.Now,
                LastModifiedBy = "TestUser",
            },
            new Root
            {
                Id = 90102, // Agreement ID 90002 needs its own Root
                Guid = Guid.Parse("*************-5555-5555-************"),
                CreationDate = DateTime.Now,
                CreatedBy = "TestUser",
                LastModificationDate = DateTime.Now,
                LastModifiedBy = "TestUser",
            },
        };

        DbContext.Roots.AddRange(testRoots);
    }

    private void AddTestParties()
    {
        var testParties = new[]
        {
            // Add party for ID 1 to match JWT org_id claim
            new Party
            {
                Id = 1,
                DpartyTypeId = 1, // Organization
            },
            new Party
            {
                Id = 90001,
                DpartyTypeId = 1, // Organization
            },
            new Party
            {
                Id = 90002,
                DpartyTypeId = 2, // Person
            },
            new Party
            {
                Id = 90003,
                DpartyTypeId = 2, // Person
            },
        };

        DbContext.Parties.AddRange(testParties);
    }

    private void AddTestOrganizations()
    {
        var testOrganizations = new[]
        {
            // Add organization for ID 1 to match JWT org_id claim
            new Organization
            {
                Id = 1,
                Name = "Test Chapter Organization",
                DorganizationTypeId = 2, // Chapter type
            },
            new Organization
            {
                Id = 90001,
                Name = "Test Employer Organization",
                DorganizationTypeId = 5, // Employer type
            },
        };

        DbContext.Organizations.AddRange(testOrganizations);
    }

    private void AddTestChapters()
    {
        var testChapters = new[]
        {
            new Chapter
            {
                Id = 1,
                EmployerAssociationId = "TEST_EMPLOYER_ASSOC",
                EmployeeAssociationId = "TEST_EMPLOYEE_ASSOC",
                Limited = false,
            },
        };

        DbContext.Chapters.AddRange(testChapters);
    }

    private void AddTestUnions()
    {
        var testUnions = new[]
        {
            new Union { Id = 1, DefaultDelinquentDay = 15 },
        };

        DbContext.Unions.AddRange(testUnions);
    }

    private void AddTestAgreementTypes()
    {
        var testAgreementTypes = new[]
        {
            new DagreementType
            {
                Id = 1,
                Name = "Standard Agreement",
                Description = "Standard collective bargaining agreement",
            },
        };

        DbContext.DagreementTypes.AddRange(testAgreementTypes);
    }

    private void AddTestEmployers()
    {
        var testEmployers = new[]
        {
            new Employer
            {
                Id = 90001,
                FEIN = "123456789",
                Dba = "Test Employer DBA",
                BusinessDescription = "Test Business",
                IsAssociationMember = false,
            },
        };

        DbContext.Employers.AddRange(testEmployers);
    }

    private void AddTestPersons()
    {
        var testPersons = new[]
        {
            new Person
            {
                Id = 90002,
                FirstName = "John",
                LastName = "Doe",
                MiddleName = "A",
            },
            new Person
            {
                Id = 90003,
                FirstName = "Jane",
                LastName = "Smith",
                MiddleName = "B",
            },
        };

        DbContext.Persons.AddRange(testPersons);
    }

    private void AddTestEmployees()
    {
        var testEmployees = new[]
        {
            new Employee
            {
                Id = 90002,
                BirthDate = DateTime.Now.AddYears(-30),
                DateOfHire = DateTime.Now.AddYears(-2),
                DateOfTermination = null, // Active employee
                ExternalEmployeeId = "EMP001",
            },
            new Employee
            {
                Id = 90003,
                BirthDate = DateTime.Now.AddYears(-25),
                DateOfHire = DateTime.Now.AddYears(-1),
                DateOfTermination = null, // Active employee
                ExternalEmployeeId = "EMP002",
            },
        };

        DbContext.Employees.AddRange(testEmployees);
    }

    private void AddTestRelationships()
    {
        var testRelationships = new[]
        {
            new Relationship
            {
                Id = 90001,
                LeftPartyId = 90001, // Employer
                RightPartyId = 90002, // Employee 1
                DrelationshipTypeId = 30, // EmployerEmployee relationship
            },
            new Relationship
            {
                Id = 90002,
                LeftPartyId = 90001, // Employer
                RightPartyId = 90003, // Employee 2
                DrelationshipTypeId = 30, // EmployerEmployee relationship
            },
            new Relationship
            {
                Id = 90003,
                LeftPartyId = 1, // Chapter 1
                RightPartyId = 90001, // Employer
                DrelationshipTypeId = 31, // ChapterEmployer relationship
            },
        };

        DbContext.Relationships.AddRange(testRelationships);
    }

    private void AddTestEmployersToAgreements()
    {
        var testEmployersToAgreements = new[]
        {
            new EmployersToAgreement
            {
                Id = 90003, // Provide explicit ID for testing
                EmployerId = 90001, // Employer Root ID
                AgreementId = 90101, // Agreement Root ID
            },
            new EmployersToAgreement
            {
                Id = 90004, // Provide explicit ID for testing
                EmployerId = 90001, // Employer Root ID
                AgreementId = 90102, // Agreement Root ID
            },
        };

        DbContext.EmployersToAgreements.AddRange(testEmployersToAgreements);
    }

    private void AddTestServiceSubscriptions()
    {
        // Add subscription service for timesheet service
        var subscriptionService = new SubscriptionService { Id = 1, Name = "Timesheet Service" };
        DbContext.SubscriptionServices.Add(subscriptionService);

        // Get the organization for chapter 1 from the local entities (not yet saved to DB)
        var organization = DbContext.Organizations.Local.FirstOrDefault(o => o.Id == 1);
        if (organization == null)
        {
            throw new InvalidOperationException(
                "Organization with ID 1 not found in local entities. Ensure AddTestOrganizations() is called before AddTestServiceSubscriptions()."
            );
        }

        // Add service subscription for timesheet service (ID 1) for chapter 1
        // This is required for the GraphQL signatoryAgreements query to work
        var serviceSubscription = new ServiceSubscription
        {
            Id = 90001,
            PartyId = 1, // Chapter ID 1 (matches JWT org_id claim)
            SubscriptionServiceId = 1, // Timesheet service
            Party = organization,
            SubscriptionService = subscriptionService,
        };

        DbContext.ServiceSubscriptions.Add(serviceSubscription);
    }

    private void AddTestAuthenticationData()
    {
        // Create test user that matches our JWT token claims
        var testUserId = Guid.Parse("*************-0000-0000-000000000001");
        var cheemaUserId = Guid.Parse("*************-0000-0000-000000000002");
        var testOrgGuid = Guid.Parse("5ec4dbfb-3a9e-4047-a244-9cb4876cd6f8"); // Matches JWT org_guid
        var applicationId = Guid.Parse("00000000-0000-0000-0000-000000000001");

        // Add AspNetUser - matches the username in JWT token
        var testAspNetUser = new AspnetUser
        {
            UserId = testUserId,
            UserName = "test-user", // Matches JWT username claim
            LoweredUserName = "test-user",
            ApplicationId = applicationId,
            MobileAlias = null,
            IsAnonymous = false,
            LastActivityDate = DateTime.Now,
        };

        // Add AspNetUser for "cheema" username used in tests
        var cheemaAspNetUser = new AspnetUser
        {
            UserId = cheemaUserId,
            UserName = "cheema", // Matches test username
            LoweredUserName = "cheema",
            ApplicationId = applicationId,
            MobileAlias = null,
            IsAnonymous = false,
            LastActivityDate = DateTime.Now,
        };

        DbContext.AspnetUsers.AddRange(testAspNetUser, cheemaAspNetUser);

        // Add AspNetMembership records (required for email in UserInfoService)
        var testMembership = new AspnetMembership
        {
            UserId = testUserId,
            ApplicationId = applicationId,
            Email = "<EMAIL>",
            LoweredEmail = "<EMAIL>",
            Password = "hashedpassword123", // Required field
            PasswordFormat = 1,
            PasswordSalt = "salt",
            IsApproved = true,
            IsLockedOut = false,
            CreateDate = DateTime.Now,
            LastLoginDate = DateTime.Now,
            LastPasswordChangedDate = DateTime.Now,
            LastLockoutDate = DateTime.MinValue,
            FailedPasswordAttemptCount = 0,
            FailedPasswordAttemptWindowStart = DateTime.MinValue,
            FailedPasswordAnswerAttemptCount = 0,
            FailedPasswordAnswerAttemptWindowStart = DateTime.MinValue,
        };

        var cheemaMembership = new AspnetMembership
        {
            UserId = cheemaUserId,
            ApplicationId = applicationId,
            Email = "<EMAIL>",
            LoweredEmail = "<EMAIL>",
            Password = "hashedpassword123", // Required field
            PasswordFormat = 1,
            PasswordSalt = "salt",
            IsApproved = true,
            IsLockedOut = false,
            CreateDate = DateTime.Now,
            LastLoginDate = DateTime.Now,
            LastPasswordChangedDate = DateTime.Now,
            LastLockoutDate = DateTime.MinValue,
            FailedPasswordAttemptCount = 0,
            FailedPasswordAttemptWindowStart = DateTime.MinValue,
            FailedPasswordAnswerAttemptCount = 0,
            FailedPasswordAnswerAttemptWindowStart = DateTime.MinValue,
        };

        DbContext.AspnetMemberships.AddRange(testMembership, cheemaMembership);

        // Add EPRUser - links to organization
        var testEprUser = new Epruser
        {
            AspnetUserId = testUserId, // Primary key
            OrganizationId = 1, // Links to chapter/organization ID 1 (matches JWT org_id)
            FirstName = "Test",
            LastName = "User",
        };

        var cheemaEprUser = new Epruser
        {
            AspnetUserId = cheemaUserId, // Primary key
            OrganizationId = 1, // Links to chapter/organization ID 1
            FirstName = "Cheema",
            LastName = "TestUser",
        };

        DbContext.Eprusers.AddRange(testEprUser, cheemaEprUser);
    }

    // Add new response classes for Employee queries after the existing response classes

    protected class EmployeeNode
    {
        public string Id { get; set; } = string.Empty; // GraphQL IDs are strings
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public bool Active { get; set; }
    }

    protected class EmployeesByEmployerIdAsyncResponse
    {
        [JsonPropertyName("employeesByEmployerIdAsync")]
        public ConnectionResponse<EmployeeNode> EmployeesByEmployerIdAsync { get; set; } = new();
    }

    protected class EmployeesByEmployerGuidAsyncResponse
    {
        [JsonPropertyName("employeesByEmployerGuidAsync")]
        public ConnectionResponseWithPagination<EmployeeNode> EmployeesByEmployerGuidAsync { get; set; } =
            new();
    }

    protected class ConnectionResponseWithPagination<T>
    {
        public List<T> Nodes { get; set; } = new List<T>();
        public int TotalCount { get; set; }
        public PageInfo PageInfo { get; set; } = new();
    }

    public virtual void Dispose()
    {
        CleanupTestDataAsync().Wait();
        HttpClient?.Dispose();
        _scope?.Dispose();
        Factory?.Dispose();
    }
}
