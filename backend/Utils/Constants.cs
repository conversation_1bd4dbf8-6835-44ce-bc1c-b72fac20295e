using backend.Types.Outputs;
using OpenIddict.Abstractions;

namespace backend.Utils;

public static class Constants
{
    public const string InvalidUsernameErrorCode = "INVALID_USERNAME";
    public const string CustomViewSuffix = "CustomViews";
    public const string UserOwnerType = "User";

    public static class Claims
    {
        public const string Subject = OpenIddictConstants.Claims.Subject;
        public const string Username = OpenIddictConstants.Claims.Username;
        public const string Email = OpenIddictConstants.Claims.Email;
        public const string OrganizationId = "org_id";
        public const string OrganizationGuid = "org_guid";
        public const string Permission = "permission";
    }

    public static class Cookies
    {
        public const string EPRLiveAccessToken = "EPRLiveAPIToken";
        public const string EPRLiveRefreshToken = "EPRLiveRefreshToken";
    }

    public static class Roles
    {
        public const string ChapterAdministrator = "Chapter Administrator";
        public const string TradeAdministrator = "Trade Administrator";
        public const string SystemAdministrator = "System Administrator";
    }

    public enum EventTypes
    {
        DelinquencyLetterError = 195,
    }

    public enum OrganizationTypes
    {
        Chapter = 2,
        Union = 3,
        FundAdministrator = 4,
        Employer = 5,
    }

    public enum RelationshipTypes
    {
        ChapterUnion = 3,
        ChapterFundAdministrator = 4,
        ChapterEmployer = 5,
        EmployerEmployee = 30,
    }

    public enum RelationshipSubTypes
    {
        Privileged = 7,
    }

    public enum RelationshipStatuses
    {
        Active = 1,
    }

    public enum ValueTypes
    {
        Int,
        String,
        Decimal,
        Date,
        Boolean,
        Guid,
    }

    public enum OwnerTypes
    {
        Organization,
        Employee,
        User,
    }

    public enum FileTypes
    {
        Csv,
        Xlsx,
        Pdf,
    }

    /// <summary>
    /// Constants for PayStub validation logic
    /// </summary>
    public static class PayStubValidation
    {
        /// <summary>
        /// Minimum threshold for numeric values to be considered meaningful
        /// </summary>
        public const float MinMeaningfulValue = 0f;

        /// <summary>
        /// Maximum length for employee names in validation
        /// </summary>
        public const int MaxEmployeeNameLength = 100;

        /// <summary>
        /// Error message for PayStubs that lack meaningful data
        /// </summary>
        public const string MeaningfulDataRequiredMessage = "PayStub must have meaningful details with valid hours, job codes, cost centers, or other data";

        /// <summary>
        /// Error message template for validation failures
        /// </summary>
        public const string ValidationFailedMessageTemplate = "PayStub validation failed: {0}";
    }
}
