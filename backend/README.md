# EPRLive24 Backend

A .NET-based GraphQL API service using HotChocolate and Entity Framework Core, with enhanced security, comprehensive error handling, and simplified architecture following modern .NET 8 best practices.

## Technology Stack

- **.NET 9.0**: Modern, cross-platform development framework
- **HotChocolate**: GraphQL server implementation for .NET
- **Entity Framework Core**: ORM for database operations
- **SQL Server**: Primary database
- **OAuth 2.0/OpenID Connect**: Secure API access via identity service

## Key Features

### 🚀 **Core API Features**
- GraphQL API with support for queries and mutations
- Pagination, filtering, and sorting capabilities
- Entity Framework Core for data access
- PDF generation support
- Email services integration
- Phone number validation
- Excel file handling (via Telerik)

### 🔐 **Enhanced Security & Authentication**
- **Defensive Programming**: Comprehensive null checks with `ArgumentNullException.ThrowIfNull()`
- **Enhanced Authorization**: Robust chapter authorization with detailed logging and input validation
- **Authentication State Validation**: Proper user authentication verification with HttpContext safety
- **Security Headers**: Complete security header implementation (CSP, HSTS, X-Frame-Options, X-XSS-Protection)
- **Environment-Based Configuration**: Uses `IWebHostEnvironment.IsDevelopment()` for environment detection
- **Authentication Utilities**: Enhanced `AuthUtils.cs` with comprehensive validation and error handling

### 🛡️ **Robust Error Handling**
- **Enhanced Middleware**: `EnhancedErrorHandlingMiddleware` with security event tracking
- **Correlation IDs**: Request tracking for debugging and monitoring
- **Client IP Tracking**: X-Forwarded-For header support for proper client identification
- **Structured Logging**: Comprehensive security and performance event logging
- **Environment-Aware Details**: Automatic error detail inclusion in development

### 🎯 **Simplified Architecture**
- **YAGNI Compliance**: Eliminated unnecessary configuration complexity
- **Environment Detection**: Standard ASP.NET Core patterns instead of custom configuration
- **Minimal Dependencies**: Reduced configuration overhead
- **Standard Patterns**: Consistent with .NET ecosystem best practices

## Project Structure

```plaintext
backend/
├── Controllers/          # API controllers handling HTTP requests
├── Data/
│   ├── DTOs/             # Data Transfer Objects
│   ├── HttpQueries/      # HTTP query handlers
│   ├── Models/           # EF Core entity models
│   ├── Scaffold/         # Scaffolded code
│   ├── Settings/         # Configuration settings
│   ├── StoredProcedureOutputs/ # Outputs from stored procedures
│   └── Views/            # View models or UI components
├── ErrorHandling/        # Custom error handling middleware and utilities
├── Extensions/           # Extension methods for various classes
├── Migrations/           # Database migration files generated by EF Core
├── Properties/           # Project properties and configurations
├── Security/
│   └── Identity/         # Identity and authentication-related classes
├── Services/             # Business logic and service implementations
├── Tests/                # Unit and integration tests
├── Types/
│   ├── Inputs/           # GraphQL input type definitions
│   ├── Mutations/        # GraphQL mutation resolvers
│   ├── Outputs/          # GraphQL output type definitions
│   └── Queries/          # GraphQL query resolvers
├── UploadedImages/       # Directory for storing uploaded images
└── Utils/                # Utility classes and helper functions
```

## Getting Started

### Prerequisites

- .NET 9.0 SDK
- SQL Server instance
- IDE (recommended: Visual Studio 2022 or VS Code)

### Setup

1. Clone the repository
2. Install dependencies:

    ```sh
    dotnet restore
    ```

3. Update database connection string in `appsettings.json`
4. Apply database migrations:

    ```sh
    dotnet ef database update
    ```

5. Run the application:

    Production mode:

        dotnet run

    Development mode:

        dotnet watch run --launch-profile Development


The GraphQL endpoint will be available at `/graphql`

## Development Guidelines

### GraphQL Development

- Place new types in appropriate directories under `Types/`
- Use HotChocolate attributes for GraphQL schema configuration
- Implement pagination for list queries using `[UsePaging]`
- Add filtering and sorting where appropriate using `[UseFiltering]` and `[UseSorting]`

### Database Changes

1. List pending migrations:

```sh
dotnet ef migrations list
```

2. Create new migration:

```sh
dotnet ef migrations add MigrationName
```

3. Apply migrations:

```sh
dotnet ef database update
```

### Security Best Practices

1. **Always validate inputs**: Use `ArgumentNullException.ThrowIfNull()` for parameter validation
2. **Check authentication state**: Verify `httpContext.User?.Identity?.IsAuthenticated`
3. **Comprehensive logging**: Log security events with appropriate detail levels
4. **Environment awareness**: Use `_environment.IsDevelopment()` for conditional behavior

## Authentication & Security

### **Authentication Flow**
The API uses OAuth 2.0/OpenID Connect with token introspection handled by the Identity service:

```http
Authorization: Bearer <token>
```

### **Security Improvements**
- **Enhanced AuthUtils**: Comprehensive null checking, authentication state validation, and GUID format validation
- **Chapter Authorization**: Robust authorization checks with input validation, detailed logging, and result tracking
- **Security Middleware**: Enhanced error handling with security event logging, correlation IDs, and client IP tracking
- **Defensive Programming**: All authentication utilities use `ArgumentNullException.ThrowIfNull()` with comprehensive validation
- **Token Security**: Works with identity service for secure token introspection and validation

### **Error Handling Pattern**
```csharp
public static Guid GetUserOrgGUID(HttpContext httpContext)
{
    ArgumentNullException.ThrowIfNull(httpContext, nameof(httpContext));
    
    if (httpContext.User?.Identity?.IsAuthenticated != true)
    {
        throw new UnauthorizedAccessException("User is not authenticated");
    }
    
    // ... comprehensive validation and error handling
}
```

### **Environment-Based Configuration**
```csharp
// Before: Configuration-based approach
if (_configuration.GetValue<bool>("DetailedErrors"))

// After: Environment-based approach (standard)
if (_environment.IsDevelopment())
```

### **Security Headers**
The enhanced middleware automatically adds:
- `X-Content-Type-Options: nosniff` - Prevents MIME sniffing attacks
- `X-Frame-Options: DENY` - Prevents clickjacking attacks
- `X-XSS-Protection: 1; mode=block` - Enables XSS filtering
- `Content-Security-Policy: default-src 'self'` - Restricts resource loading for XSS protection
- `Strict-Transport-Security: max-age=31536000; includeSubDomains` - Enforces HTTPS (production only)
- Custom correlation headers for request tracking and debugging

## Configuration

### **Simplified Configuration Approach**
The application now uses minimal configuration with sensible defaults:

```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=EPRLive;..."
  },
  "Identity": {
    "Server": "http://localhost:5211",
    "ClientId": "...",
    "ClientSecret": "..."
  },

}
```



### **Design Principles Applied**
- **YAGNI**: Only configure what's actually needed
- **KISS**: Prefer environment detection over custom configuration
- **DRY**: Use consistent patterns throughout the application

## Error Handling & Monitoring

### **Enhanced Error Handling Middleware**
- **Security Event Logging**: Comprehensive tracking of authentication and authorization events
- **Correlation IDs**: Unique identifiers for request tracking and debugging
- **Client Information**: IP address, user agent, and request details
- **Environment-Aware**: Automatic detail inclusion based on environment

### **Monitoring Features**
- Structured logging with correlation IDs
- Security event categorization
- Performance metrics tracking
- Client tracking for security analysis

## Key Dependencies

- **HotChocolate**: GraphQL server framework
- **Entity Framework Core**: Database ORM
- **FluentValidation**: Input validation
- **Mapster**: Object mapping
- **MailKit**: Email services
- **Telerik.Documents**: Excel file handling

## Testing Guidelines

### **Security Testing**
- Authentication state validation
- Authorization checks
- Error handling scenarios
- Environment-based behavior

### **Integration Testing**
- GraphQL query and mutation testing
- Database integration testing
- Authentication flow testing

## Additional Resources

- [HotChocolate Documentation](https://chillicream.com/docs/hotchocolate)
- [Entity Framework Core Documentation](https://docs.microsoft.com/en-us/ef/core/)
- [GraphQL Best Practices](https://graphql.org/learn/best-practices/)
- [ASP.NET Core Security Best Practices](https://docs.microsoft.com/en-us/aspnet/core/security/)

## Contributing

1. Follow security-first development practices
2. Apply YAGNI, KISS, and DRY principles
3. Use environment detection over custom configuration
4. Implement comprehensive error handling and logging
5. Create a feature branch
6. Make your changes
7. Submit a pull request
