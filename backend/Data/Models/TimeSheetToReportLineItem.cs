﻿namespace backend.Data.Models
{
    public class TimeSheetToReportLineItem
    {
        public int PayStubID { get; set; }
        public Guid EmployerGuid { get; set; }
        public int? EmployeeId { get; set; }
        public int? AgreementId { get; set; }
        public int? ClassificationId { get; set; }
        public int? SubClassificationId { get; set; }
        public string? JobCode { get; set; }
        public string? CostCenter { get; set; }
        public float? STHours { get; set; }
        public float? OTHours { get; set; }
        public float? DTHours { get; set; }
        public float? TotalHours { get; set; }
        public float? HourlyRate { get; set; }
        public DateTimeOffset WorkPeriod { get; set; }
        public int DaysWorked { get; set; }
        public float? Bonus { get; set; }

        public int? ReportLineItemID { get; set; }
    }
}
