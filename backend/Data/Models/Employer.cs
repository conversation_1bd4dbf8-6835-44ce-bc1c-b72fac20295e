﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace backend.Data.Models;

public partial class Employer
{
    [Key]
    [ID(nameof(Employer))]
    public int Id { get; set; }

    [NotMapped]
    [GraphQLIgnore]
    public string? Name { get; set; }

    [Column("EmployerIdentificationNumber")]
    public string? FEIN { get; set; }

    public string? Dba { get; set; }

    public string? BusinessDescription { get; set; }

    public string? AssociationId { get; set; }

    public bool IsAssociationMember { get; set; }

    [ForeignKey("Id")]
    public Organization? Organization { get; set; }

    [ForeignKey("Id")]
    public Root? Root { get; set; }

    public ICollection<CostCode> CostCodes { get; set; } = new List<CostCode>();

    public ICollection<EmployersToAgreement> EmployersToAgreements { get; set; } =
        new List<EmployersToAgreement>();

    public ICollection<FundingComment> FundingComments { get; set; } = new List<FundingComment>();

    public ICollection<Report> Reports { get; set; } = new List<Report>();
}

// Edge type removed to avoid conflicts with HotChocolate's pagination edge types
