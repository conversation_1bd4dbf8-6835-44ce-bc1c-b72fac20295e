﻿using System;
using System.Collections.Generic;
using HotChocolate;

namespace backend.Data.Models;

public partial class Employee
{
    [ID(nameof(Employee))]
    public int Id { get; set; }

    public DateTime? BirthDate { get; set; }

    public byte[]? Ssn { get; set; }

    public byte[]? SearchSsn { get; set; }

    public DateTime? DateOfHire { get; set; }

    public DateTime? DateOfTermination { get; set; }

    public int? HomeLocalId { get; set; }

    public string? ExternalEmployeeId { get; set; }

    public virtual ICollection<Agreement> Agreements { get; set; } = new List<Agreement>();

    public virtual Person IdNavigation { get; set; } = null!;

    public virtual ICollection<ReportLineItem> ReportLineItems { get; set; } =
        new List<ReportLineItem>();

    // GraphQL computed properties
    public string? FirstName => IdNavigation?.FirstName;
    public string? MiddleName => IdNavigation?.MiddleName;
    public string LastName => IdNavigation?.LastName ?? string.Empty;
    public string? Suffix => IdNavigation?.Suffix;
    public bool Active => DateOfTermination == null;
}
