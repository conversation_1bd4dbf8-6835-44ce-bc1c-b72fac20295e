﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

namespace backend.Data.Models;

public partial class DsettingsType
{
    [ID(nameof(DsettingsType))]
    [GraphQLType(typeof(IdType))]
    public int Id { get; set; }

    [GraphQLNonNullType]
    public string Name { get; set; } = null!;

    public string? Description { get; set; }

    [GraphQLNonNullType]
    public bool AllowOverride { get; set; }

    public string ValueType { get; set; } = null!;

    [GraphQLType(typeof(AnyType))]
    public string? DefaultValue { get; set; }

    public string? ValidValues { get; set; }

    [NotMapped]
    public virtual ICollection<Setting?> Settings { get; set; } = new List<Setting?>(); // Initialize to avoid null reference
}
