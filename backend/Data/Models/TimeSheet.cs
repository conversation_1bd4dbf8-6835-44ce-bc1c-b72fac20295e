using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using HotChocolate;
using Microsoft.EntityFrameworkCore;

namespace backend.Data.Models;

[Node]
public partial class TimeSheet
{
    [Key]
    [ID(nameof(TimeSheet))]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; } // Auto-generated by SQL Server IDENTITY column

    public Guid? OldId { get; set; }

    [NotMapped]
    public int NumericId => Id;

    public string? Name { get; set; }

    public DateOnly? PayPeriodEndDate { get; set; }

    public string Status { get; set; } = null!;

    public string Type { get; set; } = null!;

    public string? CreatedByUserId { get; set; }

    public string? ModifiedByUserId { get; set; }

    public DateTimeOffset? CreationDate { get; set; }

    public DateTimeOffset? ModificationDate { get; set; }

    public Guid EmployerGuid { get; set; } // TODO: Switch to integer Ids

    public bool? ShowBonusColumn { get; set; }

    public bool? ShowCostCenterColumn { get; set; }

    public bool? ShowDTHoursColumn { get; set; }

    public bool? ShowEarningsCodesColumn { get; set; }

    public bool? ShowExpensesColumn { get; set; }

    public virtual ICollection<PayStub> PayStubs { get; set; } = new List<PayStub>();

    [NotMapped]
    [GraphQLIgnore]
    public int PayStubCount => PayStubs.Count;

    public int GetPayStubCount() => PayStubs.Count;

    // Node resolver removed - using query-based resolver in TimesheetsQuery instead

}
