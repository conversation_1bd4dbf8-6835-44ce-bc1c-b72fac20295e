﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace backend.Data.Models;

public partial class Setting
{
    [ID(nameof(Setting))]
    public int Id { get; set; }

    [Required]
    public int DSettingsTypeId { get; set; }

    public string? Value { get; set; }

    [Required]
    public Guid OwnerId { get; set; }

    [Required]
    public string OwnerType { get; set; } = null!;

    public Guid? LastModifiedBy { get; set; }

    public DateTime? LastModificationDate { get; set; }

    // --- Navigation Properties ---

    [ForeignKey(nameof(DSettingsTypeId))] // EF Core: Links to the FK property
    public virtual DsettingsType? SettingsType { get; set; }
}
