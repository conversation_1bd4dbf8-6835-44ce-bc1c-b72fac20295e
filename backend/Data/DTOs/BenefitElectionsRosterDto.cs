using backend.Data.Models;

namespace backend.Data.DTOs;

public class BenefitElectionsRosterDtoBase
{
    public required string EmployerName { get; set; }
    public string? FEIN { get; set; }
    public string? DBA { get; set; }
    public string? BenefitName { get; set; }
    public decimal? Override { get; set; }
    public DateTime? EffectiveStartDate { get; set; }
    public DateTime? EffectiveEndDate { get; set; }
    public bool? Elected { get; set; }
    public Guid? EmployerGUID { get; set; }
    public string? AssociationID { get; set; }
    public decimal? Discount { get; set; }
    public DateTime? LastReport { get; set; }
}

public class BenefitElectionsRosterDto : BenefitElectionsRosterDtoBase
{
    [ID(nameof(Employer))]
    public int Id { get; set; }
}

// Edge type removed to avoid conflicts with HotChocolate's pagination edge types
