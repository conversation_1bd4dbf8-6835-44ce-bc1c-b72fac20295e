using System;
using System.Collections.Generic;
using backend.Data.Models;

namespace backend.Data.DTOs;

public class EmployerRosterInfoDtoBase
{
    public required string EmployerName { get; set; }
    public string? EmployerIdentificationNumber { get; set; }
    public DateTime? LastReportDate { get; set; }
    public int? EmployerId { get; set; }
    public string? Dba { get; set; }
    public string? BusinessDescription { get; set; }
    public int? ChapterId { get; set; }
    public string? PhoneNumber { get; set; }
    public string? EmailAddress { get; set; }
    public string? PayrollContactFirstName { get; set; }
    public string? PayrollContactLastName { get; set; }
    public string? AssociationID { get; set; }
}

public class EmployerRosterInfoDto : EmployerRosterInfoDtoBase
{
    [ID(nameof(Employer))]
    public int Id { get; set; }
}

// Edge type removed to avoid conflicts with HotChocolate's pagination edge types
