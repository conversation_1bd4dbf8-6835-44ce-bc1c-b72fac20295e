using System.ComponentModel.DataAnnotations.Schema;

namespace backend.Data.Models;

public partial class EmployerRosterView
{
    [ID(nameof(Employer))]
    public int Id { get; set; }
    public Guid GUID { get; set; }
    public string Name { get; set; } = null!;
    public string? FEIN { get; set; }
    public string? Dba { get; set; }
    public string? AssociationId { get; set; }
    public string? PayrollContactFirstName { get; set; }
    public string? PayrollContactLastName { get; set; } = null!;
    public string? PayrollContactPhoneNumber { get; set; }
    public string? PayrollContactEmailAddress { get; set; }
    public DateTime? LastReportedDate { get; set; }
    public int? RelationshipStatusId { get; set; }

    [ID(nameof(Chapter))]
    public int ChapterId { get; set; }
}
