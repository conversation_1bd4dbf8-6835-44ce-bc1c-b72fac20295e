<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <DebugType>portable</DebugType>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup Condition="'$(ImplicitUsings)' == 'enable'">
    <Using Include="backend" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="..\aspire\appsettings.Development.json" Link="appsettings.Development.json" />
    <Content Include="..\aspire\appsettings.json" Link="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
	<PackageReference Include="AppAny.HotChocolate.FluentValidation" Version="0.12.0" />
	<PackageReference Include="Confluent.Kafka" Version="2.10.0" />
	<PackageReference Include="CsvHelper" Version="33.1.0" />
	<PackageReference Include="FluentValidation" Version="12.0.0" />
	<PackageReference Include="FluentValidation.AspNetCore" Version="11.3.1" />
	<PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="12.0.0" />
    <PackageReference Include="HotChocolate.AspNetCore" Version="15.1.7" />
    <PackageReference Include="HotChocolate.AspNetCore.Authorization" Version="15.1.7" />
    <PackageReference Include="HotChocolate.Data" Version="15.1.7" />
    <PackageReference Include="HotChocolate.Data.EntityFramework" Version="15.1.7" />
    <PackageReference Include="HotChocolate.Types" Version="15.1.7" />
    <PackageReference Include="HotChocolate.Types.Analyzers" Version="15.1.7" />
    <PackageReference Include="libphonenumber-csharp" Version="9.0.6" />
    <PackageReference Include="MailKit" Version="4.12.1" />
    <PackageReference Include="Mapster" Version="7.4.0" />
    <PackageReference Include="Mapster.DependencyInjection" Version="1.0.1" />
    <PackageReference Include="Mapster.EFCore" Version="5.1.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.Cors" Version="2.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.ResponseCompression" Version="2.3.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.5">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Identity.Core" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Identity.Stores" Version="9.0.5" />
	  <PackageReference Include="OpenIddict.AspNetCore" Version="6.3.0" />
	  <PackageReference Include="StrawberryShake.CodeGeneration.CSharp" Version="12.22.6" />
	<PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.4" />
	  <PackageReference Include="QuestPDF" Version="2025.5.0" />
	  <PackageReference Include="QuestPDF.HTML" Version="1.4.2" />
	<PackageReference Include="Telerik.Documents.Spreadsheet" Version="2024.1.305" />
	<PackageReference Include="Telerik.Documents.Spreadsheet.FormatProviders.OpenXml" Version="2024.1.305" />
	<PackageReference Include="Telerik.Documents.Spreadsheet.FormatProviders.Pdf" Version="2024.1.305" />

	<!-- Testing Framework -->
	<PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="9.0.5" />
	<PackageReference Include="xunit" Version="2.9.3" />
	<PackageReference Include="xunit.runner.visualstudio" Version="3.1.0">
	  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
	  <PrivateAssets>all</PrivateAssets>
	</PackageReference>
	<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.14.1" />

	<!-- GraphQL Testing -->
	<PackageReference Include="StrawberryShake.Transport.Http" Version="15.1.5" />

	<!-- Test Database -->
	<PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="9.0.5" />
	<PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.5" />

	<!-- Mocking and Assertions -->
	<PackageReference Include="Moq" Version="4.20.72" />
	<PackageReference Include="FluentAssertions" Version="8.3.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\EPREvents\EPREvents.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
  </ItemGroup>

  <!-- GraphQL files for reference (not for code generation) -->
  <ItemGroup>
    <None Include="Tests\**\*.graphql" />
  </ItemGroup>
</Project>
