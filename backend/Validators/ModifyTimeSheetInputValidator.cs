using backend.Types.Inputs;
using FluentValidation;
using System.Linq;

namespace backend.Validators;

public class ModifyTimeSheetInputValidator : AbstractValidator<ModifyTimeSheetInput>
{
    public ModifyTimeSheetInputValidator()
    {
        RuleFor(x => x.Id).NotEmpty().WithMessage("Timesheet ID is required for modification.");
        RuleFor(x => x.employerGuid).NotEmpty().WithMessage("Employer GUID is required.");

        // Validate optional fields only if they have a value
        When(x => x.status.HasValue, () =>
        {
            RuleFor(x => x.status.Value)
                .NotEmpty().WithMessage("Status cannot be empty if provided.")
                .MaximumLength(50).WithMessage("Status cannot exceed 50 characters.");
                // TODO: Add Must() rule for specific allowed status values if needed.
                // .Must(s => s == "Draft" || s == "Submitted")
                // .WithMessage("Status must be 'Draft' or 'Submitted'.");
        });

        When(x => x.type.HasValue, () =>
        {
            RuleFor(x => x.type.Value)
                .NotEmpty().WithMessage("Type cannot be empty if provided.")
                .MaximumLength(50).WithMessage("Type cannot exceed 50 characters.");
                // TODO: Add Must() rule for specific allowed type values if needed.
        });

        RuleFor(x => x.name)
            .MaximumLength(100).WithMessage("Timesheet name cannot exceed 100 characters.")
            .When(x => x.name != null); // Optional field, validate only if present

        // No validation for payPeriodEndDate Optional<> value itself here, depends on requirements.

        // Validation for new pay stub arrays
        RuleFor(x => new { x.addPayStubs, x.modifyPayStubs, x.deletePayStubIds })
            .Must(v => (v.addPayStubs?.Any() ?? false) || (v.modifyPayStubs?.Any() ?? false) || (v.deletePayStubIds?.Any() ?? false))
            .WithMessage("At least one of addPayStubs, modifyPayStubs or deletePayStubIds must be supplied.");

        // ---------------- Additional Validation Rules (duplicates & size limits) ----------------
        const int MaxArraySize = 200;

        // Size limits
        RuleFor(x => x.addPayStubs)
            .Must(list => list == null || list.Count <= MaxArraySize)
            .WithMessage($"addPayStubs cannot contain more than {MaxArraySize} items.");

        RuleFor(x => x.modifyPayStubs)
            .Must(list => list == null || list.Count <= MaxArraySize)
            .WithMessage($"modifyPayStubs cannot contain more than {MaxArraySize} items.");

        RuleFor(x => x.deletePayStubIds)
            .Must(list => list == null || list.Count <= MaxArraySize)
            .WithMessage($"deletePayStubIds cannot contain more than {MaxArraySize} items.");

        // Duplicate detection
        RuleFor(x => x.addPayStubs)
            .Must(list => list == null || list.Select(p => p.Id).Where(id => id.HasValue).Distinct().Count() == list.Where(p => p.Id.HasValue).Count())
            .WithMessage("Duplicate PayStub IDs found in addPayStubs.");

        RuleFor(x => x.modifyPayStubs)
            .Must(list => list == null || list.Select(p => p.Id).Distinct().Count() == list.Count)
            .WithMessage("Duplicate PayStub IDs found in modifyPayStubs.");

        RuleFor(x => x.deletePayStubIds)
            .Must(list => list == null || list.Distinct().Count() == list.Count)
            .WithMessage("Duplicate PayStub IDs found in deletePayStubIds.");

        // Basic validation for deletePayStubIds not null/empty
        RuleForEach(x => x.deletePayStubIds)
            .NotEmpty()
            .WithMessage("deletePayStubIds cannot contain empty values.");

        RuleForEach(x => x.addPayStubs)
            .SetValidator(new AddPayStubInputValidator())
            .When(x => x.addPayStubs != null);

        RuleForEach(x => x.modifyPayStubs)
            .SetValidator(new ModifyPayStubInputValidator())
            .When(x => x.modifyPayStubs != null);
    }
}
