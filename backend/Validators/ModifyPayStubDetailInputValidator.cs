using backend.Types.Inputs;
using FluentValidation;

namespace backend.Validators;

public class ModifyPayStubDetailInputValidator : AbstractValidator<ModifyPayStubDetailInput>
{
    public ModifyPayStubDetailInputValidator()
    {
        // Assuming the ID must be provided for modification
        RuleFor(x => x.id)
            .NotNull().WithMessage("PayStubDetail ID is required for modification.")
            .GreaterThan(0).WithMessage("PayStubDetail ID must be a positive integer.");

        RuleFor(x => x.workDate)
            .NotEmpty().WithMessage("Work date is required.");

        RuleFor(x => x.OTHours)
            .GreaterThanOrEqualTo(0).WithMessage("OT hours cannot be negative.")
            .When(x => x.OTHours.HasValue);

        RuleFor(x => x.STHours)
            .GreaterThanOrEqualTo(0).WithMessage("ST hours cannot be negative.")
            .When(x => x.STHours.HasValue);

        RuleFor(x => x.DTHours)
            .GreaterThanOrEqualTo(0).WithMessage("DT hours cannot be negative.")
            .When(x => x.DTHours.HasValue);

        // totalHours is now computed automatically from STHours + OTHours + DTHours
        // No validation needed as it's not accepted as input

        RuleFor(x => x.jobCode)
            .MaximumLength(50).WithMessage("Job code cannot exceed 50 characters.")
            .When(x => x.jobCode != null);

        RuleFor(x => x.earningsCode)
            .MaximumLength(50).WithMessage("Earnings code cannot exceed 50 characters.")
            .When(x => x.earningsCode != null);

        RuleFor(x => x.agreementId)
            .GreaterThan(0).WithMessage("Agreement ID must be positive.")
            .When(x => x.agreementId.HasValue);

        RuleFor(x => x.classificationId)
            .GreaterThan(0).WithMessage("Classification ID must be positive.")
            .When(x => x.classificationId.HasValue);

        RuleFor(x => x.subClassificationId)
            .GreaterThan(0).WithMessage("Sub-Classification ID must be positive.")
            .When(x => x.subClassificationId.HasValue);

        RuleFor(x => x.costCenter)
            .MaximumLength(50).WithMessage("Cost center cannot exceed 50 characters.")
            .When(x => x.costCenter != null);

        RuleFor(x => x.hourlyRate)
            .GreaterThanOrEqualTo(0).WithMessage("Hourly rate cannot be negative.")
            .When(x => x.hourlyRate.HasValue);

        RuleFor(x => x.bonus)
            .GreaterThanOrEqualTo(0).WithMessage("Bonus cannot be negative.")
            .When(x => x.bonus.HasValue);

        RuleFor(x => x.expenses)
            .GreaterThanOrEqualTo(0).WithMessage("Expenses cannot be negative.")
            .When(x => x.expenses.HasValue);
    }
}
