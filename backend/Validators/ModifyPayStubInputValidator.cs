using backend.Types.Inputs;
using backend.Utils;
using FluentValidation;

namespace backend.Validators;

public class ModifyPayStubInputValidator : AbstractValidator<ModifyPayStubInput>
{
    public ModifyPayStubInputValidator()
    {
        // ID validation for modification operations
        RuleFor(x => x.Id)
            .GreaterThan(0).WithMessage("PayStub ID must be a positive integer.");

        // Employee ID validation (consistent with Add validator)
        RuleFor(x => x.employeeId)
            .GreaterThan(0).WithMessage("Employee ID must be positive.");

        // Employee name validation (consistent with Add validator)
        RuleFor(x => x.employeeName)
            .MaximumLength(Constants.PayStubValidation.MaxEmployeeNameLength)
            .WithMessage($"Employee name cannot exceed {Constants.PayStubValidation.MaxEmployeeNameLength} characters.")
            .When(x => x.employeeName != null);

        // Numeric field validations (consistent with Add validator)
        // Note: totalHours removed - now computed from details

        RuleFor(x => x.STHours)
            .GreaterThanOrEqualTo(Constants.PayStubValidation.MinMeaningfulValue)
            .WithMessage("ST hours cannot be negative.")
            .When(x => x.STHours.HasValue);

        RuleFor(x => x.OTHours)
            .GreaterThanOrEqualTo(Constants.PayStubValidation.MinMeaningfulValue)
            .WithMessage("OT hours cannot be negative.")
            .When(x => x.OTHours.HasValue);

        RuleFor(x => x.DTHours)
            .GreaterThanOrEqualTo(Constants.PayStubValidation.MinMeaningfulValue)
            .WithMessage("DT hours cannot be negative.")
            .When(x => x.DTHours.HasValue);

        RuleFor(x => x.bonus)
            .GreaterThanOrEqualTo(Constants.PayStubValidation.MinMeaningfulValue)
            .WithMessage("Bonus cannot be negative.")
            .When(x => x.bonus.HasValue);

        RuleFor(x => x.expenses)
            .GreaterThanOrEqualTo(Constants.PayStubValidation.MinMeaningfulValue)
            .WithMessage("Expenses cannot be negative.")
            .When(x => x.expenses.HasValue);

        // Details collection validation (consistent with Add validator)
        RuleFor(x => x.details)
            .NotNull().WithMessage("Pay stub details list cannot be null if provided, use an empty list instead.")
            .When(x => x.details != null);

        RuleForEach(x => x.details)
            .SetValidator(new ModifyPayStubDetailInputValidator())
            .When(x => x.details != null);

        // PayStub validation using shared utility
        // Header-only PayStubs are allowed as per business requirements
        RuleFor(x => x)
            .Must(PayStubValidationUtils.IsValidHeaderOnlyOrDetailBasedPayStub)
            .WithMessage("PayStub validation failed.");
    }
}
