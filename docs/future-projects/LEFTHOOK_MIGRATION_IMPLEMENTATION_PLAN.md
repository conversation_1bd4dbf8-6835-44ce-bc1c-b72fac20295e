# Lefthook Migration Implementation Plan

## Executive Summary

### Why This Migration is Necessary

The current pre-commit hook system has several critical limitations that impact developer productivity and cross-platform compatibility:

1. **Platform Compatibility Issues**: The existing bash-based hook (`ui/.githooks/pre-commit`) fails on Windows systems where bash is not available by default, creating barriers for developers using different operating systems.

2. **Monorepo Inefficiency**: The current hook is hardcoded to switch to the `ui/` directory and only validates frontend code, meaning:
   - Backend changes (C#/.NET) trigger unnecessary UI validations
   - UI changes don't trigger appropriate backend validations when needed
   - Mixed commits run irrelevant checks, slowing down development

3. **Maintenance Complexity**: The 184-line bash script contains complex logic for file detection, path manipulation, and error handling that is difficult to maintain and extend.

4. **Scalability Limitations**: Adding new workspaces (aspire, traefik, EPREvents) requires significant bash scripting knowledge and manual path management.

### Benefits of Lefthook Migration

**Immediate Benefits:**
- ✅ **Cross-platform compatibility** - Works natively on Windows, macOS, and Linux
- ✅ **Workspace isolation** - Only runs relevant checks for changed files
- ✅ **Faster execution** - Parallel command execution and intelligent file filtering
- ✅ **Zero bash dependency** - Single Go binary with no shell requirements

**Long-term Benefits:**
- ✅ **Maintainable configuration** - Declarative YAML vs imperative bash
- ✅ **Scalable architecture** - Easy to add new workspaces and checks
- ✅ **Team productivity** - Consistent experience across all development environments
- ✅ **Reduced onboarding friction** - No platform-specific setup requirements

### Target Audience

This plan is designed for a new team member implementing these changes with no prior knowledge of the application. Each phase includes detailed explanations, validation steps, and rollback procedures.

## Project Structure Overview

This is a .NET monorepo with the following workspaces:

```
eprlive24/timesheet-fix8/
├── ui/                    # React/TypeScript frontend (pnpm, Relay GraphQL)
├── backend/               # .NET 9.0 Web API with GraphQL (HotChocolate)
├── identity/              # .NET 9.0 Identity Server (OpenIddict)
├── aspire/                # .NET Aspire orchestration
├── EPREvents/             # .NET Event handling library
├── traefik/               # Reverse proxy configuration
└── docs/                  # Documentation
```

**Current Hook Location**: `ui/.githooks/pre-commit` (bash script, UI-specific)
**Target Hook Location**: `lefthook.yml` (repository root, workspace-aware)

## Implementation Phases

### Phase 1: Environment Setup and Lefthook Installation
**Estimated Time**: 30 minutes
**Risk Level**: Low
**Dependencies**: None

### Phase 2: Basic Lefthook Configuration
**Estimated Time**: 45 minutes  
**Risk Level**: Low
**Dependencies**: Phase 1

### Phase 3: UI Workspace Migration
**Estimated Time**: 60 minutes
**Risk Level**: Medium
**Dependencies**: Phase 2

### Phase 4: Backend and Identity Workspace Integration
**Estimated Time**: 45 minutes
**Risk Level**: Medium  
**Dependencies**: Phase 3

### Phase 5: Advanced Features and Optimization
**Estimated Time**: 30 minutes
**Risk Level**: Low
**Dependencies**: Phase 4

### Phase 6: Legacy Hook Cleanup and Documentation
**Estimated Time**: 15 minutes
**Risk Level**: Low
**Dependencies**: Phase 5

---

## Phase 1: Environment Setup and Lefthook Installation

### Objectives
- Install Lefthook at the repository root
- Verify cross-platform compatibility
- Establish baseline functionality

### Prerequisites
- Git repository access
- Node.js/pnpm installed (for UI workspace)
- .NET 9.0 SDK installed (for backend workspaces)

### Implementation Steps

#### Step 1.1: Install Lefthook
```bash
# Navigate to repository root
cd /path/to/eprlive24/timesheet-fix8

# Install Lefthook via npm (cross-platform)
npm install lefthook --save-dev
```

#### Step 1.2: Verify Installation
```bash
# Test Lefthook installation
npx lefthook version

# Expected output: lefthook version X.X.X
```

#### Step 1.3: Create Basic Configuration
Create `lefthook.yml` in repository root:

```yaml
# lefthook.yml
# Basic configuration to verify Lefthook is working

pre-commit:
  commands:
    welcome:
      run: echo "🎉 Lefthook is working! Repository root: $(pwd)"
```

#### Step 1.4: Install Git Hooks
```bash
# Install Lefthook hooks to .git/hooks/
npx lefthook install

# Verify hook installation
ls -la .git/hooks/pre-commit
```

### Validation Steps

#### Test 1: Basic Hook Execution
```bash
# Create a test commit to verify hook runs
echo "# Test" > test-file.md
git add test-file.md
git commit -m "Test Lefthook installation"

# Expected: Hook runs and shows welcome message
# Clean up: git reset --soft HEAD~1 && git reset HEAD test-file.md && rm test-file.md
```

#### Test 2: Cross-Platform Verification
- **Windows**: Test in PowerShell and Command Prompt
- **macOS/Linux**: Test in Terminal
- **Git Bash (Windows)**: Verify compatibility

### Success Criteria
- ✅ Lefthook installs without errors
- ✅ Basic hook executes on commit
- ✅ Works across different shells/platforms
- ✅ No impact on existing functionality

### Rollback Procedure
```bash
# Remove Lefthook hooks
npx lefthook uninstall

# Remove Lefthook package
npm uninstall lefthook

# Remove configuration
rm lefthook.yml
```

---

## Phase 2: Basic Lefthook Configuration

### Objectives
- Configure workspace-aware file detection
- Implement basic validation structure
- Establish parallel execution patterns

### Implementation Steps

#### Step 2.1: Update Lefthook Configuration
Replace the basic `lefthook.yml` with workspace-aware configuration:

```yaml
# lefthook.yml
# Workspace-aware pre-commit configuration

pre-commit:
  parallel: true
  commands:
    # Repository-wide checks
    repo-info:
      run: echo "🔍 Checking repository at $(pwd)"
    
    # UI workspace detection
    ui-detection:
      glob: "ui/**/*"
      run: echo "📱 UI files detected - would run UI validations"
    
    # Backend workspace detection  
    backend-detection:
      glob: "backend/**/*"
      run: echo "🔧 Backend files detected - would run .NET validations"
    
    # Identity workspace detection
    identity-detection:
      glob: "identity/**/*"
      run: echo "🔐 Identity files detected - would run Identity validations"
```

#### Step 2.2: Test File Detection
```bash
# Test UI file detection
echo "// Test" > ui/test.ts
git add ui/test.ts
git commit -m "Test UI detection"

# Test backend file detection  
echo "// Test" > backend/test.cs
git add backend/test.cs
git commit -m "Test backend detection"

# Test mixed workspace detection
echo "// Test UI" > ui/test2.ts
echo "// Test Backend" > backend/test2.cs
git add ui/test2.ts backend/test2.cs
git commit -m "Test mixed workspace detection"
```

### Validation Steps

#### Test 1: Workspace Isolation
- Verify UI changes only trigger UI detection
- Verify backend changes only trigger backend detection
- Verify mixed changes trigger both detections

#### Test 2: Parallel Execution
- Confirm commands run in parallel (check timing)
- Verify no race conditions or conflicts

### Success Criteria
- ✅ File detection works for all workspaces
- ✅ Parallel execution functions correctly
- ✅ No false positives or missed detections

---

## Phase 3: UI Workspace Migration

### Objectives
- Migrate existing UI validation logic to Lefthook
- Maintain all current validation capabilities
- Ensure backward compatibility

### Current UI Hook Analysis
The existing `ui/.githooks/pre-commit` performs these validations:
1. Relay GraphQL compilation (`pnpm relay`)
2. TypeScript type checking (`pnpm check`)
3. Fragment dependency validation (`node scripts/validate-fragment-references.cjs`)
4. ESLint with custom rules (`pnpm eslint`)
5. Pattern detection (ErrorBoundary + View components)
6. Fragment validation tests

### Implementation Steps

#### Step 3.1: Basic UI Commands
Update `lefthook.yml` to include UI-specific validations:

```yaml
# lefthook.yml - UI workspace integration

pre-commit:
  parallel: true
  commands:
    # UI Workspace Validations
    ui-relay-compile:
      glob: "ui/**/*.{ts,tsx,graphql}"
      root: "ui/"
      run: pnpm relay
    
    ui-typescript-check:
      glob: "ui/**/*.{ts,tsx}"
      root: "ui/"
      run: pnpm check
    
    ui-fragment-validation:
      glob: "ui/**/*.{ts,tsx}"
      root: "ui/"
      run: node scripts/validate-fragment-references.cjs
    
    ui-eslint:
      glob: "ui/**/*.{ts,tsx}"
      root: "ui/"
      run: pnpm eslint {staged_files}
```

**⚠️ IMPORTANT**: This phase implements partial UI validation. The pattern detection and fragment tests from the original hook will be added in subsequent steps. TypeScript and ESLint may show errors until the complete migration is finished.

#### Step 3.2: Add Fragment Validation Tests
```yaml
    ui-fragment-tests:
      glob: "ui/**/*.{ts,tsx}"
      root: "ui/"
      run: pnpm test src/components/TimesheetDetail/__tests__/FragmentValidation.test.tsx --no-watchAll --silent
```

#### Step 3.3: Implement Pattern Detection
Since Lefthook doesn't have built-in pattern detection, create a Node.js script:

Create `ui/scripts/pattern-detection.js`:
```javascript
#!/usr/bin/env node
// Pattern detection for problematic code patterns

const fs = require('fs');
const path = require('path');

// Get staged TypeScript files
const stagedFiles = process.argv.slice(2);
let hasIssues = false;

stagedFiles.forEach(file => {
  if (!file.endsWith('.ts') && !file.endsWith('.tsx')) return;
  
  const fullPath = path.join(process.cwd(), file);
  if (!fs.existsSync(fullPath)) return;
  
  const content = fs.readFileSync(fullPath, 'utf8');
  
  // Check for View components in error boundaries
  if (content.includes('ErrorBoundary') && content.includes('<View')) {
    console.error(`❌ Found View component in error boundary in ${file}`);
    console.error('   This can cause HTML structure violations in table contexts');
    hasIssues = true;
  }
  
  // Check for useFragment without corresponding fragment spread
  if (content.includes('useFragment')) {
    const fragmentNames = content.match(/useFragment\([^)]*\)/g);
    if (fragmentNames) {
      console.log(`Found useFragment calls in ${file}, verifying fragment spreads...`);
    }
  }
});

if (hasIssues) {
  console.error('❌ Found problematic patterns that may cause runtime errors');
  process.exit(1);
} else {
  console.log('✅ No known problematic patterns found');
  process.exit(0);
}
```

Add to `lefthook.yml`:
```yaml
    ui-pattern-detection:
      glob: "ui/**/*.{ts,tsx}"
      root: "ui/"
      run: node scripts/pattern-detection.js {staged_files}
```

### Validation Steps

#### Test 1: Individual UI Validations
Test each validation independently:
```bash
# Test Relay compilation
echo "// Test change" >> ui/src/test.ts
git add ui/src/test.ts
git commit -m "Test Relay compilation"

# Test TypeScript checking
# (Introduce a TypeScript error and verify it's caught)

# Test ESLint
# (Introduce an ESLint violation and verify it's caught)
```

#### Test 2: Fragment Validation
```bash
# Test fragment validation script
cd ui
node scripts/validate-fragment-references.cjs
```

#### Test 3: Pattern Detection
```bash
# Test pattern detection script
cd ui  
node scripts/pattern-detection.js src/components/SomeComponent.tsx
```

### Success Criteria
- ✅ All UI validations run correctly
- ✅ Validation failures prevent commits
- ✅ Performance is comparable to original hook
- ✅ All existing UI validation logic is preserved

### Known Issues in This Phase
**⚠️ DEPENDENCY ALERT**: This phase may show TypeScript or ESLint errors in backend/identity workspaces until Phase 4 is completed. This is expected and will be resolved in the next phase.

---

## Phase 4: Backend and Identity Workspace Integration

### Objectives
- Add .NET-specific validations for backend and identity workspaces
- Implement C# code formatting and build checks
- Ensure comprehensive workspace coverage

### Implementation Steps

#### Step 4.1: Backend Workspace Validations
Add backend-specific commands to `lefthook.yml`:

```yaml
    # Backend Workspace Validations
    backend-format-check:
      glob: "backend/**/*.{cs,csproj}"
      root: "backend/"
      run: dotnet format --verify-no-changes
    
    backend-build-check:
      glob: "backend/**/*.{cs,csproj}"
      root: "backend/"
      run: dotnet build --no-restore --verbosity quiet
    
    backend-test-check:
      glob: "backend/**/*.{cs,csproj}"
      root: "backend/"
      run: dotnet test --no-build --verbosity quiet
```

#### Step 4.2: Identity Workspace Validations
```yaml
    # Identity Workspace Validations
    identity-format-check:
      glob: "identity/**/*.{cs,csproj}"
      root: "identity/"
      run: dotnet format --verify-no-changes
    
    identity-build-check:
      glob: "identity/**/*.{cs,csproj}"
      root: "identity/"
      run: dotnet build --no-restore --verbosity quiet
```

#### Step 4.3: EPREvents Library Validations
```yaml
    # EPREvents Library Validations
    events-format-check:
      glob: "EPREvents/**/*.{cs,csproj}"
      root: "EPREvents/"
      run: dotnet format --verify-no-changes
    
    events-build-check:
      glob: "EPREvents/**/*.{cs,csproj}"
      root: "EPREvents/"
      run: dotnet build --no-restore --verbosity quiet
```

### Validation Steps

#### Test 1: Backend Validations
```bash
# Test backend format checking
echo "// Test comment" >> backend/Controllers/TestController.cs
git add backend/Controllers/TestController.cs
git commit -m "Test backend format check"

# Test backend build
# (Introduce a compilation error and verify it's caught)
```

#### Test 2: Identity Validations
```bash
# Test identity format checking
echo "// Test comment" >> identity/Program.cs
git add identity/Program.cs
git commit -m "Test identity format check"
```

#### Test 3: Mixed Workspace Commits
```bash
# Test mixed UI + Backend changes
echo "// UI change" >> ui/src/test.ts
echo "// Backend change" >> backend/Controllers/TestController.cs
git add ui/src/test.ts backend/Controllers/TestController.cs
git commit -m "Test mixed workspace validation"
```

### Success Criteria
- ✅ Backend validations run only for backend changes
- ✅ Identity validations run only for identity changes
- ✅ Mixed commits run appropriate validations for each workspace
- ✅ .NET build and format checks work correctly

---

## Phase 5: Advanced Features and Optimization

### Objectives
- Implement conditional execution optimizations
- Add repository-wide validations
- Configure advanced Lefthook features

### Implementation Steps

#### Step 5.1: Repository-Wide Validations
Add validations that apply to the entire repository:

```yaml
    # Repository-wide validations
    docker-validation:
      glob: "**/*.{yml,yaml,dockerfile,Dockerfile}"
      run: echo "🐳 Docker configuration files detected - validation would run here"
    
    documentation-check:
      glob: "**/*.md"
      run: echo "📚 Documentation files detected - validation would run here"
```

#### Step 5.2: Performance Optimizations
```yaml
# Add skip conditions and performance settings
pre-commit:
  parallel: true
  skip:
    - merge
    - rebase
  commands:
    # ... existing commands with optimizations
    ui-eslint:
      glob: "ui/**/*.{ts,tsx}"
      root: "ui/"
      run: pnpm eslint {staged_files}
      skip:
        - ref: main
          reason: "Skip ESLint on main branch merges"
```

#### Step 5.3: Error Handling and Output
```yaml
# Configure output and error handling
output:
  - execution
  - failure
  - summary

colors: true
```

### Validation Steps

#### Test 1: Skip Conditions
```bash
# Test skip conditions during merge
git checkout -b test-branch
echo "test" > test.txt
git add test.txt
git commit -m "Test commit"
git checkout main
git merge test-branch
# Verify hooks are skipped during merge
```

#### Test 2: Performance
```bash
# Measure execution time
time git commit -m "Performance test"
```

### Success Criteria
- ✅ Skip conditions work correctly
- ✅ Performance is optimized
- ✅ Output is clear and informative

---

## Phase 6: Legacy Hook Cleanup and Documentation

### Objectives
- Remove old bash hook
- Update documentation
- Provide team migration guide

### Implementation Steps

#### Step 6.1: Remove Legacy Hook
```bash
# Remove old bash hook
rm ui/.githooks/pre-commit

# Remove lint-staged configuration from ui/package.json
# (Edit ui/package.json to remove lint-staged section)
```

#### Step 6.2: Update Package.json
Remove the lint-staged configuration from `ui/package.json`:
```json
// Remove this section:
"lint-staged": {
    "*.{ts,tsx}": [
        "pnpm relay",
        "pnpm check", 
        "pnpm lint --fix --max-warnings=0"
    ]
}
```

#### Step 6.3: Create Migration Documentation
Create `docs/LEFTHOOK_MIGRATION_GUIDE.md` with team instructions.

#### Step 6.4: Update README Files
Update repository and workspace README files to reflect new hook system.

### Final Validation

#### Comprehensive Test Suite
```bash
# Test 1: UI-only changes
echo "// UI test" >> ui/src/test.ts
git add ui/src/test.ts
git commit -m "UI-only test"

# Test 2: Backend-only changes  
echo "// Backend test" >> backend/test.cs
git add backend/test.cs
git commit -m "Backend-only test"

# Test 3: Mixed changes
echo "// Mixed UI" >> ui/src/test2.ts
echo "// Mixed Backend" >> backend/test2.cs
git add ui/src/test2.ts backend/test2.cs
git commit -m "Mixed workspace test"

# Test 4: Cross-platform verification
# Run tests on Windows, macOS, and Linux
```

### Success Criteria
- ✅ Legacy hooks completely removed
- ✅ All validations work correctly
- ✅ Documentation is complete and accurate
- ✅ Team can follow migration guide successfully

## Rollback Strategy

If issues arise during migration, follow this rollback procedure:

### Emergency Rollback
```bash
# 1. Uninstall Lefthook
npx lefthook uninstall
npm uninstall lefthook
rm lefthook.yml

# 2. Restore original hook
git checkout HEAD -- ui/.githooks/pre-commit
chmod +x ui/.githooks/pre-commit

# 3. Reinstall original hook
cd ui
git config core.hooksPath .githooks
```

### Partial Rollback
If only specific validations are problematic:
```yaml
# Disable specific commands in lefthook.yml
pre-commit:
  commands:
    problematic-command:
      skip: true
      reason: "Temporarily disabled due to issues"
```

## Post-Migration Monitoring

### Week 1: Monitor for Issues
- Track commit success/failure rates
- Monitor developer feedback
- Check for platform-specific issues

### Week 2: Performance Analysis
- Compare hook execution times
- Analyze parallel execution benefits
- Optimize slow commands

### Month 1: Team Feedback
- Collect developer experience feedback
- Identify additional optimization opportunities
- Plan future enhancements

## Conclusion

This migration will modernize the pre-commit hook system, improve cross-platform compatibility, and provide a scalable foundation for future development. The phased approach ensures minimal disruption while maintaining all existing validation capabilities.

**Estimated Total Implementation Time**: 3.5 hours
**Risk Level**: Medium (with comprehensive rollback procedures)
**Team Impact**: Positive (improved developer experience across all platforms)
