#!/bin/bash

# Pre-commit hook for TypeScript, Relay compilation, and ESLint checks
# Fragment validation script has been removed to reduce maintenance overhead

set -e

echo "🔍 Running pre-commit fragment validation..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Track if any checks failed
FAILED=0

# Function to print colored output
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
        FAILED=1
    fi
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Change to the ui directory if we're in the repo root
if [ -d "ui" ] && [ -f "ui/package.json" ]; then
    echo "📂 Changing to ui directory..."
    cd ui
elif [ ! -f "package.json" ]; then
    print_warning "No package.json found, skipping fragment validation"
    exit 0
fi

# Check if we have pnpm (required by project)
if ! command -v pnpm &> /dev/null; then
    print_warning "pnpm not found, skipping validation"
    exit 0
fi

# Get list of staged files
STAGED_FILES=$(git diff --cached --name-only --diff-filter=ACM)
TS_FILES=$(echo "$STAGED_FILES" | grep -E '\.(ts|tsx)$' || true)
GRAPHQL_RELATED_FILES=$(echo "$STAGED_FILES" | grep -E '\.(ts|tsx|graphql)$' || true)

# If we're in the ui directory, remove the ui/ prefix from file paths
if [ "$(basename "$PWD")" = "ui" ]; then
    TS_FILES=$(echo "$TS_FILES" | sed 's|^ui/||' | grep -v '^$' || true)
    GRAPHQL_RELATED_FILES=$(echo "$GRAPHQL_RELATED_FILES" | sed 's|^ui/||' | grep -v '^$' || true)
fi

if [ -z "$TS_FILES" ] && [ -z "$GRAPHQL_RELATED_FILES" ]; then
    echo "🔄 No TypeScript or GraphQL files staged, skipping fragment validation"
    exit 0
fi

echo "📁 Found $(echo "$TS_FILES" | wc -l) TypeScript files to validate"

# 1. Run Relay compiler to ensure GraphQL is valid
echo "🔄 Running Relay compiler..."
if pnpm relay > /dev/null 2>&1; then
    print_status 0 "Relay compilation successful"
else
    print_status 1 "Relay compilation failed - check GraphQL syntax"
    echo "Run 'pnpm relay' to see detailed errors"
    exit 1
fi

# 2. Run TypeScript check
echo "🔄 Running TypeScript checks..."
if pnpm check > /dev/null 2>&1; then
    print_status 0 "TypeScript checks passed"
else
    print_status 1 "TypeScript checks failed"
    echo "Run 'pnpm check' to see detailed errors"
    exit 1
fi

# 3. Run ESLint on staged files (includes custom fragment and HTML rules)
echo "🔄 Running ESLint on staged files..."
ESLINT_EXIT=0

# Create a temporary list of files to lint
TEMP_FILE=$(mktemp)
echo "$TS_FILES" > "$TEMP_FILE"

if [ -s "$TEMP_FILE" ]; then
    # Run ESLint with all configured rules (includes our custom rules)
    if pnpm eslint $(cat "$TEMP_FILE") > /dev/null 2>&1; then
        print_status 0 "ESLint checks passed"
    else
        print_status 1 "ESLint checks failed"
        echo "Run 'pnpm eslint $(cat "$TEMP_FILE")' to see specific violations"
        ESLINT_EXIT=1
    fi
fi

rm -f "$TEMP_FILE"

# 4. Check for specific patterns that caused the original issues
echo "🔄 Checking for known problematic patterns..."

PATTERN_ISSUES=0

# Check for View components in error boundaries
for file in $TS_FILES; do
    # Only check files that exist and are not empty
    if [ -n "$file" ] && [ -f "$file" ]; then
        if grep -q "ErrorBoundary" "$file" && grep -q "<View" "$file"; then
            echo "❌ Found View component in error boundary in $file"
            echo "   This can cause HTML structure violations in table contexts"
            PATTERN_ISSUES=1
        fi

        # Check for useFragment without corresponding fragment spread
        if grep -q "useFragment" "$file"; then
            # Extract fragment names used in useFragment calls
            FRAGMENT_NAMES=$(grep -o "useFragment([^)]*)" "$file" | grep -o "[A-Za-z_][A-Za-z0-9_]*Fragment" || true)

            if [ -n "$FRAGMENT_NAMES" ]; then
                echo "Found useFragment calls in $file, verifying fragment spreads..."
                # This is a simplified check - the full validation script does more thorough analysis
            fi
        fi
    fi
done

if [ $PATTERN_ISSUES -eq 0 ]; then
    print_status 0 "No known problematic patterns found"
else
    print_status 1 "Found problematic patterns that may cause runtime errors"
fi

# Final result
echo ""
if [ $FAILED -eq 0 ] && [ $ESLINT_EXIT -eq 0 ] && [ $PATTERN_ISSUES -eq 0 ]; then
    echo -e "${GREEN}🎉 All pre-commit checks passed! Safe to commit.${NC}"
    exit 0
else
    echo -e "${RED}💥 Pre-commit checks failed!${NC}"
    echo ""
    echo "❌ Issues found that could cause the same problems we just fixed:"
    echo "   • Missing Relay fragment spreads"
    echo "   • Invalid HTML structure in table contexts"
    echo "   • Error boundaries with improper HTML elements"
    echo ""
    echo "🔧 To fix these issues:"
    echo "   1. Check that error boundaries render proper table elements"
    echo "   2. Ensure all child component fragments are spread in parent fragments"
    echo "   3. Run 'pnpm relay && pnpm check' after fixes"
    echo ""
    echo "⚡ To skip these checks (not recommended): git commit --no-verify"
    exit 1
fi
