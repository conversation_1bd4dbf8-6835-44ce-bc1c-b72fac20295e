# Timesheet Identifier Consolidation – Implementation Plan

_Last updated: 2025-06-26_

## 1 ▪ Executive Summary
The code-base currently juggles **three** identifiers for a `TimeSheet`:

| Name | Type in GraphQL | Origin / Purpose |
|------|-----------------|------------------|
| `id` | `ID!` (encoded) | Relay-global ID derived from the DB integer PK. Returned on *all* queries. |
| `numericId` | `Int` | Convenience field exposed by backend so the UI could bypass ID decoding early in the project. |
| `timeSheetId` | `UUID` | Legacy GUID kept around to support imports and old mobile APIs. Accidentally surfaced in the recent refactor and caused the GraphQL error documented in `EMPLOYEE_PAYSTUB_ADD_ERROR_ANALYSIS.md`. |

This mix confuses new developers, bloats schemas, and—most importantly—introduces **type mismatches** that manifest as runtime GraphQL validation failures.

The goal of this initiative is to **eliminate `numericId` and `timeSheetId`**, unify on a single Relay-friendly identifier (the standard `id`) and provide helper utilities for decoding the integer PK when absolutely necessary.

---

## 2 ▪ Objectives
1. Simplify the public GraphQL contract to a single identifier (`id`).
2. Remove dead/legacy ID fields from both backend and UI.
3. Ensure no feature regressions (roster filters, sorting, existing deep links, etc.).
4. Provide clear documentation and utilities for converting IDs when the integer value is required.

---

## 3 ▪ Scope
### In-scope
* Backend schema, validators, models, and resolvers/mutations involving `TimeSheet` and nested inputs.
* Front-end Relay fragments, React components, Zustand stores, and helper services.
* Build/CI scripts that rely on `numericId` (e.g. e2e tests).
* Unit/integration tests.
* Documentation (developer docs + API reference).

### Out-of-scope
* Historical REST or mobile APIs (they will continue to use the legacy stack). These endpoints already map GUID → PK internally.
* Database schema changes (PK remains `INT`).
* Feature enhancements unrelated to identifiers.

---

## 4 ▪ High-level Design
```
┌────────────┐        Encoded as relay-id        ┌────────────┐
│  DB INT PK ├──────────────────────────────────►│ id: ID!    │
└────────────┘                                    └────────────┘
      │                                              ▲
      │ decode()                                     │ encode()
      ▼                                              │
┌────────────────┐     Utility (shared)     ┌─────────────────────┐
│ RelayIdService │◄────────────────────────►│ NodeIdSerializer    │
└────────────────┘                           └─────────────────────┘
```
* The integer PK never leaves the backend except base64-encoded in `id`.
* `RelayIdService` (UI) & `NodeIdSerializer` (backend) remain the canonical encoder/decoder pair.
* All references to `numericId`/`timeSheetId` are removed; the UI calls `RelayIdService.decode()` only when it truly needs the integer.

---

## 5 ▪ Detailed Work Break-down
### 5.1 Backend
1. **GraphQL Schema (`schema.graphql`)**
   * Remove `numericId` from `TimeSheet` type (and any `@deprecated` comment).
   * Delete `timeSheetId` from `ModifyTimeSheetInput` (and any sibling input types).
   * Ensure every place that previously returned/accepted those fields now uses `id`.

2. **C# Input Records & Validators**
   * `ModifyTimeSheetInput.cs`: drop `Guid? timeSheetId` parameter.
   * `ModifyTimeSheetInputValidator.cs`: remove related `RuleFor`.

3. **Resolvers & Mutations**
   * `TimesheetMutations.cs` and any helper functions: replace usages of `input.timeSheetId` with decoded `id` or remove entirely.
   * For copy logic referencing `sourceTimesheetId` etc., use decoded `id` from `INodeIdSerializer`.

4. **Model Updates (optional)**
   * Add `[GraphQLIgnore] public int NumericId => Id;` only if some internal code still expects it, else delete the property to avoid re-exposure.

5. **Tests**
   * Update all GraphQL mutation/query payloads.
   * Ensure validator tests still pass.

### 5.2 Frontend (React + Relay)
1. **Code search & replace**
   * Locate references to `numericId` (roughly 40 files) and replace with `id` *or* `RelayIdService.decode(id)` where an integer is unavoidable.
   * Remove `timeSheetId` props / variables from mutations (`AddSinglePayStub`, `BulkAddPayStubs`, `useTimesheetSaver*` etc.).

2. **Relay Fragments & Queries**
   * Update all `*.graphql` files (filter fragments, roster fragments, detail views, upload flows) – remove `numericId`, ensure `id` is selected.
   * Regenerate artefacts (`pnpm relay:compile`).

3. **TypeScript Types**
   * Regenerate GraphQL code; update barrel exports if needed.
   * Fix compiler errors where `numericId` was used as a field name.

4. **UI State / Stores**
   * Update Zustand or Redux slices storing `numericId`.
   * Adapt selectors / memoised lookups.

5. **Utility Update**
   * Ensure `RelayIdService` exposes `encode/decode` helpers and is used wherever an int is still required (e.g. row keys for data-grid libraries).

6. **Tests**
   * Unit & component tests referencing `numericId` must be patched.
   * Update mock fixture data.

### 5.3 Docs & Tooling
* Add an **ID Semantics** section to `Timesheet-Data-Flow-Architecture.md`.
* Update README snippets and API examples.
* Remove obsolete lint-rule exceptions allowing `numericId`.

---

## 6 ▪ Verification & Acceptance Criteria
* All GraphQL operations compile (no unknown fields).
* Running `pnpm test` & `dotnet test` passes.
* Manual smoke-test:
  * Create, edit, copy, delete timesheets via UI.
  * Add pay-stubs (single & bulk) – ensure optimistic UI works.
  * Roster filtering & sorting by date/modified-by.
* GraphQL Playground introspection shows **no** `numericId` or `timeSheetId` anywhere.

---

## 7 ▪ Risks & Mitigations
| Risk | Mitigation |
|------|------------|
| Hidden runtime usages of `numericId` in business logic causing 500s | `grep -R "numericId"` in both repos and ensure 100 % removal; add compile-time check. |
| Deep links/bookmarks using `numericId` query parameters break | Decide on redirect strategy (outside scope of this change but worth documenting). |
| E2E-test snapshots break | Update Cypress/Playwright fixtures in same branch. |

---

## 8 ▪ Effort Estimate
* Backend: **0.5 d** (schema + code)  
* Frontend: **2.0 d** (search/replace, artefact regen, testing)  
* QA & docs: **0.5 d**  
* **Total:** ~3 person-days

---

## 9 ▪ Roll-out Strategy
* Single feature branch feature/ts-id-consolidation covering both repos.
* PR requires green CI & QA sign-off.
* Deploy behind usual staging gate; no feature flag (breaking change is atomic).

---

## 10 ▪ Appendices
### A. Regex to find offending code
```bash
# numericId usages (TS/JS/TSX)
rg "\\bnumericId\\b" ui/src ui/lib

# timeSheetId in backend
rg "timeSheetId" backend
```

### B. Sample Relay ID Decode Pattern
```ts
import { RelayIdService } from '@/src/services/RelayIdService';

const intId = RelayIdService.decode(globalId); // returns number
```

---

_Contact: Corellian Software Frontend Guild #timesheet-id-unification_
