# Timesheet Roster and Timesheet Detail Code Map

This document provides a comprehensive map of the source code for the Timesheet Roster and Timesheet Detail features, covering both the frontend (React/Relay) and backend (.NET/GraphQL) architecture.

## Table of Contents

- [Frontend Architecture (`ui/`)](#frontend-architecture-ui)
  - [Timesheet Roster](#timesheet-roster)
  - [Timesheet Detail](#timesheet-detail)
- [Backend Architecture (`backend/`)](#backend-architecture-backend)
- [Business Domain Primer](#business-domain-primer)
- [End-to-End "Save" Flow](#end-to-end-save-flow-frontend--backend)
- [Testing Landscape](#testing-landscape)
- [Performance Hot-Spots](#performance-hot-spots)
- [Local Development Tips](#local-development-tips)
- [Common Pitfalls](#common-pitfalls)
- [Glossary](#glossary)

## Frontend Architecture (`ui/`)

The frontend is a React application using Relay for data management and Zustand for UI state.

### Timesheet Roster

The Timesheet Roster is the main entry point for viewing and managing a list of timesheets for an employer.

**Key Container:** `ui/src/container/TimesheetRoster/TimesheetRoster.tsx`

This container is responsible for fetching and displaying the list of timesheets. It uses `useLazyLoadQuery` to fetch the initial data and `useFragment` to manage custom views.

#### Data Flow and Component Structure

```mermaid
graph TD
    subgraph "Container"
        A[TimesheetRoster.tsx]
    end

    subgraph "Components"
        B[TimesheetRosterHeader.tsx]
        C[TimesheetRosterTable.tsx]
        D[TimesheetFilter.tsx]
        E[AddTimesheet.tsx]
    end

    subgraph "State Management"
        F[useTimesheetRosterState.ts]
        G[Zustand - rosterFilterStore.ts]
    end

    subgraph "Data Fetching (Relay)"
        H[TimesheetRosterQuery.graphql.ts]
        I[TimesheetRosterUpdateCustomViewsMutation.graphql.ts]
    end

    A --> B
    A --> C
    A --> F
    A --> G
    A --> H
    A --> I

    B --> D
    B --> E
```

**Description:**

1.  **`TimesheetRoster.tsx`**: The main container that orchestrates the other components. It fetches data using `TimesheetRosterQuery` and manages the overall state of the roster.
2.  **`TimesheetRosterHeader.tsx`**: Displays the header of the roster, including filters (`TimesheetFilter.tsx`) and the "Add Timesheet" button (`AddTimesheet.tsx`).
3.  **`TimesheetRosterTable.tsx`**: Renders the table of timesheets. It receives data from the main container and handles sorting and pagination.
4.  **`useTimesheetRosterState.ts`**: A custom hook that centralizes the state management for the roster, including filters, sorting, and column visibility.
5.  **`rosterFilterStore.ts`**: A Zustand store that manages the active filters and sort order for the roster.
6.  **`TimesheetRosterQuery.graphql.ts`**: The main GraphQL query for fetching the list of timesheets.
7.  **`TimesheetRosterUpdateCustomViewsMutation.graphql.ts`**: The GraphQL mutation for saving custom views for the roster.

### Timesheet Detail

The Timesheet Detail view is used for editing and viewing the details of a single timesheet.

**Key Container:** `ui/src/container/TimesheetDetail/TimesheetDetail.tsx`

This container is responsible for fetching and displaying the details of a single timesheet.

#### Data Flow and Component Structure

```mermaid
graph TD
    subgraph "Container"
        A[TimesheetDetail.tsx]
    end

    subgraph "Components"
        B[TimesheetToolbar.tsx]
        C[TimeSheetGrid.tsx]
        D[TimeSheetDetailRow.tsx]
    end

    subgraph "State Management"
        E[Zustand - timesheetUIStore.ts]
    end

    subgraph "Hooks"
        F[useTimesheetSaver.ts]
    end

    subgraph "Data Fetching (Relay)"
        G[TimesheetDetailQuery.graphql.ts]
        H[ModifyTimeSheetMutation.graphql.ts]
    end

    A --> B
    A --> C
    A --> E

    C --> D

    B --> F
    F --> H
    A --> G
```

**Description:**

1.  **`TimesheetDetail.tsx`**: The main container that orchestrates the detail view. It fetches data using `TimesheetDetailQuery`.
2.  **`TimesheetToolbar.tsx`**: Displays the toolbar with actions like "Save", "Submit", and "Delete". It uses the `useTimesheetSaver` hook to handle save operations.
3.  **`TimeSheetGrid.tsx`**: Renders the main grid of pay stubs and their details.
4.  **`TimeSheetDetailRow.tsx`**: Represents a single row in the timesheet grid, allowing for editing of hours, job codes, etc.
5.  **`timesheetUIStore.ts`**: A Zustand store that manages the UI state for the timesheet detail view, including draft changes, validation errors, and expanded rows.
6.  **`useTimesheetSaver.ts`**: A custom hook that encapsulates the logic for saving a timesheet, including validation and calling the `ModifyTimeSheetMutation`.
7.  **`TimesheetDetailQuery.graphql.ts`**: The GraphQL query for fetching the details of a single timesheet.
8.  **`ModifyTimeSheetMutation.graphql.ts`**: The GraphQL mutation for saving changes to a timesheet.

## Backend Architecture (`backend/`)

The backend is a .NET application using GraphQL with HotChocolate for the API layer and Entity Framework Core for data access.

### Timesheet API

The backend exposes a GraphQL API for all timesheet operations.

**Key Files:**

- **`Types/Queries/Timesheets.cs`**: Defines the GraphQL queries for fetching timesheets.
- **`Types/Mutations/TimesheetMutations.cs`**: Defines the GraphQL mutations for creating, updating, and deleting timesheets.
- **`Data/Models/TimeSheet.cs`**: The Entity Framework Core model for the `TimeSheet` entity.
- **`Controllers/TimeSheetsController.cs`**: A REST controller that provides additional endpoints for exporting timesheet data.

#### Data Flow and Component Structure

```mermaid
graph TD
    subgraph "GraphQL API"
        A[GraphQL Request]
    end

    subgraph "GraphQL Types"
        B[TimesheetMutations.cs]
        C[Timesheets.cs]
    end

    subgraph "Business Logic / Data Access"
        D[TimeSheetsController.cs]
        E[EPRLiveDBContext.cs]
    end

    subgraph "Database Model"
        F[TimeSheet.cs]
    end

    A --> B
    A --> C

    B --> E
    C --> E
    D --> E

    E --> F
```

**Description:**

1.  **`TimesheetMutations.cs`**: Contains the business logic for creating, updating, and deleting timesheets. It receives input from the GraphQL API, validates the data, and interacts with the database via Entity Framework Core.
2.  **`Timesheets.cs`**: Contains the logic for fetching timesheets from the database. It uses `IQueryable` to allow for filtering, sorting, and pagination to be applied by the GraphQL engine.
3.  **`TimeSheetsController.cs`**: Provides REST endpoints for exporting timesheet data to various formats. This is used for integration with other systems.
4.  **`TimeSheet.cs`**: The EF Core model representing a timesheet in the database. It defines the schema and relationships for the `TimeSheets` table.
5.  **`EPRLiveDBContext.cs`**: The Entity Framework Core database context, which manages the connection to the database and provides `DbSet` properties for querying and saving data.

## Key Files Summary

### Frontend (`ui/`)

- **Containers**:
  - `ui/src/container/TimesheetRoster/TimesheetRoster.tsx`
  - `ui/src/container/TimesheetDetail/TimesheetDetail.tsx`
- **Hooks**:
  - `ui/src/hooks/useTimesheetRosterState.ts`
  - `ui/src/hooks/useTimesheetSaver.ts`
- **Services**:
  - `ui/src/services/timesheet-roster.ts`
  - `ui/src/services/timesheet-detail.ts`
- **State Management**:
  - `ui/src/store/timesheetUIStore.ts` (Zustand)
- **GraphQL**:
  - `ui/src/lib/relay/__generated__/TimesheetRosterQuery.graphql.ts`
  - `ui/src/lib/relay/__generated__/TimesheetDetailQuery.graphql.ts`
  - `ui/src/mutations/timesheet/ModifyTimeSheetMutation.ts`

### Backend (`backend/`)

- **GraphQL**:
  - `backend/Types/Queries/Timesheets.cs`
  - `backend/Types/Mutations/TimesheetMutations.cs`
- **Controllers**:
  - `backend/Controllers/TimeSheetsController.cs`
- **Data Models**:
  - `backend/Data/Models/TimeSheet.cs`
  - `backend/Data/Models/PayStub.cs`
  - `backend/Data/Models/PayStubDetail.cs`
- **Database Context**:
  - `backend/Data/Models/EPRLiveDBContext.cs`

---

## Business Domain Primer

| Term            | Description                                                                      |
| --------------- | -------------------------------------------------------------------------------- |
| Timesheet       | Weekly record of an employee’s worked hours, job codes and pay details.          |
| Pay Stub        | Pay summary generated from a timesheet after payroll processing.                 |
| Pay Stub Detail | Individual line in a pay stub containing earnings for a specific classification. |
| Agreement       | Employment or union agreement that defines pay rules applied during payroll.     |
| Classification  | Payroll category (e.g., Regular, Overtime) that determines rate and rules.       |

---

## End-to-End "Save" Flow (Frontend → Backend)

```mermaid
sequenceDiagram
  participant UI as TimeSheetDetailRow
  participant Store as timesheetUIStore (Zustand)
  participant Saver as useTimesheetSaver
  participant Relay as Relay Network
  participant API as modifyTimeSheet (GraphQL)

  UI->>Store: draftChange()
  UI->>Saver: onSaveClick()
  Saver->>Saver: validateTimesheet()
  Saver->>Relay: commitMutation()
  Relay->>API: GraphQL request
  API-->>Relay: payload / errors
  Relay-->>Saver: onCompleted / onError
  Saver-->>UI: toast / banner
```

**Steps**

1. User edits a cell → draft stored.
2. User clicks **Save** → `useTimesheetSaver` gathers drafts and validates.
3. If blocking errors → banner/dialog.
4. Else → Relay mutation.
5. Backend persists, returns updated node IDs.

---

## Testing Landscape

| Layer                     | Location / Pattern      | How to Run                      |
| ------------------------- | ----------------------- | ------------------------------- |
| Frontend Unit/Integration | `ui/**/*.test.{ts,tsx}` | `pnpm --filter eprlive-ui test` |
| Backend Unit              | `backend/Tests/*`       | `dotnet test backend/Tests`     |

---

## Performance Hot-Spots

- `validateTimesheet` contains nested loops; avoid O(N²) scans when possible.
- `TimeSheetGrid.tsx` uses virtualization; keep row heights stable to preserve performance.
- Monitor EF queries in `TimesheetMutations.cs` – ensure appropriate indices.

---

## Local Development Tips

Run the pnpm commands in the ui folder. dotnet commands should be run from the root of the repo.

```bash
# 1. Install dependencies
pnpm install

# 2. Generate relay artefacts (once per schema change)
pnpm relay

# 3. Run backend API (port 5100 by default)
dotnet run --launch-profile Development --project aspire

# 4. Check for TypeScript errors
pnpm check

# 5. Run UI (port 3001)
pnpm dev
```

---

## Common Pitfalls

- UUIDs are **not** valid Relay IDs – always use `toGlobalId` helpers.
- Deep linking directly to a child component may skip parent fragment data – ensure route-level queries or make fragments `@refetchable`.
- Always run `pnpm relay` after updating `.graphql` files; missing artefacts cause build errors.

---

## Glossary

| Term      | Meaning                                                             |
| --------- | ------------------------------------------------------------------- |
| D/T Hours | Double/Triple time hours lines in a pay stub.                       |
| Relay ID  | Global opaque ID created by Relay for nodes.                        |
| NumericId | Legacy integer ID used by backend models.                           |
| Saver     | The utility/hook responsible for validating and saving a timesheet. |
