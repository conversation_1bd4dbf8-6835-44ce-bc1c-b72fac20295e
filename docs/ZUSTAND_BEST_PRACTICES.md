# Zustand Best Practices and Safety Patterns

## Executive Summary

This document establishes **mandatory patterns and safety guidelines** for using Zustand state management across the application. Following these patterns prevents critical bugs including infinite render loops, performance degradation, and data inconsistencies.

**Critical Context**: This document was created after a **production infinite loop bug** caused by unsafe Zustand selector patterns. The patterns documented here are **non-negotiable** for preventing similar issues.

**Scope**: All Zustand store usage across the application including:
- Existing stores: `Store.ts`, `rosterFilterStore.ts`, `timesheetUIStore.ts`
- Future store implementations
- Component selector patterns
- Performance optimization strategies

---

## Table of Contents

1. [Fundamental Principles](#fundamental-principles)
2. [Selector Safety Patterns](#selector-safety-patterns)
3. [Store Design Patterns](#store-design-patterns)
4. [Performance Optimization](#performance-optimization)
5. [Error Prevention](#error-prevention)
6. [Testing Strategies](#testing-strategies)
7. [Migration Guidelines](#migration-guidelines)
8. [Troubleshooting Guide](#troubleshooting-guide)

---

## Fundamental Principles

### Understanding Zustand's Internal Behavior

Zustand uses <PERSON>act's `useSyncExternalStore` hook internally, which has **strict equality checking** (`===`) behavior:

```typescript
// Zustand's internal implementation (simplified)
function useStore(selector) {
    return useSyncExternalStore(
        store.subscribe,
        () => selector(store.getState()), // getSnapshot
        () => selector(store.getState())  // getServerSnapshot
    );
}
```

**Critical Insight**: If `selector(store.getState())` returns a **different reference** on subsequent calls, React assumes the data changed and triggers a re-render, even if the values are identical.

### The Golden Rules

1. **Selectors Must Return Stable References**: Same input → same object reference (unless you pass an explicit equality function).
2. **Avoid Creating Objects in Selectors**: Returning new `Map`, `Set`, arrays, or objects is safe *only* when you also supply an equality function (e.g., `zustand/shallow`). Otherwise it triggers unnecessary renders.
3. **Prefer Selective Subscriptions**: Subscribe only to the data you need.
4. **Perform Transformations in Components**: Use `useMemo` (or a memoised custom hook) for heavy computations outside the selector.
5. **Expose Actions with Stable References**: Access store actions through selectors – the functions themselves are referentially stable.
6. **Leverage Equality Functions for Advanced Cases**: When you must return composite objects (e.g., `[state.a, state.b]`) pass `shallow` or a custom comparator as the second argument to `useStore`.

---

## Selector Safety Patterns

### ❌ DANGEROUS PATTERNS (Cause Infinite Loops)

#### Object Creation in Selectors

```typescript
// ❌ NEVER DO THIS - Creates infinite render loop
const errorsByPayStubId = useTimesheetUIStore(state =>
    new Map(Array.from(state.errorsByPayStubId.entries()).map(([key, error]) => {
        if (key.startsWith(`${timeSheetId}:`)) {
            return [key.substring(`${timeSheetId}:`.length), error];
        }
        return [key, error];
    }))
);

// ❌ NEVER DO THIS - Creates new object every render
const userData = useUserStore(state => ({
    name: state.user.name,
    email: state.user.email
}));

// ❌ NEVER DO THIS - Array methods create new arrays
const activeItems = useItemStore(state =>
    state.items.filter(item => item.active)
);

// ❌ NEVER DO THIS - Spread operator creates new objects
const settings = useSettingsStore(state => ({ ...state.settings }));
```

#### Complex Transformations in Selectors

```typescript
// ❌ DANGEROUS - Complex operations in selectors
const processedData = useDataStore(state =>
    state.rawData
        .filter(item => item.status === 'active')
        .map(item => ({ ...item, computed: item.value * 2 }))
        .reduce((acc, item) => ({ ...acc, [item.id]: item }), {})
);
```

### ✅ SAFE PATTERNS (Prevent Infinite Loops)

#### Raw Selector + useMemo Pattern

```typescript
// ✅ CORRECT - Raw selector returns stable reference
const rawErrorsByPayStubId = useTimesheetUIStore(state => state.errorsByPayStubId);
const timeSheetId = useTimesheetId(); // Get from context or props

// ✅ CORRECT - Transform in component with useMemo
const errorsByPayStubId = useMemo(() => {
    const filteredMap = new Map();
    Array.from(rawErrorsByPayStubId.entries()).forEach(([key, error]) => {
        if (key.startsWith(`${timeSheetId}:`)) {
            const shortKey = key.substring(`${timeSheetId}:`.length);
            filteredMap.set(shortKey, error);
        }
    });
    return filteredMap;
}, [rawErrorsByPayStubId, timeSheetId]);
```

### Object Selectors with `zustand/shallow`

```typescript
import { shallow } from 'zustand/shallow';

// ✅ SAFE – Return an object but compare using shallow equality
a const userData = useUserStore(
    state => ({ name: state.user.name, email: state.user.email }),
    shallow
);

// Alternatively return a tuple for micro-optimisations
const [name, email] = useUserStore(
    state => [state.user.name, state.user.email],
    shallow
);
```

Using an equality function (the second argument to `useStore`) allows you to return new objects or arrays **without** causing a re-render, provided the shallow values have not changed.

**Guidelines**
- Restrict to flat collections of primitives or otherwise stable references.
- Heavy or expensive computations still belong in `useMemo` or a memoised hook.
- Collections such as `Map` / `Set` are **not** compared shallowly – avoid returning them directly.

---

#### Selective Subscription Pattern

```typescript
// ✅ CORRECT - Subscribe only to specific data
const PayStubRow: React.FC<Props> = ({ payStubId }) => {
    // Each selector is independent and specific
    const isExpanded = useTimesheetUIStore(
        useCallback(state => state.expandedPayStubs.has(payStubId), [payStubId])
    );

    const hasDraftChanges = useTimesheetUIStore(
        useCallback(state => state.draftChanges.has(payStubId), [payStubId])
    );

    // Action dispatchers (stable references)
    const updateDraft = useTimesheetUIStore(state => state.updatePayStubDraft);
    const toggleExpansion = useTimesheetUIStore(state => state.toggleExpansion);

    // Component only re-renders when its specific data changes
};
```

#### Primitive Value Selectors

```typescript
// ✅ SAFE - Primitive values are compared by value
const userName = useUserStore(state => state.user.name);
const itemCount = useItemStore(state => state.items.length);
const isLoading = useUIStore(state => state.isLoading);
const errorMessage = useErrorStore(state => state.errorMessage);
```

#### Multiple Primitive Selectors (Not Object Creation)

```typescript
// ✅ CORRECT - Separate selectors for each primitive
const userName = useUserStore(state => state.user.name);
const userEmail = useUserStore(state => state.user.email);
const userRole = useUserStore(state => state.user.role);

// ❌ WRONG - Object creation
const userData = useUserStore(state => ({
    name: state.user.name,
    email: state.user.email,
    role: state.user.role
}));
```

### Custom Hook Patterns for Complex Logic

When you need to combine multiple store values or perform complex transformations, create custom hooks:

```typescript
// ✅ CORRECT - Custom hook encapsulates complexity
export function useProcessedItems(filterId?: string) {
    const rawItems = useItemStore(state => state.items);
    const filters = useFilterStore(state => state.filters);

    return useMemo(() => {
        let items = rawItems;

        if (filterId) {
            const filter = filters.get(filterId);
            if (filter) {
                items = items.filter(item => filter.predicate(item));
            }
        }

        return items.map(item => ({
            ...item,
            computed: item.value * 2,
            formatted: formatValue(item.value)
        }));
    }, [rawItems, filters, filterId]);
}

// Usage in component
const ProcessedItemsList: React.FC<{ filterId?: string }> = ({ filterId }) => {
    const items = useProcessedItems(filterId);

    return (
        <ul>
            {items.map(item => (
                <li key={item.id}>{item.formatted}</li>
            ))}
        </ul>
    );
};
```

---

## Store Design Patterns

### Immutable State Updates

**Critical Rule**: Always create new instances of Maps, Sets, and Objects when updating state.

```typescript
// ✅ CORRECT - Create new Map instance
const store = create<Store>((set) => ({
    items: new Map(),

    addItem: (id: string, item: Item) => {
        set((state) => ({
            items: new Map(state.items).set(id, item)
        }));
    },

    removeItem: (id: string) => {
        set((state) => {
            const newItems = new Map(state.items);
            newItems.delete(id);
            return { items: newItems };
        });
    }
}));

// ❌ WRONG - Mutating existing Map
const badStore = create<Store>((set) => ({
    items: new Map(),

    addItem: (id: string, item: Item) => {
        set((state) => {
            state.items.set(id, item); // ❌ Mutation!
            return { items: state.items };
        });
    }
}));
```

### Scoped State Management

For applications with multiple instances of the same component (e.g., multiple timesheets), use scoped keys:

```typescript
interface TimesheetUIStore {
    // Scoped state - key format: "timesheetId:payStubId"
    draftChanges: Map<string, Partial<PayStubDomainModel>>;
    expandedPayStubs: Set<string>;

    // Helper for creating scoped keys
    createScopedKey: (timesheetId: string, payStubId: string) => string;

    // Scoped actions
    updatePayStubDraft: (timesheetId: string, payStubId: string, changes: Partial<PayStubDomainModel>) => void;
    toggleExpansion: (timesheetId: string, payStubId: string) => void;
}

const useTimesheetUIStore = create<TimesheetUIStore>((set, get) => ({
    draftChanges: new Map(),
    expandedPayStubs: new Set(),

    createScopedKey: (timesheetId: string, payStubId: string) => `${timesheetId}:${payStubId}`,

    updatePayStubDraft: (timesheetId: string, payStubId: string, changes: Partial<PayStubDomainModel>) => {
        set((state) => {
            const key = state.createScopedKey(timesheetId, payStubId);
            const newDrafts = new Map(state.draftChanges);
            newDrafts.set(key, { ...newDrafts.get(key), ...changes });
            return { draftChanges: newDrafts };
        });
    },

    toggleExpansion: (timesheetId: string, payStubId: string) => {
        set((state) => {
            const key = state.createScopedKey(timesheetId, payStubId);
            const newExpanded = new Set(state.expandedPayStubs);
            if (newExpanded.has(key)) {
                newExpanded.delete(key);
            } else {
                newExpanded.add(key);
            }
            return { expandedPayStubs: newExpanded };
        });
    }
}));
```

### Persistence Patterns

Use Zustand's persist middleware for data that should survive page reloads:

```typescript
import { persist } from 'zustand/middleware';

const useTimesheetUIStore = create<TimesheetUIStore>()(
    persist(
        (set, get) => ({
            // Store implementation
        }),
        {
            name: 'timesheet-ui-storage',
            version: 1,
            // Only persist critical data
            partialize: (state) => ({
                draftChanges: Array.from(state.draftChanges.entries()),
                lastSaved: state.lastSaved
                // Don't persist UI state like expandedPayStubs
            }),
            // Custom serialization for Maps/Sets
            serialize: (state) => {
                return JSON.stringify({
                    ...state,
                    state: {
                        ...state.state,
                        draftChanges: Array.from(state.state.draftChanges.entries())
                    }
                });
            },
            deserialize: (str) => {
                const parsed = JSON.parse(str);
                return {
                    ...parsed,
                    state: {
                        ...parsed.state,
                        draftChanges: new Map(parsed.state.draftChanges || [])
                    }
                };
            }
        }
    )
);
```

---

## Performance Optimization

### Selective Subscriptions

**Principle**: Components should only re-render when their specific data changes.

```typescript
// ✅ OPTIMAL - Each subscription is independent
const PayStubRow: React.FC<{ payStubId: string; timesheetId: string }> = ({ payStubId, timesheetId }) => {
    // Only re-renders when THIS payStub's expansion state changes
    const isExpanded = useTimesheetUIStore(
        useCallback(state => {
            const key = `${timesheetId}:${payStubId}`;
            return state.expandedPayStubs.has(key);
        }, [timesheetId, payStubId])
    );

    // Only re-renders when THIS payStub has draft changes
    const hasDraftChanges = useTimesheetUIStore(
        useCallback(state => {
            const key = `${timesheetId}:${payStubId}`;
            return state.draftChanges.has(key);
        }, [timesheetId, payStubId])
    );

    // Global state - all components share this subscription
    const isSaving = useTimesheetUIStore(state => state.isSaving);

    // Actions have stable references - never cause re-renders
    const updateDraft = useTimesheetUIStore(state => state.updatePayStubDraft);
    const toggleExpansion = useTimesheetUIStore(state => state.toggleExpansion);
};

// ❌ INEFFICIENT - Re-renders on any store change
const BadComponent: React.FC = () => {
    const entireStore = useTimesheetUIStore(); // Re-renders on ANY change
    const allDrafts = useTimesheetUIStore(state => state.draftChanges); // Re-renders when any draft changes
};
```

### Memoization Strategies

```typescript
// ✅ CORRECT - Memoize expensive computations
const ExpensiveComponent: React.FC<{ timesheetId: string }> = ({ timesheetId }) => {
    const rawDrafts = useTimesheetUIStore(state => state.draftChanges);

    const timesheetDrafts = useMemo(() => {
        const result = new Map();
        for (const [key, value] of rawDrafts.entries()) {
            if (key.startsWith(`${timesheetId}:`)) {
                const payStubId = key.substring(`${timesheetId}:`.length);
                result.set(payStubId, value);
            }
        }
        return result;
    }, [rawDrafts, timesheetId]);

    const totalHours = useMemo(() => {
        let total = 0;
        for (const draft of timesheetDrafts.values()) {
            total += (draft.hours?.standard || 0) + (draft.hours?.overtime || 0);
        }
        return total;
    }, [timesheetDrafts]);

    return <div>Total Hours: {totalHours}</div>;
};
```

### Batch Updates

```typescript
// ✅ CORRECT - Batch multiple state changes
const batchUpdatePayStubs = (updates: Array<{ id: string; changes: Partial<PayStub> }>) => {
    set((state) => {
        const newDrafts = new Map(state.draftChanges);
        const newExpanded = new Set(state.expandedPayStubs);

        updates.forEach(({ id, changes }) => {
            newDrafts.set(id, { ...newDrafts.get(id), ...changes });
            newExpanded.add(id); // Expand updated items
        });

        return {
            draftChanges: newDrafts,
            expandedPayStubs: newExpanded
        };
    });
};

// ❌ INEFFICIENT - Multiple separate updates
updates.forEach(({ id, changes }) => {
    updatePayStubDraft(id, changes); // Triggers re-render each time
    toggleExpansion(id); // Triggers another re-render
});
```

---

## Error Prevention

### Static Analysis and Linting

#### ESLint Rules

Add these rules to catch common Zustand anti-patterns:

```javascript
// .eslintrc.js
module.exports = {
    rules: {
        // Existing React rules that help
        'react-hooks/exhaustive-deps': 'error',
        '@eslint-react/no-unstable-context-value': 'warn',
        '@eslint-react/no-unstable-default-props': 'warn',

        // General rules that help catch object creation
        'prefer-const': 'error',
        'no-new-object': 'warn',

        // Custom warning for performance issues
        'no-warning-comments': ['warn', {
            terms: ['PERFORMANCE', 'ZUSTAND-SELECTOR'],
            location: 'start'
        }]
    }
};
```

#### Grep-Based Detection

Add to CI pipeline or pre-commit hooks:

```bash
#!/bin/bash
# check-zustand-patterns.sh

echo "Checking for dangerous Zustand selector patterns..."

# Check for object creation in selectors
if grep -r "useStore.*new Map\|useStore.*new Set\|useStore.*{.*}\|useStore.*\[.*\]" src/; then
    echo "❌ Found potential object creation in Zustand selectors"
    echo "Use raw selector + useMemo pattern instead"
    exit 1
fi

# Check for array methods in selectors
if grep -r "useStore.*\.filter\|useStore.*\.map\|useStore.*\.reduce" src/; then
    echo "❌ Found array transformation methods in Zustand selectors"
    echo "Use raw selector + useMemo pattern instead"
    exit 1
fi

echo "✅ No dangerous Zustand patterns detected"
```

### Runtime Validation (Development Only)

```typescript
// zustand-dev-utils.ts
export function createStoreWithValidation<T>(storeConfig: StateCreator<T>) {
    const store = create(storeConfig);

    if (process.env.NODE_ENV === 'development') {
        const originalUse = store.getState;

        // Wrap selector usage to detect unstable returns
        const selectorCache = new WeakMap();

        store.subscribe = ((originalSubscribe) => (selector, equalityFn) => {
            const wrappedSelector = (state: T) => {
                const result = selector(state);

                // Warn if selector returns new object references
                if (typeof result === 'object' && result !== null) {
                    const cached = selectorCache.get(selector);
                    if (cached && cached !== result && JSON.stringify(cached) === JSON.stringify(result)) {
                        console.warn(
                            'Zustand selector returned new object with same content. ' +
                            'This may cause unnecessary re-renders. Consider using useMemo.',
                            { selector: selector.toString(), result }
                        );
                    }
                    selectorCache.set(selector, result);
                }

                return result;
            };

            return originalSubscribe(wrappedSelector, equalityFn);
        })(store.subscribe);
    }

    return store;
}
```

### Code Review Checklist

When reviewing Zustand-related code, check for:

- [ ] **No object creation in selectors** (`new Map()`, `{}`, `[]`, etc.)
- [ ] **No array methods in selectors** (`.filter()`, `.map()`, `.reduce()`)
- [ ] **Selective subscriptions** (components only subscribe to needed data)
- [ ] **Proper memoization** (`useMemo` for data transformations)
- [ ] **Immutable updates** (new instances for Maps/Sets/Objects)
- [ ] **Scoped state** (appropriate key scoping for multi-instance components)
- [ ] **Action stability** (actions retrieved outside render or with stable references)

---

## Testing Strategies

### Selector Stability Tests

```typescript
// selector-stability.test.ts
import { renderHook } from '@testing-library/react';
import { useTimesheetUIStore } from '../stores/timesheetUIStore';

describe('Selector Stability', () => {
    it('should return stable references for primitive values', () => {
        const { result, rerender } = renderHook(() =>
            useTimesheetUIStore(state => state.isSaving)
        );

        const firstResult = result.current;
        rerender();
        const secondResult = result.current;

        expect(firstResult).toBe(secondResult); // Same reference
    });

    it('should return stable references when state has not changed', () => {
        const { result, rerender } = renderHook(() =>
            useTimesheetUIStore(state => state.draftChanges)
        );

        const firstResult = result.current;
        rerender();
        const secondResult = result.current;

        expect(firstResult).toBe(secondResult); // Same Map instance
    });

    it('should detect unstable selectors', () => {
        const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

        const { rerender } = renderHook(() =>
            useTimesheetUIStore(state => new Map(state.draftChanges)) // Unstable!
        );

        rerender();

        expect(consoleSpy).toHaveBeenCalledWith(
            expect.stringContaining('returned new object with same content')
        );

        consoleSpy.mockRestore();
    });
});
```

### Performance Tests

```typescript
// performance.test.ts
describe('Zustand Performance', () => {
    it('should not cause excessive re-renders', () => {
        let renderCount = 0;

        const TestComponent = () => {
            renderCount++;
            const isSaving = useTimesheetUIStore(state => state.isSaving);
            return <div>{isSaving ? 'Saving...' : 'Idle'}</div>;
        };

        const { rerender } = render(<TestComponent />);
        const initialRenderCount = renderCount;

        // Update unrelated state
        act(() => {
            useTimesheetUIStore.getState().updatePayStubDraft('ts1', 'ps1', { hours: { standard: 8 } });
        });

        rerender();

        // Should not have caused additional renders
        expect(renderCount).toBe(initialRenderCount);
    });
});
```

### Integration Tests

```typescript
// integration.test.ts
describe('Zustand Store Integration', () => {
    it('should handle complex update scenarios without infinite loops', async () => {
        const TestComponent = () => {
            const rawErrors = useTimesheetUIStore(state => state.errorsByPayStubId);
            const timeSheetId = 'ts1';

            // ✅ CORRECT - Raw selector + useMemo
            const filteredErrors = useMemo(() => {
                const result = new Map();
                for (const [key, error] of rawErrors.entries()) {
                    if (key.startsWith(`${timeSheetId}:`)) {
                        result.set(key.substring(`${timeSheetId}:`.length), error);
                    }
                }
                return result;
            }, [rawErrors, timeSheetId]);

            return <div>Errors: {filteredErrors.size}</div>;
        };

        const { container } = render(<TestComponent />);

        // Should render without infinite loop
        await waitFor(() => {
            expect(container.textContent).toBe('Errors: 0');
        }, { timeout: 1000 });

        // Update store and verify no infinite loop
        act(() => {
            useTimesheetUIStore.getState().setError('ts1', 'ps1', {
                message: 'Test error',
                severity: 'error',
                timestamp: Date.now()
            });
        });

        await waitFor(() => {
            expect(container.textContent).toBe('Errors: 1');
        }, { timeout: 1000 });
    });
});
```

---

## Migration Guidelines

### From React Context to Zustand

```typescript
// Before: React Context pattern
const TimesheetContext = createContext<{
    draftChanges: Map<string, PayStub>;
    updateDraft: (id: string, changes: Partial<PayStub>) => void;
}>(null!);

const TimesheetProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const [draftChanges, setDraftChanges] = useState(new Map());

    const updateDraft = useCallback((id: string, changes: Partial<PayStub>) => {
        setDraftChanges(prev => new Map(prev).set(id, { ...prev.get(id), ...changes }));
    }, []);

    return (
        <TimesheetContext.Provider value={{ draftChanges, updateDraft }}>
            {children}
        </TimesheetContext.Provider>
    );
};

// After: Zustand store
const useTimesheetUIStore = create<TimesheetUIStore>((set) => ({
    draftChanges: new Map(),

    updateDraft: (id: string, changes: Partial<PayStub>) => {
        set((state) => ({
            draftChanges: new Map(state.draftChanges).set(id, {
                ...state.draftChanges.get(id),
                ...changes
            })
        }));
    }
}));

// Component migration
const PayStubComponent: React.FC = () => {
    // Before: Context usage
    // const { draftChanges, updateDraft } = useContext(TimesheetContext);

    // After: Zustand usage with selective subscription
    const hasDrafts = useTimesheetUIStore(state => state.draftChanges.size > 0);
    const updateDraft = useTimesheetUIStore(state => state.updateDraft);
};
```

### Gradual Migration Strategy

1. **Phase 1**: Create Zustand store alongside existing Context
2. **Phase 2**: Migrate leaf components to Zustand selectors
3. **Phase 3**: Migrate parent components and remove Context usage
4. **Phase 4**: Remove Context provider and related code

```typescript
// Phase 1-2: Hybrid approach
const PayStubComponent: React.FC = () => {
    // Keep Context for now
    const { draftChanges: contextDrafts } = useContext(TimesheetContext);

    // Start using Zustand for new features
    const zustandDrafts = useTimesheetUIStore(state => state.draftChanges);
    const updateZustandDraft = useTimesheetUIStore(state => state.updateDraft);

    // Use feature flag to switch between implementations
    const drafts = useFeatureFlag('use-zustand-drafts') ? zustandDrafts : contextDrafts;
};
```

---

## Troubleshooting Guide

### Common Issues and Solutions

#### Issue: Infinite Re-render Loop

**Symptoms**:
- Browser tab becomes unresponsive
- Console error: "Maximum update depth exceeded"
- React DevTools shows continuous re-renders

**Diagnosis**:
```typescript
// Check if you're creating objects in selectors
const badSelector = useStore(state => new Map(state.data)); // ❌ CAUSE
const badSelector2 = useStore(state => ({ ...state.obj })); // ❌ CAUSE
const badSelector3 = useStore(state => state.items.filter(x => x.active)); // ❌ CAUSE
```

**Solution**:
```typescript
// Use raw selector + useMemo pattern
const rawData = useStore(state => state.data);
const transformedData = useMemo(() => new Map(rawData), [rawData]);
```

#### Issue: Unnecessary Re-renders

**Symptoms**:
- Components re-render when unrelated data changes
- Performance issues with large lists
- React DevTools Profiler shows excessive renders

**Diagnosis**:
```typescript
// Check if you're subscribing to too much data
const entireStore = useStore(); // ❌ CAUSE - subscribes to everything
const allItems = useStore(state => state.items); // ❌ CAUSE - re-renders when any item changes
```

**Solution**:
```typescript
// Use selective subscriptions
const specificItem = useStore(
    useCallback(state => state.items.get(itemId), [itemId])
);
const itemCount = useStore(state => state.items.size); // Primitive value
```

#### Issue: State Not Persisting

**Symptoms**:
- Draft changes lost on page reload
- User has to re-enter data after browser refresh

**Solution**:
```typescript
// Add persist middleware
const useStore = create<Store>()(
    persist(
        (set, get) => ({
            // Store implementation
        }),
        {
            name: 'store-name',
            partialize: (state) => ({
                // Only persist critical data
                draftChanges: Array.from(state.draftChanges.entries())
            })
        }
    )
);
```

#### Issue: TypeScript Errors with Selectors

**Symptoms**:
- TypeScript compilation errors
- Type inference not working correctly

**Solution**:
```typescript
// Properly type your selectors
const typedSelector = useStore((state): SpecificType => state.specificData);

// Use type assertions only when necessary
const safeSelector = useStore(state => state.maybeUndefined ?? defaultValue);
```

### Debugging Tools

#### Development Console Commands

```typescript
// Add to window in development
if (process.env.NODE_ENV === 'development') {
    (window as any).debugZustand = {
        // Get current store state
        getState: () => useTimesheetUIStore.getState(),

        // Subscribe to all changes
        watchChanges: () => {
            return useTimesheetUIStore.subscribe((state) => {
                console.log('Store updated:', state);
            });
        },

        // Validate selector stability
        testSelector: (selector: (state: any) => any) => {
            const state = useTimesheetUIStore.getState();
            const result1 = selector(state);
            const result2 = selector(state);

            if (result1 !== result2) {
                console.warn('Unstable selector detected!', { selector, result1, result2 });
            } else {
                console.log('Selector is stable ✅');
            }
        }
    };
}
```

#### React DevTools Integration

```typescript
// Add to store for better debugging
const useTimesheetUIStore = create<TimesheetUIStore>()(
    devtools(
        persist(
            (set, get) => ({
                // Store implementation
            }),
            { name: 'timesheet-ui-storage' }
        ),
        { name: 'TimesheetUIStore' } // Shows in Redux DevTools
    )
);
```

---

## Conclusion

Following these Zustand best practices is **mandatory** for preventing critical bugs and ensuring optimal performance. The patterns documented here are based on real-world production issues and their solutions.

### Key Takeaways

1. **Never create objects in selectors** - Use raw selector + useMemo pattern
2. **Use selective subscriptions** - Components should only re-render when their data changes
3. **Maintain immutable updates** - Always create new instances for Maps/Sets/Objects
4. **Test selector stability** - Ensure selectors return stable references
5. **Follow scoped state patterns** - Use appropriate key scoping for multi-instance components

### Enforcement

- **Code Reviews**: Use the provided checklist for all Zustand-related changes
- **Static Analysis**: Implement the suggested ESLint rules and grep-based detection
- **Testing**: Include selector stability and performance tests
- **Documentation**: Keep this document updated as new patterns emerge

### Getting Help

If you encounter Zustand-related issues:

1. **Check this document** for common patterns and solutions
2. **Use debugging tools** provided in the troubleshooting section
3. **Ask for code review** before implementing complex selector logic
4. **Update this document** when you discover new patterns or solutions

Remember: **Prevention is better than debugging**. Following these patterns prevents entire classes of bugs from occurring in the first place.
